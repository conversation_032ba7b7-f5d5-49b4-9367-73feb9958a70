import re

def _clean_category_title(title):
    """Remove 'نامه X -' or 'خطبه X -' prefix from title"""
    if not title:
        return title
    
    # Remove pattern like "نامه 1 -", "نامه 23 -", "خطبه 1 -", "خطبه 23 -", "حکمت 1 -", etc.
    cleaned_title = re.sub(r'^(نامه|خطبه|حکمت)\s+\d+\s*-\s*', '', title.strip())
    
    # If cleaned title is empty, use the original title
    if not cleaned_title.strip():
        return title.strip()
        
    return cleaned_title.strip()

# Test with the problematic title
test_title = "حکمت 473 -"
result = _clean_category_title(test_title)
print(f"Input: '{test_title}'")
print(f"Output: '{result}'")
print(f"Is empty: {not result}")
print(f"Is empty after strip: {not result.strip()}")