import os
import django
from django.db.models import Count
from decimal import Decimal

# تنظیم محیط جنگو
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.account.models import User

def calculate_language_stats():
    # دریافت تعداد کل کاربران
    total_users = User.objects.count()
    
    # گروه‌بندی کاربران بر اساس زبان و شمارش آنها
    language_stats = User.objects.values('language__name').annotate(
        count=Count('id')
    ).order_by('-count')
    
    print("\nآمار زبان‌های کاربران:")
    print("=" * 60)
    print(f"{'زبان':<25} {'تعداد':<10} {'درصد':>10}")
    print("-" * 60)
    
    for stat in language_stats:
        language_name = stat['language__name'] or 'نامشخص'
        count = stat['count']
        percentage = (Decimal(count) / Decimal(total_users)) * 100
        print(f"{language_name:<25} {count:<10} {percentage:>10.2f}%")
    
    print("=" * 60)
    print(f"تعداد کل کاربران: {total_users}")

if __name__ == '__main__':
    calculate_language_stats() 