import json
import os.path
import re
import time
import sys

sys.path.append('/home/<USER>/Documents/porjects/najm')
sys.path.append('/home/<USER>/web/')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

from django.core.files import File
from django_countries import countries
from geopy.geocoders import Nominatim

from selenium import webdriver
from selenium.common import NoSuchElementException
from selenium.webdriver import Proxy
from selenium.webdriver.common.by import By
from selenium.webdriver.common.proxy import Proxy, ProxyType

from apps.habibnet.models import Center, CenterImages, CenterReview
from utils.download_file import download_file_from_remote

options = webdriver.FirefoxOptions()


class GooglePlaceGrabber:
    driver = None

    def __init__(self, driver=None):
        self.driver = driver or webdriver.Firefox(
            options=options,
        )
        time.sleep(50)

    def get_about(self):
        time.sleep(2)
        try:
            self.xpath('//div[text()="About"]').click()
            about_box = self.css_select('div.m6QErb.DxyBCb.kA9KIf.dS8AEf')
            sections = about_box.find_elements(By.XPATH, 'child::*')
            data = []
            for section in sections:
                try:
                    if self.css_select('h2', elem=section):
                        data.append({
                            'title': self.css_select('h2', elem=section).text,
                            'content': [e.text for e in self.css_select_all('ul li', elem=section)]
                        })
                    elif txt := section.text.strip():
                        data.append(txt)

                except Exception as e:
                    print('get_about error: ', e)

            return data
        except Exception as e:
            print(e)
            return []

    def get_reviews(self):
        try:
            time.sleep(1)
            self.xpath('//div[text()="Reviews"]').click()
            time.sleep(2)
            reviews_box = self.css_select('div.m6QErb.DxyBCb.kA9KIf.dS8AEf')
            self.xpath('//button[@aria-label="Sort reviews"]', ).click()
            time.sleep(0.5)

            # sort by most relevant
            self.xpath('//div[@id="action-menu"]//div[text()="Most relevant"]/parent::div/parent::div').click()
            time.sleep(1)

            reviews = self.xpath_all("//div[@jsaction and @data-review-id]", ) or []

            data = []
            for section in reviews[:30]:
                data.append({
                    'avatar': (
                            self.css_select('img.NBa7we', elem=section, attr='src') or ""
                    ).replace("=w36-h36", "=w360-h360"),
                    'name': self.css_select('div.d4r55', attr='text', elem=section),
                    'stars': (self.css_select('span.kvMYJc', attr='aria-label') or '').replace(' stars', ''),
                    'review': self.css_select('.wiI7pd', elem=section, attr='text'),
                    'date': self.css_select('span.rsqaWe', elem=section, attr='text')
                })

            return data
        except Exception as e:
            print("error getting reviews:", e)
            return []

    def get_gallery_images(self):
        try:
            self.xpath('//button[@class="hh2c6 " and contains(@aria-label, "Overview")]').click()
            time.sleep(0.5)

            # document.querySelectorAll('.e07Vkf.kA9KIf')[4]
            self.xpath('//button[@class="ofKBgf " and @aria-label="All"]').click()

            time.sleep(2)
            scroll_script = "document.querySelector('.m6QErb.DxyBCb.kA9KIf.dS8AEf').scrollBy(0, document.querySelector('.m6QErb.DxyBCb.kA9KIf.dS8AEf').scrollHeight)"
            self.driver.execute_script(scroll_script)
            time.sleep(1)
            self.driver.execute_script(scroll_script)
            time.sleep(1)
            self.driver.execute_script(scroll_script)
            self.driver.implicitly_wait(2)
            time.sleep(3)

            images_elem = self.xpath_all('//div[@class="U39Pmb" and @role="img"]') or []
            images = []
            for image in images_elem[:15]:
                try:
                    style_attr = image.get_attribute('style')
                    link = re.search(r'url\("(.*)"\)', style_attr).groups()[0]

                    # maximizing image by replacing specified sizes
                    if "pitch" in link:
                        # for 3d view images
                        link = link.replace("&w=203&h=100", "&w=1300&h=1300")
                        if link == "//:=w1300-h1300":
                            print(link)
                            continue

                        images.append(link)
                    else:
                        link = link[:link.rfind('=')] + "=w1300-h1300"
                        if link == "//:=w1300-h1300":
                            print(link)
                            continue

                        images.append(link)

                except Exception as e:
                    print('image error:', str(e))
                    pass

            return images
        except Exception as e:
            print('getting gallery error:', e)

    def get_stars(self):
        try:
            return self.css_select(".ceNzKf").find_element(By.XPATH, "preceding-sibling::*[1]").text
        except:
            return ''

    def xpath(self, query, attr=None, elem=None):
        elem = elem or self.driver
        try:
            elem = elem.find_element(By.XPATH, query)
            if attr:
                if attr == 'text':
                    return elem.text
                return elem.get_attribute(attr)
            return elem
        except NoSuchElementException:
            return

    def xpath_all(self, query, elem=None):
        elem = elem or self.driver
        try:
            return elem.find_elements(By.XPATH, query)
        except NoSuchElementException:
            return

    def css_select_all(self, query, elem=None):
        elem = elem or self.driver
        try:
            return elem.find_elements(By.CSS_SELECTOR, query)
        except NoSuchElementException:
            return

    def css_select(self, query, attr=None, elem=None):
        elem = elem or self.driver
        try:
            elem = elem.find_element(By.CSS_SELECTOR, query)
            if attr:
                if attr == 'text':
                    return elem.text
                return elem.get_attribute(attr)
            return elem
        except NoSuchElementException:
            return

    def crawl_link(self, link):
        self.driver.get(link)
        data = {
            'link': link,
            'name': self.css_select("h1", 'text'),
            'stars': self.get_stars(),
            'address': self.css_select('.Io6YTe.kR99db', 'text'),
            'phone': self.xpath('//button[@data-tooltip="Copy phone number"]//div[contains(@class, "Io6YTe")]', 'text'),
            'plus_code': self.xpath('//button[@data-tooltip="Copy plus code"]//div[contains(@class, "Io6YTe")]',
                                    'text'),
            'website': self.xpath('//a[@data-tooltip="Open website"]//div[contains(@class, "Io6YTe")]', 'text'),
            'about': self.get_about(),
            'reviews': self.get_reviews(),
            'gallery': self.get_gallery_images(),
            'lat_lon': self.get_lat_lon(),
        }

        return data

    def get_lat_lon(self):
        url = self.driver.current_url
        try:
            lat_lon = ",".join(url[url.find('@') + 1: url.find('/data')].split(',')[:2])
            geolocator = Nominatim(user_agent="Humboldt-Universität")
            location = geolocator.reverse(lat_lon)
            address = location.raw['address']
            return {
                'lat_lon': lat_lon,
                'address': address,
            }

        except Exception:
            return {
                'lat_lon': '',
                'address': {},
            }


def import_crawled_data():
    def get_country(code: str):
        return dict(countries).get(code.upper())

    def download_image(url):
        time.sleep(1)
        file, _ = download_file_from_remote(url, '/tmp/centers')
        if file:
            return File(open(file, 'rb'), os.path.basename(file))

    def gallery(urls, center):
        if urls:
            for url in urls:
                file = download_image(url)
                if file:
                    CenterImages.objects.create(
                        center=center,
                        image=file,
                    )

    def reviews(items, center):
        for item in items:
            CenterReview.objects.create(
                **{
                    'center': center,
                    'name': item['name'],
                    'google_avatar': download_image(item['avatar']),
                    'is_google': True,
                    'has_seen': True,
                    'review': item['review'] or '',
                    'stars': int(item['stars'])
                }
            )

    def parse_about(about):
        content = ""
        for i in about:
            if type(i) is str:
                content += i
            else:
                title, c = i['title'], ", ".join(i['content'])
                content += f"\n{title}:\n{c}"
        return content

    data_list = json.load(open("centers_data.json"))

    for row in data_list:
        try:
            lat_lon = row['lat_lon']
            if type(lat_lon) is str:
                continue
            obj = Center.objects.get_or_create(
                google_map_link=row['link'],
                name=row['name'],
                status=Center.Status.approved,
                lat=lat_lon['lat_lon'].split(',')[0],
                lon=lat_lon['lat_lon'].split(',')[1],
                address=row['address'],
                website=row['website'],
                phone_numbers=row['phone'] or '',
                country=get_country(lat_lon['address'].get('country_code')),
                city=lat_lon['address'].get('city') or lat_lon['address'].get('county'),
                stars=float(row['stars'] or 4),
                header_image=download_image(row['thumbnail']),
                about_center=parse_about(row['about']),
            )
            gallery(row['gallery'], obj)
            reviews(row['reviews'], obj)

        except Exception as e:
            print(row['link'], ' failed ->', str(e))


if __name__ == '__main__':
    import_crawled_data()

    # Crawl
    # centers = json.load(open("all_centers.json"))
    #
    # if os.path.exists("centers_data.json"):
    #     data_list = json.load(open("centers_data.json"))
    # else:
    #     data_list = []
    #
    #
    # def is_already_crawled(link):
    #     for i in data_list:
    #         if i['link'] == link:
    #             return True
    #     return False
    #
    #
    # spider = GooglePlaceGrabber()
    #
    # for num, center in enumerate(centers[:700]):
    #     thumbnail = center['thumbnail']
    #     link = center['url'].replace('hl=de', 'hl=en')
    #     if is_already_crawled(link):
    #         continue
    #
    #     data = spider.crawl_link(link)
    #
    #     data['thumbnail'] = thumbnail[:thumbnail.rfind('=')] + "=w1300-h1300"
    #     data_list.append(data)
    #
    #     time.sleep(3)
    #     json.dump(data_list, open('centers_data.json', 'w'), indent=4, ensure_ascii=False)
    #     print(f"center number {num} crawled")
