"""
Habib Command RAG Manager for V2
"""

import logging
from apps.quran.models import <PERSON><PERSON><PERSON>, QuranSura
from django.db.models import Min
from .resource import get_message, get_audio_link

logger = logging.getLogger(__name__)

class RAGResponseManagerABC:
    """
    Abstract base class for RAG response managers
    """
    
    def _handle_result_field(self, response):
        """
        Handles the case where response has a 'result' field.
        Moves all contents of 'result' up to the parent level.
        
        Args:
            response (dict): The response to process
            
        Returns:
            dict: The processed response
        """
        try:
            if isinstance(response, dict) and 'result' in response:
                # Get the result field
                result = response.pop('result')
                
                # If result is a dictionary, merge its contents with the response
                if isinstance(result, dict):
                    for key, value in result.items():
                        response[key] = value
                        
            return response
        except Exception as exp:
            logger.error(f"Error in _handle_result_field: {str(exp)}")
            return response
            
    def _handle_metadata_field(self, response):
        """
        Handles the case where response has a 'metadata' field.
        If metadata is a list of dictionaries, moves all key-value pairs from each metadata item up to the parent level.
        If metadata is a dictionary, moves all key-value pairs from the metadata dictionary up to the parent level.
        
        Args:
            response (dict): The response to process
            
        Returns:
            dict: The processed response
        """
        try:
            # Check if response is a dictionary and contains metadata field
            if not isinstance(response, dict) or 'metadata' not in response:
                return response
                
            # Extract and remove metadata from response
            metadata = response.pop('metadata')
            
            # Process metadata based on its type
            if isinstance(metadata, list):
                # Case 1: metadata is a list of dictionaries
                self._process_metadata_list(response, metadata)
            elif isinstance(metadata, dict):
                # Case 2: metadata is a dictionary
                self._process_metadata_dict(response, metadata)
                
            return response
        except Exception as exp:
            logger.error(f"Error in _handle_metadata_field: {str(exp)}")
            return response
            
    def _process_metadata_list(self, response, metadata_list):
        """
        Process a list of metadata dictionaries and add their key-value pairs to the response.
        
        Args:
            response (dict): The response to update
            metadata_list (list): List of metadata dictionaries
        """
        for metadata_item in metadata_list:
            if isinstance(metadata_item, dict):
                self._process_metadata_dict(response, metadata_item)
                
    def _process_metadata_dict(self, response, metadata_dict):
        """
        Process a metadata dictionary and add its key-value pairs to the response.
        
        Args:
            response (dict): The response to update
            metadata_dict (dict): Metadata dictionary
        """
        for key, value in metadata_dict.items():
            response[key] = value
    
    def _custom_response_quran(self, response):
        """
        Customizes the response for Quran-related queries
        
        Args:
            response (dict): The response from the RAG system
            
        Returns:
            dict: The customized response
        """
        try:
            info = response.get('additional_info', {})
            surah = info.get('surah')
            ayah = info.get("ayah")
            page = info.get("page")
            part = info.get("part")
            
            # Case 1: Only page is specified
            if surah is None and ayah is None and page is not None and part is None:
                result = QuranVerse.objects.filter(page=page).aggregate(first_ayah_index=Min('index'))
                response['additional_info']['ayah'] = result.get('first_ayah_index')
        
            # Case 2: Only surah is specified
            elif surah is not None and ayah is None and page is None and part is None:
                result = QuranVerse.objects.filter(sura_id=surah).aggregate(first_ayah_index=Min('index'))
                response['additional_info']['ayah'] = result.get('first_ayah_index')
        
            # Case 3: Both surah and ayah are specified
            elif surah is not None and ayah is not None and page is None and part is None:
                verse = QuranVerse.objects.get(sura_id=surah, number_in_surah=ayah)
                response['additional_info']['ayah'] = verse.index
        
            # Case 4: Only part is specified
            elif surah is None and ayah is None and page is None and part is not None:
                result = QuranVerse.objects.filter(juz=part).aggregate(first_ayah_index=Min('index'))
                response['additional_info']['ayah'] = result.get('first_ayah_index')
        
            # Case 5: Both page and ayah are specified
            elif surah is None and ayah is not None and page is not None and part is None:
                verse = QuranVerse.objects.get(page=page, number_in_surah=ayah)
                response['additional_info']['ayah'] = verse.index
        
            return response
        except Exception as exp:
            logger.error(f"Error in _custom_response_quran: {str(exp)}")
            # Add error message but don't change the class
            response['error'] = str(exp)
            return response
    
    def _custom_response_global(self, response):
        """
        Customizes the response for global commands
        
        Args:
            response (dict): The response from the RAG system
            
        Returns:
            dict: The customized response
        """
        try:
            if response.get("action") == "change_language":
                additional_info = response.get("additional_info")

                # Check if additional_info has a value
                if additional_info:
                    # Convert to a new dictionary
                    response["additional_info"] = {
                        "language_code": additional_info
                    }
            return response            
        except Exception as exp:
            logger.error(f"Error in _custom_response_global: {str(exp)}")
            # Add error message but don't change the class
            response['error'] = str(exp)
            return response
    
    def _custom_response_ahkam(self, response):
        """
        Customizes the response for Ahkam-related queries
        
        Args:
            response (dict): The response from the RAG system
            
        Returns:
            dict: The customized response
        """
        try:
            response['consultant'] = "<EMAIL>",
            response['status'] = True
            return response
        except Exception as exp:
            logger.error(f"Error in _custom_response_ahkam: {str(exp)}")
            # Add error message but don't change the class
            response['error'] = str(exp)
            return response
    def _custom_response_faq(self, response):
        try:
            response['consultant'] = "<EMAIL>"
            response['status'] = True
            return response
        except Exception as exp:
            logger.error(f"Error in _custom_response_faq: {str(exp)}")
            # Add error message but don't change the class
            response['error'] = str(exp)
            return response


class RAGResponseManager(RAGResponseManagerABC):
    """
    Manager class for handling RAG responses based on their class
    """
    
    def __init__(self, rag_response):
        """
        Initialize the manager with the RAG response
        
        Args:
            rag_response (dict): The response from the RAG system
        """
        self.response = rag_response
        self.response_class = rag_response.get('class', 'error')
    
    def process(self, language_code='en'):
        """
        Process the RAG response based on its class and create a standardized response
        
        Args:
            language_code (str): The language code for the response messages
            
        Returns:
            dict: The standardized processed response
        """
        # Special handling for help class - always set status to error
        if self.response_class == 'help':
            status = 'error'
        else:
            # Determine the status based on the class
            status = 'error' if self.response_class == 'error' else 'success'
        
        # Process the response based on its class
        processed_response = self.response
        if self.response_class != 'error':
            processor_method = getattr(self, f"process_{self.response_class}", None)
            
            if processor_method and callable(processor_method):
                logger.info(f"Processing response with class: {self.response_class}")
                processed_response = processor_method()
            else:
                logger.info(f"No specific processor for class: {self.response_class}")
        else:
            logger.warning("Received error response from RAG system")
        
        # Get the appropriate message and audio link
        message_text = get_message(language_code, status, self.response_class)
        audio = get_audio_link(language_code, self.response_class)
        
        # Remove 'retrieval' field from processed_response if it exists
        if isinstance(processed_response, dict) and 'retrieval' in processed_response:
            processed_response = {k: v for k, v in processed_response.items() if k != 'retrieval'}
        
        # Handle result field if it exists
        if isinstance(processed_response, dict):
            processed_response = self._handle_result_field(processed_response)
            # After handling result field, handle metadata field
            processed_response = self._handle_metadata_field(processed_response)
        
        # Create the standardized response
        standardized_response = {
            "message_text": message_text,
            "message_status": status,
            "audio": audio,
            "data": processed_response
        }
        
        return standardized_response
    
    def process_quran(self):
        """
        Process Quran-related responses
        
        Returns:
            dict: The processed response
        """
        logger.info("Processing Quran response")
        return self._custom_response_quran(self.response)
    
    def process_ahkam(self):
        """
        Process Ahkam-related responses
        
        Returns:
            dict: The processed response
        """
        logger.info("Processing Ahkam response")
        return self._custom_response_ahkam(self.response)
    
    def process_global(self):
        """
        Process global command responses
        
        Returns:
            dict: The processed response
        """
        logger.info("Processing Global response")
        return self._custom_response_global(self.response)
    
    def process_hadis(self):
        """
        Process hadis-related responses
        
        Returns:
            dict: The processed response
        """
        logger.info("Processing hadis response")
        return self.response
    
    def process_hussainiya(self):
        """
        Process Hussainiya-related responses
        
        Returns:
            dict: The processed response
        """
        logger.info("Processing Hussainiya response")
        return self.response
    
    def process_library(self):
        """
        Process Library-related responses
        
        Returns:
            dict: The processed response
        """
        logger.info("Processing Library response")
        return self.response
    
    def process_calendar(self):
        """
        Process Calendar-related responses
        
        Returns:
            dict: The processed response
        """
        logger.info("Processing Calendar response")
        return self.response
    
    def process_duas(self):
        """
        Process duas-related responses
        
        Returns:
            dict: The processed response
        """
        logger.info("Processing duas response")
        return self.response
    
    def process_help(self):
        """
        Process Help-related responses
        
        Returns:
            dict: The processed response
        """
        logger.info("Processing Help response")
        return self.response
    
    def process_faq(self):
        """
        Process Faq-related responses
        
        Returns:
            dict: The processed response
        """
        logger.info("Processing Faq response")
        return self._custom_response_faq(self.response)