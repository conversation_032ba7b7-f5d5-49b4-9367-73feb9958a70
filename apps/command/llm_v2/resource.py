"""
Resource file for RAG response messages and audio links
"""

import logging

logger = logging.getLogger(__name__)

# Messages for different languages and classes
MESSAGES = [
    # General messages (no class specified)
    {"language_code": "en", "status": "success", "message": "Command processed successfully."},
    {"language_code": "en", "status": "error", "message": "An error occurred while processing your command."},
    {"language_code": "fa", "status": "success", "message": "دستور با موفقیت پردازش شد."},
    {"language_code": "fa", "status": "error", "message": "خطایی در پردازش دستور شما رخ داد."},
    
    # Quran class messages
    {"language_code": "en", "status": "success", "class": "quran", "message": "Quran verse retrieved successfully."},
    {"language_code": "en", "status": "error", "class": "quran", "message": "Error retrieving Quran verse."},
    {"language_code": "fa", "status": "success", "class": "quran", "message": "آیه قرآن با موفقیت بازیابی شد."},
    {"language_code": "fa", "status": "error", "class": "quran", "message": "خطا در بازیابی آیه قرآن."},
    
    # Ahkam class messages
    {"language_code": "en", "status": "success", "class": "ahkam", "message": "Islamic ruling retrieved successfully."},
    {"language_code": "en", "status": "error", "class": "ahkam", "message": "Error retrieving Islamic ruling."},
    {"language_code": "fa", "status": "success", "class": "ahkam", "message": "حکم اسلامی با موفقیت بازیابی شد."},
    {"language_code": "fa", "status": "error", "class": "ahkam", "message": "خطا در بازیابی حکم اسلامی."},
    
    # Hadith class messages
    {"language_code": "en", "status": "success", "class": "hadith", "message": "Hadith retrieved successfully."},
    {"language_code": "en", "status": "error", "class": "hadith", "message": "Error retrieving hadith."},
    {"language_code": "fa", "status": "success", "class": "hadith", "message": "حدیث با موفقیت بازیابی شد."},
    {"language_code": "fa", "status": "error", "class": "hadith", "message": "خطا در بازیابی حدیث."},
    
    # Global class messages
    {"language_code": "en", "status": "success", "class": "global", "message": "Global command executed successfully."},
    {"language_code": "en", "status": "error", "class": "global", "message": "Error executing global command."},
    {"language_code": "fa", "status": "success", "class": "global", "message": "دستور عمومی با موفقیت اجرا شد."},
    {"language_code": "fa", "status": "error", "class": "global", "message": "خطا در اجرای دستور عمومی."},
    
    # Hussainiya class messages
    {"language_code": "en", "status": "success", "class": "hussainiya", "message": "Hussainiya information retrieved successfully."},
    {"language_code": "en", "status": "error", "class": "hussainiya", "message": "Error retrieving Hussainiya information."},
    {"language_code": "fa", "status": "success", "class": "hussainiya", "message": "اطلاعات حسینیه با موفقیت بازیابی شد."},
    {"language_code": "fa", "status": "error", "class": "hussainiya", "message": "خطا در بازیابی اطلاعات حسینیه."},
    
    # Library class messages
    {"language_code": "en", "status": "success", "class": "library", "message": "Library information retrieved successfully."},
    {"language_code": "en", "status": "error", "class": "library", "message": "Error retrieving library information."},
    {"language_code": "fa", "status": "success", "class": "library", "message": "اطلاعات کتابخانه با موفقیت بازیابی شد."},
    {"language_code": "fa", "status": "error", "class": "library", "message": "خطا در بازیابی اطلاعات کتابخانه."},
    
    # Calendar class messages
    {"language_code": "en", "status": "success", "class": "calendar", "message": "Calendar information retrieved successfully."},
    {"language_code": "en", "status": "error", "class": "calendar", "message": "Error retrieving calendar information."},
    {"language_code": "fa", "status": "success", "class": "calendar", "message": "اطلاعات تقویم با موفقیت بازیابی شد."},
    {"language_code": "fa", "status": "error", "class": "calendar", "message": "خطا در بازیابی اطلاعات تقویم."},
    
    # Mafatih class messages
    {"language_code": "en", "status": "success", "class": "mafatih", "message": "Mafatih information retrieved successfully."},
    {"language_code": "en", "status": "error", "class": "mafatih", "message": "Error retrieving Mafatih information."},
    {"language_code": "fa", "status": "success", "class": "mafatih", "message": "اطلاعات مفاتیح با موفقیت بازیابی شد."},
    {"language_code": "fa", "status": "error", "class": "mafatih", "message": "خطا در بازیابی اطلاعات مفاتیح."},
    
    # Help class messages
    {"language_code": "en", "status": "success", "class": "help", "message": "Help information retrieved successfully."},
    {"language_code": "en", "status": "error", "class": "help", "message": "Error retrieving help information."},
    {"language_code": "fa", "status": "success", "class": "help", "message": "اطلاعات راهنما با موفقیت بازیابی شد."},
    {"language_code": "fa", "status": "error", "class": "help", "message": "خطا در بازیابی اطلاعات راهنما."},
]

# Audio links for different languages and classes
AUDIO_LINKS = [
    # Quran class audio
    {"language_code": "en", "class": "quran", "audio_link": "https://example.com/audio/quran_en.mp3"},
    {"language_code": "fa", "class": "quran", "audio_link": "https://example.com/audio/quran_fa.mp3"},
    
    # Ahkam class audio
    {"language_code": "en", "class": "ahkam", "audio_link": "https://example.com/audio/ahkam_en.mp3"},
    {"language_code": "fa", "class": "ahkam", "audio_link": "https://example.com/audio/ahkam_fa.mp3"},

    # Help class audio
    {"language_code": "en", "class": "help", "audio_link": "https://habibapp.com/static/uploads/main/48/6d/486df679-0265-4ff8-99f1-1dbe32287f58/help_en.wav"},

    # Hadith class audio
    {"language_code": "en", "class": "hadith", "audio_link": "https://example.com/audio/hadith_en.mp3"},
    {"language_code": "fa", "class": "hadith", "audio_link": "https://example.com/audio/hadith_fa.mp3"},
    
    # Global class audio
    {"language_code": "en", "class": "global", "audio_link": "https://example.com/audio/global_en.mp3"},
    {"language_code": "fa", "class": "global", "audio_link": "https://example.com/audio/global_fa.mp3"},
]

def get_message(language_code, status, class_name=None):
    """
    Get the appropriate message based on language code, status, and class name
    
    Args:
        language_code (str): The language code (e.g., 'en', 'fa')
        status (str): The status of the response ('success' or 'error')
        class_name (str, optional): The class name of the response
        
    Returns:
        str: The appropriate message
    """
    # Default to English if language code is not supported
    if language_code not in ['en', 'fa']:
        language_code = 'en'
    
    # First try to find a message with matching class name
    if class_name:
        for message in MESSAGES:
            if (message.get('language_code') == language_code and 
                message.get('status') == status and 
                message.get('class') == class_name):
                return message.get('message')
    
    # If no class-specific message found or no class name provided, 
    # look for a general message
    for message in MESSAGES:
        if (message.get('language_code') == language_code and 
            message.get('status') == status and 
            'class' not in message):
            return message.get('message')
    
    # Fallback to English error message if nothing else matches
    return "An error occurred while processing your command."

def get_audio_link(language_code, class_name):
    """
    Get the appropriate audio link based on language code and class name
    
    Args:
        language_code (str): The language code (e.g., 'en', 'fa')
        class_name (str): The class name of the response
        
    Returns:
        dict: A dictionary containing the audio link, or an empty dict if no audio is available
    """
    # Default to English if language code is not supported
    if language_code not in ['en', 'fa']:
        language_code = 'en'
    
    # Try to find an audio link with matching language code and class name
    for audio in AUDIO_LINKS:
        if (audio.get('language_code') == language_code and 
            audio.get('class') == class_name):
            return {"audio_link": audio.get('audio_link')}
    
    # Return empty dict if no audio link found
    return None