
from rest_framework import serializers
from .models import CommandHelp


class CommandHelpSerializer(serializers.ModelSerializer):
    description = serializers.SerializerMethodField()
    
    class Meta:
        model = CommandHelp
        fields = ('service', 'description')
        
    def get_description(self, obj):
        request = self.context.get('request')
        return obj.get_description(request.LANGUAGE_CODE)
    
