"""
Habib Command Ai Prompt
"""
import logging
import json
from collections import OrderedDict
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from rest_framework.generics import ListAPIView
from rest_framework.authentication import TokenAuthentication
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.response import Response

from apps.command.llm.manager import AIRequestManager
from apps.command.models import CommandLog, CommandHelp
from apps.command.serializer import CommandHelpSerializer
from apps.command.tasks.send_log_tel import send_commandlog_to_telegram
logger = logging.getLogger(__name__)


@csrf_exempt
@api_view(['POST'])
# @authentication_classes([TokenAuthentication])
def handle_request(request):
    """Handles incoming API requests and routes them to the relevant handler."""
    logger.info(f"API called by user: {request.user}")
    if request.method != 'POST':
        response_data = {'error': 'Invalid request method'}
        status_code = 405
        return JsonResponse(response_data, status=status_code)


    status_code = 200
    response_data = None
    command_log = CommandLog(
        user=request.user if request.user.is_authenticated else None,
        started_at=timezone.now()
    )  
    
    try:
        data = json.loads(request.body)
        command = data.get('command', '')
        if not command:
            response_data = {'error': 'No command provided'}
            status_code = 400
            return JsonResponse(response_data, status=status_code)

        command_log.command = command
        # Classify the command using GPT based on central handler
        category = classify_request(command)
        command_log.category = category
        # Route to the appropriate handler based on classification
        if category == "Quran App":
            response_data = handle_quran_request(command)
        elif category == "Islamic Rulings (Ahkam)":
            response_data = handle_ahkam_request(command)
        elif category == "Global Commands":
            response_data = handle_global_command(command)
        elif category == "Hussainiya":
            response_data = handle_hussainiya_request(command)
        elif category == "Hadith":
            response_data = handle_hadith_request(command)
        elif category == "Library":
            response_data = handle_library_request(command)
        elif category == "Calendar":
            response_data = handle_calendar_request(command)
        elif category == "Mafatih":
            response_data = handle_mafatih_request(command)
        elif category == "Qibla":
            response_data = handle_qibla_request(command)
        else:
            response_data = {'error': 'Unknown command category'}
            status_code = 400
    except json.JSONDecodeError:
        response_data = {'error': 'Invalid JSON'}
        status_code = 400
    except Exception as e:
        # Log unexpected exceptions
        response_data = {'error': 'An unexpected error occurred'}
        status_code = 500
        # Optionally log the exception details somewhere
    finally:
        # Ensure that command_log is saved with the response, status code, and timestamps
        command_log.response = response_data
        command_log.status_code = status_code  # Save status_code here
        command_log.finished_at = timezone.now()
        command_log.save()
        send_commandlog_to_telegram.delay(command_log.id)  

    return JsonResponse(response_data, status=status_code)
                        

# Helper function to classify request using central handler prompt
def classify_request(command):
    """Classifies the user command into the appropriate section using GPT."""
    try:
        response_command = AIRequestManager(command).central()
        if "section" in response_command:
            section_value = response_command["section"]
            section_value = "Global Commands" if section_value == '' else section_value
            return section_value 
                    
        return "Global Commands"    
    except Exception as exp:
        print(f'----> {exp}')
        return "Global Commands"




# Handlers for each section using the helper function
def handle_quran_request(command):
    """Handles Quran-specific commands."""
    response = AIRequestManager(command).quran()
    ordered_response = OrderedDict([('content_type', 'quran')])
    ordered_response.update(response)
    
    return ordered_response

def handle_ahkam_request(command):
    """Handles Ahkam-specific commands."""
    response = AIRequestManager(command).ahkam()
    ordered_response = OrderedDict([('content_type', 'ahkam')])
    ordered_response.update(response)
    return ordered_response

def handle_global_command(command):
    """Handles general or global commands."""
    response = AIRequestManager(command).global_()
    ordered_response = OrderedDict([('content_type', 'global_command')])
    ordered_response.update(response)
    return ordered_response

def handle_hussainiya_request(command):
    """Handles Hussainiya-specific commands."""
    response = AIRequestManager(command).hussainiya()
    ordered_response = OrderedDict([('content_type', 'hussainiya')])
    ordered_response.update(response)
    return ordered_response

def handle_hadith_request(command):
    """Handles Hadith-specific commands."""
    response = AIRequestManager(command).hadith()
    ordered_response = OrderedDict([('content_type', 'hadith')])
    ordered_response.update(response)
    return ordered_response

def handle_library_request(command):
    """Handles Library-specific commands."""
    response = AIRequestManager(command).library()
    ordered_response = OrderedDict([('content_type', 'library')])
    ordered_response.update(response)
    return ordered_response

def handle_calendar_request(command):
    """Handles Calendar-specific commands."""
    response = AIRequestManager(command).calendar()
    ordered_response = OrderedDict([('content_type', 'calendar')])
    ordered_response.update(response)
    return ordered_response

def handle_mafatih_request(command):
    """Handles Mafatih-specific commands."""
    response = AIRequestManager(command).mafatih()
    ordered_response = OrderedDict([('content_type', 'mafatih')])
    ordered_response.update(response)
    return ordered_response

def handle_qibla_request(command):
    """Handles Qibla-specific commands."""
    return JsonResponse(OrderedDict([('content_type', 'qibla'), ('message', 'Qibla direction will be handled.')]))



class CommandHelpListView(ListAPIView):
    queryset = CommandHelp.objects.filter(is_active=True).exclude(service=CommandHelp.Service.MAIN).order_by('priority')
    serializer_class = CommandHelpSerializer
    pagination_class = None    
        
    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        main_service = CommandHelp.objects.filter(service=CommandHelp.Service.MAIN, is_active=True).first()
        help_text = ''
        if main_service:
            help_text = main_service.get_description(request.LANGUAGE_CODE)
            
        response.data = {
            "help_text": help_text,
            "results": response.data
        }
        return Response(response.data)        
