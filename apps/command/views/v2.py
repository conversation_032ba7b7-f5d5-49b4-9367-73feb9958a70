"""
Habib Command Ai Rag Retrival System
"""

import logging
import json
import requests
from collections import OrderedDict
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from rest_framework.generics import GenericAPIView
from rest_framework.authentication import TokenAuthentication
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from apps.command.llm_v2 import RAGResponseManager
from apps.command.models import CommandLog, CommandHelp
from apps.command.serializer import CommandHelpSerializer
from apps.command.tasks.send_log_tel import send_commandlog_to_telegram
logger = logging.getLogger(__name__)

# Create a session for connection pooling and reuse
dify_session = requests.Session()

class HandleRequestV2(GenericAPIView):
    """
    Generic API View that handles incoming command requests and processes them through RAG system
    """
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['command', 'language_code'],
            properties={
                'command': openapi.Schema(type=openapi.TYPE_STRING, description='User command or query'),
                'language_code': openapi.Schema(type=openapi.TYPE_STRING, description='Language code (e.g., "en", "fa")', default='en'),
            }
        ),
        operation_description="Handles incoming command requests and processes them through RAG system"
    )
    def post(self, request, *args, **kwargs):
        """Handles incoming API requests and routes them to the relevant handler."""
        logger.info(f"API called by user: {request.user}")
        command = request.data.get('command', '')
        language_code = request.data.get('language_code', 'en')
        status_code = status.HTTP_200_OK
        response_data = None
        command_log = CommandLog(
            user=request.user if request.user.is_authenticated else None,
            version='v2',
            command=command,
            language_code=language_code,
            started_at=timezone.now()
        )  
        
        try:            
            if not command:
                # Create a standardized error response for no command
                from apps.command.llm_v2.resource import get_message, get_audio_link
                message_text = get_message(language_code, 'error')
                audio = get_audio_link(language_code, 'error')
                
                response_data = {
                    "message_text": message_text,
                    "message_status": "error",
                    "audio": audio,
                    "data": {'error': 'No command provided'}
                }
                
                status_code = status.HTTP_400_BAD_REQUEST
                return Response(response_data, status=status_code)
            
            # Get the raw response from the RAG system
            rag_response = rag_request(command, language_code, request.user.id)
            if isinstance(rag_response, dict) and 'class' in rag_response:
                command_log.category = rag_response.get('class')
            # print(f'--> {rag_response}')
            # Process the response using the RAGResponseManager
            manager = RAGResponseManager(rag_response)
            response_data = manager.process(language_code=language_code)

        except Exception as e:
            # Log unexpected exceptions
            logger.error(f"Unexpected error: {str(e)}")
            
            # Create a standardized error response
            from apps.command.llm_v2.resource import get_message, get_audio_link
            message_text = get_message(language_code, 'error')
            audio = get_audio_link(language_code, 'error')
            
            response_data = {
                "message_text": message_text,
                "message_status": "error",
                "audio": audio,
                "data": {'error': 'An unexpected error occurred', 'details': str(e)}
            }
            
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        finally:
            # Ensure that command_log is saved with the response, status code, and timestamps
            command_log.response = response_data
            command_log.status_code = status_code  # Save status_code here
            command_log.finished_at = timezone.now()
            command_log.save()
            send_commandlog_to_telegram.delay(command_log.id)  

        return Response(response_data, status=status_code)



def rag_request(command, language_code, user_id):
    """
    Makes a request to the Dify API with the given command and language code.
    
    Args:
        command (str): The user's command/query
        language_code (str): The language code (e.g., 'en', 'fa')
        user_id: The ID of the user making the request
        
    Returns:
        dict: The 'outputs' dictionary from the Dify API response.
             Always includes a 'class' key, which will be set to 'error' if not present
             or if an error occurs during the request.
    """
    url = 'https://dify.nwhco.ir/api/v1/workflows/run'
    headers = {
        'Authorization': 'Bearer app-xSbdUYweUQwJsTlxeZq3Ckwl',
        'Content-Type': 'application/json'
    }
    
    
    # Construct the payload according to the Dify API requirements
    payload = {
        "inputs": {
            "query": command,
            "user_id": str(user_id)
        },
        "response_mode": "blocking",
        "user": user_id
    }
    
    
    try:
        logger.info(f"Sending request to Dify API: {payload}")
        # Comment out the actual API call and use a mock response instead
        response = dify_session.post(
            url, 
            headers=headers, 
            json=payload, 
            timeout=20
        )
        response.raise_for_status()  # Raise an exception for HTTP errors
        response_data = response.json()
        # Use a mock response for testing
        response_data_help = {
            'task_id': 'e0c8192f-1e7a-4b4e-b27e-e5486fe4ed0f', 
            'workflow_run_id': '517e9424-0477-4bba-961d-e08658224df4', 
            'data': {
                'id': '517e9424-0477-4bba-961d-e08658224df4', 
                'workflow_id': '5e680d83-1baa-4800-bed3-f2a43593d045', 
                'status': 'succeeded', 
                'outputs': {'class ': 'help', 'query': 'سلام کمک'}, 
                'error': None, 
                'elapsed_time': 1.3576459549367428, 
                'total_tokens': 1182, 
                'total_steps': 4, 
                'created_at': 1746632546, 
                'finished_at': 1746632547
            }
        }

        # logger.info(f"Received response from Dify API: type={type(response_data)}, data:{response_data}")
        
        # Extract the outputs dictionary from the nested response structure
        # The structure is: response_data -> data -> outputs
        if 'data' in response_data and 'outputs' in response_data['data']:
            outputs = response_data['data']['outputs']
            print(f'-outputs--> {outputs}')
            
            # Clean up the class key if it has a space
            if 'class ' in outputs:
                outputs['class'] = outputs.pop('class ')
                
            return outputs
        else:
            # If no outputs, return a dictionary with class=error
            return {"class": "error", "error": "No outputs in response"}
            
    except requests.exceptions.Timeout as e:
        error_msg = f"Timeout error when connecting to Dify API (20s limit): {str(e)}"
        logger.error(error_msg)
        return {
            "class": "error",
            "error": "AI service request timed out after 20 seconds", 
            "details": str(e)
        }
    except requests.exceptions.RequestException as e:
        error_msg = f"Error making request to Dify API: {str(e)}"
        logger.error(error_msg)
        return {
            "class": "error",
            "error": "Failed to communicate with AI service", 
            "details": str(e)
        }
    except json.JSONDecodeError as e:
        error_msg = f"Error parsing JSON response from Dify API: {str(e)}"
        logger.error(error_msg)
        return {
            "class": "error",
            "error": "Invalid response from AI service",
            "details": str(e)
        }
    except Exception as e:
        error_msg = f"Unexpected error in rag_request: {str(e)}"
        logger.error(error_msg)
        return {
            "class": "error",
            "error": "An unexpected error occurred",
            "details": str(e)
        }
