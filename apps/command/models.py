
from django.db import models
from dj_language.field import LanguageField
from django.utils.translation import gettext as _



class CommandLog(models.Model):
    user = models.ForeignKey("account.User", on_delete=models.SET_NULL, null=True, blank=True)
    command = models.TextField(null=True, blank=True)
    category = models.CharField(max_length=255, null=True, blank=True)
    response = models.JSONField(null=True,blank=True)
    status_code = models.IntegerField(null=True, blank=True) 

    started_at = models.DateTimeField(null=True,blank=True)
    finished_at = models.DateTimeField(null=True,blank=True)
    version = models.CharField(max_length=255,null=True,blank=True,default='v1')
    language_code = models.CharField(max_length=255, null=True,blank=True)
    created_at = models.DateTimeField(auto_now_add=True) 




class CommandHelp(models.Model):
    class Service(models.TextChoices):
        HUSSAINIYAH = 'hussainiyah', _('Hussainiyah')
        LIBRARY = 'library', _('Library')
        TALK = 'Talk', _('Talk')
        MEET = 'meet', _('Meet')
        DUAS = 'mafatih', _('Duas')
        HADIS = 'hadith', _('Hadis')
        QURAN = 'quran', _('Quran')
        AHKAM = 'ahkam', _('Ahkam')
        COIN = 'coin', _('Coin')
        CALENDAR = 'calendar', _('Calendar')
        QIBLAH = 'qiblah', _('Qiblah')
        ELALHABIB = 'elalHabib', _('ElalHabib')
        MAIN = 'main', _('Main')        
            
    service = models.CharField(
        max_length=50,
        choices=Service.choices,
    )
    descriptions = models.JSONField(verbose_name=_('descriptions'), null=True, blank=True, default=dict)
    priority = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    
    def get_description(self, lang):
        try:
            for tr in self.descriptions:
                if tr['language_code'] == lang:
                    return tr['text']                  
            return self.descriptions[0]['text']
        except Exception as exp:
            print(f'---> {exp}')
            return None


    def __str__(self):
        return f"{self.service}"
    

    
    