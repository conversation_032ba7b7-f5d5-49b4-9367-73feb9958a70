
import random
# import pyarabic.araby as araby
# from fuzzywuzzy import fuzz
# from utils.similarity import find_similarity ,rm_sign
import json
import os


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
from apps.command.llm.clients import GroqClient
from apps.account.models import UserSettings, User
from django.db import transaction


input_file = "./EN.json"
output_file = "./output.json"


chunk_size = 2

prompt = """
You are an expert in linguistic and cultural translation.
Your task is to translate the **VALUE** of each key-value pair in the following JSON-like structure from English to [Persian].
These terms are used in a Shiite religious app, so please ensure the translations are respectful, culturally sensitive, and accurate.
Maintain the exact format of the structure, ensuring that only the **VALUE** fields are translated while keeping the keys unchanged.

Input:
{
    "About Us": "About Us",
    "ahkam": "Ahkam",
    "All": "All",
    "April": "April",

}

Output:
{
    "About Us": "[Translated_Value]",
    "ahkam": "[Translated_Value]",
    "All": "[Translated_Value]",
    "April": "[Translated_Value]",
}
"""



client = ClaudeClient()
with open(input_file, "r", encoding="utf-8") as f:
    data = json.load(f)
    
responses = []

# for i in range(0, len(data), chunk_size):
for i in range(0, 2, chunk_size):
    chunk = data[i:i + chunk_size]
    user_message = json.dumps(chunk, ensure_ascii=False)
    response = client.send(assistant_message, user_message)
    responses.append(response)

# ذخیره پاسخ‌ها در فایل JSON خروجی
with open(output_file, "w", encoding="utf-8") as f:
    json.dump(responses, f, ensure_ascii=False, indent=4)

print(f"Responses saved to {output_file}")










