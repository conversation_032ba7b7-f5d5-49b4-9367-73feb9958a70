import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

get_wsgi_application()


import json
import requests
from datetime import datetime

from celery import shared_task

from apps.command.models import CommandLog
from django.utils.timezone import localtime
from django.utils.timezone import is_naive, make_aware, localtime
import pytz
from datetime import timedelta


# تابع برای محاسبه زمان پاسخ
def calculate_response_time(started_at, finished_at):
    if not started_at or not finished_at:
        return "N/A"
    
    if is_naive(started_at):
        started_at = make_aware(started_at, timezone=pytz.UTC)
    if is_naive(finished_at):
        finished_at = make_aware(finished_at, timezone=pytz.UTC)
    
    response_time = finished_at - started_at
    return str(response_time).split(".")[0]  # حذف میکروثانیه‌ها

def escape_markdown_v2(text):
    escape_chars = '_*[]()~`>#+-=|{}.!'
    return ''.join(['\\' + char if char in escape_chars else char for char in str(text)])

def safe_localtime(dt):
    """تبدیل ایمن datetime به تایم محلی"""
    if dt is None:
        return "N/A"
    
    if is_naive(dt):
        dt = make_aware(dt, timezone=pytz.UTC)
    
    return localtime(dt).strftime('%b %d, %Y, %H:%M')

# ... [previous imports and setup] ...

@shared_task(ignore_result=True)
def send_commandlog_to_telegram(command_log):    
    command_log = CommandLog.objects.filter(id=command_log).first()
    user_email = "Anonymous User"

    try:
        if command_log.user:
            user_email = command_log.user.email or "Anonymous User"

        user_email = escape_markdown_v2(user_email)
        command = escape_markdown_v2(command_log.command)
        category = escape_markdown_v2(command_log.category)

        response_time = calculate_response_time(command_log.started_at, command_log.finished_at)
    except Exception as exp:
        return
           
    timestamps = f"""
⏳ *Response Time:* `{escape_markdown_v2(response_time)}`
🕒 *Created:* {escape_markdown_v2(safe_localtime(command_log.created_at))}"""

    response_info = ""
    if command_log.response:
        try:
            res = command_log.response
            # اگر response یک دیکشنری باشد
            if isinstance(res, dict):
                response_info = "📋 *Response Details:*\n"
                for key, value in res.items():
                    key_escaped = escape_markdown_v2(str(key))
                    value_escaped = escape_markdown_v2(str(value))
                    response_info += f"├─ *{key_escaped}:* `{value_escaped}`\n"
                response_info = response_info.rstrip("\n")  # حذف خط اضافی در انتها
            else:
                # اگر response لیست یا نوع دیگری باشد
                raw_response = escape_markdown_v2(json.dumps(res, ensure_ascii=False, indent=2))
                response_info = f"📦 *Raw Response:*\n```json\n{raw_response}\n```"
        except Exception as e:
            logger.error(f"Error formatting response: {str(e)}")
            raw_response = escape_markdown_v2(json.dumps(command_log.response, ensure_ascii=False, indent=2))
            response_info = f"📦 *Raw Response:*\n```json\n{raw_response}```"




    status_emoji = "✅" if command_log.status_code == 200 else "⚠️"
    status_code_escaped = escape_markdown_v2(str(command_log.status_code))
    status_text = 'Success' if command_log.status_code == 200 else 'Error'
    status_text_escaped = escape_markdown_v2(f'({status_text})')
    
    message = f"""
🚀 **New Command Log**
━━━━━━━━━━━━━━━━━━━━
🆔 *Log ID:* `{escape_markdown_v2(str(command_log.id))}`
📧 *User:* `{user_email}`
⌨️ *Command:* ` {command}`
🏷️ *Category:* {category}
━━━━━━━━━━━━━━━━━━━━
{response_info}
━━━━━━━━━━━━━━━━━━━━
{timestamps}
{status_emoji} *Status:* `{status_code_escaped}`
"""
    
    TELEGRAM_CHANNEL_ID = "-1002312061764"
    url = f"https://api.telegram.org/bot7207581748:AAFeymryw7S44D86LYfWqYK-tSNeV3TOwBs/sendMessage"
    payload = {
        'chat_id': TELEGRAM_CHANNEL_ID,
        'text': message,
        'parse_mode': 'MarkdownV2'
    }
    
    try:
        response = requests.post(url, data=payload)
        response.raise_for_status()
        return True
    except requests.exceptions.RequestException as e:
        print(f"--error-->: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Response content: {e.response.text}")
        return False    
    
if __name__ == '__main__':
    # command = CommandLog.objects.filter(id=5420).first()
    send_commandlog_to_telegram(command)