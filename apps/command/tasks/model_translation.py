"""
Celery tasks for model translation
"""
import time
from typing import List, Dict, Any, Optional
from celery import shared_task
from django.db import models
from dj_language.models import Language

try:
    from openai import OpenAI
except ImportError:
    OpenAI = None

try:
    import requests
    import json
except ImportError:
    requests = None
    json = None


# Translation Client Classes (copied from utils/model_translation.py)
class OpenAITranslationClient:
    """OpenAI client for translating text using GPT-4o-mini model"""
    
    def __init__(self, api_key: Optional[str] = None):
        if OpenAI is None:
            raise ImportError("OpenAI package not installed. Run: pip install openai")
            
        self.api_key = api_key or "***********************************************************************************************"
        self.client = OpenAI(api_key=self.api_key)
        self.model = "gpt-4o-mini"
        self.max_tokens = 4000
        self.temperature = 0.1
        
    def translate_text(self, text: str, target_language: str, target_language_name: str, 
                      source_language: str = "English") -> str:
        if not text or not text.strip():
            return text
            
        system_prompt = f"""You are an expert translator specializing in Islamic and religious content.
Your task is to translate content for a Shia Islamic application.

Guidelines:
1. Analyze the content type automatically (religious text, general content, HTML content, etc.)
2. Maintain the respectful and appropriate tone for Islamic content
3. Preserve the meaning and context accurately
4. Consider cultural sensitivity for the target language
5. Keep the same structure and formatting
6. Provide accurate, culturally appropriate translation
7. Return ONLY the translated text - no JSON, no extra formatting, no explanations
8. Do not include language labels like [{target_language_name}] in your response
9. Preserve any HTML tags or special formatting in the original text
10. For religious occasions, use appropriate terminology
11. For general content, maintain natural flow
12. NEVER wrap the translated text in quotation marks or any other punctuation

Target language: {target_language_name} ({target_language})

CRITICAL: Return ONLY the translated text without quotes, brackets, or any wrapper characters."""

        user_prompt = f"""Please translate the following text to {target_language_name}:

Text to translate: {text}

Context: This text is from an Islamic application and may contain religious content, general information, or HTML formatting.

CRITICAL: Return ONLY the translated text without any quotes, formatting, or explanations. Do not wrap the result in quotation marks."""

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                timeout=20  # 20 second timeout per request
            )
            
            response_text = response.choices[0].message.content.strip()

            cleaned_response = response_text.strip()

            # Remove code blocks if present
            if cleaned_response.startswith('```'):
                lines = cleaned_response.split('\n')
                if len(lines) > 1:
                    cleaned_response = '\n'.join(lines[1:-1])

            # Remove surrounding quotes if present
            if (cleaned_response.startswith('"') and cleaned_response.endswith('"')) or \
               (cleaned_response.startswith("'") and cleaned_response.endswith("'")):
                cleaned_response = cleaned_response[1:-1]

            # Remove extra quotes that might be added by AI
            if (cleaned_response.startswith('"') and cleaned_response.endswith('"')) or \
               (cleaned_response.startswith("'") and cleaned_response.endswith("'")):
                cleaned_response = cleaned_response[1:-1]

            time.sleep(1)  # 1 second delay between requests
            return cleaned_response.strip()
            
        except Exception as e:
            print(f"OpenAI API error: {e}")
            raise Exception(f"Translation failed: {e}")


class GeminiTranslationClient:
    """Google Gemini client for batch translation"""
    
    def __init__(self, api_key: Optional[str] = None):
        if requests is None or json is None:
            raise ImportError("requests and json packages required for Gemini client")
            
        self.api_key = api_key or "AIzaSyBz8OtW_zJPRiDA4YxTOBxaw2sv3oU6gwY"
        self.api_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent?key={self.api_key}"
        self.model = "gemini-2.0-flash-lite"
        self.max_retries = 3
        self.retry_delay = 2
        
    def translate_text(self, text: str, target_language: str, target_language_name: str, 
                      source_language: str = "English") -> str:
        result = self.translate_batch([text], [target_language], [target_language_name], source_language)
        return result.get(target_language, [""])[0] if result else ""
        
    def translate_batch(self, texts: List[str], target_language_codes: List[str], 
                       target_language_names: List[str], source_language: str = "English") -> Dict[str, List[str]]:
        if not texts or not target_language_codes:
            return {}
            
        prompt = self._build_translation_prompt(texts, target_language_names, source_language)
        
        for attempt in range(self.max_retries):
            try:
                response = self._make_api_request(prompt)
                translations = self._parse_translation_response(response, target_language_codes, len(texts))
                return translations
                
            except Exception as e:
                print(f"Gemini API attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
                else:
                    print(f"All Gemini API attempts failed: {e}")
                    return {}
    
    def _build_translation_prompt(self, texts: List[str], target_languages: List[str], source_language: str) -> str:
        texts_str = "\n".join([f"{i+1}. {text}" for i, text in enumerate(texts)])
        languages_str = ", ".join(target_languages)
        
        prompt = f"""You are a professional translator specializing in Islamic and religious content. Please translate the following texts from {source_language} to {languages_str}.

Texts to translate:
{texts_str}

Please provide the response in JSON format with this structure:
{{
    "language_1": ["translation of text 1", "translation of text 2", ...],
    "language_2": ["translation of text 1", "translation of text 2", ...],
    ...
}}

Important notes:
- Analyze each text automatically (religious content, general text, HTML content, etc.)
- Translations should be accurate and culturally appropriate for Islamic applications
- For religious occasions and content, use appropriate Islamic terminology
- Preserve any HTML tags or special formatting in the original text
- Maintain respectful tone for religious content
- If a text is empty, keep it empty
- Maintain the same order as the original texts
- Consider cultural sensitivity for each target language
- Return ONLY the JSON, no additional text or explanations
"""
        return prompt
    
    def _make_api_request(self, prompt: str) -> str:
        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.1,
                "topK": 1,
                "topP": 1,
                "maxOutputTokens": 4096,
            }
        }
        
        headers = {"Content-Type": "application/json"}
        response = requests.post(self.api_url, json=payload, headers=headers, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        if 'candidates' in result and result['candidates']:
            return result['candidates'][0]['content']['parts'][0]['text']
        else:
            raise Exception(f"Invalid API response: {result}")
    
    def _parse_translation_response(self, response: str, language_codes: List[str], text_count: int) -> Dict[str, List[str]]:
        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start == -1 or json_end == 0:
                raise ValueError("No JSON found in response")
            
            json_str = response[json_start:json_end]
            translations_data = json.loads(json_str)
            
            result = {}
            for i, code in enumerate(language_codes):
                if i < len(translations_data):
                    lang_key = list(translations_data.keys())[i]
                    result[code] = translations_data[lang_key]
                else:
                    result[code] = [""] * text_count
            
            return result
            
        except Exception as e:
            print(f"Error parsing response: {e}")
            return {code: [""] * text_count for code in language_codes}


class MockTranslationClient:
    """Mock client for testing without actual API calls"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.model = "mock-translator"
        
    def translate_text(self, text: str, target_language: str, target_language_name: str, 
                      source_language: str = "English") -> str:
        if not text or not text.strip():
            return text
        mock_translation = f"[MOCK_{target_language.upper()}] {text}"
        time.sleep(0.05)
        return mock_translation
    
    def translate_batch(self, texts: List[str], target_language_codes: List[str], 
                       target_language_names: List[str], source_language: str = "English") -> Dict[str, List[str]]:
        if not texts or not target_language_codes:
            return {}
        
        print(f"    🧪 Mock Batch Translation:")
        print(f"      Source: {source_language}")
        print(f"      Texts: {len(texts)} items")
        print(f"      Target languages: {target_language_codes}")
        
        result = {}
        for code in target_language_codes:
            result[code] = [f"[MOCK_{code.upper()}] {text}" for text in texts]
        
        time.sleep(1)
        return result


def get_translation_client(use_mock: bool = False, use_gemini: bool = False, api_key: Optional[str] = None):
    """Factory function to get translation client"""
    if use_mock:
        return MockTranslationClient(api_key=api_key)
    elif use_gemini:
        return GeminiTranslationClient(api_key=api_key)
    else:
        return OpenAITranslationClient(api_key=api_key)


# Helper functions
def _has_translatable_content(instance):
    """Check if the instance has content that can be translated"""
    for field_name in ['title', 'summary', 'description']:
        field_data = getattr(instance, field_name, [])
        if field_data and isinstance(field_data, list):
            for item in field_data:
                if isinstance(item, dict) and item.get('text', '').strip():
                    return True
    return False


def _needs_translation(instance):
    """Check if the instance needs translation"""
    try:
        active_languages = set(Language.objects.filter(status=True).values_list('code', flat=True))
        if not active_languages:
            return False

        for field_name in ['title', 'summary', 'description']:
            field_data = getattr(instance, field_name, [])
            if not field_data:
                continue

            existing_langs = set()
            for item in field_data:
                if isinstance(item, dict) and 'language_code' in item:
                    existing_langs.add(item['language_code'])

            missing_langs = active_languages - existing_langs
            if missing_langs:
                return True

        return False

    except Exception as e:
        print(f"❌ Error checking translation needs: {e}")
        return False


def _get_source_text(field_data: List[Dict], field_name: str) -> Optional[str]:
    """Get source text for translation from field data"""
    if not field_data or not isinstance(field_data, list):
        return None

    # Try to find English text first
    for item in field_data:
        if isinstance(item, dict) and item.get('language_code') == 'en' and item.get('text'):
            text = item['text'].strip()
            if text:
                return text

    # If no English, get first non-empty text
    for item in field_data:
        if isinstance(item, dict) and item.get('text'):
            text = item['text'].strip()
            if text:
                return text

    return None


def _needs_field_translation(field_data: List[Dict], language_code: str) -> bool:
    """Check if translation is needed for a specific language"""
    if not field_data or not isinstance(field_data, list):
        return True

    # Check if translation already exists with non-empty text
    for item in field_data:
        if isinstance(item, dict) and item.get('language_code') == language_code:
            text = item.get('text', '').strip()
            if text:
                return False
            else:
                return True

    return True


def _is_valid_translation(translated_text: str, target_lang_code: str, use_mock: bool = False) -> bool:
    """Validate if the translation is valid"""
    if not translated_text or not translated_text.strip():
        return False

    # Check if it's a mock response (for testing)
    if use_mock and translated_text.startswith(f"[MOCK_{target_lang_code.upper()}]"):
        return False  # Don't save mock data

    # Check for common AI failure patterns
    failure_patterns = [
        "I cannot translate", "I'm unable to translate", "Sorry, I cannot",
        "I don't have the ability", "I can't provide", "I'm not able to",
        "Translation not available", "Unable to translate"
    ]

    translated_lower = translated_text.lower()
    for pattern in failure_patterns:
        if pattern.lower() in translated_lower:
            return False

    # Check if response is too short (likely an error)
    if len(translated_text.strip()) < 2:
        return False

    return True


def _add_translation_to_field(obj, field_name: str, translated_text: str, language_code: str):
    """Add translation to the model field"""
    field_data = getattr(obj, field_name, [])

    if not isinstance(field_data, list):
        field_data = []

    # Check if translation already exists
    translation_exists = False
    for item in field_data:
        if isinstance(item, dict) and item.get('language_code') == language_code:
            # Update existing translation
            item['text'] = translated_text
            translation_exists = True
            break

    # Add new translation if it doesn't exist
    if not translation_exists:
        new_translation = {
            'language_code': language_code,
            'text': translated_text
        }
        field_data.append(new_translation)

    # Update the field
    setattr(obj, field_name, field_data)


# Celery Tasks
@shared_task(bind=True, max_retries=3)
def translate_model_fields_task(self, model_name: str, app_name: str, object_id: int,
                               field_configs: List[Dict], use_gemini: bool = False,
                               use_mock: bool = False):
    """
    Celery task to translate model fields in background

    Args:
        model_name: Name of the model class
        app_name: Name of the app containing the model
        object_id: ID of the object to translate
        field_configs: List of field configurations
        use_gemini: Whether to use Gemini client
        use_mock: Whether to use mock client for testing
    """
    try:
        print(f"🚀 Starting background translation task")
        print(f"   Model: {app_name}.{model_name}")
        print(f"   Object ID: {object_id}")
        print(f"   Fields: {len(field_configs)}")
        print(f"   Use Gemini: {use_gemini}")

        # Import the model dynamically
        from django.apps import apps
        model_class = apps.get_model(app_name, model_name)

        # Get the object
        try:
            obj = model_class.objects.get(id=object_id)
        except model_class.DoesNotExist:
            print(f"❌ Object {model_name} with ID {object_id} not found")
            return {'success': False, 'error': 'Object not found'}

        # Check if translation is needed
        if not _has_translatable_content(obj):
            print(f"ℹ️  No translatable content found")
            return {'success': True, 'message': 'No content to translate'}

        if not _needs_translation(obj):
            print(f"ℹ️  All translations already exist")
            return {'success': True, 'message': 'All translations exist'}

        # Initialize translation client
        translation_client = get_translation_client(
            use_mock=use_mock,
            use_gemini=use_gemini
        )

        # Get active languages
        active_languages = Language.objects.filter(status=True)

        # Statistics
        total_translations = 0
        successful_translations = 0
        failed_translations = 0
        consecutive_failures = 0
        max_consecutive_failures = 3
        changes_made = False

        # Process each field
        for field_config in field_configs:
            field_name = field_config['field_name']
            keys = field_config['keys']

            print(f"  📝 Processing field: {field_name}")

            # Get field data and source text
            if 'source_field' in field_config:
                # Translation from another field (e.g., name_translation from fullname)
                source_field_name = field_config['source_field']
                source_text = getattr(obj, source_field_name, '')
                if not source_text or not source_text.strip():
                    print(f"    ⚠️  No source text found in {source_field_name}")
                    continue

                # Get or initialize target field data
                field_data = getattr(obj, field_name, [])
                if not isinstance(field_data, list):
                    field_data = []
                    setattr(obj, field_name, field_data)
            else:
                # Translation within same field (e.g., title field with existing translations)
                field_data = getattr(obj, field_name, [])
                source_text = _get_source_text(field_data, field_name)
            if not source_text:
                print(f"    ⚠️  No source text found for {field_name}")
                continue

            # Translate to each active language
            for language in active_languages:
                # Check for consecutive failures limit
                if consecutive_failures >= max_consecutive_failures:
                    print(f"❌ Stopping translation due to {max_consecutive_failures} consecutive failures")
                    break

                # Check if translation is needed for this language
                needs_translation = False
                if 'source_field' in field_config:
                    # For source field translations, check if target field has this language
                    needs_translation = _needs_field_translation(field_data, language.code)
                else:
                    # For same field translations, use existing logic
                    needs_translation = _needs_field_translation(field_data, language.code)

                if needs_translation:
                    print(f"    🔄 Translating to {language.name} ({language.code})...")
                    total_translations += 1

                    try:
                        # Simple translation with 20 second timeout - no retries
                        translated_text = translation_client.translate_text(
                            text=source_text,
                            target_language=language.code,
                            target_language_name=language.name
                        )

                        # Validate translation
                        if _is_valid_translation(translated_text, language.code, use_mock):
                            # Add translation to the field
                            if 'source_field' in field_config:
                                # For source field translations, add to target field
                                _add_translation_to_field(obj, field_name, translated_text, language.code)
                            else:
                                # For same field translations, use existing logic
                                _add_translation_to_field(obj, field_name, translated_text, language.code)

                            # Save immediately after each successful translation
                            try:
                                obj.save()
                                print(f"    ✅ Translation completed and saved for {language.name}")
                                changes_made = True
                                successful_translations += 1
                                consecutive_failures = 0  # Reset on success
                            except Exception as save_error:
                                print(f"    ⚠️  Translation completed but save failed for {language.name}: {save_error}")
                                failed_translations += 1
                                consecutive_failures += 1
                        else:
                            print(f"    ❌ Invalid translation for {language.name}")
                            failed_translations += 1
                            consecutive_failures += 1

                    except Exception as e:
                        # Simple error handling - just skip to next language
                        print(f"    ❌ Translation failed for {language.name}: {e}")
                        print(f"    ⏭️  Skipping to next language...")
                        failed_translations += 1
                        consecutive_failures += 1

                    # Wait 20 seconds before next translation (if not last language)
                    remaining_languages = [lang for lang in active_languages if lang != language]
                    if remaining_languages:
                        print(f"    ⏱️  Waiting 20 seconds before next translation...")
                        time.sleep(20)

                else:
                    print(f"    ✅ {language.name} translation already exists")

            # Break if too many consecutive failures
            if consecutive_failures >= max_consecutive_failures:
                break

        # Note: Individual translations are saved immediately after each success
        if changes_made:
            print(f"  💾 All successful translations saved for {model_name} ID: {object_id}")
        else:
            print(f"  ℹ️  No changes needed for {model_name} ID: {object_id}")

        print(f"✅ Background translation completed!")
        print(f"📊 Total translations attempted: {total_translations}")
        print(f"📊 Successful: {successful_translations}")
        print(f"📊 Failed: {failed_translations}")

        return {
            'success': True,
            'total_translations': total_translations,
            'successful_translations': successful_translations,
            'failed_translations': failed_translations,
            'changes_made': changes_made
        }

    except Exception as e:
        print(f"❌ Translation task failed: {e}")

        # Retry logic
        if self.request.retries < self.max_retries:
            print(f"🔄 Retrying task (attempt {self.request.retries + 1}/{self.max_retries})")
            raise self.retry(countdown=60 * (self.request.retries + 1))

        return {
            'success': False,
            'error': str(e),
            'retries': self.request.retries
        }


@shared_task(bind=True, max_retries=3)
def translate_occasion_details_batch_task(self, model_name: str, app_name: str,
                                         occasion_detail_ids: List[int], batch_size: int = 10,
                                         use_gemini: bool = True, use_mock: bool = False):
    """
    Celery task for batch translation of OccasionDetail objects

    Args:
        model_name: Name of the model class (usually 'OccasionDetail')
        app_name: Name of the app containing the model
        occasion_detail_ids: List of OccasionDetail IDs to translate
        batch_size: Number of languages to translate in one API call
        use_gemini: Whether to use Gemini client for batch translation
        use_mock: Whether to use mock client for testing
    """
    try:
        print(f"🚀 Starting batch translation task")
        print(f"   Model: {app_name}.{model_name}")
        print(f"   Objects: {len(occasion_detail_ids)}")
        print(f"   Batch size: {batch_size}")
        print(f"   Use Gemini: {use_gemini}")

        # Import the model dynamically
        from django.apps import apps
        model_class = apps.get_model(app_name, model_name)

        # Get source objects
        source_objects = model_class.objects.filter(id__in=occasion_detail_ids)

        if not source_objects.exists():
            print("❌ No objects found to translate")
            return {'success': False, 'error': 'No objects found'}

        # Initialize translation client
        translation_client = get_translation_client(
            use_mock=use_mock,
            use_gemini=use_gemini
        )

        # Get active languages
        active_languages = Language.objects.filter(status=True)

        # Group by occasion and source language
        occasion_groups = {}
        for obj in source_objects:
            occasion_id = obj.occasion_id
            lang_code = obj.language.code if obj.language else 'unknown'

            if occasion_id not in occasion_groups:
                occasion_groups[occasion_id] = {}
            if lang_code not in occasion_groups[occasion_id]:
                occasion_groups[occasion_id][lang_code] = []

            occasion_groups[occasion_id][lang_code].append(obj)

        stats = {'processed': 0, 'successful': 0, 'failed': 0, 'created_objects': 0}

        # Process each occasion group
        for occasion_id, lang_groups in occasion_groups.items():
            print(f"\n🔄 Processing occasion {occasion_id}")

            # Find source language (prefer 'fa', then 'en', then first available)
            source_lang = None
            source_objects_for_occasion = []

            for preferred_lang in ['fa', 'en']:
                if preferred_lang in lang_groups:
                    source_lang = preferred_lang
                    source_objects_for_occasion = lang_groups[preferred_lang]
                    break

            if not source_lang and lang_groups:
                source_lang = list(lang_groups.keys())[0]
                source_objects_for_occasion = lang_groups[source_lang]

            if not source_objects_for_occasion:
                print(f"  ⚠️  No source objects found for occasion {occasion_id}")
                stats['failed'] += 1
                continue

            # Get existing languages for this occasion
            existing_languages = set(lang_groups.keys())

            # Find target languages
            target_languages = []
            for lang in active_languages:
                if lang.code not in existing_languages:
                    target_languages.append({
                        'code': lang.code,
                        'name': lang.name,
                        'obj': lang
                    })

            if not target_languages:
                print(f"  ℹ️  Occasion {occasion_id} already has all translations")
                stats['processed'] += 1
                continue

            print(f"  📝 Source language: {source_lang}")
            print(f"  🎯 Target languages: {[lang['code'] for lang in target_languages]}")

            # Extract texts to translate
            texts_to_translate = []
            for obj in source_objects_for_occasion:
                if obj.content and obj.content.strip():
                    texts_to_translate.append(obj.content.strip())
                else:
                    texts_to_translate.append("")

            if not any(texts_to_translate):
                print(f"  ⚠️  No content to translate for occasion {occasion_id}")
                stats['failed'] += 1
                continue

            # Perform batch translation
            try:
                if hasattr(translation_client, 'translate_batch'):
                    # Use batch translation (Gemini)
                    target_codes = [lang['code'] for lang in target_languages]
                    target_names = [lang['name'] for lang in target_languages]

                    translations = translation_client.translate_batch(
                        texts_to_translate, target_codes, target_names, source_lang
                    )
                else:
                    # Fallback to individual translations (OpenAI)
                    translations = {}
                    for lang in target_languages:
                        lang_translations = []
                        for text in texts_to_translate:
                            if text:
                                translated = translation_client.translate_text(
                                    text, lang['code'], lang['name'], source_lang
                                )
                                lang_translations.append(translated)
                            else:
                                lang_translations.append("")
                        translations[lang['code']] = lang_translations

                if not translations:
                    print(f"  ❌ Translation failed for occasion {occasion_id}")
                    stats['failed'] += 1
                    continue

                # Create new OccasionDetail objects
                created_count = 0
                for lang_code, translated_texts in translations.items():
                    lang_obj = next((lang['obj'] for lang in target_languages if lang['code'] == lang_code), None)
                    if not lang_obj:
                        continue

                    for i, (source_obj, translated_text) in enumerate(zip(source_objects_for_occasion, translated_texts)):
                        if translated_text and _is_valid_translation(translated_text, lang_code, use_mock):
                            # Create new OccasionDetail
                            if not use_mock:  # Only create if not in mock mode
                                new_detail = model_class.objects.create(
                                    occasion=source_obj.occasion,
                                    language=lang_obj,
                                    content=translated_text
                                )
                                print(f"    ✅ Created {lang_code} translation for detail {source_obj.id}")
                                created_count += 1
                            else:
                                print(f"    🧪 Mock: Would create {lang_code} translation")
                                created_count += 1

                if created_count > 0:
                    stats['successful'] += 1
                    stats['created_objects'] += created_count
                    print(f"  ✅ Created {created_count} new translations for occasion {occasion_id}")
                else:
                    print(f"  ⚠️  No valid translations created for occasion {occasion_id}")
                    stats['failed'] += 1

            except Exception as e:
                print(f"  ❌ Translation error for occasion {occasion_id}: {e}")
                stats['failed'] += 1
                continue

            stats['processed'] += 1

        print(f"\n✅ Batch translation task completed!")
        print(f"📊 Processed: {stats['processed']}")
        print(f"📊 Successful: {stats['successful']}")
        print(f"📊 Failed: {stats['failed']}")
        print(f"📊 Created objects: {stats['created_objects']}")

        return {
            'success': True,
            'stats': stats
        }

    except Exception as e:
        print(f"❌ Batch translation task failed: {e}")

        # Retry logic
        if self.request.retries < self.max_retries:
            print(f"🔄 Retrying task (attempt {self.request.retries + 1}/{self.max_retries})")
            raise self.retry(countdown=60 * (self.request.retries + 1))

        return {
            'success': False,
            'error': str(e),
            'retries': self.request.retries
        }
