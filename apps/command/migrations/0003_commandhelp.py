# Generated by Django 3.2.25 on 2024-12-28 19:31

import dj_language.field
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
        ('command', '0002_commandlog_command'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommandHelp',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service', models.CharField(choices=[('hussainiyah', 'Hussainiyah'), ('library', 'Library'), ('Talk', 'Talk'), ('meet', 'Meet'), ('mafatih', 'Duas'), ('hadith', 'Hadis'), ('quran', 'Quran'), ('ahkam', 'Ahkam'), ('coin', 'Coin'), ('calendar', 'Calendar'), ('qiblah', '<PERSON>blah'), ('elalHabib', 'ElalHabib')], max_length=50)),
                ('description', models.TextField(blank=True, null=True)),
                ('language', dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language')),
            ],
            options={
                'unique_together': {('service', 'language')},
            },
        ),
    ]
