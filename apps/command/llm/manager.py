
from .clients import *
from .prompts import *
from apps.quran.models import QuranSura, QuranVerse
from django.db.models import Min

class PromptServiceHandler(object):
    
    def central_handler(self):
        return central_handler_v6
    
    def quran_handler(self):
        return quran_handler_v1
    
    def ahkam_handler(self):
        return ahkam_handler_v1
    
    def global_handler(self):
        return global_handler_v1
    
    def hussainiya_handler(self): 
        return hussainiya_handler_v1
    
    def hadith_handler(self):
        return hadith_handler_v1
    
    def library_handler(self):
        return library_handler_v1
    
    def calendar_handler(self):
        return calendar_handler_v1
    
    def mafatih_handler(self):
        return mafatih_handler_v1
        

class AIRequestManagerABC:
    
    def _custom_response_quran(self, response):
        try:
            info = response.get('additional_info')
            surah = info.get('surah')
            ayah = info.get("ayah")
            page = info.get("page")
            part = info.get("part")
            
            # حالت 1: فقط صفحه مشخص شده (page)
            if surah is None and ayah is None and page is not None and part is None:
                result = QuranVerse.objects.filter(page=page).aggregate(first_ayah_index=Min('index'))
                response['additional_info']['ayah'] = result.get('first_ayah_index')
    
            # حالت 2: فقط سوره مشخص شده (surah)
            elif surah is not None and ayah is None and page is None and part is None:
                result = QuranVerse.objects.filter(sura_id=surah).aggregate(first_ayah_index=Min('index'))
                response['additional_info']['ayah'] = result.get('first_ayah_index')
    
            # حالت 3: سوره و آیه مشخص شده (surah و ayah)
            elif surah is not None and ayah is not None and page is None and part is None:
                verse = QuranVerse.objects.get(sura_id=surah, number_in_surah=ayah)
                response['additional_info']['ayah'] = verse.index
    
            # حالت 4: فقط جز مشخص شده (part)
            elif surah is None and ayah is None and page is None and part is not None:
                result = QuranVerse.objects.filter(juz=part).aggregate(first_ayah_index=Min('index'))
                response['additional_info']['ayah'] = result.get('first_ayah_index')
    
            # حالت 5: صفحه و آیه مشخص شده (page و ayah)
            elif surah is None and ayah is not None and page is not None and part is None:
                verse = QuranVerse.objects.get(page=page, number_in_surah=ayah)
                response['additional_info']['ayah'] = verse.index
    
            # بازگشت پاسخ
            return response
        except Exception as exp:
            return response
        
    def _custom_response_global(self, response):
        try:
            if response.get("action") == "change_language":
                additional_info = response.get("additional_info")

                # بررسی اگر additional_info مقدار داشت
                if additional_info:
                    # تبدیل به دیکشنری جدید
                    response["additional_info"] = {
                        "language_code": additional_info
                    }
            return response            
        except Exception as exp:
            return response
            
        
class AIRequestManager(AIRequestManagerABC):
    
    def __init__(self, command):
        self.command = command
        self.prompt: PromptServiceHandler = PromptServiceHandler()
        
    def quran(self):
        return self._custom_response_quran(
            self._client_request(self.prompt.quran_handler())
        ) 
    
    def central(self):
        return self._client_request(self.prompt.central_handler())

    def hadith(self):
        return self._client_request(self.prompt.hadith_handler())
        
    def hussainiya(self):
        return self._client_request(self.prompt.hussainiya_handler())
    
    def ahkam(self):
        return self._client_request(self.prompt.ahkam_handler())

    def global_(self):
        return self._custom_response_global(
            self._client_request(self.prompt.global_handler())
        )

    def library(self):
        return self._client_request(self.prompt.library_handler())
    
    def calendar(self):
        return self._client_request(self.prompt.calendar_handler())
    
    def mafatih(self):
        return self._client_request(self.prompt.mafatih_handler())
    
            
    def _client_request(self, prompt):
        clients = [GPT4oClient(), ClaudeClient(), FireworkAi(), GroqClient()]        
        for client in clients:
            try:
                response = client.send(prompt, self.command)
                if response and "error" not in response and response is not None:
                    print(f"Response successfully received from: {type(client).__name__}")
                    return response  
                print(f"No valid response from: {type(client).__name__}: {response}")
            except json.JSONDecodeError as exp:
                return {"error": "Invalid JSON in response from the service"}
            except Exception as exp:
                pass
        return {"error": "All services failed to provide a valid response"}
        


