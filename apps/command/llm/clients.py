
from abc import ABC, abstractmethod
import json
import re
from openai import OpenAI
import anthropic
from groq import Groq
from groq._exceptions import BadRequestError
import requests


class Client(ABC):
    """
    Abstract class for chat clients like <PERSON> and others.
    """

    API_KEY: str 

    def __init__(self):
        """
        Initialize the client using the `_client` method defined in the subclass.
        """
        self.client = self._client()              

    @abstractmethod
    def _client(self):
        """
        Abstract method to initialize the client.
        Must be implemented in subclasses.
        """
        pass

    @abstractmethod
    def send(self, assistant_message: str, user_message: str):
        """
        Abstract method to send a message using the client.
        Must be implemented in subclasses.
        
        Args:
            assistant_message (str): System message or assistant role content.
            user_message (str): User message.
        
        Returns:
            dict: Parsed JSON response from the API.
        """
        pass


    
    
class ClaudeClient(Client):
    API_KEY = "************************************************************************************************************"
    
    def __init__(self):
        self.client = self._client()              
        self.max_token = 300
        
        
    def _client(self):
        return anthropic.Anthropic(
            api_key=self.API_KEY,
        )
        
    def send(self, assistant_message: str, user_message: str):
        
        try:
            message = self.client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=self.max_token,
                messages=[
                    {"role": "assistant", "content": assistant_message},            
                    {"role": "user", "content": user_message+" Return a JSON response"}
                ],
                temperature=0,
                timeout=10.0
            )

            # print(f'---clude-res---------> {message}')
            response_text = message.content[0].text
            print(f'--->{response_text}')
            parsed_response = json.loads(response_text.strip())
            return parsed_response
        
        except json.JSONDecodeError:
            action_match = re.search(r'"action"\s*:\s*"([^"]+)"', response_text)
            if action_match:
                return {
                    "action": action_match.group(1),
                    "additional_info": {}  # یا مقدار پیشفرض دیگر
                }
            else:
                return {"error": "Invalid JSON and no action found"}
        except Exception as exp:
            print(f'-----ClaudeClient----error---> {exp}')
            return None
    
    
class GPT4oClient(Client):
    API_KEY = "***********************************************************************************************"

    def __init__(self):
        self.client = self._client()              
        self.max_token = 300
        
        
    def _client(self):
        return OpenAI(
            api_key=self.API_KEY,
            timeout=10.0
            ) 
        
    def send(self, system_message: str, user_message: str):
        
        response = self.client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": system_message},
                {"role": "user", "content": user_message+" Return a JSON response"}
            ],
            temperature=0,
            max_tokens=self.max_token,
            response_format={"type": "json_object"},    
        )
        try:
            # Assuming OpenAI response is a valid JSON string
            parsed_response = json.loads(response.choices[0].message.content.strip())
            return parsed_response
        except json.JSONDecodeError:
            return {"error": "Invalid JSON in OpenAI response"}
        except Exception as exp:
            return None



import re

class GroqClient(Client):
    API_KEY = "********************************************************"

    def __init__(self):
        self.client = self._client()              
        self.max_token = 3000
        
        
    def _client(self):
        return Groq(api_key=self.API_KEY)
        
    def send(self, system_message: str, user_message: str):


        try:
            completion = self.client.chat.completions.create(
                model="llama-3.2-90b-text-preview",
                messages=[
                    {
                        "role": "system",
                        "content": system_message
                    },
                    {
                        "role": "user",
                        "content": user_message + " Please return the response in JSON format and ensure it is fully written in English."
                    }
                ],
                response_format={"type": "json_object"},  
                temperature=0,  
                max_tokens=self.max_token,
                top_p=1,
                stream=False,  
                stop=None,
            )
        
            response_content = completion.choices[0].message.content        
            print(f'-response_content->{response_content}')
            response_dict = json.loads(response_content)
            return response_dict
        
        except json.JSONDecodeError:
            return {"error": "Invalid JSON in OpenAI response"}
        except Exception as exp:
            error_details = exp.response.json() 
            failed_generation = error_details.get("error", {}).get("failed_generation", None)
            if failed_generation:
                try:
                    failed_response = self.extract_values_from_text(failed_generation)
                    return failed_response
                except Exception as exp:
                    return None
            return None



    def extract_values_from_text(self, text):
        """
        استخراج مقادیر کلیدها از متن `failed_generation` و ساخت یک JSON معتبر.
        """
        try:
            # الگوهای کلید-مقدار
            key_value_pattern = r'"(\w+)":\s*(?:(?:"([^"]*)")|(null))'
            matches = re.findall(key_value_pattern, text)

            result = {}
            for match in matches:
                key, value, is_null = match
                if is_null == "null":
                    result[key] = None
                else:
                    result[key] = value            
            return result

        except Exception as e:
            print(f"Error in extracting values: {e}")
            return None
        

class FireworkAi(Client):
    # API_KEY = "fw_3ZcwkqvYEVhjWcvq29nN7vhv"
    API_KEY = "fw_3ZdUS722cGRLCSmu4eRx9iLR"
    
    def __init__(self):
        # self.client = self._client()              
        self.max_token = 2000
        self.url = "https://api.fireworks.ai/inference/v1/chat/completions"

        
    def _client(self, headers, payload):
        return requests.request("POST", self.url, headers=headers, data=json.dumps(payload))


    def send(self, system_message: str, user_message: str):
        print('---------------------------------------')
        try:
            headers = {
              "Accept": "application/json",
              "Content-Type": "application/json",
              "Authorization": f"Bearer {self.API_KEY}"
            }

            payload = {
              "model": "accounts/fireworks/models/deepseek-v3",
              "max_tokens": 4096,
              "top_p": 1,
              "top_k": 40,
              "presence_penalty": 0,
              "frequency_penalty": 0,
              "temperature": 0.6,
              "response_format": {
                "type": "json_object",
                # "schema": {}
              },
              "messages": [
                {
                  "role": "system",
                  "content": f"{system_message}"
                },
                {
                  "role": "user",
                  "content":  user_message+" Return a JSON response"

                }
              ]
            }
            response = self._client(headers, payload)
            response_data = response.json()
            choices = response_data.get("choices", [])
            if choices and "message" in choices[0]:
                content = choices[0]["message"]["content"]
                content_dict = json.loads(content)
                return content_dict
            return None
            
        except json.JSONDecodeError:
            return {"error": "Invalid JSON in OpenAI response"}
        except Exception as exp:
            return None
