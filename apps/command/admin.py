import json
from django.contrib import admin
from ajaxdatatable.admin import AjaxDatatable
from django import forms
from django.core.exceptions import ValidationError
from utils.json_editor_field import JsonEditorWidget
from django.utils.translation import gettext_lazy as _
from dj_language.models import Language
from django.utils.safestring import mark_safe

from apps.command.models import CommandLog, CommandHelp



@admin.register(CommandLog)
class CommandLogAdmin(AjaxDatatable):
    list_display = ('user', 'command', 'category', 'status_code', 'response_time')
    search_fields = ('user__username', 'user__email', 'category', 'response')
    readonly_fields = ('user', 'command','category', 'response', 'status_code', 'started_at', 'finished_at', 'created_at')

    
    def response_time(self, obj):
        if obj.started_at and obj.finished_at:
            duration = obj.finished_at - obj.started_at
            return f"{duration.total_seconds():.2f} s"
        else:
            return 'N/A'
    response_time.short_description = 'Response Time'
    
    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


def get_command_descriptions_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Descriptions')),
            'properties': {
                'text': {'type': 'string', "format": "textarea",'title': str(_('text'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }

    
class CommandHelpForm(forms.ModelForm):
    class Meta:
        model = CommandHelp
        exclude = ()
        widgets = {
            'descriptions': JsonEditorWidget(attrs={'schema': get_command_descriptions_schema}),
        }

    def clean_service(self):
        service = self.cleaned_data.get('service')
        if self.instance.pk is None and CommandHelp.objects.filter(service=service).exists():
            raise ValidationError(f"The service '{service}' already exists.")
        return service


    
@admin.register(CommandHelp)
class CommandHelpAdmin(AjaxDatatable):
    form = CommandHelpForm
    list_display = ('service', '_descriptions')
    list_filter = ('service',)
    search_fields = ('service',)


    @admin.display(description='Descriptions', ordering='descriptions')
    def _descriptions(self, obj):
        try:
            return mark_safe(" | ".join([i.get('language_code') for i in obj.descriptions] or '-'))
        except:
            return '-'
