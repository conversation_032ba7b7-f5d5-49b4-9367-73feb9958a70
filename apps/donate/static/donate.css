

.shipping-method-choose-title-card {
    border-radius: 0.5rem 0.5rem 0 0;
    border-color: #00b894;
}

.shipping-method-choose-card {
    border-radius: 0 0 0.5rem 0.5rem;
}

.single-payment-method a {
    display: block;
    position: relative;
    z-index: 1;
    background-color: #ffffff;
    text-align: center;
    padding: 2rem 1rem;
    border-radius: 6px;
}

.single-payment-method a::after {
    -webkit-transition-duration: 500ms;
    -o-transition-duration: 500ms;
    transition-duration: 500ms;
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    content: "\f00c";
    font-family: "FontAwesome";
    border-radius: 50%;
    width: 1.75rem;
    height: 1.75rem;
    background-color: #00b894;
    color: #ffffff;
    line-height: 1.75rem;
    opacity: 0;
}

.single-payment-method a i {
    -webkit-transition-duration: 500ms;
    -o-transition-duration: 500ms;
    transition-duration: 500ms;
    font-size: 3rem;
    margin-bottom: 0.5rem;
    display: block;
    color: #00b894;
}

.single-payment-method a h6 {
    -webkit-transition-duration: 500ms;
    -o-transition-duration: 500ms;
    transition-duration: 500ms;
    margin-bottom: 0;
    color: #747794;
}

.single-payment-method a.active::after, .single-payment-method a:hover::after, .single-payment-method a:focus::after {
    opacity: 1;
}

.single-payment-method a.active h6, .single-payment-method a:hover h6, .single-payment-method a:focus h6 {
    color: #020310;
}

.credit-card-info-wrapper img {
    margin: 0 auto;
    max-width: 300px;
}

.pay-credit-card-form {
    position: relative;
    z-index: 1;
}

.pay-credit-card-form label {
    font-size: 14px;
    font-weight: 500;
    margin-left: 0.25rem;
    text-transform: capitalize;
}

.pay-credit-card-form small {
    font-size: 12px;
}

.pay-credit-card-form .form-control {
    height: 44px;
    padding-top: 5px;
    padding-bottom: 5px;
    -webkit-box-shadow: 0 1px 1px 0px #d7def4;
    box-shadow: 0 1px 1px 0px #d7def4;
}

.bank-ac-info .list-group-item {
    font-size: 14px;
}

.language-area-wrapper,
.shipping-method-choose {
    position: relative;
}

.language-area-wrapper ul,
.shipping-method-choose ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.language-area-wrapper ul li,
.shipping-method-choose ul li {
    display: block;
    position: relative;
    z-index: 1;
    width: 100%;
}

.language-area-wrapper ul li input[type=radio],
.shipping-method-choose ul li input[type=radio] {
    position: absolute;
    visibility: hidden;
}

.language-area-wrapper ul li label,
.shipping-method-choose ul li label {
    display: block;
    position: relative;
    padding: 10px 10px 10px 45px;
    z-index: 9;
    cursor: pointer;
    -webkit-transition: all 0.25s linear;
    -o-transition: all 0.25s linear;
    transition: all 0.25s linear;
    margin-bottom: 0;
    border-radius: 0.4rem;
    font-size: 14px;
    font-weight: 500;
}

.language-area-wrapper ul li label span,
.shipping-method-choose ul li label span {
    font-size: 11px;
    margin-left: 0.5rem;
}

.language-area-wrapper ul li .check,
.shipping-method-choose ul li .check {
    display: block;
    position: absolute;
    border: 4px solid #00b894;
    border-radius: 100%;
    height: 20px;
    width: 20px;
    top: 50%;
    margin-top: -10px;
    left: 15px;
    z-index: 5;
    -webkit-transition: all 0.25s linear;
    -o-transition: all 0.25s linear;
    transition: all 0.25s linear;
}

.language-area-wrapper ul li .check::before,
.shipping-method-choose ul li .check::before {
    display: block;
    position: absolute;
    content: "";
    border-radius: 100%;
    height: 8px;
    width: 8px;
    top: 50%;
    left: 50%;
    margin: -4px;
    -webkit-transition: background 0.25s linear;
    -o-transition: background 0.25s linear;
    transition: background 0.25s linear;
}

.language-area-wrapper input[type=radio]:checked ~ .check,
.shipping-method-choose input[type=radio]:checked ~ .check {
    border-color: #100DD1;
}

.language-area-wrapper input[type=radio]:checked ~ .check::before,
.shipping-method-choose input[type=radio]:checked ~ .check::before {
    background: #100DD1;
}

.language-area-wrapper input[type=radio]:checked ~ label,
.shipping-method-choose input[type=radio]:checked ~ label {
    color: #100DD1;
    background-color: rgba(16, 13, 209, 0.103);
}
