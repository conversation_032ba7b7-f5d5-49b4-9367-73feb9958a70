from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation
from django.db import models
from django.utils.translation import gettext_lazy as _

from paypal.standard.ipn.models import PayPalIPN


def random_invoice_id():
    from random import randint
    invoice_id = randint(100_000_000, 999_999_999)
    if DonateUser.objects.filter(invoice_id=invoice_id).exists():
        return random_invoice_id()

    return f"donate:{invoice_id}"


class DonateUser(models.Model):
    class DonateType(models.TextChoices):
        MONTHLY = 'monthly', 'Monthly'
        ONCE = 'once', 'Once'

    donate_type = models.CharField(
        max_length=10, 
        choices=DonateType.choices,  
        default=DonateType.ONCE,  
    )
    
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, verbose_name=_('user'), null=True)
    phone = models.CharField(null=True, blank=True, max_length=255, verbose_name=_('phone'))
    amount = models.DecimalField(verbose_name=_('amount'), decimal_places=2, max_digits=10)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))
    invoice_id = models.CharField(
        max_length=150,
        default=random_invoice_id, unique=True, editable=False,
        verbose_name=_('invoice id')
    )
    # payment = GenericRelation('payment.Payment')

    def __str__(self):
        return f"{self.amount} By: {self.user}"

    class Meta:
        verbose_name = _('donate')
        verbose_name_plural = _('donations')
        ordering = ('-id',)
