{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <link rel="stylesheet" href="{% static 'donate.css' %}">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;

            margin: 0;
            color: #4d4d4d;
        }

        .container {
            max-width: 600px;
            padding: 16px;
        }

        .logo img {
            max-width: 134px;
            height: auto;
        }

        h2, h3 {
            margin-block-start: 0em;
            margin-block-end: 0em;
            color: #7e7e7e;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .payment-method {
            display: inline-block;
            margin: 10px;
            text-align: center;
            text-decoration: none;
            color: #333;
        }

        .payment-method img {
            max-width: 100px;
            max-height: 50px;
        }

        .footer-fixed {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: #f8f9fa;
            padding: 10px;
        }
    </style>
    <title>Habibapp Donate</title>
</head>
<body>

<div class="container">

    <div class="text-center">
        <div class="logo">
            <img src="https://habibapp.com/static/uploads/main/fb/f3/fbf31d0f-eef0-4013-b876-b3afba2ebd12/splash.png">
        </div>
        <h2>Donate Habib Project</h2>
        <h3>A friendly message for you</h3>
    </div>


    <div class="content mt-5">
        {{ donate_text|linebreaksbr|safe }}
    </div>


    <form action="" method="post" class="mb-4">
        {% csrf_token %}
        <input type="hidden" name="t" value="{{ request.GET.t }}">

        <div class="shipping-method-choose mb-3">
            <div class="card shipping-method-choose-title-card bg-secondary-subtle">
                <div class="card-body"><p class="text-bold small text-center mb-0 text-black">Payment Method</p></div>
            </div>
            <div class="card shipping-method-choose-card">
                <div class="card-body">
                    <div class="shipping-method-choose">
                        <ul class="ps-0">
                            {% for item in payment_methods %}
                                <li>
                                    <input {% if item.method == default_method %} checked {% endif %}
                                                                                  id="normalShipping{{ item.method }}"
                                                                                  type="radio" name="method"
                                                                                  value="{{ item.method }}"
                                                                                  data-gtm-form-interact-field-id="1">
                                    <label for="normalShipping{{ item.method }}"> <img
                                            width="250"
                                            style="max-height: 70px;object-fit: contain"
                                            src="{{ item.icon }}"
                                            alt="">
                                    </label>
                                    <div class="check"></div>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="input-group">
                    <span class="input-group-text">
                        <img src="{% static "wa-icon.png" %}"
                             alt="WhatsApp">
                    </span>
                <input name="whatsapp" type="text" class="form-control" id="phoneInput"
                       placeholder="Enter your phone number">
            </div>
        </div>

        <button type="submit" class="btn btn-success w-100">Donate</button>
    </form>


</div>

<div class="footer-fixed">
    <div id="payment-methods-container">
    </div>
</div>

<script>
    $(document).ready(function () {
        // Your existing AJAX code

        function displayPaymentMethods(methods) {
            var methodsContainer = $('#payment-methods-container');
            methods.forEach(function (method) {
                var paymentMethod = $('<a>', {
                    class: 'payment-method',
                    href: '#',
                    target: '_blank',
                });

                var icon = $('<img>', {
                    src: method.icon,
                    alt: method.name,
                });

                var name = $('<p>', {
                    text: method.name,
                });

                paymentMethod.append(icon, name);
                methodsContainer.append(paymentMethod);
            });
        }
    });
</script>

</body>
</html>
