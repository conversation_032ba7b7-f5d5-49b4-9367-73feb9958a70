from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.db.models import F
from django.utils.safestring import mark_safe

from .models import DonateUser


@admin.register(DonateUser)
class DonateUserAdmin(AjaxDatatable):
    list_display = ['user', 'amount', 'invoice_id', 'created_at']
    readonly_fields = list_display
    ordering = ('-id',)
    search_fields = ('user__mobile_device_id', 'user__email',)

    # @admin.display(description='IsPaid')
    # def _paid(self, obj):
    #     badge = 'badge bg-danger'
    #     if obj.payment_status == "confirmed":
    #         badge = 'badge bg-success bg-opacity-20 text-white ms-2'
    #
    #     return mark_safe(
    #         f"<span class='{badge}'>{obj.payment_status}</span>"
    #     )

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            # payment_status=F('payment__status'),
        )
