from django.urls import path, include
from . import views
from .views import stripe_paylink_view, WebDonateFormView, DonateDetailView, CreateDonateView, DonateDetailV2View

urlpatterns = [
    # path('pay/<int:pk>/', payment_details, name='donate-pay-2'),
    path('pay-stripe/<int:pk>/', stripe_paylink_view, name='stripe-pay'),
    path('donate/', views.DonateFormView.as_view()),
    path('web-donate/', WebDonateFormView.as_view()),

    path('donate/v2/', views.DonateDetailView.as_view()),
    path('donate/v2/create/', views.CreateDonateView.as_view()),

    path('web/donate/v2/', views.DonateDetailView.as_view()),
    path('web/donate/v2/create/', views.CreateDonateView.as_view()),

    # New V3 API with improved structure
    path('donate/v3/', DonateDetailV2View.as_view(), name='donate-detail-v3'),

]
