from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema


def donate_detail_v2_get_docs():
    """
    Documentation for DonateDetailV2View GET method
    API documentation for retrieving donation details with pricing and payment buttons
    """
    return {
        'operation_description': """
        **Donation Details API V3**

        Retrieve donation pricing information and available payment methods with intelligent localization support.

        **Key Features:**
        - Monthly and one-time donation options
        - Dynamic pricing based on user location
        - Multiple payment gateway support
        - Localized currency and payment methods
        - Russian language and payment gateway support

        **Donation Types:**
        - **Monthly**: Recurring donations with subscription pricing
        - **Once**: One-time donations with flexible amounts

        **Payment Methods:**
        - **Stripe**: International payment gateway (default)
        - **CloudTips**: Russian payment gateway for Russian users

        **Localization Logic:**
        - Detects user language from request headers or query parameters
        - Detects user country from authenticated user profile
        - Russian users see ruble pricing and CloudTips payment option
        - Non-Russian users see USD pricing with Stripe only

        **Business Logic:**
        - Monthly donations: Same pricing for all users ($5, $10, $20, $50)
        - Once donations: USD for international users, RUB for Russian users
        - Russian detection: language_code=ru OR user.country=RU
        - Payment buttons include icon, text, and payment method ID

        **Authentication**: Not required - Public API
        **Permissions**: None - Accessible to all users
        """,
        'tags': ['Donate'],
        'manual_parameters': [
            openapi.Parameter(
                'language_code',
                openapi.IN_QUERY,
                description="Language preference (e.g., 'ru' for Russian, 'en' for English)",
                type=openapi.TYPE_STRING,
                required=False
            )
        ],
        'responses': {
            200: openapi.Response(
                description="Donation details retrieved successfully",
                examples={
                    'application/json': {
                        'international_user_response': {
                            'summary': 'International User Response',
                            'description': 'Default response for non-Russian users with USD pricing',
                            'value': {
                                "monthly": {
                                    "prices": [
                                        {"value": "$5", "price": "5.00"},
                                        {"value": "$10", "price": "10.00"},
                                        {"value": "$20", "price": "20.00"},
                                        {"value": "$50", "price": "50.00"}
                                    ],
                                    "buttons": [
                                        {
                                            "icon": "https://habibapp.com/static/payicon.jpg",
                                            "text": "Donate Now",
                                            "id": "stripe"
                                        }
                                    ]
                                },
                                "once": {
                                    "prices": [
                                        {"value": "$5", "price": "5.00"},
                                        {"value": "$15", "price": "15.00"},
                                        {"value": "$30", "price": "30.00"},
                                        {"value": "$40", "price": "40.00"}
                                    ],
                                    "buttons": [
                                        {
                                            "icon": "https://habibapp.com/static/payicon.jpg",
                                            "text": "Donate Now",
                                            "id": "stripe"
                                        }
                                    ]
                                }
                            }
                        },
                        'russian_user_response': {
                            'summary': 'Russian User Response',
                            'description': 'Localized response for Russian users with ruble pricing and CloudTips',
                            'value': {
                                "monthly": {
                                    "prices": [
                                        {"value": "$5", "price": "5.00"},
                                        {"value": "$10", "price": "10.00"},
                                        {"value": "$20", "price": "20.00"},
                                        {"value": "$50", "price": "50.00"}
                                    ],
                                    "buttons": [
                                        {
                                            "icon": "https://habibapp.com/static/payicon.jpg",
                                            "text": "Donate Now",
                                            "id": "stripe"
                                        }
                                    ]
                                },
                                "once": {
                                    "prices": [
                                        {"value": "₽500", "price": "5.00"},
                                        {"value": "₽1500", "price": "15.00"},
                                        {"value": "₽3000", "price": "30.00"},
                                        {"value": "₽4000", "price": "40.00"}
                                    ],
                                    "buttons": [
                                        {
                                            "icon": "https://habibapp.com/static/payicon.jpg",
                                            "text": "Donate Now",
                                            "id": "stripe"
                                        },
                                        {
                                            "icon": "https://habibapp.com/static/cloudtips_icon.png",
                                            "text": "Пожертвовать через CloudTips",
                                            "id": "cloudtips"
                                        }
                                    ]
                                }
                            }
                        }
                    }
                }
            )
        }
    }


def create_donate_post_docs():
    """
    Documentation for CreateDonateView POST method
    API documentation for creating donations with error-free operation
    """
    return {
        'operation_description': """
        **Donation Creation API**

        Create a donation record and receive a payment link. This API is designed to never fail and always returns a working donation link without any errors.

        **Key Features:**
        - Error-free operation - never returns HTTP errors
        - All fields are completely optional with intelligent defaults
        - Automatic payment method selection based on user location
        - Support for multiple payment gateways
        - Backward compatibility with existing integrations

        **Smart Defaults:**
        - **donate_type**: Defaults to 'once' if not provided or invalid
        - **price**: Defaults to '10.00' if not provided or invalid
        - **id**: Auto-selected based on user location (CloudTips for Russian users, Stripe for others)

        **Payment Methods:**
        - **Stripe**: International payment gateway with predefined donation links
        - **CloudTips**: Russian payment gateway for Russian users

        **Localization Logic:**
        - Russian users (language_code=ru OR user.country=RU) default to CloudTips
        - All other users default to Stripe payment gateway
        - Fallback to default donation links for custom amounts

        **Business Logic:**
        - Creates donation record in database (optional, doesn't affect response)
        - Maps donation amounts to predefined Stripe payment links
        - Uses fallback links for unmapped amounts
        - Always returns HTTP 200 with working payment link

        **Authentication**: Not required - Public API
        **Permissions**: None - Accessible to all users
        """,
        'tags': ['Donate'],
        'request_body': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'donate_type': openapi.Schema(
                    type=openapi.TYPE_STRING, 
                    enum=['monthly', 'once'],
                    description="Type of donation - either 'monthly' for recurring donations or 'once' for one-time donations. This field is completely optional and will default to 'once' if not provided or if an invalid value is sent."
                ),
                'price': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Donation amount as a string (e.g., '5.00', '10.00', '20.00'). This field is completely optional and will default to '10.00' if not provided or if an invalid value is sent."
                ),
                'id': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=['stripe', 'cloudtips'],
                    description="Payment gateway identifier. This field is completely optional. If not provided, the system will automatically choose 'stripe' for non-Russian users and 'cloudtips' for Russian users (detected by language_code=ru or user country=RU). If an invalid value is provided, it defaults to 'stripe'."
                ),
            },
            required=[],
            example={
                "donate_type": "once",
                "price": "15.00",
                "id": "stripe"
            }
        ),
        'responses': {
            200: openapi.Response(
                description="Donation link created successfully. This API is guaranteed to always return HTTP 200 status with a working donation link, regardless of the input provided. Even if no data is sent in the request body, the API will respond with appropriate defaults.",
                examples={
                    'application/json': {
                        'complete_request': {
                            'summary': 'Complete Request',
                            'description': 'Response when all fields are provided',
                            'value': {
                                "donate_user": 1,
                                "donate_link": "https://donate.stripe.com/bIYdSDgiK7Pwh0ccMN"
                            }
                        },
                        'empty_request_default': {
                            'summary': 'Empty Request (Default)',
                            'description': 'Response when no fields are provided - uses intelligent defaults',
                            'value': {
                                "donate_user": 1,
                                "donate_link": "https://donate.stripe.com/7sI3dZaYq7Pw9xKcMV"
                            }
                        },
                        'empty_request_russian_user': {
                            'summary': 'Empty Request (Russian User)',
                            'description': 'Response for Russian users when no fields are provided',
                            'value': {
                                "donate_user": 1,
                                "donate_link": "https://pay.cloudtips.ru/p/d5881ddf"
                            }
                        },
                        'cloudtips_payment': {
                            'summary': 'CloudTips Payment',
                            'description': 'Response when CloudTips payment method is selected',
                            'value': {
                                "donate_user": 1,
                                "donate_link": "https://pay.cloudtips.ru/p/d5881ddf"
                            }
                        },
                        'invalid_data_fallback': {
                            'summary': 'Invalid Data Fallback',
                            'description': 'Response when invalid data is provided - normalized to defaults',
                            'value': {
                                "donate_user": 1,
                                "donate_link": "https://donate.stripe.com/7sI3dZaYq7Pw9xKcMV"
                            }
                        }
                    }
                }
            )
        }
    }
