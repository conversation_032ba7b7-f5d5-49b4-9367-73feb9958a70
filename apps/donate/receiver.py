from django.conf import settings
from django.dispatch import receiver
from django.views.decorators.csrf import csrf_exempt
from djongo.base import logger
from paypal.standard.ipn.models import PayPalIPN
from paypal.standard.ipn.signals import invalid_ipn_received, valid_ipn_received
from paypal.standard.models import ST_PP_COMPLETED

from apps.donate.models import DonateUser
from apps.library.models import PurchaseBook


@csrf_exempt
@receiver(invalid_ipn_received)
def paypal_payment_received(sender: PayPalIPN, **kwargs):
    print(PayPalIPN)
    if sender.payment_status == ST_PP_COMPLETED:
        # WARNING !
        # Check that the receiver email is the same we previously
        # set on the `business` field. (The user could tamper with
        # that fields on the payment form before it goes to PayPal)
        if sender.receiver_email != settings.PAYPAL_ACCOUNT:
            # Not a valid payment
            return

        # ALSO: for the same reason, you need to check the amount
        # received, `custom` etc. are all what you expect or what
        # is allowed.
        donate = DonateUser.objects.get(
            invoice_id=sender.invoice,
        )
        donate.payment = sender
        donate.paid = False
        donate.save()

        print(donate, '--------')
    else:
        logger.debug('Paypal payment status not completed: %s' % sender.payment_status)


@csrf_exempt
@receiver(valid_ipn_received)
def paypal_payment_received(sender: PayPalIPN, **kwargs):
    print(PayPalIPN)
    if sender.payment_status == ST_PP_COMPLETED:
        # WARNING !
        # Check that the receiver email is the same we previously
        # set on the `business` field. (The user could tamper with
        # that fields on the payment form before it goes to PayPal)
        if sender.receiver_email != settings.PAYPAL_ACCOUNT:
            # Not a valid payment
            return

        # ALSO: for the same reason, you need to check the amount
        # received, `custom` etc. are all what you expect or what
        # is allowed.
        if sender.invoice.startswith('library'):
            if obj := PurchaseBook.objects.filter(invoice_id=sender.invoice).first():
                obj.status = True
                obj.payment = sender
                obj.save()
        elif sender.invoice.startswith('donate'):
            if obj := DonateUser.objects.filter(
                    invoice_id=sender.invoice,
            ).first():
                obj.payment = sender
                obj.paid = True
                obj.save()

    else:
        logger.debug('Paypal payment status not completed: %s' % sender.payment_status)
