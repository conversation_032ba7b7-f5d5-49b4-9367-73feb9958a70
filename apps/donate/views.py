import base64
import json
from decimal import Decimal

import requests
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from django.conf import settings
from django.http import HttpRequest
from django.shortcuts import get_object_or_404, redirect, render
from django.template.response import TemplateResponse
from django.urls import reverse
from django.views.generic import FormView
from django.views.generic import TemplateView
from rest_framework.generics import CreateAPIView, ListAPIView, GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from apps.account.models import User
from apps.donate.models import DonateUser
from apps.donate.serializer import DonateUserSerializer
from apps.donate.doc import donate_detail_v2_get_docs, create_donate_post_docs
# from apps.payment.models import Payment
from utils import get_client_ip


class DonateDetailView(ListAPIView):    

    def get(self, request, *args, **kwargs):
        response_data = {}
        # if request.LANGUAGE_CODE not in ['ru', 'fa']:
        response_data['monthly'] = {
            "prices": [
                {"value": "$5", "price": "5.00"},
                {"value": "$10", "price": "10.00"},
                {"value": "$20", "price": "20.00"},
                {"value": "$50", "price": "50.00"}
            ],
            "icon": "https://habibapp.com/static/payicon.jpg"
        }

        response_data['once'] = {
            "prices": [
                {"value": "$5", "price": "5.00"},
                {"value": "$15", "price": "15.00"},
                {"value": "$30", "price": "30.00"},
                {"value": "$40", "price": "40.00"}
            ],
            "icon": "https://habibapp.com/static/payicon.jpg"
        }
        # else:
        #     response_data['monthly'] = None            
        #     response_data['once'] = {
        #         "prices": [
        #             {"value": "$5", "price": "5.00"},
        #             {"value": "$15", "price": "15.00"},
        #             {"value": "$30", "price": "30.00"},
        #             {"value": "$40", "price": "40.00"}
        #         ],
        #         "icon": "https://habibapp.com/static/payicon.jpg"
        #     }

        return Response(response_data)


class DonateDetailV2View(ListAPIView):
    """
    V2 API for donation details with improved structure including payment buttons.
    Supports different pricing and payment options based on user language and country.
    """

    @swagger_auto_schema(**donate_detail_v2_get_docs())
    def get(self, request, *args, **kwargs):
        is_russian_user = self._is_russian_user(request)
        response_data = self._build_donation_response(is_russian_user)
        return Response(response_data)

    def _is_russian_user(self, request):
        """
        Determine if user should see Russian-specific content based on:
        1. Request language code
        2. User's country (if authenticated)
        """
        # Check request language code
        language_code = getattr(request, 'LANGUAGE_CODE', None)
        if language_code == 'ru':
            return True

        # Check authenticated user's country
        if request.user.is_authenticated and hasattr(request.user, 'country'):
            user_country = request.user.country
            if user_country and str(user_country).upper() == 'RU':
                return True

        return False

    def _build_donation_response(self, is_russian_user):
        """Build the donation response structure based on user type"""
        response_data = {}

        # Monthly donations (same for all users)
        response_data['monthly'] = self._get_monthly_donation_data()

        # Once donations (different for Russian users)
        response_data['once'] = self._get_once_donation_data(is_russian_user)

        return response_data

    def _get_monthly_donation_data(self):
        """Get monthly donation structure (same for all users)"""
        return {
            "prices": [
                {"value": "$5", "price": "5.00"},
                {"value": "$10", "price": "10.00"},
                {"value": "$20", "price": "20.00"},
                {"value": "$50", "price": "50.00"}
            ],
            "buttons": [
                {
                    "icon": "https://habibapp.com/static/payicon.jpg",
                    "text": "Donate Now",
                    "id": "stripe"
                }
            ]
        }

    def _get_once_donation_data(self, is_russian_user):
        """Get one-time donation structure (different for Russian users)"""
        if is_russian_user:
            return {
                "prices": [
                    {"value": "₽500", "price": "5.00"},
                    {"value": "₽1500", "price": "15.00"},
                    {"value": "₽3000", "price": "30.00"},
                    {"value": "₽4000", "price": "40.00"}
                ],
                "buttons": [
                    {
                        "icon": "https://habibapp.com/static/payicon.jpg",
                        "text": "Donate Now",
                        "id": "stripe"
                    },
                    {
                        "icon": "https://habibapp.com/static/uploads/main/7e/9e/7e9e00de-5275-48ae-8474-2544f010d28b/cloudtips.png",
                        "text": "Пожертвовать через CloudTips",
                        "id": "cloudtips"
                    }
                ]
            }
        else:
            return {
                "prices": [
                    {"value": "$5", "price": "5.00"},
                    {"value": "$15", "price": "15.00"},
                    {"value": "$30", "price": "30.00"},
                    {"value": "$40", "price": "40.00"}
                ],
                "buttons": [
                    {
                        "icon": "https://habibapp.com/static/payicon.jpg",
                        "text": "Donate Now",
                        "id": "stripe"
                    }
                ]
            }


class CreateDonateView(GenericAPIView):
    """
    API for creating donations with support for different payment methods.
    Supports both legacy format and new v2 format with payment method selection.
    """

    # Payment gateway configurations
    CLOUDTIPS_LINK = "https://pay.cloudtips.ru/p/d5881ddf"
    STRIPE_CUSTOM_LINK = "https://donate.stripe.com/7sI3dZaYq7Pw9xKcMV"

    STRIPE_LINKS = {
        'monthly': {
            "5.00": "https://donate.stripe.com/9AQ01N5E68TAbFS002",
            "10.00": "https://donate.stripe.com/bIYdSDgiK7Pwh0ccMN",
            "20.00": "https://donate.stripe.com/dR6dSDc2u6Ls5hu6or",
            "50.00": "https://donate.stripe.com/00geWHfeG1r8bFSbIM",
        },
        'once': {
            "5.00": "https://donate.stripe.com/4gw7uf4A26Ls25i9AF",
            "15.00": "https://donate.stripe.com/aEU6qb2rU0n4dO0fZ4",
            "30.00": "https://donate.stripe.com/bIY6qbeaC2vc9xK007",
            "40.00": "https://donate.stripe.com/fZe4i30jM2vch0ccMU",
        }
    }

    @swagger_auto_schema(**create_donate_post_docs())
    def post(self, request, *args, **kwargs):
        try:
            # Extract request data with safe defaults
            data = request.data
            donate_type = data.get('donate_type', 'once')  # Default to 'once'
            price = data.get('price', '10.00')  # Default price
            payment_id = data.get('id', 'stripe')  # Default to stripe
            user = request.user

            # Normalize donate_type (ensure it's valid)
            if donate_type not in ['monthly', 'once']:
                donate_type = 'once'  # Default to 'once' for invalid types

            # Normalize price (ensure it's a string)
            try:
                price = str(price) if price else '10.00'
            except (ValueError, TypeError):
                price = '10.00'  # Default price for invalid formats

            # Normalize payment_id (ensure it's valid)
            if payment_id not in ['stripe', 'cloudtips']:
                payment_id = 'stripe'  # Default to stripe for invalid payment IDs

            # Check if user is Russian to determine default payment method
            is_russian_user = self._is_russian_user(request)
            if not data.get('id') and is_russian_user:
                payment_id = 'cloudtips'  # Default to cloudtips for Russian users

            # Create donation record (optional, don't fail if it doesn't work)
            try:
                donate_user = DonateUser.objects.create(
                    donate_type=donate_type,
                    amount=price,
                    user=user,
                )
            except Exception as exp:
                pass

            # Generate payment link based on payment method
            donate_link = self._get_payment_link(payment_id, donate_type, price)

            return Response({
                "donate_user": 1,
                "donate_link": donate_link
            })

        except Exception as e:
            # Ultimate fallback - always return a working link
            is_russian_user = self._is_russian_user_safe(request)
            fallback_link = self.CLOUDTIPS_LINK if is_russian_user else self.STRIPE_CUSTOM_LINK

            return Response({
                "donate_user": 1,
                "donate_link": fallback_link
            })

    def _is_russian_user(self, request):
        """
        Determine if user should see Russian-specific content based on:
        1. Request language code
        2. User's country (if authenticated)
        """
        try:
            # Check request language code
            language_code = getattr(request, 'LANGUAGE_CODE', None)
            if language_code == 'ru':
                return True

            # Check authenticated user's country
            if request.user.is_authenticated and hasattr(request.user, 'country'):
                user_country = request.user.country
                if user_country and str(user_country).upper() == 'RU':
                    return True
        except Exception:
            pass

        return False

    def _is_russian_user_safe(self, request):
        """Safe version of _is_russian_user that never throws exceptions"""
        try:
            return self._is_russian_user(request)
        except Exception:
            return False

    def _get_payment_link(self, payment_id, donate_type, price):
        """Generate appropriate payment link based on payment method"""
        if payment_id == 'cloudtips':
            return self.CLOUDTIPS_LINK

        # Default to Stripe (maintains backward compatibility)
        return self.STRIPE_LINKS.get(donate_type, {}).get(price, self.STRIPE_CUSTOM_LINK)




def decrypt_aes_cbc(encrypted_data, key):
    key_bytes = key.encode()
    iv_bytes = key_bytes

    cipher = Cipher(algorithms.AES(key_bytes), modes.CBC(iv_bytes), backend=default_backend())
    decryptor = cipher.decryptor()

    # Base64 decode the encrypted data
    encrypted_data_bytes = base64.b64decode(encrypted_data)

    # Perform the decryption
    decrypted_data = decryptor.update(encrypted_data_bytes) + decryptor.finalize()

    # Remove padding
    unpadded_data = decrypted_data.rstrip(b'\x08')

    return unpadded_data.decode('utf-8')


class WebDonateFormView(TemplateView):
    template_name = 'web-donate.html'

    def get(self, request, *args, **kwargs):
        token_param = request.GET.get('t', '').replace(' ', '+')
        token = decrypt_aes_cbc(token_param, 'be42891b2c29cd68')
        user = User.objects.filter(auth_token__key=token).first()
        lang = 'en'

        if user:
            lang = user.language.code if user.language else 'en'
            payment_methods = requests.get(f'https://habibapp.com/payment/methods/?language_code={lang}').json()
        else:
            payment_methods = requests.get(f'https://habibapp.com/payment/methods/?language_code=en').json()

        trans_file_path = str(settings.BASE_DIR) + '/apps/donate/donate_text.json'
        with open(trans_file_path, 'r') as f:
            text_trans = json.load(f)
            donate_text = text_trans.get(self.request.LANGUAGE_CODE.lower())
            if not donate_text:
                donate_text = text_trans.get('en')

        return render(request, "web-donate.html", {
            'default_method': 'vtb' if lang == 'ru' else 'stripe',
            'payment_methods': payment_methods,
            'donate_text': donate_text,
        })

    def post(self, request):
        token_param = request.POST.get('t', '').replace(' ', '+')
        token = decrypt_aes_cbc(token_param, 'be42891b2c29cd68')
        user = User.objects.filter(auth_token__key=token).first()

        DonateUser.objects.create(
            user=user,
            phone=request.POST.get('whatsapp'),
            amount=0,
        )
        pay_method = request.POST.get('method')

        if pay_method == "stripe":
            url = 'https://donate.stripe.com/dR65mfbtJg6i6OY7ss'

        elif pay_method == "vtb":
            url = "https://vtb.paymo.ru/collect-money/qr/?transaction=d0cc1364-417e-4f5e-a017-17a5b5e07fc4"

        else:
            url = "/web-donate/"

        return redirect(url)


class DonateFormView(CreateAPIView):
    serializer_class = DonateUserSerializer
    permission_classes = (IsAuthenticated,)

    def get_client_ip(self):
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip

    def create(self, request: HttpRequest, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        obj: DonateUser = serializer.save(
            user=request.user,
        )

        pay_method = request.data.get('pay_method', 'paypal')

        if pay_method == 'paypal':
            payment = Payment.objects.create(
                user=request.user,
                # variant='paypal',
                variant=pay_method,
                description='Donate',
                total=Decimal(obj.amount),
                tax=Decimal(0),
                currency='USD',
                delivery=Decimal(0),
                customer_ip_address=get_client_ip(request),
                content_object=obj,
            )
            url = f"https://pay.habibapp.com/pay-stripe/{payment.id}/"

        elif pay_method == "stripe":
            donate_links = {
                "custom": "https://donate.stripe.com/dR65mfbtJg6i6OY7ss",
                "25.00": "https://donate.stripe.com/00g15ZbtJ07k2yIdQR",
                "50.00": "https://donate.stripe.com/fZe01VfJZ9HU2yI3ce",
                "100.00": "https://donate.stripe.com/00gg0T69p9HUb5e7sv",
                "200.00": "https://donate.stripe.com/00g01V69pbQ2b5edQW",
                "250.00": "https://donate.stripe.com/3cs8yr7dt4nA7T2bIM",
                "500.00": "https://donate.stripe.com/7sIeWP55laLY6OYaEJ",
            }

            url = donate_links.get(str(obj.amount)) or donate_links.get('custom')
        elif pay_method == "vtb":
            url = "https://vtb.paymo.ru/collect-money/qr/?transaction=d0cc1364-417e-4f5e-a017-17a5b5e07fc4"

        else:
            url = ""

        return Response({
            'ok': True, 'amount': obj.amount,
            'payment_url': url,
        })


class PaypalFormView(FormView):
    template_name = 'paypal_form.html'

    # permission_classes = (IsAuthenticated,)

    def get_initial(self):
        donate = get_object_or_404(DonateUser, invoice_id=self.request.GET.get('t'))

        # if self.request.user.id != donate.user_id:
        #     return {
        #         'error': 'user is incorrect'
        #     }

        return {
            "business": settings.PAYPAL_ACCOUNT,
            "amount": donate.amount,
            "currency_code": "USD",
            "item_name": 'Donate for Habib',
            "invoice": donate.invoice_id,
            "notify_url": self.request.build_absolute_uri(reverse('paypal-ipn')),
            "return_url": self.request.build_absolute_uri(reverse('paypal-return')),
            "cancel_return": self.request.build_absolute_uri(reverse('paypal-cancel')),
            "lc": 'EN',
            "no_shipping": '1',
        }


class PaypalReturnView(TemplateView):
    template_name = 'paypal_success.html'

    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class PaypalCancelView(TemplateView):
    template_name = 'paypal_cancel.html'

    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


def payment_details(request, pk):
    # payment = get_object_or_404(Payment, id=pk)

    return TemplateResponse(
        request,
        'payment.html',
        {'form': '', 'payment': 'payment'}
    )


def stripe_paylink_view(request, pk):
    donate_links = {
        "custom": "https://donate.stripe.com/dR65mfbtJg6i6OY7ss",
        "25.00": "https://donate.stripe.com/00g15ZbtJ07k2yIdQR",
        "50.00": "https://donate.stripe.com/fZe01VfJZ9HU2yI3ce",
        "100.00": "https://donate.stripe.com/00gg0T69p9HUb5e7sv",
        "200.00": "https://donate.stripe.com/00g01V69pbQ2b5edQW",
        "250.00": "https://donate.stripe.com/3cs8yr7dt4nA7T2bIM",
        "500.00": "https://donate.stripe.com/7sIeWP55laLY6OYaEJ",
        'btb': '',
    }

    donate = get_object_or_404(DonateUser, pk=pk)
    if link := donate_links.get(str(donate.amount)):
        return redirect(link)

    return redirect(donate_links['custom'])
