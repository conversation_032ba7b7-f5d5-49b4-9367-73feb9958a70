from rest_framework import serializers

from apps.donate.models import DonateUser


class DonateUserSerializer(serializers.ModelSerializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    pay_method = serializers.ChoiceField(choices=[
        ('paypal', 'paypal'),
        ('vtb', 'vtb'),
        ('stripe', 'stripe'),
    ], default='stripe', write_only=True)

    class Meta:
        model = DonateUser
        fields = ('amount', 'phone', 'user', 'pay_method')

    def validate(self, attrs):
        attrs.pop('pay_method', None)
        return attrs
