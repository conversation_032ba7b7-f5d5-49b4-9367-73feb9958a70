from rest_framework import serializers

from apps.hadis.models import Narrates, Hadis
from dj_filer.admin import get_thumbs


class NarratorSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    hadis_count = serializers.IntegerField(allow_null=True)

    def get_name(self, obj):
        request = self.context.get('request')

        if request.LANGUAGE_CODE == 'ar':
            return obj.name

        for i in obj.translations:
            if i['language_code'] == request.LANGUAGE_CODE:
                if txt := i.get('text'):
                    return txt

        return obj.name

    class Meta:
        model = Narrates
        fields = ('id', 'name', 'hadis_count', 'photo',)


class NarratorSerializerV2(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    hadis_count = serializers.IntegerField(allow_null=True)
    hadis_index = serializers.SerializerMethodField()

    def get_photo(self, obj):
        return get_thumbs(obj.photo, self.request)

    def get_name(self, obj):
        request = self.context.get('request')

        if request.LANGUAGE_CODE == 'ar':
            return obj.name

        for i in obj.translations:
            if i['language_code'] == request.LANGUAGE_CODE:
                if txt := i.get('text'):
                    return txt

        return obj.name

    def get_hadis_index(self, obj):
        hadis_ids = Hadis.objects.filter(narrated_by=obj).only('number').values_list('number', flat=True)
        return list(hadis_ids)    

    class Meta:
        model = Narrates
        fields = ('id', 'name', 'hadis_count', 'photo', 'hadis_index')
