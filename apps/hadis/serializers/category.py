from dj_filer.admin import get_thumbs
from django.contrib.postgres.aggregates import Array<PERSON>gg
from rest_framework import serializers

from apps.hadis.models import Category, Hadis


class HadisCategorySerializer(serializers.ModelSerializer):
    thumbnails = serializers.SerializerMethodField('get_thumbnail_object')
    children = serializers.SerializerMethodField('get_children')
    name = serializers.SerializerMethodField()
    hadis_count = serializers.SerializerMethodField()

    def get_hadis_count(self, obj):
        if hasattr(obj, 'tag_ids'):
            return Hadis.objects.filter(tags__in=obj.tag_ids).count()
        return None

    def get_name(self, obj):
        request = self.context.get('request')
        return obj.get_translation(request.LANGUAGE_CODE, 'en')

    def get_thumbnail_object(self, obj):
        return get_thumbs(obj.thumbnail, self.context.get('request'))

    def get_children(self, obj):
        return [self.to_dict(cat) for cat in obj.get_children()]

    def to_dict(self, c):
        children = c.get_children().annotate(
            tag_ids=ArrayAgg('tags')
        )
        return {
            'name': self.get_name(c),
            'hadis_count': self.get_hadis_count(c),
            'thumbnails': self.get_thumbnail_object(c),
            'children': [] if not children else [self.to_dict(i) for i in children],
        }

    class Meta:
        model = Category
        fields = 'name', 'hadis_count', 'thumbnails', 'children'
