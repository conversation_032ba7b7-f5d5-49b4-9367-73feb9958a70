import datetime
from apps.hadis.models import Hadis
from apps.account.admin import PushMessageAdmin
from apps.account.models import User, PushMessage
from apps.hadis.views.interest import get_user_interests2
from django.utils import timezone
from datetime import timedelta

from hijri_converter import Gregorian




title = {
    "en": "A message from <PERSON><PERSON> al<PERSON> (AS)",
    "fr": "Un message d'Ahl al-Bay<PERSON> (AS)",
    "es": "Un mensaje de Ahl al-Bayt (AS)",
    "tr": "Ahl al-Bayt'tan bir mesaj (AS)",
    "ru": "Сообщение от Ахль-аль-Байт (AS)",
    "ar": "رسالة من أهل البيت (ع)",
    "az": "Əhlil-beytdən bir mesaj (AS)",
    "sw": "Ujumbe kutoka Ahl al-Bayt (AS)",
    "cn": "来自阿尔·拜特的消息 (AS)",
    "du": "<PERSON><PERSON> be<PERSON> van <PERSON> (AS)",
    "ur": "اہل بیت سے ایک پیغام (ع)",
    "id": "Se<PERSON>ah <PERSON> (AS)"
}



def user_hadis(user):
    hadises = get_user_interests2(user)
    title_hadis = title.get(user.language.code) or title['en'],
    user_tz = '1'

    if login_history := user.login_history.first():
        user_tz = login_history.timezone

    user_local_yesterday_date = timezone.now().utcnow() + timedelta(hours=float(user_tz), days=-1)
    today = datetime.datetime.today().date()
    hijri_date = Gregorian(today.year, today.month, today.day).to_hijri().isoformat().replace('-', '/')

    # if user_local_yesterday_date.hour != 20:
        # return False

    # Check whether a message has been sent by the user's local time within the past 24 hours.
    has_already_sent = PushMessage.objects.filter(
        data__model='Hadis', created_at__date=today,
        users__in=[user],
    ).exists()

    # if has_already_sent:
        # return False

    to_exclude_hadises = PushMessage.objects.filter(
        users__in=[user], data__model='Hadis', data__value__in=list(hadises.values_list('id', flat=True))
    ).values_list('data__value', flat=True)

    today_hadis_based_on_lunar_date = Hadis.objects.exclude(id__in=list(to_exclude_hadises)).filter(
        notif_dates__contains=hijri_date[5:],
        status=True,
    ).order_by('?').first()

    if today_hadis_based_on_lunar_date:
        hadis = today_hadis_based_on_lunar_date
    else:
        hadis = hadises.order_by('?').exclude(id__in=list(to_exclude_hadises)).first()
        if not hadis:
            return False

    if user.language.code == 'ar':
        tr = hadis.text
    else:
        tr = hadis.get_translation(user.language.code)
        if not tr:
            tr = hadis.get_translation('en')

    if narrator := hadis.narrated_by:
        narrator = narrator.get_translation(user.language.code)
        if not narrator:
            narrator = narrator.get_translation('en')
    else:
        narrator = ''
    
    return {
        'hadis_id': hadis.id,
        'title': title_hadis,
        'message': f"""{tr} \n{narrator}"""
    }    
