#!/bin/bash

# =============================================================================
# AI Category Batch Manager Script
# =============================================================================
# This script manages batch translation operations for the ai_category_batch_importer.py
# 
# Mode 1 (create): Gets active languages, creates batches, saves batch IDs to JSON
# Mode 2 (process): Reads batch IDs from JSON, processes each batch
#
# Author: Generated for Habib Backend
# Date: $(date +%Y-%m-%d)
# =============================================================================

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# =============================================================================
# GLOBAL VARIABLES AND CONFIGURATION
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPT_NAME="$(basename "$0")"
LOG_FILE="${SCRIPT_DIR}/batch_manager.log"
JSON_FILE="${SCRIPT_DIR}/batch_ids.json"
PYTHON_SCRIPT="${SCRIPT_DIR}/ai_category_batch_importer.py"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Exit codes
EXIT_SUCCESS=0
EXIT_ERROR=1
EXIT_INVALID_ARGS=2
EXIT_FILE_NOT_FOUND=3
EXIT_DEPENDENCY_ERROR=4

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

# =============================================================================
# TEST FUNCTIONS (TDD APPROACH)
# =============================================================================

run_tests() {
    log_info "Running TDD tests..."
    local test_count=0
    local passed_count=0
    
    # Test 1: Check if Python script exists
    test_count=$((test_count + 1))
    if test_python_script_exists; then
        passed_count=$((passed_count + 1))
        log_success "Test 1 PASSED: Python script exists"
    else
        log_error "Test 1 FAILED: Python script not found"
    fi
    
    # Test 2: Check Django environment
    test_count=$((test_count + 1))
    if test_django_environment; then
        passed_count=$((passed_count + 1))
        log_success "Test 2 PASSED: Django environment accessible"
    else
        log_error "Test 2 FAILED: Django environment not accessible"
    fi
    
    # Test 3: Validate argument parsing
    test_count=$((test_count + 1))
    if test_argument_parsing; then
        passed_count=$((passed_count + 1))
        log_success "Test 3 PASSED: Argument parsing works"
    else
        log_error "Test 3 FAILED: Argument parsing failed"
    fi
    
    # Test 4: JSON file operations
    test_count=$((test_count + 1))
    if test_json_operations; then
        passed_count=$((passed_count + 1))
        log_success "Test 4 PASSED: JSON operations work"
    else
        log_error "Test 4 FAILED: JSON operations failed"
    fi
    
    # Test 5: Language fetching
    test_count=$((test_count + 1))
    if test_language_fetching; then
        passed_count=$((passed_count + 1))
        log_success "Test 5 PASSED: Language fetching works"
    else
        log_error "Test 5 FAILED: Language fetching failed"
    fi
    
    # Test 6: Python script execution
    test_count=$((test_count + 1))
    if test_python_script_execution; then
        passed_count=$((passed_count + 1))
        log_success "Test 6 PASSED: Python script execution works"
    else
        log_error "Test 6 FAILED: Python script execution failed"
    fi
    
    # Test 7: Batch ID extraction
    test_count=$((test_count + 1))
    if test_batch_id_extraction; then
        passed_count=$((passed_count + 1))
        log_success "Test 7 PASSED: Batch ID extraction works"
    else
        log_error "Test 7 FAILED: Batch ID extraction failed"
    fi
    
    log_info "Tests completed: $passed_count/$test_count passed"
    
    if [ "$passed_count" -eq "$test_count" ]; then
        log_success "All tests passed!"
        return 0
    else
        log_error "Some tests failed!"
        return 1
    fi
}

test_python_script_exists() {
    [ -f "$PYTHON_SCRIPT" ] && [ -r "$PYTHON_SCRIPT" ]
}

test_django_environment() {
    # Test if we can import Django and the required models
    local backend_root
    backend_root=$(cd "$SCRIPT_DIR/../../.." && pwd)

    local venv_path="$backend_root/.venv"
    local python_cmd="python3"

    if [ -d "$venv_path" ] && [ -f "$venv_path/bin/activate" ]; then
        python_cmd="$venv_path/bin/python"
    fi

    cd "$backend_root" && PYTHONPATH="$backend_root" $python_cmd -c "
import os
import sys
# Add the backend root directory to Python path
backend_root = os.path.abspath('.')
sys.path.insert(0, backend_root)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
import django
django.setup()
from dj_language.models import Language
print('Django environment OK')
" 2>/dev/null
}

test_argument_parsing() {
    # Test basic argument validation
    local test_mode="create"
    validate_arguments "$test_mode" && return 0 || return 1
}

test_json_operations() {
    # Test JSON file creation and reading
    local test_json="${SCRIPT_DIR}/test_batch_ids.json"
    
    # Test writing
    echo '{"test": "value"}' > "$test_json" || return 1
    
    # Test reading
    [ -f "$test_json" ] && [ -r "$test_json" ] || return 1
    
    # Cleanup
    rm -f "$test_json"
    return 0
}

test_language_fetching() {
    # Test if we can fetch languages from database
    local languages_output
    languages_output=$(get_active_languages 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$languages_output" ]; then
        # Check if output contains valid JSON structure
        echo "$languages_output" | grep -q '"code"' && echo "$languages_output" | grep -q '"name"' && return 0
    fi
    return 1
}

test_python_script_execution() {
    # Test if Python script can be executed with help
    local backend_root
    backend_root=$(cd "$SCRIPT_DIR/../../.." && pwd)
    
    local venv_path="$backend_root/.venv"
    local python_cmd="python3"
    
    if [ -d "$venv_path" ] && [ -f "$venv_path/bin/activate" ]; then
        python_cmd="$venv_path/bin/python"
    fi
    
    # Test script execution with help (should not fail)
    cd "$backend_root" && PYTHONPATH="$backend_root" $python_cmd "$PYTHON_SCRIPT" --help >/dev/null 2>&1
    return $?
}

test_batch_id_extraction() {
    # Test batch ID extraction from sample output
    local sample_output="Log sent successfully
Batch translation requests sent. Batch ID: msgbatch_014zy3Zqv9pvLZsiygxVA5qT"
    
    local extracted_id
    extracted_id=$(echo "$sample_output" | grep "Batch ID:" | tail -1 | sed 's/.*Batch ID: //')
    
    if [ -n "$extracted_id" ] && [ "$extracted_id" = "msgbatch_014zy3Zqv9pvLZsiygxVA5qT" ]; then
        return 0
    fi
    return 1
}

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

validate_dependencies() {
    log_info "Validating dependencies..."

    # Check if Python 3 is available
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed or not in PATH"
        return $EXIT_DEPENDENCY_ERROR
    fi

    # Check if the Python script exists
    if [ ! -f "$PYTHON_SCRIPT" ]; then
        log_error "Python script not found: $PYTHON_SCRIPT"
        return $EXIT_FILE_NOT_FOUND
    fi

    # Check if the Python script is readable
    if [ ! -r "$PYTHON_SCRIPT" ]; then
        log_error "Python script is not readable: $PYTHON_SCRIPT"
        return $EXIT_FILE_NOT_FOUND
    fi

    log_success "All dependencies validated successfully"
    return $EXIT_SUCCESS
}

validate_arguments() {
    local mode="$1"

    case "$mode" in
        "create"|"process"|"test"|"create-test")
            return $EXIT_SUCCESS
            ;;
        *)
            log_error "Invalid mode: $mode. Must be 'create', 'process', 'test', or 'create-test'"
            return $EXIT_INVALID_ARGS
            ;;
    esac
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

show_usage() {
    cat << EOF
Usage: $SCRIPT_NAME <mode>

MODES:
    create      Mode 1: Create batches for all active languages (excluding Persian)
                        and save batch IDs to JSON file

    create-test Mode 1: Create batches for first 2 languages only (for testing)

    process     Mode 2: Process batches using previously saved batch IDs

    test        Run TDD tests to validate script functionality

EXAMPLES:
    $SCRIPT_NAME create       # Create batches and save IDs
    $SCRIPT_NAME create-test  # Create test batches (first 2 languages only)
    $SCRIPT_NAME process      # Process saved batches
    $SCRIPT_NAME test         # Run validation tests

FILES:
    Input:  $PYTHON_SCRIPT
    Output: $JSON_FILE
    Log:    $LOG_FILE

EXIT CODES:
    0 - Success
    1 - General error
    2 - Invalid arguments
    3 - File not found
    4 - Dependency error

EOF
}

cleanup() {
    log_info "Performing cleanup..."
    # Add any cleanup operations here if needed
}

# =============================================================================
# LANGUAGE MANAGEMENT FUNCTIONS
# =============================================================================

get_active_languages() {
    log_info "Fetching active languages from database..."

    # Get backend root and Python command
    local backend_root
    backend_root=$(cd "$SCRIPT_DIR/../../.." && pwd)

    local venv_path="$backend_root/.venv"
    local python_cmd="python3"

    if [ -d "$venv_path" ] && [ -f "$venv_path/bin/activate" ]; then
        python_cmd="$venv_path/bin/python"
    fi

    # Python script to get active languages excluding Persian
    local languages_output
    languages_output=$(cd "$backend_root" && PYTHONPATH="$backend_root" $python_cmd -c "
import os
import sys
import json
# Add the backend root directory to Python path
backend_root = os.path.abspath('.')
sys.path.insert(0, backend_root)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
import django
django.setup()
from dj_language.models import Language

# Get active languages excluding Persian ('fa')
active_languages = Language.objects.filter(status=True).exclude(code='fa')
languages_data = []
for lang in active_languages:
    languages_data.append({
        'code': lang.code,
        'name': lang.name
    })

print(json.dumps(languages_data))
" 2>/dev/null)

    if [ $? -ne 0 ] || [ -z "$languages_output" ]; then
        log_error "Failed to fetch active languages from database"
        return $EXIT_ERROR
    fi

    echo "$languages_output"
    return $EXIT_SUCCESS
}

# =============================================================================
# JSON FILE MANAGEMENT FUNCTIONS
# =============================================================================

create_json_file() {
    local json_content="$1"

    log_info "Creating JSON file: $JSON_FILE"

    # Create backup if file exists
    if [ -f "$JSON_FILE" ]; then
        local backup_file="${JSON_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$JSON_FILE" "$backup_file"
        log_info "Created backup: $backup_file"
    fi

    # Write JSON content to file
    echo "$json_content" > "$JSON_FILE"

    if [ $? -eq 0 ]; then
        log_success "JSON file created successfully: $JSON_FILE"
        return $EXIT_SUCCESS
    else
        log_error "Failed to create JSON file: $JSON_FILE"
        return $EXIT_ERROR
    fi
}

read_json_file() {
    if [ ! -f "$JSON_FILE" ]; then
        log_error "JSON file not found: $JSON_FILE"
        return $EXIT_FILE_NOT_FOUND
    fi

    if [ ! -r "$JSON_FILE" ]; then
        log_error "JSON file is not readable: $JSON_FILE"
        return $EXIT_FILE_NOT_FOUND
    fi

    cat "$JSON_FILE"
    return $EXIT_SUCCESS
}

# =============================================================================
# BATCH PROCESSING FUNCTIONS
# =============================================================================

execute_python_script() {
    local lang_code="$1"
    local lang_name="$2"
    local action="$3"
    local batch_id="$4"

    log_info "Executing Python script for language: $lang_code ($lang_name) with action: $action"

    # Build command arguments
    local cmd_args="--lang_code '$lang_code' --lang_name '$lang_name' --action '$action'"

    # Add batch_id if provided (for 'check' action)
    if [ -n "$batch_id" ]; then
        cmd_args="$cmd_args --batch_id '$batch_id'"
    fi

    # Execute the Python script and capture output
    local output
    local exit_code

    log_info "Running command: python3 $PYTHON_SCRIPT $cmd_args"

    # Change to backend root directory to ensure proper Python path resolution
    local backend_root
    backend_root=$(cd "$SCRIPT_DIR/../../.." && pwd)

    # Check if virtual environment exists and activate it
    local venv_path="$backend_root/.venv"
    local python_cmd="python3"

    if [ -d "$venv_path" ] && [ -f "$venv_path/bin/activate" ]; then
        log_info "Activating virtual environment: $venv_path"
        python_cmd="$venv_path/bin/python"
    fi

    # Execute from backend root with proper PYTHONPATH and virtual environment
    output=$(cd "$backend_root" && PYTHONPATH="$backend_root" eval "$python_cmd '$PYTHON_SCRIPT' $cmd_args" 2>&1)
    exit_code=$?

    if [ $exit_code -eq 0 ]; then
        log_success "Python script executed successfully for $lang_code"
        log_info "Output: $output"

        # Extract batch ID from output if action is 'send'
        if [ "$action" = "send" ]; then
            local batch_id_line
            batch_id_line=$(echo "$output" | grep "Batch ID:" | tail -1)
            if [ -n "$batch_id_line" ]; then
                local extracted_batch_id
                extracted_batch_id=$(echo "$batch_id_line" | sed 's/.*Batch ID: //')
                echo "$extracted_batch_id"
                return $EXIT_SUCCESS
            else
                log_warning "Could not extract batch ID from output for $lang_code"
                return $EXIT_ERROR
            fi
        fi

        return $EXIT_SUCCESS
    else
        log_error "Python script failed for $lang_code with exit code: $exit_code"
        log_error "Error output: $output"
        return $EXIT_ERROR
    fi
}

# =============================================================================
# MODE 1: BATCH CREATION FUNCTIONS
# =============================================================================

create_batches() {
    local test_mode="${1:-}"
    log_info "Starting Mode 1: Batch Creation${test_mode:+ (Test Mode)}"

    # Get active languages
    local languages_json
    languages_json=$(get_active_languages)

    if [ $? -ne 0 ]; then
        log_error "Failed to get active languages"
        return $EXIT_ERROR
    fi

    log_info "Found languages: $languages_json"

    # Parse languages and create batches
    local batch_results="{}"
    local total_languages=0
    local successful_batches=0
    local failed_batches=0
    local temp_json_file="${SCRIPT_DIR}/temp_batch_results.json"

    # Process each language
    while IFS= read -r lang_entry; do
        # Extract language code and name using basic string manipulation
        local lang_code
        local lang_name

        lang_code=$(echo "$lang_entry" | sed -n 's/.*"code": *"\([^"]*\)".*/\1/p')
        lang_name=$(echo "$lang_entry" | sed -n 's/.*"name": *"\([^"]*\)".*/\1/p')

        if [ -n "$lang_code" ] && [ -n "$lang_name" ]; then
            total_languages=$((total_languages + 1))

            # In test mode, only process first 2 languages
            if [ "$test_mode" = "test" ] && [ "$total_languages" -gt 2 ]; then
                log_info "Test mode: Skipping remaining languages after processing 2"
                break
            fi

            log_info "Processing language $total_languages: $lang_code ($lang_name)"

            # Execute Python script to create batch
            local batch_id
            batch_id=$(execute_python_script "$lang_code" "$lang_name" "send" "")

            if [ $? -eq 0 ] && [ -n "$batch_id" ]; then
                successful_batches=$((successful_batches + 1))
                log_success "Batch created for $lang_code: $batch_id"

                # Add to results JSON using Python for proper JSON handling
                if [ "$batch_results" = "{}" ]; then
                    batch_results="{\"$lang_code\": \"$batch_id\"}"
                else
                    batch_results=$(python3 -c "
import json
import sys
try:
    data = json.loads('$batch_results')
    data['$lang_code'] = '$batch_id'
    print(json.dumps(data))
except Exception as e:
    print('$batch_results', end='')
    sys.exit(1)
" 2>/dev/null || echo "$batch_results")
                fi
            else
                failed_batches=$((failed_batches + 1))
                log_error "Failed to create batch for $lang_code"
            fi
        fi
    done < <(echo "$languages_json" | grep -o '{[^}]*}')

    # Save results to JSON file
    if [ "$successful_batches" -gt 0 ]; then
        create_json_file "$batch_results"
        log_success "Batch creation completed: $successful_batches successful, $failed_batches failed"
        log_info "Batch IDs saved to: $JSON_FILE"
    else
        log_error "No batches were created successfully"
        return $EXIT_ERROR
    fi

    return $EXIT_SUCCESS
}

# =============================================================================
# MODE 2: BATCH PROCESSING FUNCTIONS
# =============================================================================

process_batches() {
    log_info "Starting Mode 2: Batch Processing"

    # Read batch IDs from JSON file
    local batch_json
    batch_json=$(read_json_file)

    if [ $? -ne 0 ]; then
        log_error "Failed to read batch IDs from JSON file"
        return $EXIT_ERROR
    fi

    log_info "Loaded batch IDs: $batch_json"

    # Get language information for processing
    local languages_json
    languages_json=$(get_active_languages)

    if [ $? -ne 0 ]; then
        log_error "Failed to get active languages"
        return $EXIT_ERROR
    fi

    local total_batches=0
    local successful_processes=0
    local failed_processes=0

    # Process each language batch
    while IFS= read -r lang_entry; do
        local lang_code
        local lang_name

        lang_code=$(echo "$lang_entry" | sed -n 's/.*"code": *"\([^"]*\)".*/\1/p')
        lang_name=$(echo "$lang_entry" | sed -n 's/.*"name": *"\([^"]*\)".*/\1/p')

        if [ -n "$lang_code" ] && [ -n "$lang_name" ]; then
            # Extract batch ID for this language from JSON
            local batch_id
            batch_id=$(echo "$batch_json" | sed -n "s/.*\"$lang_code\": *\"\([^\"]*\)\".*/\1/p")

            if [ -n "$batch_id" ]; then
                total_batches=$((total_batches + 1))
                log_info "Processing batch $total_batches: $lang_code ($lang_name) with batch ID: $batch_id"

                # Execute Python script to process batch
                if execute_python_script "$lang_code" "$lang_name" "check" "$batch_id"; then
                    successful_processes=$((successful_processes + 1))
                    log_success "Batch processed successfully for $lang_code"
                else
                    failed_processes=$((failed_processes + 1))
                    log_error "Failed to process batch for $lang_code"
                fi
            else
                log_warning "No batch ID found for language: $lang_code"
            fi
        fi
    done < <(echo "$languages_json" | grep -o '{[^}]*}')

    log_success "Batch processing completed: $successful_processes successful, $failed_processes failed"

    if [ "$failed_processes" -gt 0 ]; then
        return $EXIT_ERROR
    fi

    return $EXIT_SUCCESS
}

# =============================================================================
# MAIN EXECUTION LOGIC
# =============================================================================

main() {
    # Initialize logging
    log_info "Starting $SCRIPT_NAME with arguments: $*"

    # Validate arguments
    if [ $# -ne 1 ]; then
        log_error "Invalid number of arguments"
        show_usage
        exit $EXIT_INVALID_ARGS
    fi

    local mode="$1"

    # Validate mode
    if ! validate_arguments "$mode"; then
        show_usage
        exit $EXIT_INVALID_ARGS
    fi

    # Validate dependencies
    if ! validate_dependencies; then
        exit $EXIT_DEPENDENCY_ERROR
    fi

    # Execute based on mode
    case "$mode" in
        "test")
            if run_tests; then
                log_success "All tests passed successfully"
                exit $EXIT_SUCCESS
            else
                log_error "Some tests failed"
                exit $EXIT_ERROR
            fi
            ;;
        "create")
            if create_batches; then
                log_success "Mode 1: Batch creation completed successfully"
                exit $EXIT_SUCCESS
            else
                log_error "Mode 1: Batch creation failed"
                exit $EXIT_ERROR
            fi
            ;;
        "create-test")
            if create_batches "test"; then
                log_success "Mode 1: Test batch creation completed successfully"
                exit $EXIT_SUCCESS
            else
                log_error "Mode 1: Test batch creation failed"
                exit $EXIT_ERROR
            fi
            ;;
        "process")
            if process_batches; then
                log_success "Mode 2: Batch processing completed successfully"
                exit $EXIT_SUCCESS
            else
                log_error "Mode 2: Batch processing failed"
                exit $EXIT_ERROR
            fi
            ;;
    esac

    log_success "Script completed successfully"
}

# =============================================================================
# SCRIPT ENTRY POINT
# =============================================================================

# Set up signal handlers for cleanup
trap cleanup EXIT
trap 'log_error "Script interrupted"; exit 130' INT TERM

# Execute main function with all arguments
main "$@"
