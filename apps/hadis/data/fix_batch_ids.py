#!/usr/bin/env python3
import json
import re

# زبان‌ها به ترتیب
languages = [
    "en", "es", "de", "uz", "pt", "bn", "zh", "az", "ur", "fr", 
    "tr", "id", "sw", "ru", "ar", "tg", "gu", "ks", "ha", "ul"
]

# خواندن لاگ و استخراج batch ID ها
batch_ids = []
with open('batch_manager.log', 'r', encoding='utf-8') as f:
    content = f.read()
    
    # پیدا کردن خطوط "Batch translation requests sent. Batch ID:"
    pattern = r'Batch translation requests sent\. Batch ID: (msgbatch_[a-zA-Z0-9]+)'
    matches = re.findall(pattern, content)
    
    # گرفتن batch ID های منحصر به فرد به ترتیب
    unique_batches = []
    seen = set()
    for batch_id in matches:
        if batch_id not in seen:
            unique_batches.append(batch_id)
            seen.add(batch_id)
    
    # گرفتن 20 batch ID اول
    batch_ids = unique_batches[:20]

print(f"Found {len(batch_ids)} unique batch IDs")
print("Batch IDs:", batch_ids)

# ایجاد JSON
batch_json = {}
for i, (lang, batch_id) in enumerate(zip(languages, batch_ids)):
    batch_json[lang] = batch_id
    print(f"{lang}: {batch_id}")

# ذخیره در فایل
with open('batch_ids_fixed.json', 'w', encoding='utf-8') as f:
    json.dump(batch_json, f, indent=2, ensure_ascii=False)

print(f"\nFixed JSON saved to: batch_ids_fixed.json")
print("JSON content:")
print(json.dumps(batch_json, indent=2, ensure_ascii=False)) 