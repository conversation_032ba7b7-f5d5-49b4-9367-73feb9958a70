#!/usr/bin/env python3
import json
import anthropic
from datetime import datetime
import pytz

# تنظیم API key
client = anthropic.Anthropic(
    api_key="************************************************************************************************************"
)

def format_datetime(dt):
    """تبدیل datetime به فرمت قابل خواندن"""
    if dt:
        # تبدیل به timezone محلی
        local_tz = pytz.timezone('Asia/Tehran')
        if dt.tzinfo is None:
            dt = pytz.utc.localize(dt)
        local_dt = dt.astimezone(local_tz)
        return local_dt.strftime('%Y-%m-%d %H:%M:%S')
    return "N/A"

def check_batch_status(batch_id):
    """بررسی وضعیت یک batch خاص"""
    try:
        batch = client.beta.messages.batches.retrieve(batch_id)
        
        print(f"\n{'='*60}")
        print(f"Batch ID: {batch.id}")
        print(f"{'='*60}")
        print(f"وضعیت: {batch.processing_status}")
        print(f"نوع: {batch.type}")
        print(f"تاریخ ایجاد: {format_datetime(batch.created_at)}")
        print(f"تاریخ انقضا: {format_datetime(batch.expires_at)}")
        
        if batch.ended_at:
            print(f"تاریخ پایان: {format_datetime(batch.ended_at)}")
        
        if batch.archived_at:
            print(f"تاریخ آرشیو: {format_datetime(batch.archived_at)}")
            
        if batch.cancel_initiated_at:
            print(f"تاریخ لغو: {format_datetime(batch.cancel_initiated_at)}")
        
        # آمار درخواست‌ها
        if batch.request_counts:
            counts = batch.request_counts
            print(f"\n📊 آمار درخواست‌ها:")
            print(f"  ✅ موفق: {counts.succeeded}")
            print(f"  ⏳ در حال پردازش: {counts.processing}")
            print(f"  ❌ خطا: {counts.errored}")
            print(f"  🚫 لغو شده: {counts.canceled}")
            print(f"  ⏰ منقضی شده: {counts.expired}")
            print(f"  📈 کل: {counts.succeeded + counts.processing + counts.errored + counts.canceled + counts.expired}")
        
        # محاسبه زمان باقی‌مانده
        if batch.expires_at:
            now = datetime.now(pytz.utc)
            time_left = batch.expires_at - now
            if time_left.total_seconds() > 0:
                hours = int(time_left.total_seconds() // 3600)
                minutes = int((time_left.total_seconds() % 3600) // 60)
                print(f"\n⏰ زمان باقی‌مانده: {hours} ساعت و {minutes} دقیقه")
            else:
                print(f"\n⚠️  Batch منقضی شده است!")
        
        # بررسی آماده بودن برای پردازش
        if batch.processing_status == "completed":
            print(f"\n✅ Batch آماده برای پردازش است!")
        elif batch.processing_status == "in_progress":
            print(f"\n⏳ Batch هنوز در حال پردازش است...")
        elif batch.processing_status == "failed":
            print(f"\n❌ Batch با خطا مواجه شده است!")
        elif batch.processing_status == "expired":
            print(f"\n⏰ Batch منقضی شده است!")
        elif batch.processing_status == "canceled":
            print(f"\n🚫 Batch لغو شده است!")
            
        return batch
        
    except Exception as e:
        print(f"\n❌ خطا در بررسی batch {batch_id}: {str(e)}")
        return None

def check_all_batches():
    """بررسی تمام batch های موجود در فایل JSON"""
    try:
        # خواندن فایل batch IDs
        with open('batch_ids.json', 'r', encoding='utf-8') as f:
            batch_data = json.load(f)
        
        print(f"🔍 بررسی {len(batch_data)} batch...")
        print(f"زمان بررسی: {format_datetime(datetime.now())}")
        
        completed_batches = []
        in_progress_batches = []
        failed_batches = []
        
        for lang_code, batch_id in batch_data.items():
            batch = check_batch_status(batch_id)
            if batch:
                if batch.processing_status == "completed":
                    completed_batches.append((lang_code, batch_id))
                elif batch.processing_status == "in_progress":
                    in_progress_batches.append((lang_code, batch_id))
                else:
                    failed_batches.append((lang_code, batch_id))
        
        # خلاصه
        print(f"\n{'='*60}")
        print(f"📋 خلاصه وضعیت batch ها:")
        print(f"{'='*60}")
        print(f"✅ تکمیل شده: {len(completed_batches)}")
        print(f"⏳ در حال پردازش: {len(in_progress_batches)}")
        print(f"❌ ناموفق: {len(failed_batches)}")
        
        if completed_batches:
            print(f"\n✅ Batch های آماده برای پردازش:")
            for lang, batch_id in completed_batches:
                print(f"  - {lang}: {batch_id}")
        
        if in_progress_batches:
            print(f"\n⏳ Batch های در حال پردازش:")
            for lang, batch_id in in_progress_batches:
                print(f"  - {lang}: {batch_id}")
        
        if failed_batches:
            print(f"\n❌ Batch های ناموفق:")
            for lang, batch_id in failed_batches:
                print(f"  - {lang}: {batch_id}")
                
    except FileNotFoundError:
        print("❌ فایل batch_ids.json پیدا نشد!")
    except Exception as e:
        print(f"❌ خطا در خواندن فایل: {str(e)}")

def check_specific_batch(batch_id):
    """بررسی یک batch خاص"""
    check_batch_status(batch_id)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # بررسی batch خاص
        batch_id = sys.argv[1]
        check_specific_batch(batch_id)
    else:
        # بررسی تمام batch ها
        check_all_batches() 