#!/usr/bin/env python3
import json
import anthropic
from datetime import datetime
import pytz

# تنظیم API key
client = anthropic.Anthropic(
    api_key="************************************************************************************************************"
)

def format_datetime(dt):
    """تبدیل datetime به فرمت قابل خواندن"""
    if dt:
        local_tz = pytz.timezone('Asia/Tehran')
        if dt.tzinfo is None:
            dt = pytz.utc.localize(dt)
        local_dt = dt.astimezone(local_tz)
        return local_dt.strftime('%Y-%m-%d %H:%M:%S')
    return "N/A"

def cancel_batch(batch_id):
    """لغو یک batch خاص"""
    try:
        # ابتدا وضعیت batch را بررسی کنیم
        batch = client.beta.messages.batches.retrieve(batch_id)
        
        print(f"\n{'='*60}")
        print(f"Batch ID: {batch.id}")
        print(f"{'='*60}")
        print(f"وضعیت فعلی: {batch.processing_status}")
        
        # بررسی امکان لغو
        if batch.processing_status in ["completed", "failed", "expired", "canceled"]:
            print(f"❌ این batch قابل لغو نیست (وضعیت: {batch.processing_status})")
            return False
        
        if batch.processing_status == "in_progress":
            print(f"⚠️  این batch در حال پردازش است")
            print(f"📊 آمار فعلی:")
            if batch.request_counts:
                counts = batch.request_counts
                print(f"  ✅ موفق: {counts.succeeded}")
                print(f"  ⏳ در حال پردازش: {counts.processing}")
                print(f"  ❌ خطا: {counts.errored}")
            
            # سوال از کاربر
            response = input(f"\n🤔 آیا مطمئن هستید که می‌خواهید این batch را لغو کنید؟ (y/N): ")
            if response.lower() != 'y':
                print("❌ لغو متوقف شد")
                return False
        
        # لغو batch
        print(f"🔄 در حال لغو batch...")
        canceled_batch = client.beta.messages.batches.cancel(batch_id)
        
        print(f"✅ Batch با موفقیت لغو شد!")
        print(f"وضعیت جدید: {canceled_batch.processing_status}")
        
        if canceled_batch.cancel_initiated_at:
            print(f"زمان لغو: {format_datetime(canceled_batch.cancel_initiated_at)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطا در لغو batch {batch_id}: {str(e)}")
        return False

def cancel_all_batches():
    """لغو تمام batch های موجود در فایل JSON"""
    try:
        # خواندن فایل batch IDs
        with open('batch_ids.json', 'r', encoding='utf-8') as f:
            batch_data = json.load(f)
        
        print(f"🔍 بررسی {len(batch_data)} batch برای لغو...")
        print(f"زمان: {format_datetime(datetime.now())}")
        
        canceled_count = 0
        failed_count = 0
        
        for lang_code, batch_id in batch_data.items():
            print(f"\n📝 بررسی batch برای زبان {lang_code}...")
            if cancel_batch(batch_id):
                canceled_count += 1
            else:
                failed_count += 1
        
        # خلاصه
        print(f"\n{'='*60}")
        print(f"📋 خلاصه لغو batch ها:")
        print(f"{'='*60}")
        print(f"✅ لغو شده: {canceled_count}")
        print(f"❌ ناموفق: {failed_count}")
        print(f"📊 کل: {len(batch_data)}")
        
        if canceled_count > 0:
            print(f"\n💡 پیشنهاد: بعد از لغو، batch های جدید با اندازه کوچک‌تر ایجاد کنید")
        
    except FileNotFoundError:
        print("❌ فایل batch_ids.json پیدا نشد!")
    except Exception as e:
        print(f"❌ خطا در خواندن فایل: {str(e)}")

def cancel_specific_batch(batch_id):
    """لغو یک batch خاص"""
    cancel_batch(batch_id)

def cancel_in_progress_batches():
    """فقط batch های در حال پردازش را لغو کن"""
    try:
        with open('batch_ids.json', 'r', encoding='utf-8') as f:
            batch_data = json.load(f)
        
        print(f"🔍 بررسی batch های در حال پردازش...")
        
        in_progress_batches = []
        
        # ابتدا وضعیت همه batch ها را بررسی کنیم
        for lang_code, batch_id in batch_data.items():
            try:
                batch = client.beta.messages.batches.retrieve(batch_id)
                if batch.processing_status == "in_progress":
                    in_progress_batches.append((lang_code, batch_id))
            except:
                pass
        
        if not in_progress_batches:
            print("✅ هیچ batch در حال پردازشی پیدا نشد!")
            return
        
        print(f"⏳ {len(in_progress_batches)} batch در حال پردازش پیدا شد:")
        for lang, batch_id in in_progress_batches:
            print(f"  - {lang}: {batch_id}")
        
        response = input(f"\n🤔 آیا می‌خواهید همه این batch ها را لغو کنید؟ (y/N): ")
        if response.lower() != 'y':
            print("❌ لغو متوقف شد")
            return
        
        canceled_count = 0
        for lang_code, batch_id in in_progress_batches:
            print(f"\n📝 لغو batch برای زبان {lang_code}...")
            if cancel_batch(batch_id):
                canceled_count += 1
        
        print(f"\n✅ {canceled_count} batch لغو شد")
        
    except Exception as e:
        print(f"❌ خطا: {str(e)}")

if __name__ == "__main__":
    import sys
    
    print("🚫 اسکریپت لغو Batch ها")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--in-progress":
            # فقط batch های در حال پردازش
            cancel_in_progress_batches()
        else:
            # لغو batch خاص
            batch_id = sys.argv[1]
            cancel_specific_batch(batch_id)
    else:
        # نمایش منو
        print("انتخاب کنید:")
        print("1. لغو تمام batch ها")
        print("2. لغو فقط batch های در حال پردازش")
        print("3. لغو batch خاص")
        
        choice = input("\nانتخاب شما (1-3): ")
        
        if choice == "1":
            cancel_all_batches()
        elif choice == "2":
            cancel_in_progress_batches()
        elif choice == "3":
            batch_id = input("Batch ID را وارد کنید: ")
            cancel_specific_batch(batch_id)
        else:
            print("❌ انتخاب نامعتبر") 