#!/usr/bin/env python3
import anthropic
import json
import sys
from datetime import datetime
import pytz

# تنظیم API key
client = anthropic.Anthropic(
    api_key="************************************************************************************************************"
)

def format_datetime():
    """زمان فعلی"""
    local_tz = pytz.timezone('Asia/Tehran')
    now = datetime.now(local_tz)
    return now.strftime('%Y-%m-%d %H:%M:%S')

def send_message(message, model="claude-3-5-sonnet-20241022"):
    """ارسال پیام به Anthropic"""
    try:
        print(f"\n🤖 در حال ارسال پیام...")
        
        response = client.messages.create(
            model=model,
            max_tokens=4096,
            messages=[
                {"role": "user", "content": message}
            ]
        )
        
        return response.content[0].text
        
    except Exception as e:
        return f"❌ خطا: {str(e)}"

def interactive_chat():
    """چت تعاملی"""
    print("🤖 چت با Anthropic Claude")
    print("=" * 50)
    print("💡 دستورات:")
    print("  /help - نمایش راهنما")
    print("  /model - تغییر مدل")
    print("  /clear - پاک کردن تاریخچه")
    print("  /save - ذخیره چت")
    print("  /quit یا /exit - خروج")
    print("=" * 50)
    
    conversation_history = []
    current_model = "claude-3-5-sonnet-20241022"
    
    while True:
        try:
            # دریافت پیام از کاربر
            user_input = input(f"\n👤 شما ({current_model}): ").strip()
            
            if not user_input:
                continue
            
            # بررسی دستورات
            if user_input.lower() in ['/quit', '/exit', 'quit', 'exit']:
                print("👋 خداحافظ!")
                break
                
            elif user_input.lower() == '/help':
                print("\n📖 راهنما:")
                print("  /help - نمایش این راهنما")
                print("  /model - تغییر مدل AI")
                print("  /clear - پاک کردن تاریخچه چت")
                print("  /save - ذخیره چت در فایل")
                print("  /quit یا /exit - خروج")
                print("  /status - بررسی وضعیت API")
                continue
                
            elif user_input.lower() == '/model':
                print("\n🤖 مدل‌های موجود:")
                print("  1. claude-3-5-sonnet-20241022 (پیش‌فرض)")
                print("  2. claude-3-5-haiku-20241022 (سریع)")
                print("  3. claude-3-opus-20240229 (قوی)")
                print("  4. claude-3-sonnet-20240229")
                
                choice = input("انتخاب کنید (1-4): ").strip()
                models = {
                    "1": "claude-3-5-sonnet-20241022",
                    "2": "claude-3-5-haiku-20241022", 
                    "3": "claude-3-opus-20240229",
                    "4": "claude-3-sonnet-20240229"
                }
                
                if choice in models:
                    current_model = models[choice]
                    print(f"✅ مدل تغییر کرد به: {current_model}")
                else:
                    print("❌ انتخاب نامعتبر")
                continue
                
            elif user_input.lower() == '/clear':
                conversation_history.clear()
                print("✅ تاریخچه چت پاک شد")
                continue
                
            elif user_input.lower() == '/save':
                if conversation_history:
                    filename = f"chat_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(conversation_history, f, ensure_ascii=False, indent=2)
                    print(f"✅ چت در فایل {filename} ذخیره شد")
                else:
                    print("❌ هیچ پیامی برای ذخیره وجود ندارد")
                continue
                
            elif user_input.lower() == '/status':
                print(f"\n📊 وضعیت:")
                print(f"  مدل فعلی: {current_model}")
                print(f"  تعداد پیام‌ها: {len(conversation_history)}")
                print(f"  زمان: {format_datetime()}")
                continue
            
            # ارسال پیام
            print(f"\n🔄 در حال پردازش...")
            
            # ساخت پیام با تاریخچه
            messages = []
            for msg in conversation_history[-10:]:  # آخرین 10 پیام
                messages.append(msg)
            messages.append({"role": "user", "content": user_input})
            
            response = client.messages.create(
                model=current_model,
                max_tokens=4096,
                messages=messages
            )
            
            ai_response = response.content[0].text
            
            # نمایش پاسخ
            print(f"\n🤖 Claude ({current_model}):")
            print(f"{'='*50}")
            print(ai_response)
            print(f"{'='*50}")
            
            # ذخیره در تاریخچه
            conversation_history.append({"role": "user", "content": user_input})
            conversation_history.append({"role": "assistant", "content": ai_response})
            
        except KeyboardInterrupt:
            print("\n\n👋 خداحافظ!")
            break
        except Exception as e:
            print(f"\n❌ خطا: {str(e)}")

def quick_chat(message, model="claude-3-5-sonnet-20241022"):
    """چت سریع - یک پیام"""
    print(f"🤖 ارسال پیام به {model}...")
    response = send_message(message, model)
    print(f"\n🤖 پاسخ:")
    print(f"{'='*50}")
    print(response)
    print(f"{'='*50}")

def batch_status_check():
    """بررسی وضعیت batch ها"""
    try:
        with open('batch_ids.json', 'r', encoding='utf-8') as f:
            batch_data = json.load(f)
        
        print(f"🔍 بررسی {len(batch_data)} batch...")
        
        for lang_code, batch_id in batch_data.items():
            try:
                batch = client.beta.messages.batches.retrieve(batch_id)
                status = batch.processing_status
                if batch.request_counts:
                    counts = batch.request_counts
                    print(f"  {lang_code}: {status} (✅{counts.succeeded} ⏳{counts.processing} ❌{counts.errored})")
                else:
                    print(f"  {lang_code}: {status}")
            except Exception as e:
                print(f"  {lang_code}: خطا - {str(e)}")
                
    except FileNotFoundError:
        print("❌ فایل batch_ids.json پیدا نشد!")
    except Exception as e:
        print(f"❌ خطا: {str(e)}")

if __name__ == "__main__":
    print("🤖 Anthropic Chat Tool")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--batch-status":
            # بررسی وضعیت batch ها
            batch_status_check()
        else:
            # چت سریع
            message = " ".join(sys.argv[1:])
            quick_chat(message)
    else:
        # چت تعاملی
        interactive_chat() 