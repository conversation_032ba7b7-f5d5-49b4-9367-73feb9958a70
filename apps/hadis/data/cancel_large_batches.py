#!/usr/bin/env python3
import json
import anthropic
from datetime import datetime
import pytz

# تنظیم API key
client = anthropic.Anthropic(
    api_key="************************************************************************************************************"
)

def format_datetime(dt):
    """تبدیل datetime به فرمت قابل خواندن"""
    if dt:
        local_tz = pytz.timezone('Asia/Tehran')
        if dt.tzinfo is None:
            dt = pytz.utc.localize(dt)
        local_dt = dt.astimezone(local_tz)
        return local_dt.strftime('%Y-%m-%d %H:%M:%S')
    return "N/A"

def cancel_batch(batch_id, lang_code):
    """لغو یک batch خاص"""
    try:
        # ابتدا وضعیت batch را بررسی کنیم
        batch = client.beta.messages.batches.retrieve(batch_id)
        
        print(f"\n{'='*60}")
        print(f"Batch ID: {batch.id} ({lang_code})")
        print(f"{'='*60}")
        print(f"وضعیت فعلی: {batch.processing_status}")
        
        # بررسی امکان لغو
        if batch.processing_status in ["completed", "failed", "expired", "canceled"]:
            print(f"❌ این batch قابل لغو نیست (وضعیت: {batch.processing_status})")
            return False
        
        if batch.processing_status == "in_progress":
            print(f"⚠️  این batch در حال پردازش است")
            print(f"📊 آمار فعلی:")
            if batch.request_counts:
                counts = batch.request_counts
                total_requests = counts.succeeded + counts.processing + counts.errored + counts.canceled + counts.expired
                print(f"  📈 کل درخواست‌ها: {total_requests}")
                print(f"  ✅ موفق: {counts.succeeded}")
                print(f"  ⏳ در حال پردازش: {counts.processing}")
                print(f"  ❌ خطا: {counts.errored}")
        
        # لغو batch
        print(f"🔄 در حال لغو batch...")
        canceled_batch = client.beta.messages.batches.cancel(batch_id)
        
        print(f"✅ Batch با موفقیت لغو شد!")
        print(f"وضعیت جدید: {canceled_batch.processing_status}")
        
        if canceled_batch.cancel_initiated_at:
            print(f"زمان لغو: {format_datetime(canceled_batch.cancel_initiated_at)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطا در لغو batch {batch_id}: {str(e)}")
        return False

def cancel_large_batches():
    """لغو batch هایی که بیشتر از 1000 ترجمه دارند"""
    try:
        # خواندن فایل batch IDs
        with open('batch_ids.json', 'r', encoding='utf-8') as f:
            batch_data = json.load(f)
        
        print(f"🔍 بررسی batch های بزرگ (بیشتر از 1000 ترجمه)...")
        print(f"زمان: {format_datetime(datetime.now())}")
        
        # batch های بزرگ که باید لغو شوند
        large_batches = []
        
        # ابتدا وضعیت همه batch ها را بررسی کنیم
        for lang_code, batch_id in batch_data.items():
            try:
                batch = client.beta.messages.batches.retrieve(batch_id)
                if batch.processing_status == "in_progress" and batch.request_counts:
                    total_requests = (batch.request_counts.succeeded + 
                                    batch.request_counts.processing + 
                                    batch.request_counts.errored + 
                                    batch.request_counts.canceled + 
                                    batch.request_counts.expired)
                    
                    if total_requests > 1390:
                        large_batches.append((lang_code, batch_id, total_requests))
            except:
                pass
        
        if not large_batches:
            print("✅ هیچ batch بزرگی (بیشتر از 1000 ترجمه) پیدا نشد!")
            return
        
        print(f"⚠️  {len(large_batches)} batch بزرگ پیدا شد:")
        for lang, batch_id, count in large_batches:
            print(f"  - {lang}: {batch_id} ({count} ترجمه)")
        
        print(f"\n🤔 این batch های بزرگ باعث کندی سیستم می‌شوند.")
        print(f"💡 پیشنهاد: آنها را لغو کرده و batch های کوچک‌تر (50-100 ترجمه) ایجاد کنید.")
        
        response = input(f"\n❓ آیا می‌خواهید همه این batch های بزرگ را لغو کنید؟ (y/N): ")
        if response.lower() != 'y':
            print("❌ لغو متوقف شد")
            return
        
        canceled_count = 0
        for lang_code, batch_id, count in large_batches:
            print(f"\n📝 لغو batch برای زبان {lang_code} ({count} ترجمه)...")
            if cancel_batch(batch_id, lang_code):
                canceled_count += 1
        
        print(f"\n{'='*60}")
        print(f"📋 خلاصه:")
        print(f"{'='*60}")
        print(f"✅ لغو شده: {canceled_count}")
        print(f"❌ ناموفق: {len(large_batches) - canceled_count}")
        print(f"📊 کل: {len(large_batches)}")
        
        if canceled_count > 0:
            print(f"\n💡 پیشنهاد بعدی:")
            print(f"1. منتظر بمانید تا batch های کوچک تکمیل شوند")
            print(f"2. برای زبان‌های لغو شده، batch های جدید با اندازه 50-100 ایجاد کنید")
            print(f"3. از اسکریپت batch_creator.py استفاده کنید")
        
    except Exception as e:
        print(f"❌ خطا: {str(e)}")

if __name__ == "__main__":
    print("🚫 اسکریپت لغو Batch های بزرگ")
    print("=" * 50)
    cancel_large_batches()