from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.hadis.models import Category


class Command(BaseCommand):
    help = 'Delete all children of a specific category'

    def add_arguments(self, parser):
        parser.add_argument(
            'category_id',
            type=int,
            help='ID of the parent category'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )

    def handle(self, *args, **options):
        category_id = options['category_id']
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('Running in DRY-RUN mode - no actual deletions will be performed')
            )
        
        try:
            # Get the parent category
            parent_category = Category.objects.get(id=category_id)
            self.stdout.write(f'Found parent category: {parent_category}')
            
        except Category.DoesNotExist:
            raise CommandError(f'Category with ID {category_id} does not exist')
        
        # Get all children (descendants) of the category
        children = parent_category.get_descendants()
        children_count = children.count()
        
        self.stdout.write(f'Found {children_count} children categories to delete')
        
        if children_count == 0:
            self.stdout.write(
                self.style.SUCCESS('No children found. Nothing to delete.')
            )
            return
        
        # Log the children that will be deleted
        self.stdout.write('\nChildren categories that will be deleted:')
        for child in children:
            self.stdout.write(f'  - ID: {child.id}, Name: {child}')
        
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(f'\n[DRY-RUN] Would delete {children_count} children categories')
            )
            return
        
        # Confirm deletion
        confirm = input(f'\nAre you sure you want to delete {children_count} children categories? (yes/no): ')
        if confirm.lower() != 'yes':
            self.stdout.write(
                self.style.WARNING('Deletion cancelled by user')
            )
            return
        
        # Perform the deletion
        try:
            with transaction.atomic():
                deleted_count, deleted_details = children.delete()
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully deleted {children_count} children categories'
                    )
                )
                
                # Log deletion details
                if deleted_details:
                    self.stdout.write('\nDeletion details:')
                    for model, count in deleted_details.items():
                        if count > 0:
                            self.stdout.write(f'  - {model}: {count} objects')
                            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during deletion: {e}')
            )
            raise