import json
import os
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.hadis.models import Hadis, ReferenceAddr


class Command(BaseCommand):
    help = 'Delete hadises that were created from nahj_letters.json file'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            default='apps/hadis/data/nahj_letters.json',
            help='Path to JSON file',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )

    def handle(self, *args, **options):
        file_path = options['file']
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('Running in DRY-RUN mode - no actual deletions will be performed')
            )
        
        # Check if file exists
        if not os.path.exists(file_path):
            raise CommandError(f'File {file_path} does not exist')
        
        # Load JSON data
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            raise CommandError(f'Error loading JSON file: {e}')
        
        self.stdout.write(f'Loaded data from {file_path}')
        self.stdout.write(f'Found {len(data["letters"])} letters in JSON file')
        
        # Find matching hadises
        matching_hadises = []
        total_containers = 0
        
        for letter in data['letters']:
            letter_title = letter.get('title', '')
            self.stdout.write(f'Processing letter: {letter_title}')
            
            for container in letter['containers']:
                total_containers += 1
                arabic_text = container.get('arabic-text', {}).get('main', '').strip()
                translate_text = container.get('translate-text', {}).get('main', '').strip()
                
                if not arabic_text or not translate_text:
                    continue
                
                # Find hadis with exact matching text and translation
                hadis_matches = Hadis.objects.filter(
                    text=arabic_text
                )
                
                for hadis in hadis_matches:
                    # Check if translation also matches
                    hadis_translation = hadis.get_translation('fa')
                    if hadis_translation and hadis_translation.strip() == translate_text:
                        matching_hadises.append({
                            'hadis': hadis,
                            'letter_title': letter_title,
                            'container_number': container.get('number', 'unknown')
                        })
                        self.stdout.write(f'  Found matching hadis: ID={hadis.id}, Number={hadis.number}')
        
        self.stdout.write(f'\nProcessed {total_containers} containers from {len(data["letters"])} letters')
        self.stdout.write(f'Found {len(matching_hadises)} matching hadises to delete')
        
        if len(matching_hadises) == 0:
            self.stdout.write(
                self.style.SUCCESS('No matching hadises found. Nothing to delete.')
            )
            return
        
        # Log details of hadises to be deleted
        self.stdout.write('\nHadises that will be deleted:')
        for match in matching_hadises:
            hadis = match['hadis']
            self.stdout.write(
                f'  - ID: {hadis.id}, Number: {hadis.number}, '
                f'Text: {hadis.text[:50]}..., '
                f'From: {match["letter_title"]}'
            )
        
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(f'\n[DRY-RUN] Would delete {len(matching_hadises)} hadises')
            )
            return
        
        # Confirm deletion
        confirm = input(f'\nAre you sure you want to delete {len(matching_hadises)} hadises? (yes/no): ')
        if confirm.lower() != 'yes':
            self.stdout.write(
                self.style.WARNING('Deletion cancelled by user')
            )
            return
        
        # Perform the deletion
        deleted_hadises = 0
        deleted_references = 0
        
        try:
            with transaction.atomic():
                hadis_ids = [match['hadis'].id for match in matching_hadises]
                
                # Delete related references first
                references_deleted = ReferenceAddr.objects.filter(hadis_id__in=hadis_ids).delete()
                if references_deleted[0] > 0:
                    deleted_references = references_deleted[0]
                    self.stdout.write(f'Deleted {deleted_references} related references')
                
                # Delete hadises
                for match in matching_hadises:
                    hadis = match['hadis']
                    hadis_id = hadis.id
                    hadis_number = hadis.number
                    hadis.delete()
                    deleted_hadises += 1
                    self.stdout.write(f'Deleted hadis: ID={hadis_id}, Number={hadis_number}')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during deletion: {e}')
            )
            raise
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully deleted {deleted_hadises} hadises and {deleted_references} references'
            )
        )