from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.hadis.models import Category


class Command(BaseCommand):
    help = 'Create three subcategories (خطبه‌ها, نامه‌ها, حکمت‌ها) under category ID 15801'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('Running in DRY-RUN mode - no database changes will be made')
            )
        
        # Find the parent category
        try:
            parent_category = Category.objects.get(id=15801)
            self.stdout.write(f'Found parent category: {parent_category}')
        except Category.DoesNotExist:
            raise CommandError('Parent category with ID 15801 not found')
        
        # Define the three subcategories
        subcategories = [
            {
                'name': [
                    {'language_code': 'fa', 'title': 'خطبه‌ها'},
                ]
            },
            {
                'name': [
                    {'language_code': 'fa', 'title': 'نامه‌ها'},
                ]
            },
            {
                'name': [
                    {'language_code': 'fa', 'title': 'حکمت‌ها'},

                ]
            }
        ]
        
        if dry_run:
            self.stdout.write('\n[DRY-RUN] Would create the following subcategories:')
            for i, subcat in enumerate(subcategories, 1):
                fa_title = next(item['title'] for item in subcat['name'] if item['language_code'] == 'fa')
                
                self.stdout.write(f'  {i}. {fa_title}')
                self.stdout.write(f'     Parent: {parent_category}')
                self.stdout.write(f'     Name field: {subcat["name"]}')
                self.stdout.write('')
            
            self.stdout.write(
                self.style.SUCCESS(f'[DRY-RUN] Would create {len(subcategories)} subcategories')
            )
            return
        
        # Check if subcategories already exist
        existing_categories = []
        for subcat in subcategories:
            fa_title = next(item['title'] for item in subcat['name'] if item['language_code'] == 'fa')
            existing = Category.objects.filter(
                parent=parent_category,
                name__contains=[{'language_code': 'fa', 'title': fa_title}]
            ).first()
            
            if existing:
                existing_categories.append((fa_title, existing))
        
        if existing_categories:
            self.stdout.write(
                self.style.WARNING('Some subcategories already exist:')
            )
            for title, category in existing_categories:
                self.stdout.write(f'  - {title} (ID: {category.id})')
            
            confirm = input('\nDo you want to continue and create only the missing ones? (yes/no): ')
            if confirm.lower() != 'yes':
                self.stdout.write(
                    self.style.WARNING('Operation cancelled by user')
                )
                return
        
        # Create the subcategories
        created_count = 0
        
        try:
            with transaction.atomic():
                for subcat in subcategories:
                    fa_title = next(item['title'] for item in subcat['name'] if item['language_code'] == 'fa')
                    
                    # Check if this specific subcategory already exists
                    existing = Category.objects.filter(
                        parent=parent_category,
                        name__contains=[{'language_code': 'fa', 'title': fa_title}]
                    ).first()
                    
                    if existing:
                        self.stdout.write(f'Skipping {fa_title} - already exists (ID: {existing.id})')
                        continue
                    
                    # Create the subcategory
                    category = Category.objects.create(
                        name=subcat['name'],
                        parent=parent_category
                    )
                    
                    created_count += 1
                    self.stdout.write(f'Created subcategory: {fa_title} (ID: {category.id})')
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating subcategories: {e}')
            )
            raise
        
        if created_count > 0:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created {created_count} subcategories under category {parent_category}'
                )
            )
        else:
            self.stdout.write(
                self.style.WARNING('No new subcategories were created (all already exist)')
            )