from django.core.management.base import BaseCommand, CommandError
from apps.hadis.models import Category


class Command(BaseCommand):
    help = 'Count all children (descendants) of a specific category'

    def add_arguments(self, parser):
        parser.add_argument(
            'category_id',
            type=int,
            help='ID of the parent category'
        )
        parser.add_argument(
            '--detail',
            action='store_true',
            help='Show detailed list of all children categories',
        )

    def handle(self, *args, **options):
        category_id = options['category_id']
        show_detail = options['detail']
        
        try:
            # Get the parent category
            parent_category = Category.objects.get(id=category_id)
            self.stdout.write(f'دسته‌بندی والد: {parent_category} (ID: {parent_category.id})')
            
        except Category.DoesNotExist:
            raise CommandError(f'دسته‌بندی با ایدی {category_id} وجود ندارد')
        
        # Get all children (descendants) of the category - includes all levels
        # This includes direct children, grandchildren, great-grandchildren, etc.
        children = parent_category.get_descendants()
        children_count = children.count()
        
        # Alternative method to double-check (recursive approach)
        def count_all_descendants(category):
            total = 0
            direct_children = category.get_children()
            total += direct_children.count()
            
            for child in direct_children:
                total += count_all_descendants(child)
            
            return total
        
        # Verify the count using recursive method
        recursive_count = count_all_descendants(parent_category)
        
        # Use the higher count to be safe
        children_count = max(children_count, recursive_count)
        
        self.stdout.write(
            self.style.SUCCESS(f'\n📊 تعداد کل فرزندان (همه سطوح): {children_count}')
        )
        
        # Show debug info if counts differ
        if children_count != len(children):
            self.stdout.write(
                self.style.WARNING(f'🔍 تشخیص اختلاف در شمارش - get_descendants: {len(children)}, recursive: {recursive_count}')
            )
        
        if children_count == 0:
            self.stdout.write(
                self.style.WARNING('هیچ فرزندی برای این دسته‌بندی یافت نشد.')
            )
            return
        
        # Show breakdown by level if there are children
        levels_info = {}
        for child in children:
            level = child.level - parent_category.level
            if level not in levels_info:
                levels_info[level] = []
            levels_info[level].append(child)
        
        self.stdout.write('\n📈 تفکیک بر اساس سطح:')
        for level in sorted(levels_info.keys()):
            count = len(levels_info[level])
            level_name = 'فرزند مستقیم' if level == 1 else f'سطح {level}'
            self.stdout.write(f'  - {level_name}: {count} دسته‌بندی')
        
        # Show detailed list if requested
        if show_detail:
            self.stdout.write('\n📋 لیست تفصیلی همه فرزندان:')
            
            for level in sorted(levels_info.keys()):
                level_name = 'فرزندان مستقیم' if level == 1 else f'سطح {level}'
                self.stdout.write(f'\n🔸 {level_name}:')
                
                for child in levels_info[level]:
                    indent = '  ' + '  ' * (level - 1)
                    self.stdout.write(f'{indent}- ID: {child.id}, نام: {child}')
        else:
            self.stdout.write('\n💡 برای مشاهده لیست تفصیلی از --detail استفاده کنید')
        
        # Show some statistics
        if children_count > 0:
            direct_children = parent_category.get_children().count()
            max_depth = max(levels_info.keys()) if levels_info else 0
            
            self.stdout.write(f'\n📌 خلاصه آماری:')
            self.stdout.write(f'  • فرزندان مستقیم (سطح 1): {direct_children}')
            self.stdout.write(f'  • کل فرزندان همه سطوح: {children_count}')
            self.stdout.write(f'  • عمیق‌ترین سطح درخت: {max_depth} سطح')
            
            # Show breakdown by each level
            self.stdout.write(f'\n🔢 تعداد در هر سطح:')
            for level in sorted(levels_info.keys()):
                count = len(levels_info[level])
                if level == 1:
                    self.stdout.write(f'  • سطح {level} (فرزند مستقیم): {count}')
                elif level == 2:
                    self.stdout.write(f'  • سطح {level} (نوه): {count}')
                elif level == 3:
                    self.stdout.write(f'  • سطح {level} (نتیجه): {count}')
                else:
                    self.stdout.write(f'  • سطح {level}: {count}')
            
            self.stdout.write(f'\n✅ مجموع همه سطوح: {sum(len(levels_info[level]) for level in levels_info.keys())} دسته‌بندی')