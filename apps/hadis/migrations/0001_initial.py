# Generated by Django 3.2.12 on 2022-05-25 12:58

from django.db import migrations, models
import django.db.models.deletion
import limitless_dashboard.fields.comma_sep
import limitless_dashboard.fields.summernote


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Narrates',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='name')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='hadis/photos', verbose_name='photo')),
                ('translations', models.J<PERSON>NField(verbose_name='translations')),
            ],
            options={
                'verbose_name': 'narrate',
                'verbose_name_plural': 'narrates',
            },
        ),
        migrations.CreateModel(
            name='Reference',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='title')),
                ('translations', models.J<PERSON>NField(verbose_name='translations')),
            ],
            options={
                'verbose_name': 'reference',
                'verbose_name_plural': 'references',
            },
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=119, verbose_name='title')),
                ('translations', models.JSONField(verbose_name='translations')),
            ],
            options={
                'verbose_name': 'tag',
                'verbose_name_plural': 'tags',
            },
        ),
        migrations.CreateModel(
            name='Hadis',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', limitless_dashboard.fields.summernote.SummernoteField(verbose_name='text')),
                ('translations', models.JSONField(blank=True, null=True, verbose_name='translation')),
                ('narrates_series', limitless_dashboard.fields.comma_sep.CommaSepModelField(blank=True, max_length=255, null=True, verbose_name='narrates series')),
                ('reference_addr', models.JSONField(blank=True, null=True, verbose_name='reference address')),
                ('meta_data', models.JSONField(blank=True, null=True, verbose_name='meta data')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('narrated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='hadis.narrates', verbose_name='narrated by')),
                ('reference', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='hadis.reference', verbose_name='reference')),
                ('tags', models.ManyToManyField(blank=True, to='hadis.Tag', verbose_name='tag')),
            ],
            options={
                'verbose_name': 'hadis',
                'verbose_name_plural': 'hadises',
            },
        ),
    ]
