# Generated by Django 3.2.12 on 2022-05-25 16:32

from django.db import migrations, models
import limitless_dashboard.fields.comma_sep


class Migration(migrations.Migration):

    dependencies = [
        ('hadis', '0002_auto_20220525_1327'),
    ]

    operations = [
        migrations.AddField(
            model_name='hadis',
            name='ref_doc_numb',
            field=models.CharField(blank=True, max_length=64, null=True, verbose_name='reference document number'),
        ),
        migrations.AddField(
            model_name='hadis',
            name='ref_doc_page',
            field=models.CharField(blank=True, max_length=64, null=True, verbose_name='reference document page'),
        ),
        migrations.AddField(
            model_name='hadis',
            name='ref_hadis_num',
            field=models.CharField(blank=True, max_length=64, null=True, verbose_name='reference hadis number'),
        ),
        migrations.AlterField(
            model_name='hadis',
            name='narrates_series',
            field=limitless_dashboard.fields.comma_sep.CommaSep<PERSON>odel<PERSON>ield(blank=True, help_text='separate by Comma ( , )', max_length=255, null=True, verbose_name='narrates series'),
        ),
    ]
