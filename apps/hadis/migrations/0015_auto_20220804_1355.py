# Generated by Django 3.2.13 on 2022-08-04 13:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('tag', '0001_initial'),
        ('hadis', '0014_alter_hadisadminlog_tags'),
    ]

    operations = [
        migrations.AlterField(
            model_name='hadis',
            name='tags',
            field=models.ManyToManyField(blank=True, through='hadis.HadisTag', to='tag.Tag', verbose_name='tag'),
        ),
        migrations.AlterField(
            model_name='hadisadminlog',
            name='tags',
            field=models.ManyToManyField(to='tag.Tag'),
        ),
        migrations.AlterField(
            model_name='hadistag',
            name='tag',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tag.tag', verbose_name='tag'),
        ),
        migrations.DeleteModel(
            name='Tag',
        ),
    ]
