# Generated by Django 3.2.13 on 2022-06-30 14:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('hadis', '0011_tag_level'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='tag',
            name='level',
        ),
        migrations.CreateModel(
            name='HadisTag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.PositiveSmallIntegerField(default=1, verbose_name='level')),
                ('hadis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hadis.hadis', verbose_name='hadis')),
                ('tag', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hadis.tag', verbose_name='tag')),
            ],
            options={
                'db_table': 'hadis_hadis_tags',
                'unique_together': {('tag', 'hadis', 'level')},
            },
        ),
        migrations.AlterField(
            model_name='hadis',
            name='tags',
            field=models.ManyToManyField(blank=True, through='hadis.HadisTag', to='hadis.Tag', verbose_name='tag'),
        ),
    ]
