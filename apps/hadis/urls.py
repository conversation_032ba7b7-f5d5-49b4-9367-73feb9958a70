from django.urls import path
from django.views.decorators.cache import cache_page

from apps.hadis.views.category import CategoryListView, CategoryHadisListView, CategoryHadisV2ListView
from apps.hadis.views.event import EventHadisListView
from apps.hadis.views.interest import InterestListView, UserInterestView, UserHadisListView, HadisSearchView, HadisSingleView, ResourcesListView, UserHadisListViewV2
from apps.hadis.views.narrator import NarratorListView, NarratorHadisListView 
from apps.hadis.views.stats import HadisStatsApi
from apps.hadis.views.trend import TrendListView, TrendHadisListView
from apps.hadis.views.hadis_v2 import HadisViewV2, NarratorListViewV2, HadisUserDailyListView



urlpatterns = [
    # user interests
    path('interests-items/', InterestListView.as_view()),
    path('user-interests/', UserInterestView.as_view()),
    path('user-hadises/', UserHadisListView.as_view()),
    path('stats/', HadisStatsApi.as_view()),
    path('search/<str:query>/', HadisSearchView.as_view()),
    path('<int:id>/', HadisSingleView.as_view()),

    # trends
    path('trends/', TrendListView.as_view()),
    path('trends/<str:trend>/', TrendHadisListView.as_view()),

    path('narrators/', NarratorListView.as_view()),
    path('narrators/<int:pk>/', NarratorHadisListView.as_view()),

    # categories
    path('categories/', CategoryListView.as_view()),
    path('categories/<str:category>/', CategoryHadisListView.as_view()),

    path('events/', EventHadisListView.as_view()),
    path('resources/', ResourcesListView.as_view()),
    

    # categore v2
    path('v2/categories/', CategoryHadisV2ListView.as_view()),

    # hadis v2
    path('v2/hadis/', HadisViewV2.as_view()),
    
    # hadis narrator v2 
    path('v2/narrators/', NarratorListViewV2.as_view()),

    # hadis user-hadises
    path('v2/user-hadises/', UserHadisListViewV2.as_view()),

    # hadis daily
    path('v2/user-daily/', HadisUserDailyListView.as_view()),
    
]
