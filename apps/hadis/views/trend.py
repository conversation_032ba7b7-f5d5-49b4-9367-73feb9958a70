from functools import reduce
from operator import add

from django.contrib.postgres.aggregates import <PERSON><PERSON>yAgg
from django.db.models import F, Case, When, Value, Sum
from rest_framework.exceptions import NotFound
from rest_framework.filters import SearchFilter
from rest_framework.generics import ListAPIView

from apps.hadis.models import Trend, Hadis
from apps.hadis.serializers.hadis import HadisSerializer
from apps.hadis.serializers.trend import TrendSerializer


class TrendHadisListView(ListAPIView):
    serializer_class = HadisSerializer

    def map_tags_by_level(self, tags):
        _map = {}
        for tag in tags:
            if _map.get(tag.level) is None:
                _map[tag.level] = []
            _map[tag.level].append(tag.tag_id)

        return _map

    def get_queryset(self):
        trend = Trend.objects.filter(title__contains=[{'title': self.kwargs['trend']}]).first()
        if not trend:
            raise NotFound("No trend found with title '{}'".format(self.kwargs['trend']))

        tags_map = self.map_tags_by_level(trend.trendtags_set.all())
        tags_map_query = {}
        for level, tds in tags_map.items():
            for i in range(1, 4):
                tags_map_query[f'grade_{i}_{level}'] = Case(
                    When(**{'hadistag__tag__id__in': tds, 'hadistag__level': i}, then=Value(level + i)),
                    default=Value(0))
        if self.request.LANGUAGE_CODE == 'ar':
            qs = Hadis.objects.filter(tags__in=trend.tags.all(), status=True)
        else:
            qs = Hadis.objects.filter(
                translations__contains=[{'language_code': self.request.LANGUAGE_CODE}],
                tags__in=trend.tags.all(),
                status=True,
            )

        qs = qs.annotate(
            **tags_map_query,
            total_grade=Sum(reduce(add, [F(f) for f in list(tags_map_query)]), distinct=True)
        ).prefetch_related(
            'referenceaddr_set', 'referenceaddr_set__book', 'narrated_by', 'tags', 'tags__categories'
        ).order_by('total_grade')

        return qs


class TrendListView(ListAPIView):
    serializer_class = TrendSerializer
    search_fields = ['title']
    filter_backends = [SearchFilter]

    def get_queryset(self):
        return Trend.objects.order_by('-id').filter(
            title__contains=[{'language_code': self.request.LANGUAGE_CODE}]
        ).prefetch_related('tags').annotate(
            tag_ids=ArrayAgg('tags'),
        )
