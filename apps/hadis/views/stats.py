from rest_framework.response import Response
from rest_framework.views import APIView

from apps.hadis.models import Hadis, Category
from apps.hadis.views.interest import UserHadisListView


class HadisStatsApi(APIView):
    def get(self, request, **kwargs):
        user_hadises = UserHadisListView(request=request).get_queryset().count()
        return Response({
            'subject': Category.objects.count(),
            'all': Hadis.objects.filter(status=True).count(),
            'for_you': user_hadises,
        })
