from functools import reduce
from operator import add

from django.db import models
from django.db.models import Q, Case, When, Value, F, Sum
from pydantic import generics
from rest_framework.exceptions import NotFound
from rest_framework.generics import ListAPIView, RetrieveUpdateAPIView
from rest_framework.permissions import IsAuthenticated
from django.utils.decorators import method_decorator
from django.core.cache import cache
from django.views.decorators.cache import cache_page
from rest_framework.response import Response

from apps.account.models import User
from apps.account.models.interest import Interest, UserInterest
from apps.hadis.models import Hadis, Reference
from apps.hadis.serializers.hadis import HadisSerializer, ResourcesSerializer
from apps.hadis.serializers.interest import InterestSerializer, UserInterestSerializer
from rest_framework.exceptions import PermissionDenied
from rest_framework import generics
from utils.pageless import ThirtyPerPagePagination

def get_user_interests(user):
    query = Q(
        translations__contains=[{'language_code': user.language.code}]
    )
    user_options = UserInterest.objects.filter(user=user).first()
    if not user_options:
        raise NotFound("No data available for user %s, first of all complete user data" % user, )

    if gender := user_options.gender:
        f = {f'meta_data__gender__{gender}': True}
        query &= Q(meta_data__gender__for_all=True) | Q(**f)

    if age := user_options.age:
        f = {}
        if age >= 40:
            f = {'meta_data__age__from_40': True}
        elif age <= 13:
            f = {'meta_data__age__to_13': True}
        elif 28 < age < 40:
            f = {'meta_data__age__from_28_40': True}
        elif 18 < age < 28:
            f = {'meta_data__age__from_18_28': True}
        elif 13 < age < 18:
            f = {'meta_data__age__from_13_18': True}

        query &= Q(meta_data__age__for_all=True) | Q(**f)

    if ms := user_options.marital_status:
        if ms:
            f = {
                'meta_data__married_with_kids': user_options.has_children,
                'meta_data__married_no_kids': True,
            }
        else:
            f = {
                'meta_data__single_not_to_marry': True,
                'meta_data__single_to_marry': True,
            }
        query &= Q(meta_data__marital__for_all=True) | Q(**f)

    if interests := user_options.interests.all():
        tags = list(interests.values_list('tags__id', flat=True))
        query &= Q(tags__id__in=tags)

    return Hadis.objects.filter(query, status=True).prefetch_related(
        'referenceaddr_set', 'referenceaddr_set__book', 'category', 'narrated_by',
    ).all()


def get_user_interests2(user):
    grades_table = {
        'gender_grade': 1,
        'age_grade': 1,
        'martial_grade': 2,

        'children_grade': 2,
        'tend_to_marry_grade': 2,

        'interest_grade': 4,
    }
    user_options = UserInterest.objects.filter(user=user).first()
    if not user_options:
        raise NotFound("No data available for user %s, first of all complete user data" % user, )

    grade_dict_query = {}

    if gender := user_options.gender:
        grade_dict_query['gender_grade'] = Case(
            When(
                Q(**{f'meta_data__gender__{gender}': True}),
                then=Value(grades_table['gender_grade']),
            ), output_field=models.IntegerField(), default=Value(0),
        )

    age_map = {
        0: 'meta_data__age__to_13',
        1: 'meta_data__age__from_13_18',
        2: 'meta_data__age__from_18_28',
        3: 'meta_data__age__from_28_40',
        4: 'meta_data__age__from_40',
    }

    grade_dict_query['age_grade'] = Case(
        When(Q(**{age_map.get(user_options.age, 'meta_data__age__from_28_40'): True}),
             then=Value(grades_table['age_grade'])),
        output_field=models.IntegerField(),
        default=Value(0),
    )

    mq = Q()
    if user_options.marital_status:
        if user_options.has_children:
            grade_dict_query['children_grade'] = Case(
                When(meta_data__marital__married_with_kids=True, then=Value(grades_table['children_grade']), ),
                default=Value(0), output_field=models.IntegerField(),
            )
            mq &= Q(meta_data__marital__married_with_kids=True)
        else:
            mq &= Q(meta_data__marital__married_no_kids=True)
    else:
        if user_options.tend_to_marry:
            grade_dict_query['tend_to_marry_grade'] = Case(
                When(meta_data__marital__single_to_marry=True, then=Value(grades_table['tend_to_marry_grade']), ),
                default=Value(0), output_field=models.IntegerField(),
            )
            mq &= Q(meta_data__marital__single_to_marry=True)
        else:
            mq &= Q(meta_data__marital__single_not_to_marry=True)

    grade_dict_query['martial_grade'] = Case(
        When(mq, then=Value(grades_table['martial_grade'])),
        output_field=models.IntegerField(), default=Value(0),
    )

    if interests := user_options.interests.all():
        tags = list(interests.values_list('tags__id', flat=True))
        grade_dict_query['interests_grade'] = Case(
            When(tags__id__in=tags, then=Value(grades_table['interest_grade'])), output_field=models.IntegerField(),
            default=Value(0),
        )

    qs = Hadis.objects.filter(status=True)
    if user and user.language and user.language.code == 'de':
        qs = qs.filter(
            Q(translations__contains=[{'language_code': 'en'}]) | Q(translations__contains=[{'language_code': 'de'}])
        )

    elif user and user.language and user.language.code != 'ar':
        qs = qs.filter(
            translations__contains=[{'language_code': user.language.code}]
        )

    return qs.annotate(**grade_dict_query).annotate(
        total_grade=Sum(reduce(add, [F(f) for f in list(grade_dict_query)]), distinct=True)
    ).order_by('-total_grade', '?').prefetch_related(
        'referenceaddr_set', 'referenceaddr_set__book', 'category', 'narrated_by',
    ).filter(total_grade__gte=1)


class InterestListView(ListAPIView):
    serializer_class = InterestSerializer
    pagination_class = None
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        return Interest.objects.all()

class HadisSingleView(generics.RetrieveAPIView):
    queryset = Hadis.objects.all()
    serializer_class = HadisSerializer
    lookup_field = 'id'

class UserInterestView(RetrieveUpdateAPIView):
    serializer_class = UserInterestSerializer
    permission_classes = (IsAuthenticated,)

    def perform_update(self, serializer):
        interests = serializer.validated_data.pop('interests_ids')
        obj, _ = UserInterest.objects.update_or_create(
            user=self.request.user,
            defaults=serializer.validated_data
        )
        obj.interests.set(interests)

        cache_key = f'hadis_user_{self.request.user.id}_list_ids_{self.request.LANGUAGE_CODE}'
        cache.delete(cache_key)

        return obj

    def get_object(self):
        return UserInterest.objects.filter(user=self.request.user).first()


class UserHadisListView(ListAPIView):
    serializer_class = HadisSerializer
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        if self.request.user.is_authenticated:
            return get_user_interests2(self.request.user)
        else:
            raise PermissionDenied("Token Is invalid")


class UserHadisListViewV2(ListAPIView):
    serializer_class = None
    permission_classes = (IsAuthenticated,)


    def get_queryset(self):
        if self.request.user.is_authenticated:
            return get_user_interests2(self.request.user).only('number')
        else:
            raise PermissionDenied("Token Is invalid")
    def get(self, request, *args, **kwargs):
        cache_key = f'hadis_user_{request.user.id}_list_ids_{request.LANGUAGE_CODE}'
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data)

        queryset = self.get_queryset()
        hadis_ids = list(queryset.values_list('number', flat=True))
        
        # this cache will be cleared automatically if the version of this service is increased or updated
        cache.set(cache_key, hadis_ids, timeout=60 * 60 * 3)

        return Response(hadis_ids)


class HadisSearchView(ListAPIView):
    serializer_class = HadisSerializer
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        search = self.kwargs['query']
        if search.isnumeric():
            q = Q(number=int(search))
        else:
            q = Q(text__unaccent__contains=search) | Q(translations__icontains=search) | Q(
                category__name__icontains=search)

        return Hadis.objects.filter(q, status=True).prefetch_related(
            'referenceaddr_set', 'referenceaddr_set__book', 'category', 'narrated_by',
        ).all()


class ResourcesListView(ListAPIView):
    serializer_class = ResourcesSerializer
    pagination_class = ThirtyPerPagePagination
    
    def get_queryset(self):
        """
        Return references ordered by priority:
        1. Those with thumbnails first
        2. Those with descriptions second
        3. Then by title alphabetically
        """
        return Reference.objects.annotate(
            has_thumb=Case(
                When(thumbnail__isnull=False, then=Value(1)),
                default=Value(0),
                output_field=models.IntegerField()
            ),
            has_desc=Case(
                When(
                    ~Q(description__exact={}) &
                    ~Q(description__exact=[]) &
                    Q(description__isnull=False),
                    then=Value(1)
                ),
                default=Value(0),
                output_field=models.IntegerField()
            )
        ).order_by('-has_thumb', '-has_desc', 'title')

