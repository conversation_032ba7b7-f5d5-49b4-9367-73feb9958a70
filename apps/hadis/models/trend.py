from django.db import models
from django.utils.translation import gettext_lazy as _
from filer.fields.image import Filer<PERSON>mageField

from apps.account.models import User


class TrendTags(models.Model):
    tag = models.ForeignKey("tag.Tag", on_delete=models.CASCADE, verbose_name=_('tag'))
    trend = models.ForeignKey("hadis.Trend", on_delete=models.CASCADE, )
    level = models.PositiveSmallIntegerField(default=1, verbose_name=_('level'))

    class Meta:
        unique_together = ('tag', 'trend', 'level')
        db_table = 'hadis_trend_tags'


class Trend(models.Model):
    title = models.JSONField(verbose_name=_('title'), default=dict)
    thumbnail = FilerImageField(
        related_name='+', on_delete=models.PROTECT, null=True, blank=True,
        verbose_name=_('thumbnail')
    )
    tags = models.ManyToManyField("tag.Tag", related_name='+', blank=True, through=TrendTags)
    # dates = models.JSO<PERSON>ield(verbose_name=_('dates'), default=dict)
    events = models.ManyToManyField("najm_calendar.CalendarOccasions", related_name='related_trends', blank=True)

    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('updated at'))
    updated_by = models.ForeignKey(
        User, related_name='+', on_delete=models.SET_NULL, verbose_name=_('updated by'), null=True, editable=False
    )
    created_by = models.ForeignKey(
        User, related_name='+', on_delete=models.SET_NULL, verbose_name=_('created by'), null=True, editable=False
    )

    def __str__(self):
        for lang in ['fa', 'en', 'ar']:
            for tr in self.title:
                if tr['language_code'] == lang:
                    return tr['title']

        return self.title[0]['title']
