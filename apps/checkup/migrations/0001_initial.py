# Generated by Django 3.2.25 on 2025-07-26 15:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Checkup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.JSONField(default=dict, verbose_name='عنوان')),
                ('description', models.JSONField(default=dict, verbose_name='توضیحات')),
                ('is_active', models.BooleanField(default=True, verbose_name='فعال')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاریخ ایجاد')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاریخ بروزرسانی')),
            ],
            options={
                'verbose_name': 'Checkup',
                'verbose_name_plural': 'Checkups',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CheckupParameter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.JSONField(default=dict, verbose_name='عنوان سوال')),
                ('description', models.JSONField(default=dict, verbose_name='توضیحات')),
                ('hadith_text', models.TextField(blank=True, null=True, verbose_name='متن حدیث')),
                ('hadith_translation', models.JSONField(default=dict, verbose_name='ترجمه حدیث')),
                ('hadith_source', models.CharField(blank=True, max_length=655, null=True, verbose_name='منبع حدیث')),
                ('parameter_type', models.CharField(choices=[('positive', 'مثبت'), ('negative', 'منفی')], default='positive', max_length=10, verbose_name='نوع پارامتر')),
                ('weight', models.IntegerField(default=1, verbose_name='وزن/اهمیت')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='ترتیب نمایش')),
                ('is_emergency', models.BooleanField(default=False, help_text='آیا این پارامتر در صورت انتخاب گزینه\u200cهای منفی، جزو توصیه\u200cهای اضطراری قرار گیرد؟', verbose_name='اضطراری')),
                ('is_active', models.BooleanField(default=True, verbose_name='فعال')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاریخ ایجاد')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاریخ بروزرسانی')),
                ('checkup', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parameters', to='checkup.checkup', verbose_name='چک\u200cآپ')),
            ],
            options={
                'verbose_name': 'Checkup Parameter',
                'verbose_name_plural': 'Checkup Parameters',
                'ordering': ['checkup', 'order', 'id'],
            },
        ),
    ]
