from django.contrib import admin
from django import forms
from django.utils.translation import gettext_lazy as _
from ajaxdatatable.admin import AjaxDatatable
from utils.json_editor_field import JsonEditorWidget
from utils import get_translation_schema, get_description_translation_schema
from apps.checkup.models import Checkup, CheckupParameter


class CheckupForm(forms.ModelForm):
    class Meta:
        model = Checkup
        fields = '__all__'
        widgets = {
            'title': JsonEditorWidget(attrs={'schema': get_translation_schema}),
            'description': JsonEditorWidget(attrs={'schema': get_description_translation_schema}),
        }


class CheckupParameterForm(forms.ModelForm):
    class Meta:
        model = CheckupParameter
        fields = '__all__'
        widgets = {
            'title': JsonEditorWidget(attrs={'schema': get_translation_schema}),
            'description': JsonEditorWidget(attrs={'schema': get_description_translation_schema}),
            'hadith_translation': JsonEditorWidget(attrs={'schema': get_description_translation_schema}),
        }


class CheckupParameterInline(admin.TabularInline):
    model = CheckupParameter
    form = CheckupParameterForm
    extra = 0
    fields = (
        'title', 'description', 'parameter_type', 'weight', 'order',
        'is_emergency', 'is_active'
    )
    readonly_fields = ()


@admin.register(Checkup)
class CheckupAdmin(AjaxDatatable):
    form = CheckupForm
    list_display = ('get_title_display', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('title',)
    ordering = ('-created_at',)

    fields = ('title', 'description', 'is_active')

    inlines = [CheckupParameterInline]

    def get_title_display(self, obj):
        return str(obj)
    get_title_display.short_description = 'عنوان'
    get_title_display.admin_order_field = 'title'


@admin.register(CheckupParameter)
class CheckupParameterAdmin(AjaxDatatable):
    form = CheckupParameterForm
    list_display = ('get_title_display', 'checkup', 'parameter_type', 'weight', 'order', 'is_emergency', 'is_active')
    list_filter = ('parameter_type', 'is_emergency', 'is_active', 'checkup')
    search_fields = ('title', 'checkup__title')
    ordering = ('checkup', 'order', 'id')
    autocomplete_fields = ('checkup',)

    fields = (
        'checkup', 'title', 'description', 'hadith_text', 'hadith_translation',
        'hadith_source', 'parameter_type', 'weight', 'order', 'is_emergency', 'is_active'
    )

    def get_title_display(self, obj):
        return str(obj)
    get_title_display.short_description = 'عنوان'
    get_title_display.admin_order_field = 'title'

