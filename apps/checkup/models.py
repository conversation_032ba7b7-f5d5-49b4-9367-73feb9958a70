from django.db import models
from django.utils.translation import gettext_lazy as _


class Checkup(models.Model):
    """
    مدل تعریف چک‌آپ - مثل چک‌آپ رزق و روزی، چک‌آپ ازدواج، چک‌آپ طبی و غیره
    """
    title = models.JSONField(default=dict, verbose_name=_('عنوان'))
    description = models.JSONField(verbose_name=_('توضیحات'), default=dict)
    is_active = models.BooleanField(default=True, verbose_name=_('فعال'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('تاریخ ایجاد'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('تاریخ بروزرسانی'))

    class Meta:
        verbose_name = _('Checkup')
        verbose_name_plural = _('Checkups')
        ordering = ['-created_at']

    def __str__(self):
        title = self.get_field_translation(lang='fa', checkup_field=self.title)
        return title if title else f"Checkup #{self.id}"


    def get_field_translation(self, lang, checkup_field):
        try:
            for tr in checkup_field:
                if tr['language_code'] == lang:
                    return tr['title']                       
            return checkup_field[0]['title']
        except Exception as exp:
            print(f'---> {exp}')
            return None



class CheckupParameter(models.Model):
    """
    مدل پارامترها/شاخص‌های چک‌آپ - سوالات و گزینه‌های هر چک‌آپ
    """
    SCORE_CHOICES = [
        (0, _('هیچ وقت')),
        (25, _('گاهی اوقات')),
        (50, _('به طور مساوی')),
        (75, _('بیشتر اوقات')),
        (100, _('همیشه')),
    ]

    TYPE_CHOICES = [
        ('positive', _('مثبت')),
        ('negative', _('منفی')),
    ]

    checkup = models.ForeignKey(
        Checkup,
        on_delete=models.CASCADE,
        related_name='parameters',
        verbose_name=_('چک‌آپ')
    )
    title = models.JSONField(verbose_name=_('عنوان سوال'), default=dict)
    description = models.JSONField(verbose_name=_('توضیحات'), default=dict)
    hadith_text = models.TextField(verbose_name=_('متن حدیث'), blank=True, null=True)
    hadith_translation = models.JSONField(verbose_name=_('ترجمه حدیث'), default=dict)
    hadith_source = models.CharField(max_length=655, verbose_name=_('منبع حدیث'), blank=True, null=True)
    parameter_type = models.CharField(
        max_length=10,
        choices=TYPE_CHOICES,
        default='positive',
        verbose_name=_('نوع پارامتر')
    )
    weight = models.IntegerField(default=1, verbose_name=_('وزن/اهمیت'))
    order = models.PositiveIntegerField(default=0, verbose_name=_('ترتیب نمایش'))
    is_emergency = models.BooleanField(
        default=False,
        verbose_name=_('اضطراری'),
        help_text=_('آیا این پارامتر در صورت انتخاب گزینه‌های منفی، جزو توصیه‌های اضطراری قرار گیرد؟')
    )
    is_active = models.BooleanField(default=True, verbose_name=_('فعال'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('تاریخ ایجاد'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('تاریخ بروزرسانی'))

    class Meta:
        verbose_name = _('Checkup Parameter')
        verbose_name_plural = _('Checkup Parameters')
        ordering = ['checkup', 'order', 'id']

    def __str__(self):
        title = self.get_field_translation(lang='fa', checkup_field=self.title)
        return title if title else f"Parameter #{self.id}"


    def get_field_translation(self, lang, checkup_field):
        try:
            for tr in checkup_field:
                if tr['language_code'] == lang:
                    return tr['title']                       
            return checkup_field[0]['title']
        except Exception as exp:
            print(f'---> {exp}')
            return None