
from django.db import models
from django.db.models import Sum

from django.utils.translation import gettext as _
from apps.account.models import User
from limitless_dashboard.fields.comma_sep import CommaSepModelField
from config.settings import base as settings





class Transaction(models.Model):
    class TransactionType(models.TextChoices):
        INCOME = 'income', _('Income')
        WITHDRAWAL = 'withdrawal', _('Withdrawal')
        WITHDRAWAL_IN_APP = 'withdrawal_in_app', _('Withdrawal In App')
        
    class ServiceType(models.TextChoices):
        HUSSAINIYA = 'hussainiya', _('Hussainiya')
        LIBRARY = 'library', _('Library')
        TALK = 'talk', _('Talk')
        MEET = 'meet', _('Meet')


    wallet = models.ForeignKey('wallet.Wallet', on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=55, choices=TransactionType.choices)
    # amount = models.DecimalField(max_digits=10, decimal_places=2)
    coin_count = models.PositiveIntegerField()
    description = models.CharField(max_length=255, null=True, blank=True)
    service = models.CharField(max_length=50, choices=ServiceType.choices, null=True, blank=True)
    fee = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    purpose = models.CharField(max_length=255, null=True, blank=True)  

    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Transaction {self.id} for wallet {self.wallet.user}" 
    
    class Meta:
        verbose_name = "Transaction"
        verbose_name_plural = "Transactions"


    @classmethod
    def create_transaction(cls, wallet, service, coin_count, description, transaction_type, fee, purpose: str = None):
        return cls.objects.create(
            wallet=wallet,
            coin_count=coin_count,
            transaction_type=transaction_type,
            description=description,
            service=service,
            purpose=purpose,
            fee=fee
        )
        
    @property
    def fee_percentage(self):
        if self.transaction_type == self.TransactionType.WITHDRAWAL and self.coin_count > 0:
            return (self.fee / self.wallet.convert_coins_to_dollars(self.coin_count)) * 100
        return 0
    
    @property    
    def convert_coin_to_amount(self):
        """
        Convert the coin count of the transaction to its dollar value using the wallet's conversion rate.

        """
        return self.wallet.convert_coins_to_dollars(self.coin_count)