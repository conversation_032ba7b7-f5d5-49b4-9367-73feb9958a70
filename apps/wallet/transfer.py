from abc import ABC, abstractmethod
from apps.wallet.models import Wallet, Transaction



class CoinTransferService(ABC):
    @abstractmethod
    def transfer(self, amount, purchase_object, user):
        pass
    
    
class ConsultationCoinTransferService(CoinTransferService):
    def transfer(self, amount, purchase_object, user):
        provider = purchase_object.get_user()
        if provider is None:
            return False

        wallet = Wallet.get_or_create_user_wallet(provider.id)
        if wallet is None:
            return False

        wallet.add_coins(amount, "Coins added for user consultation", Transaction.ServiceType.TALK, user)
        return True


class MeetingCoinTransferService(CoinTransferService):
    def transfer(self, amount, purchase_object, user):
        provider = purchase_object.provider
        if provider is None:
            return False

        wallet = Wallet.get_or_create_user_wallet(provider.id)
        if wallet is None:
            return False

        wallet.add_coins(amount, "Coins added for meeting", Transaction.ServiceType.MEET, user)
        return True