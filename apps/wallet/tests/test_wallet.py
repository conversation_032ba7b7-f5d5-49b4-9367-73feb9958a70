from django.test import TestCase
from django.contrib.auth import get_user_model
from apps.wallet.models import Wallet, Transaction, WithdrawalRequest


User = get_user_model()



class WalletModelTest(TestCase):
    def setUp(self):
        # Create a user for testing
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword',
            email='<EMAIL>'
        )
        self.wallet = Wallet.get_or_create_user_wallet(self.user.id)
        
    def test_get_or_create_user_wallet_existing(self):
        # wallet = Wallet.get_or_create_user_wallet(self.user.id)
        
        self.assertIsNotNone(self.wallet)
        self.assertEqual(self.wallet.user, self.user)
        
    def test_balance_coin_count_initially_zero(self):
        # Create a wallet for the user
        # wallet = Wallet.get_or_create_user_wallet(self.user.id)
        

        self.assertIsNotNone(self.wallet)
        self.assertEqual(self.wallet.balance_coin_count(), 0)  

    def test_add_coins(self):
        initial_balance = self.wallet.balance_coin_count()  # Should be 0 initially
        self.assertEqual(initial_balance, 0)

        # Add 100 coins to the wallet
        self.wallet.add_coins(100, description='Initial deposit', service='hussainiya')

        # Check if the balance is now 100
        new_balance = self.wallet.balance_coin_count()
        self.assertEqual(new_balance, 100)

       # Also check if a transaction was created
        self.assertEqual(self.wallet.transactions.count(), 1)
        transaction = self.wallet.transactions.first()
        self.assertEqual(transaction.coin_count, 100)
        self.assertEqual(transaction.transaction_type, Transaction.TransactionType.INCOME)
        self.assertEqual(transaction.description, 'Initial deposit')

    def test_balance_amount_conversion(self):
        # Add 100 coins to the wallet
        self.wallet.add_coins(100, description='Initial deposit', service='hussainiya')

        dollar_amount = self.wallet.balance_amount()

        self.assertEqual(dollar_amount, 100 * 0.5)  # 100 coins * 2 = 200 dollars



    def test_add_and_withdraw_inapp_coins(self):
        # Add 100 coins to the wallet
        self.wallet.add_coins(100, description='Initial deposit', service='hussainiya')
        self.assertEqual(self.wallet.balance_coin_count(), 100)  # Check initial balance

        self.wallet.process_transaction(
            user_id=self.user.id,
            service='hussainiya',
            coin_count=20,
            description='Withdraw 20 coins',
            transaction_type=Transaction.TransactionType.WITHDRAWAL_IN_APP,
            fee=0
        )

        self.assertEqual(self.wallet.balance_coin_count(), 80)  # Should be 80 after withdrawal
        total_dollar_after_addition = self.wallet.balance_amount()

    def test_add_and_withdraw_request_coins(self):
        # Add 100 coins to the wallet
        self.wallet.add_coins(100, description='Initial deposit', service='hussainiya')
        self.assertEqual(self.wallet.balance_coin_count(), 100)  # Check initial balance

        self.wallet.process_transaction(
            user_id=self.user.id,
            service='hussainiya',
            coin_count=20,
            description='Withdraw 20 coins',
            transaction_type=Transaction.TransactionType.WITHDRAWAL,
            fee=0
        )

        self.assertEqual(self.wallet.balance_coin_count(), 80)  # Should be 80 after withdrawal
        total_dollar_after_addition = self.wallet.balance_amount()



    def test_create_withdrawal_request(self):
        self.wallet.add_coins(100, description="Initial Deposit")
        balance_before_withdrawal = self.wallet.balance_coin_count()

        self.assertEqual(balance_before_withdrawal, 100, "The wallet balance should be 100 coins.")


        
        transaction = self.wallet.process_transaction(
            user_id=self.user.id,
            coin_count=100,
            service=None,
            description='Withdraw 100 coins',
            transaction_type=Transaction.TransactionType.WITHDRAWAL,
            fee=self.wallet.calculate_fee  
        )
        self.assertIsNotNone(transaction)
        self.assertEqual(transaction.coin_count, 100)
        self.assertEqual(transaction.transaction_type, Transaction.TransactionType.WITHDRAWAL)


        withdrawal_request = WithdrawalRequest.create_withdrawal_request(
            self.wallet,
            transaction,
            100,
            "withdeawal 100 habib count equal 50 usd",
        )        
        self.assertIsNotNone(withdrawal_request, "Withdrawal request should not be None.")
        self.assertEqual(withdrawal_request.status, WithdrawalRequest.RequestStatus.PENDING, "The withdrawal request status should be PENDING.")

        count_dollars= self.wallet.convert_coins_to_dollars(coin_count=100)
        self.assertEqual(withdrawal_request.amount, count_dollars, "The amount in the withdrawal request should be 5 USD based on the fee.")
        self.assertEqual(withdrawal_request.coin_count, 100, "The coin count in the withdrawal request should be 100.")

        self.assertEqual(self.wallet.get_reserved_coin_count, 100,)
        
        withdrawal_request.approve_withdrawal_request()
