

from django.core.management.base import BaseCommand, CommandError
from apps.account.models import User
from apps.wallet.models import Wallet, WithdrawalRequest, Transaction


class Command(BaseCommand):
    help = 'Performs wallet actions for a specified user'


    def add_arguments(self, parser):
        parser.add_argument('user_id', type=int, help="ID of the user for the wallet action")
        parser.add_argument('action', type=str, choices=['add', 'withdraw', 'balance'], help="Action to perform: add, withdraw, or balance")
        parser.add_argument('--coins', type=int, help="Number of coins to add or withdraw (only required for add and withdraw actions)")
        parser.add_argument('--description', type=str, help="")

    def handle(self, *args, **options):
        user_id = options['user_id']
        action = options['action']
        coin_count = options.get('coins', 10)
        description = options.get('description', "Management command add coins test")

        try:
            user = User.objects.get(id=user_id)
            wallet = Wallet.get_or_create_user_wallet(user.id)
            if not wallet:
                self.stdout.write(self.style.ERROR("User does not have a wallet. (user not provider)"))
                return
        except User.DoesNotExist:
            raise CommandError("User with the specified ID does not exist.")
        
        # Perform the specified action
        if action == 'balance':
            balance = wallet.balance_coin_count()
            self.stdout.write(self.style.SUCCESS(f"User {user_id} wallet balance: {balance} coins"))
        
        elif action == 'add':
            wallet.add_coins(coin_count, description=description)
            self.stdout.write(self.style.SUCCESS(f"Added {coin_count} coins to user {user_id}'s wallet."))
            
        elif action == 'withdraw-request':
            try:
                
                request = WithdrawalRequest.create_withdrawal_request(
                    coin_count=coin_count,
                    description=description,
                    wallet=wallet, 
                )
                    
                # wallet.withdraw_coins(coin_count, service=None, description=description)
                self.stdout.write(self.style.SUCCESS(f"Withdrew {coin_count} coins from user {user_id}'s wallet."))
            except ValueError as e:
                self.stdout.write(self.style.ERROR(f"Error: {e}"))
        elif action == 'withdraw-approve':
            try:
                
                WithdrawalRequest.approve_withdrawal_request(
                    coin_count=coin_count,
                    description=description,
                    wallet=wallet, 
                )
                    
                # wallet.withdraw_coins(coin_count, service=None, description=description)
                self.stdout.write(self.style.SUCCESS(f"Withdrew {coin_count} coins from user {user_id}'s wallet."))
            except ValueError as e:
                self.stdout.write(self.style.ERROR(f"Error: {e}"))
