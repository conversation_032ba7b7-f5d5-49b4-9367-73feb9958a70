from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin

from apps.subscription.models import Subscription, UsedCredits


class UsedCreditAdmin(admin.TabularInline):
    model = UsedCredits
    extra = 1
    readonly_fields = ('at_time',)


@admin.register(Subscription)
class SubscriptionAdmin(AjaxDatatable):
    list_display = ('user', 'plan', 'activated_at', 'expire_at',)
    inlines = [UsedCreditAdmin]
    autocomplete_fields = 'user',

    @admin.display(description='Title', ordering='title')
    def _title(self, obj):
        return str(obj)
