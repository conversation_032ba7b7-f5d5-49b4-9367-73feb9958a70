from dj_language.models import Language
from django.db import models

from apps.subscription.keyval_field import JsonKeyValueField

SERVICES_FIELD_SCHEMA = {
    "type": "array",
    "format": "table",
    "title": " ",
    "uniqueItems": True,
    "items": {
        "type": "object",
        "title": "Service",
        "properties": {
            "service": {
                "title": "Service",
                "type": "string",
                "enum": [
                    "habibtalk",
                    "library",
                ],
            },
            "credit": {
                "type": "integer",
                "title": "Credit",
                "minimum": 1,
                "maximum": 99999,
            },
        }
    }
}


def get_language_field_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str('Title'),
            'properties': {
                'title': {'type': 'string', 'title': 'Title'},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': "Language Code"
                }
            }
        }
    }


class Plan(models.Model):
    class Services(models.TextChoices):
        talk = "habibtalk", "Talk"
        library = "library", "Library"

    title = JsonKeyValueField(key_index='language_code', value_index='title', schema=get_language_field_schema)
    price = models.DecimalField(max_digits=7, decimal_places=2)
    duration = models.PositiveIntegerField(default=1, help_text='Duration in days')
    services = JsonKeyValueField(key_index='service', value_index='credit', schema=SERVICES_FIELD_SCHEMA)

    def __str__(self):
        for i in ['fa', 'en', 'ar']:
            if tr := self.get_translation(i):
                return tr

        return self.title[next(iter(self.title))]

    def get_translation(self, lang):
        return self.title.get(lang)

    class Meta:
        verbose_name = 'plan'
        verbose_name_plural = 'plans'
        ordering = '-id',
