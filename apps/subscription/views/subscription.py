from rest_framework import generics

from apps.subscription.models import Plan, Subscription
from apps.subscription.serializers.subscription import SubscribeSerializer


class SubscribeView(generics.CreateAPIView):
    serializer_class = SubscribeSerializer

    def get_queryset(self):
        return Plan.objects.all()


class SubscriptionHistory(generics.ListAPIView):
    serializer_class = SubscribeSerializer

    def get_queryset(self):
        return Subscription.objects.filter(
            user=self.request.user,
        ).order_by('-id').prefetch_related('used_credits')
