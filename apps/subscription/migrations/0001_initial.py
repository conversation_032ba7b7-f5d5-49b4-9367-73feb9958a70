# Generated by Django 3.2.22 on 2023-11-30 15:17

import apps.subscription.keyval_field
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Plan',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', apps.subscription.keyval_field.JsonKeyValueField(default=dict)),
                ('price', models.DecimalField(decimal_places=2, max_digits=7)),
                ('duration', models.PositiveIntegerField(default=1, help_text='Duration in days')),
                ('services', apps.subscription.keyval_field.JsonKeyValueField(default=dict)),
            ],
            options={
                'verbose_name': 'plan',
                'verbose_name_plural': 'plans',
                'ordering': ('-id',),
            },
        ),
    ]
