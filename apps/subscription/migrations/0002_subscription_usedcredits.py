# Generated by Django 3.2.22 on 2023-11-30 15:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('subscription', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activated_at', models.DateTimeField(auto_now_add=True)),
                ('expire_at', models.DateTimeField()),
                ('plan', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='users', to='subscription.plan')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'subscription',
                'verbose_name_plural': 'subscriptions',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='UsedCredits',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('at_time', models.DateTimeField(auto_now_add=True)),
                ('service', models.CharField(choices=[('habibtalk', 'Talk'), ('library', 'Library')], max_length=16)),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='used_credits', to='subscription.subscription')),
            ],
            options={
                'verbose_name': 'used credit',
                'verbose_name_plural': 'used credits',
                'ordering': ('-id',),
            },
        ),
    ]
