import os
import json
import time
import argparse
from django.db import models
from utils.telegram_logger import telegram_logger
import anthropic
from django.db.models import Q

# Set up environment for Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
from apps.najm_calendar.models import CalendarOccasions
from dj_language.models import Language

# Argument parser for language inputs
parser = argparse.ArgumentParser(description='Translation Script for Najm Calendar')
parser.add_argument('--lang_code', type=str, help='Target language code', required=True)
parser.add_argument('--lang_name', type=str, help='Target language name', required=True)
parser.add_argument('--action', type=str, help='Batch Translation Manager', required=True)
parser.add_argument('--batch_id', type=str, help='Batch Translation Manager')
args = parser.parse_args()

target_language_code = args.lang_code
target_language_name = args.lang_name
action = args.action
batch_id = args.batch_id

telegram_logger(f"Starting the translation process for Najm Calendar to {target_language_name}...")
print(f"Starting the translation process for {target_language_name}...")

client = anthropic.Anthropic(api_key="************************************************************************************************************")

def prepare_translation_requests(text_items):
    requests = []
    for idx, (text, context, item_id) in enumerate(text_items):
        if target_language_code == "ul":
            prompt = f"""Translate the following Islamic religious event into Roman Urdu.

This text is from the Calendar, which contains important shia muslim  dates and events. The translation should:

Be faithful to the original meaning, while respecting Islamic and cultural context.

Use simple and clear vocabulary appropriate for Roman Urdu readers.

Be fluent and natural, not just a word-for-word rendering.

Please provide ONLY the translation."""
        else:
            prompt = f"""Translate the following Islamic religious event into {target_language_name}.

This text is from the Calendar, which contains important shia muslim dates and events. The translation should be accurate, capturing the deep cultural and religious context.

Please provide ONLY the translation."""

        prompt += f"\n\nOriginal Text: {text}"

        requests.append({
            "custom_id": f"{context}__{item_id}",
            "params": {
                "model": "claude-3-7-sonnet-latest",
                "max_tokens": 2048,
                "messages": [{"role": "user", "content": prompt}]
            }
        })
    return requests

def send_translation_requests():
    occasions_list = CalendarOccasions.objects.filter(
        Q(event_type='religious') & Q(is_global=True)
    )

    text_items = []
    for item in occasions_list:
        if not any(t.get("language_code") == target_language_code for t in item.translations):
            persian_text = next((t.get("title") for t in item.translations if t.get("language_code") == "fa"), None)
            if persian_text:
                text_items.append((persian_text, "title", item.id))

    requests = prepare_translation_requests(text_items)
    with open(f'calendar_translation_map_{target_language_code}.json', 'w', encoding='utf-8') as f:
        json.dump(requests, f, ensure_ascii=False, indent=4)

    batch = client.beta.messages.batches.create(requests=requests)
    telegram_logger(f"Batch translation requests sent. Batch detail: {batch}")
    print(f"Batch translation requests sent. Batch ID: {batch.id}")

def check_and_update_translations():
    results = client.beta.messages.batches.results(batch_id)
    successful_translations = 0
    skipped_translations = 0

    for result in results:
        custom_id = result.custom_id
        translation = result.result.message.content[0].text if result.result.type == "succeeded" else None

        if not translation:
            continue

        # Extract context and item_id from custom_id
        context, item_id = custom_id.split("__")
        item_id = int(item_id)

        # Check if the language already exists in the database
        try:
            calendar_item = CalendarOccasions.objects.get(id=item_id)
            
            if context == "title":
                if any(t.get("language_code") == target_language_code for t in calendar_item.translations):
                    skipped_translations += 1
                    skip_message = f"Item ID {item_id} already has {target_language_name} translation. Skipping."
                    telegram_logger(skip_message)
                    print(skip_message)
                    continue
                calendar_item.translations.append({"title": translation, "language_code": target_language_code})
                calendar_item.save()
                successful_translations += 1
                
                log_message = (
                    f"Item ID {item_id} ({context}) translated to {target_language_name}.\n\n"
                    f"Translation: {translation}\n\n"
                    f"Total successful translations so far: {successful_translations}"
                )
                telegram_logger(log_message)
        except CalendarOccasions.DoesNotExist:
            error_message = f"Item with ID {item_id} not found in database. Skipping translation."
            telegram_logger(error_message)
            print(error_message)
            continue

    summary_message = (
        f"Batch {batch_id} processing completed:\n"
        f"- Successful translations: {successful_translations}\n"
        f"- Skipped translations (already exist): {skipped_translations}"
    )
    telegram_logger(summary_message)
    print(summary_message)

def main():
    if action == "send":
        send_translation_requests()
    elif action == "check":
        check_and_update_translations()
    else:
        print("Invalid action. Use 'send' or 'check'.")

if __name__ == "__main__":
    main()
