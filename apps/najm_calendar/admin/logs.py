from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from django.utils.translation import gettext_lazy as _

from apps.najm_calendar.models import CalendarOccasions
from apps.tag.models import TaggingLog


class CalendarTaggingLog(TaggingLog):
    class Meta:
        proxy = True
        verbose_name = _('log')
        verbose_name_plural = _('calendar tagging log')


@admin.register(CalendarTaggingLog)
class CalendarAdminLogAdmin(AjaxDatatable):
    list_display = ('admin', '_calendar', 'updated_at',)
    latest_by = 'created_at'
    search_fields = ('admin', 'admin__email', 'admin__first_name', 'admin__last_name')
    list_filter = ('admin',)
    readonly_fields = ('admin', 'tags', 'updated_at', 'model_object_id', 'model')

    @admin.display(description=_('Calendar'), ordering='model_object_id')
    def _calendar(self, obj):
        url = reverse('admin:najm_calendar_calendaroccasions_change', args=(obj.model_object_id,))
        return f"<a href='{url}'>open <{obj.model_object_id}></a>"

    def get_queryset(self, request):
        return TaggingLog.objects.filter(
            model=ContentType.objects.get_for_model(CalendarOccasions)
        )

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False
