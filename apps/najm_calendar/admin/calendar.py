import json
from ajaxdatatable.admin import AjaxDatatableWithExcelExport
from ajaxdatatable.import_mixin import ImportMixin
from dj_language.models import Language
from django.contrib import admin
from django.urls import reverse, path
from django.utils.html import format_html
from django.contrib.postgres.aggregates import JSONBAgg
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from filer.models import Image
from import_export import fields, widgets
from import_export.resources import ModelResource

from apps.najm_calendar.models import CalendarOccasions, OccasionDetail
from apps.tag.admin_ext import TagLevelingAdminExtension
from apps.tag.forms import TagLevelForm
from utils.json_editor_field import JsonEditorWidget


class CalendarResource(ModelResource):
    dates = fields.Field(widget=widgets.CharWidget())

    class Meta:
        model = CalendarOccasions
        fields = (
            'translations', 'countries', 'occasion_type', 'is_yearly',
            'dates', 'thumbnail', 'holiday_in_countries'
        )
        import_id_fields = ()

    def before_import_row(self, row, row_number=None, **kwargs):
        lang = row.pop('language').lower()

        title = row.pop('title')
        row['translations'] = [{'title': title, 'language_code': lang}]

        thumbnail_id: str = row.pop('thumbnail', None)
        if thumbnail_id and thumbnail_id.isnumeric():
            row['thumbnail'] = Image.objects.filter(id=int(thumbnail_id)).first()

        countries = row['countries']

        if "all" in countries.lower():
            row['countries'] = []
        elif "," in countries:
            row['countries'] = countries.split(',')

        json_dates = []
        dates = row['dates'].split('\n')
        for date in dates:
            if "-" in date:
                date = date.split('-')
            else:
                date = date.split('/')

            if len(date) >= 3:
                # date includes year
                year, month, day = date
                json_dates.append({'year': year, 'month': month, 'day': day})
            else:
                month, day = date
                json_dates.append({'year': '', 'month': month, 'day': day})
        row['dates'] = json_dates
        pass

    def validate_instance(self, instance, import_validation_errors=None, validate_unique=True):
        errors = {'dates': []}
        for d in instance.dates:
            pass

        if errors['dates']:
            import_validation_errors = errors
        return super(CalendarResource, self).validate_instance(instance, import_validation_errors, validate_unique)

    def init_instance(self, row=None):
        return CalendarOccasions(**row)


def get_translation_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Translation')),
            'properties': {
                'title': {'type': 'string', 'title': str(_('Title'))},
                'short_title': {'type': 'string', 'title': str(_('Short Title'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }


class CalendarForm(TagLevelForm):
    class Meta:
        model = CalendarOccasions
        exclude = ()
        widgets = {
            'translations': JsonEditorWidget(attrs={'schema': get_translation_schema})
        }


class LanguageFilter(admin.SimpleListFilter):
    title = 'language'
    parameter_name = 'language'

    def lookups(self, request, model_admin):
        return [(i.code, i.name) for i in Language.objects.filter(status=True)]

    def queryset(self, request, queryset):
        if request.GET.get('language'):
            return queryset.filter(translations__contains=[{'language_code': request.GET.get('language')}])
        return queryset


class HasDetailsFilter(admin.SimpleListFilter):
    title = _('Has Details')
    parameter_name = 'has_details'

    def lookups(self, request, model_admin):
        return (
            ('yes', _('Has Details')),
            ('no', _('No Details')),
        )

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(occasion_details__isnull=False).distinct()
        elif self.value() == 'no':
            return queryset.filter(occasion_details__isnull=True)
        return queryset


@admin.register(CalendarOccasions)
class CalendarOccasionsAdmin(TagLevelingAdminExtension, ImportMixin, AjaxDatatableWithExcelExport):
    list_display = (
        '_title_', 'occasion_type', 'event_type', 'is_yearly', '_dates', '_countries', '_details', '_translation',
    )
    list_filter = ('is_yearly', 'occasion_type', LanguageFilter, 'event_type', HasDetailsFilter,)
    search_fields = ('countries', 'translations', 'title')
    form = CalendarForm
    ordering = ('-id',)
    latest_by = 'updated_at'
    change_form_template = 'admin/calendar.html'
    actions = ['make_global']
    fieldsets = (
        (None, {
            'fields': ('translations', 'countries', 'occasion_type', 'event_type', 'trends', 'is_yearly','is_global')
        }),
        ('Events Dates', {
            'description': _('Please set Year field to empty if the event is annually'),
            'fields': ('dates',)
        }),
        (_('Holidays'), {
            'fields': ('holiday_in_countries',)
        }),
        (_('Thumbnail'), {
            'fields': ('thumbnail',)
        }),
        (_('Tags'), {
            'fields': ('tags_1', 'tags_2', 'tags_3')
        }),
    )

    resource_class = CalendarResource

    def has_delete_permission(self, request, obj=None):
        # if obj and len(obj.translations) > 1:
            # return False
        return True

    @admin.display(description=_('Translation'), ordering='translations')
    def _translation(self, obj):
        languages = [i['language_code'] for i in obj.translations]
        badges = [
            f"""<span class='badge' style='
                display: inline-block;
                margin: 3px;
                padding: 5px 10px;
                font-size: 12px;
                font-weight: 500;
                color: #fff;
                background: linear-gradient(90deg, #1cc88a, #4e73df);
                border-radius: 10px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            '>{lang}</span>"""
            for lang in languages
        ]
        return format_html(
            f"""
            <div style="
                max-width: 300px;
                max-height: 60px;
                overflow-y: auto;
                border: 1px solid #ddd;
                border-radius: 10px;
                padding: 5px;
                background-color: #f8f9fc;
            ">
                {"".join(badges)}
            </div>
            """
        )
        
    @admin.display(description=_('Title'), ordering='translations')
    def _title_(self, obj: CalendarOccasions):
        return obj._title()

    @admin.display(description=_('Dates'), ordering='dates__0__month')
    def _dates(self, obj: CalendarOccasions):
        return "\n".join([f"{i['month']}/{i['day']}" for i in obj.dates])

    @admin.display(description=_('Countries'), ordering='countries')
    def _countries(self, obj):
        html = ""
        if len(obj.countries):
            for country in obj.countries:
                html += "<span class='badge badge-secondary px-2 m-1'>%s</span>" % country.code
        else:
            html = "<span class='badge badge-secondary px-2 m-1'>%s</span>" % 'All'

        return mark_safe(html)



    @admin.display(description=_('Details'), ordering='translations')
    def _details(self, obj):
        occasion_detail_exists = obj.occasion_details.exists()  # بررسی موجود بودن جزئیات
    
        # if occasion_detail_exists:
        #     details_url = reverse('admin:najm_calendar_occasiondetail_change', args=[obj.occasion_details.first().id])
        # else:
        #     details_url = reverse('admin:najm_calendar_occasiondetail_add') + f'?_changelist_filters=occasion__id__exact%3D{obj.id}'
        details_url = reverse('admin:najm_calendar_occasiondetail_changelist') + f'?occasion__id__exact={obj.id}'

        # اضافه کردن نقطه‌ی قرمز اگر جزئیات وجود داشته باشد
        red_dot = mark_safe(
            '<span style="'
            'display: inline-block; '
            'width: 4px; '
            'height: 4px; '
            'background-color: red; '
            'border-radius: 50%; '
            'margin-left: 4px; '
            'vertical-align: middle;'
            '"></span>'
        ) if occasion_detail_exists else ''
    
        return format_html(
            """
            <a href="{}" class="modern-button" style="
                display: inline-flex;
                align-items: center;
                justify-content: center;
                text-decoration: none;
                padding: 6px 12px;
                font-size: 13px;
                font-weight: 500;
                color: #fff;
                background: linear-gradient(90deg, #4e73df, #1cc88a);
                border: none;
                border-radius: 20px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            " onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 12px rgba(0, 0, 0, 0.15)';"
            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.1)';">
                <span style="margin-right: 4px; display: flex; align-items: center;">
                    <i class="fas fa-info-circle" style="font-size: 12px;"></i>
                </span>
                Details{}
            </a>
            """,
            details_url,
            red_dot  # اضافه کردن نقطه‌ی قرمز به متن
        )
            
    def get_search_results(self, request, queryset, search_term):
        if '/' in search_term:
            date = search_term.strip().split('/')
            q = {
                'year': "",
                'month': "",
                'day': ""
            }
            if len(date) > 2:
                q['year'], q['month'], q['day'] = date
            elif len(date) > 1:
                q['month'], q['day'] = date
            elif len(date) <= 1:
                q['day'] = date
            # date search
            queryset = queryset.filter(dates__contains=[q])
            return queryset, True

        return super(CalendarOccasionsAdmin, self).get_search_results(request, queryset, search_term)

    def get_queryset(self, request):
        return super(CalendarOccasionsAdmin, self).get_queryset(request).annotate(
            date=JSONBAgg('dates__0__month', ordering='-dates__0__month'),
        )

    @admin.action(description='Set selected occasions as global')
    def make_global(self, request, queryset):
        updated = queryset.update(is_global=True)
        self.message_user(request, f'{updated} occasions were successfully marked as global.')

