# Generated by Django 3.2.25 on 2025-06-17 22:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notification', '0010_add_reminder_model'),
    ]

    operations = [
        migrations.AddField(
            model_name='reminder',
            name='object_id',
            field=models.CharField(default='0', help_text='ID of the related object (meet ID, dua ID, etc.)', max_length=100, verbose_name='Object ID'),
            preserve_default=False,
        ),
        migrations.AddIndex(
            model_name='reminder',
            index=models.Index(fields=['service_name', 'object_id'], name='notificatio_service_582708_idx'),
        ),
        migrations.AddIndex(
            model_name='reminder',
            index=models.Index(fields=['user', 'service_name', 'object_id'], name='notificatio_user_id_933c3f_idx'),
        ),
    ]
