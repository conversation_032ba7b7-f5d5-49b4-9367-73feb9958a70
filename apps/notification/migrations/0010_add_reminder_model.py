# Generated by Django 3.2.25 on 2025-06-17 22:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('notification', '0009_rename_discription_usernotification_description'),
    ]

    operations = [
        migrations.CreateModel(
            name='Reminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_name', models.CharField(help_text='Name of the service (e.g., "meet", "dua", etc.)', max_length=100, verbose_name='Service Name')),
                ('text', models.TextField(help_text='The reminder message content', verbose_name='Reminder Text')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the reminder was created', verbose_name='Created At')),
                ('call_time', models.DateTimeField(help_text='When the reminder should be triggered/called', verbose_name='Call Time')),
                ('is_sent', models.BooleanField(default=False, help_text='Whether the reminder has been sent', verbose_name='Is Sent')),
                ('is_read', models.BooleanField(default=False, help_text='Whether the user has opened/read the reminder', verbose_name='Is Read')),
                ('status', models.BooleanField(default=True, help_text='General status management for the reminder', verbose_name='Status')),
                ('notif_data', models.JSONField(blank=True, help_text='Additional notification metadata', null=True, verbose_name='Notification Data')),
                ('user', models.ForeignKey(help_text='User who set the reminder', on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Reminder',
                'verbose_name_plural': 'Reminders',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='reminder',
            index=models.Index(fields=['user', 'service_name'], name='notificatio_user_id_70f082_idx'),
        ),
        migrations.AddIndex(
            model_name='reminder',
            index=models.Index(fields=['call_time'], name='notificatio_call_ti_173eab_idx'),
        ),
        migrations.AddIndex(
            model_name='reminder',
            index=models.Index(fields=['is_sent', 'call_time'], name='notificatio_is_sent_261f24_idx'),
        ),
    ]
