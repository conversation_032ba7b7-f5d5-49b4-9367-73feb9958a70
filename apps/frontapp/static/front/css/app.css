@charset "UTF-8";

@-webkit-keyframes slide-down {
	0% {
		opacity: 1;
	}

	90% {
		opacity: 0;
	}
}

@keyframes slide-down {
	0% {
		opacity: 1;
	}

	90% {
		opacity: 0;
	}
}

.visually-hidden {
	margin: -1px;
	padding: 0;
	width: 1px;
	height: 1px;
	overflow: hidden;
	clip: rect(0 0 0 0);
	clip: rect(0, 0, 0, 0);
	position: absolute;
}

body {
	font-family: "Inter", sans-serif;
	font-size: 16px;
	line-height: 28px;
	color: #4f5158;
	overflow-x: hidden;
}

html {
	font-size: 16px;
}

svg {
	display: block;
}

a,
button,
input,
textarea,
button {
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

input,
textarea {
	width: 100%;
	padding: 10px 12px;
	outline: 0;
}

button {
	cursor: pointer;
	outline: 0;
}

a,
a:hover {
	text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: "Inter", sans-serif;
	color: #1c1e21;
	line-height: 1.2;
	margin-top: 0;
	font-weight: 600;
}

img {
	max-width: 100%;
	height: auto;
}

section {
	position: relative;
}

@media (min-width: 1200px) {

	.container,
	.container-lg,
	.container-md,
	.container-sm,
	.container-xl {
		max-width: 1200px;
	}
}

@media (max-width: 767px) {
	.gp-order-2 {
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		order: 2;
	}
}

/*
Flaticon icon font: Flaticon
Creation date: 04/06/2018 14:38
*/
@font-face {
	font-family: "Flaticon";
	src: url("./Flaticon.eot");
	src: url("../fonts/Flaticon.eot?#iefix") format("embedded-opentype"), url("../fonts/Flaticon.woff") format("woff"), url("../fonts/Flaticon.ttf") format("truetype"), url("../fonts/Flaticon.svg#Flaticon") format("svg");
	font-weight: normal;
	font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
	@font-face {
		font-family: "Flaticon";
		src: url("../fonts/Flaticon.svg#Flaticon") format("svg");
	}
}

.section-padding {
	padding: 120px 0;
}

.mt-80 {
	margin-top: 80px;
}

.mt-75 {
	margin-top: 76px;
}

.mt-30 {
	margin-top: 30px;
}

.mt-220 {
	margin-top: 220px;
}

@media (max-width: 991px) {
	.mt-220 {
		margin-top: 100px;
	}
}

@media (max-width: 991px) {
	.tt-order-2 {
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		order: 2;
	}
}

@media (max-width: 767px) {
	.tt-order-md-2 {
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		order: 2;
	}
}

.mb--5 {
	margin-bottom: 5px !important;
}

.pr {
	position: relative;
	z-index: 2;
}

@media (max-width: 767px) {
	.tt-md-3 {
		margin-bottom: 30px;
	}
}

/*--------------------------------------------------------------
  ##  Button
  --------------------------------------------------------------*/
.tt__btn {
	padding: 11px 25px;
	position: relative;
	z-index: 1;
	line-height: 1.2;
	border-radius: 30px;
	border: 2px solid #3283fd;
	color: #ffffff;
	overflow: hidden;
	font-size: 15px;
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	font-weight: 500;
	-webkit-transition: all 0.4s ease-in-out;
	-o-transition: all 0.4s ease-in-out;
	transition: all 0.4s ease-in-out;
	background: #3283fd;
}

.tt__btn i {
	margin-left: 5px;
}

.tt__btn:before {
	content: "";
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: -1;
	height: 300%;
	background-color: #3283fd;
	border-radius: 50% 50% 0 0;
	-webkit-transition: all 0.4s ease-in-out;
	-o-transition: all 0.4s ease-in-out;
	transition: all 0.4s ease-in-out;
}

.tt__btn.btn-round {
	border-radius: 6px;
}

.tt__btn.btn-circle {
	border-radius: 30px;
}

.tt__btn.error_btn {
	padding: 14px 40px;
	width: 240px;
	border-radius: 6px;
}

.tt__btn.error_btn i {
	margin-right: 5px;
	font-size: 16px;
}

.tt__btn.submit-btn {
	border-radius: 6px;
	width: 100%;
	text-align: center;
	padding: 14px;
}

.tt__btn.submit-btn i {
	display: none;
}

.tt__btn:hover {
	background: transparent;
	color: #3283fd;
}

.tt__btn:hover:before {
	height: 0;
}

.tt__btn.btn-small {
	padding: 5px 30px;
}

.tt__btn.btn-lg {
	padding: 14px 35px;
	font-size: 20px;
}

.tt__btn.btn-outline {
	background: transparent;
	color: #3283fd;
	border-color: #e8e9eb;
}

.tt__btn.btn-outline:before {
	height: 0;
}

.tt__btn.btn-outline:hover {
	color: #fff;
	border-color: #3283fd;
}

.tt__btn.btn-outline:hover:before {
	height: 300px;
}

.tt__btn.btn-outline-bg {
	border-color: #ccd8ff;
	color: #3283fd;
	background-color: #f4f7ff;
}

.tt__btn.btn-outline-bg:before {
	background-color: transparent;
}

.tt__btn.btn-outline-bg:hover {
	border-color: #3283fd;
	color: #fff;
	background-color: #3283fd;
}

.tt__btn.btn-outline-dark {
	border-color: #e8eaf0;
	color: #1c1e21;
}

.tt__btn.btn-outline-dark:hover {
	border-color: #3283fd;
}

.tt__btn.btn-light {
	color: #1c1e21;
	border-color: #fff;
	-webkit-box-shadow: 0 14px 20px 0 rgba(29, 6, 117, 0.2);
	box-shadow: 0 14px 20px 0 rgba(29, 6, 117, 0.2);
}

.tt__btn.btn-light:before {
	background: #fff;
	border-color: #fff;
}

.tt__btn.btn-light:hover {
	background: transparent;
	color: #fff;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.tt__btn.btn-light.btn-outline {
	border-color: rgba(255, 255, 255, 0.4);
	color: #fff;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.tt__btn.btn-light.btn-outline:hover {
	color: #3283fd;
	border-color: #fff;
}

.tt__btn.btn__link {
	padding: 0;
	background-color: transparent;
	color: #1c1e21;
	border: 0;
}

.tt__btn.btn__link i {
	font-size: 15px;
}

.tt__btn.btn__link:before {
	display: none;
}

.tt__btn.btn__link:hover {
	color: #3283fd;
}

.tt__btn.btn-color-two {
	border-color: #fa5441;
}

.tt__btn.btn-color-two:before {
	background-color: #fa5441;
}

.tt__btn.btn-color-two:hover {
	color: #fa5441;
}

.tt__btn.btn-sqr {
	border-radius: 0;
}

.tt__btn-content-wrapper {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.tt__btn-content-wrapper i {
	font-size: 14px;
}

.tt__btn-content-wrapper .elementor-align-icon-right {
	margin-left: 10px;
}

.tt__btn-content-wrapper .elementor-align-icon-left {
	margin-right: 10px;
}

.tt__btn-content-wrapper.icon-right .elementor-align-icon-right {
	-webkit-box-ordinal-group: 3;
	-ms-flex-order: 2;
	order: 2;
}

.play-button .popup-play-btn {
	height: 100px;
	width: 100px;
	line-height: 100px;
	font-size: 25px;
	text-align: center;
	position: static;
	-webkit-animation: none;
	animation: none;
	-webkit-transform: translate(0);
	-ms-transform: translate(0);
	transform: translate(0);
	display: inline-block;
	color: #fd2cb5;
}

.play-button .popup-play-btn:after {
	content: "";
	position: absolute;
	width: 100%;
	height: 100%;
	background-color: #fff;
	top: 0;
	left: 0;
	border-radius: 50%;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.play-button .popup-play-btn:before {
	content: "";
	height: 120px;
	width: 120px;
	position: absolute;
	left: -10px;
	top: -10px;
	border: 1px solid rgba(255, 255, 255, 0.302);
	border-radius: 50%;
	-webkit-animation: videoBtnAnim 3s linear infinite;
	animation: videoBtnAnim 3s linear infinite;
	display: block;
}

.play-button .popup-play-btn i {
	z-index: 2;
	position: relative;
	position: relative;
	margin-left: 10px;
}

.play-button .popup-play-btn:hover:after {
	-webkit-transform: scale(1.15);
	-ms-transform: scale(1.15);
	transform: scale(1.15);
}

@-webkit-keyframes videoBtnAnim {
	0% {
		-webkit-transform: scale(0.8);
		transform: scale(0.8);
	}

	25% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}

	50% {
		-webkit-transform: scale(1.2);
		transform: scale(1.2);
	}

	75% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}

	to {
		-webkit-transform: scale(0.8);
		transform: scale(0.8);
	}
}

@keyframes videoBtnAnim {
	0% {
		-webkit-transform: scale(0.8);
		transform: scale(0.8);
	}

	25% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}

	50% {
		-webkit-transform: scale(1.2);
		transform: scale(1.2);
	}

	75% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}

	to {
		-webkit-transform: scale(0.8);
		transform: scale(0.8);
	}
}

.tt-app-btn {
	border: 2px solid #1c1e21;
	color: #1c1e21;
	padding: 13px 30px;
	font-size: 16px;
	font-weight: 500;
	border-radius: 6px;
	display: block;
}

.tt-app-btn.btn-circle {
	border-radius: 40px;
}

.tt-app-btn .btn-icon {
	line-height: 1;
}

.tt-app-btn i {
	font-size: 27px;
	margin-right: 11px;
	vertical-align: -4px;
}

.tt-app-btn:hover {
	background-color: #3283fd;
	border-color: #3283fd;
	color: #fff;
}

.button-wrapper .tt-app-btn:not(:last-child) {
	margin-bottom: 20px;
}

.button-wrapper.inline-items .tt-app-btn {
	display: inline-block;
}

.button-wrapper.inline-items .tt-app-btn:not(:last-child) {
	margin-bottom: 0;
	margin-right: 20px;
}

.btn__link {
	color: #1c1e21;
	display: inline-block;
}

.btn__link i {
	margin-left: 5px;
}

.btn__link:hover {
	color: #572aff;
}

/*--------------------------------------------------------------
  ##  Section Title
  --------------------------------------------------------------*/
.section-heading {
	position: relative;
	z-index: 2;
	margin-bottom: 75px;
}

@media (max-width: 991px) {
	.section-heading {
		margin-bottom: 40px;
	}
}

.section-heading .subtitle {
	font-size: 14px;
	width: 100%;
	font-weight: 500;
	margin-bottom: 20px;
	text-transform: uppercase;
}

.section-heading .section-title {
	font-size: 44px;
	line-height: 1.2;
	font-weight: 700;
	margin-bottom: 8px;
}

@media (max-width: 992px) {
	.section-heading .section-title {
		font-size: 32px !important;
	}
}

@media (max-width: 767px) {
	.section-heading .section-title br {
		display: none;
	}
}

@media (max-width: 576px) {
	.section-heading .section-title {
		font-size: 28px !important;
	}
}

.section-heading .description {
	font-weight: 400;
	margin: 0;
}

@media (max-width: 991px) {
	.section-heading .description br {
		display: none;
	}
}

.section-heading.text-left .description {
	margin: 0;
}

.section-heading.text-right .description {
	margin: 0 0 0 auto;
}

.section-heading.style-two {
	margin-bottom: 65px;
}

.section-heading.style-two .section-subtitle {
	font-size: 40px;
	font-weight: 300;
	margin-bottom: 0;
}

.section-heading.style-two .section-title {
	font-weight: 800;
	margin-bottom: 10px;
}

.section-heading.style-three {
	margin-bottom: 51px;
}

.section-heading.style-three .section-title {
	font-size: 40px;
	font-weight: 800;
	margin-bottom: 11px;
}

.section-heading.style-four {
	margin-bottom: 61px;
}

.section-heading.style-four .section-title {
	font-size: 40px;
	font-weight: 800;
	margin-bottom: 11px;
	color: #11266d;
}

.section-heading.style-four .description {
	color: #3c435b;
}

.section-heading.header-light .section-title {
	color: #fff;
}

.section-heading.header-light .description {
	color: rgba(255, 255, 255, 0.8);
}

/*--------------------------------------------------------------
  ##  List Items
  --------------------------------------------------------------*/
.list-items {
	margin: 0;
	padding: 0;
	list-style: none;
}

.list-items li {
	position: relative;
	font-weight: 600;
	line-height: 40px;
	font-size: 16px;
}

.list-items li i {
	margin-right: 10px;
	color: #572aff;
}

/*--------------------------------------------------------------
  ##  Social Link
  --------------------------------------------------------------*/
/*--------------------------------------------------------------
  ##  Page Loader
  --------------------------------------------------------------*/
.page-loader {
	background: #fff;
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 99999999;
}

.page-loader .loader {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}

.loader.animation-1 {
	-webkit-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	transform: rotate(45deg);
}

.loader.animation-2,
.loader.animation-4 {
	-webkit-transform: rotate(10deg);
	-ms-transform: rotate(10deg);
	transform: rotate(10deg);
}

.loader.animation-2 .shape,
.loader.animation-4 .shape {
	border-radius: 5px;
}

.loader.animation-2,
.loader.animation-3,
.loader.animation-4 {
	-webkit-animation: rotation 1s infinite;
	animation: rotation 1s infinite;
}

.loader.animation-3 .shape1 {
	border-top-left-radius: 10px;
}

.loader.animation-3 .shape2 {
	border-top-right-radius: 10px;
}

.loader.animation-3 .shape3 {
	border-bottom-left-radius: 10px;
}

.loader.animation-3 .shape4 {
	border-bottom-right-radius: 10px;
}

.loader.animation-4,
.loader.animation-5 {
	-webkit-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	transform: rotate(45deg);
}

.loader.animation-5 .shape {
	width: 15px;
	height: 15px;
}

.loader.animation-6 {
	-webkit-animation: rotation 1s infinite;
	animation: rotation 1s infinite;
}

.loader.animation-6 .shape {
	width: 12px;
	height: 12px;
	border-radius: 2px;
}

.loader .shape {
	position: absolute;
	width: 10px;
	height: 10px;
	border-radius: 1px;
}

.loader .shape.shape1 {
	left: 0;
	background-color: #5c6bc0;
}

.loader .shape.shape2 {
	right: 0;
	background-color: #8bc34a;
}

.loader .shape.shape3 {
	bottom: 0;
	background-color: #ffb74d;
}

.loader .shape.shape4 {
	bottom: 0;
	right: 0;
	background-color: #f44336;
}

@-webkit-keyframes rotation {
	from {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	to {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes rotation {
	from {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	to {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

.animation-1 .shape1 {
	-webkit-animation: animation1shape1 0.5s ease 0s infinite alternate;
	animation: animation1shape1 0.5s ease 0s infinite alternate;
}

@-webkit-keyframes animation1shape1 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(16px, 16px);
		transform: translate(16px, 16px);
	}
}

@keyframes animation1shape1 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(16px, 16px);
		transform: translate(16px, 16px);
	}
}

.animation-1 .shape2 {
	-webkit-animation: animation1shape2 0.5s ease 0s infinite alternate;
	animation: animation1shape2 0.5s ease 0s infinite alternate;
}

@-webkit-keyframes animation1shape2 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-16px, 16px);
		transform: translate(-16px, 16px);
	}
}

@keyframes animation1shape2 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-16px, 16px);
		transform: translate(-16px, 16px);
	}
}

.animation-1 .shape3 {
	-webkit-animation: animation1shape3 0.5s ease 0s infinite alternate;
	animation: animation1shape3 0.5s ease 0s infinite alternate;
}

@-webkit-keyframes animation1shape3 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(16px, -16px);
		transform: translate(16px, -16px);
	}
}

@keyframes animation1shape3 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(16px, -16px);
		transform: translate(16px, -16px);
	}
}

.animation-1 .shape4 {
	-webkit-animation: animation1shape4 0.5s ease 0s infinite alternate;
	animation: animation1shape4 0.5s ease 0s infinite alternate;
}

@-webkit-keyframes animation1shape4 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-16px, -16px);
		transform: translate(-16px, -16px);
	}
}

@keyframes animation1shape4 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-16px, -16px);
		transform: translate(-16px, -16px);
	}
}

.animation-2 .shape1 {
	-webkit-animation: animation2shape1 0.5s ease 0s infinite alternate;
	animation: animation2shape1 0.5s ease 0s infinite alternate;
}

@-webkit-keyframes animation2shape1 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(20px, 20px);
		transform: translate(20px, 20px);
	}
}

@keyframes animation2shape1 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(20px, 20px);
		transform: translate(20px, 20px);
	}
}

.animation-2 .shape2 {
	-webkit-animation: animation2shape2 0.5s ease 0s infinite alternate;
	animation: animation2shape2 0.5s ease 0s infinite alternate;
}

@-webkit-keyframes animation2shape2 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-20px, 20px);
		transform: translate(-20px, 20px);
	}
}

@keyframes animation2shape2 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-20px, 20px);
		transform: translate(-20px, 20px);
	}
}

.animation-2 .shape3 {
	-webkit-animation: animation2shape3 0.5s ease 0s infinite alternate;
	animation: animation2shape3 0.5s ease 0s infinite alternate;
}

@-webkit-keyframes animation2shape3 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(20px, -20px);
		transform: translate(20px, -20px);
	}
}

@keyframes animation2shape3 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(20px, -20px);
		transform: translate(20px, -20px);
	}
}

.animation-2 .shape4 {
	-webkit-animation: animation2shape4 0.5s ease 0s infinite alternate;
	animation: animation2shape4 0.5s ease 0s infinite alternate;
}

@-webkit-keyframes animation2shape4 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-20px, -20px);
		transform: translate(-20px, -20px);
	}
}

@keyframes animation2shape4 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-20px, -20px);
		transform: translate(-20px, -20px);
	}
}

.animation-3 .shape1 {
	-webkit-animation: animation3shape1 0.5s ease 0s infinite alternate;
	animation: animation3shape1 0.5s ease 0s infinite alternate;
}

@-webkit-keyframes animation3shape1 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(5px, 5px);
		transform: translate(5px, 5px);
	}
}

@keyframes animation3shape1 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(5px, 5px);
		transform: translate(5px, 5px);
	}
}

.animation-3 .shape2 {
	-webkit-animation: animation3shape2 0.5s ease 0s infinite alternate;
	animation: animation3shape2 0.5s ease 0s infinite alternate;
}

@-webkit-keyframes animation3shape2 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-5px, 5px);
		transform: translate(-5px, 5px);
	}
}

@keyframes animation3shape2 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-5px, 5px);
		transform: translate(-5px, 5px);
	}
}

.animation-3 .shape3 {
	-webkit-animation: animation3shape3 0.5s ease 0s infinite alternate;
	animation: animation3shape3 0.5s ease 0s infinite alternate;
}

@-webkit-keyframes animation3shape3 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(5px, -5px);
		transform: translate(5px, -5px);
	}
}

@keyframes animation3shape3 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(5px, -5px);
		transform: translate(5px, -5px);
	}
}

.animation-3 .shape4 {
	-webkit-animation: animation3shape4 0.5s ease 0s infinite alternate;
	animation: animation3shape4 0.5s ease 0s infinite alternate;
}

@-webkit-keyframes animation3shape4 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-5px, -5px);
		transform: translate(-5px, -5px);
	}
}

@keyframes animation3shape4 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-5px, -5px);
		transform: translate(-5px, -5px);
	}
}

.animation-4 .shape1 {
	-webkit-animation: animation4shape1 0.3s ease 0s infinite alternate;
	animation: animation4shape1 0.3s ease 0s infinite alternate;
}

@-webkit-keyframes animation4shape1 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(5px, 5px);
		transform: translate(5px, 5px);
	}
}

@keyframes animation4shape1 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(5px, 5px);
		transform: translate(5px, 5px);
	}
}

.animation-4 .shape2 {
	-webkit-animation: animation4shape2 0.3s ease 0.3s infinite alternate;
	animation: animation4shape2 0.3s ease 0.3s infinite alternate;
}

@-webkit-keyframes animation4shape2 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-5px, 5px);
		transform: translate(-5px, 5px);
	}
}

@keyframes animation4shape2 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-5px, 5px);
		transform: translate(-5px, 5px);
	}
}

.animation-4 .shape3 {
	-webkit-animation: animation4shape3 0.3s ease 0.3s infinite alternate;
	animation: animation4shape3 0.3s ease 0.3s infinite alternate;
}

@-webkit-keyframes animation4shape3 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(5px, -5px);
		transform: translate(5px, -5px);
	}
}

@keyframes animation4shape3 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(5px, -5px);
		transform: translate(5px, -5px);
	}
}

.animation-4 .shape4 {
	-webkit-animation: animation4shape4 0.3s ease 0s infinite alternate;
	animation: animation4shape4 0.3s ease 0s infinite alternate;
}

@-webkit-keyframes animation4shape4 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-5px, -5px);
		transform: translate(-5px, -5px);
	}
}

@keyframes animation4shape4 {
	from {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	to {
		-webkit-transform: translate(-5px, -5px);
		transform: translate(-5px, -5px);
	}
}

.animation-5 .shape1 {
	animation: animation5shape1 2s ease 0s infinite reverse;
}

@-webkit-keyframes animation5shape1 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(0, 15px);
		transform: translate(0, 15px);
	}

	50% {
		-webkit-transform: translate(15px, 15px);
		transform: translate(15px, 15px);
	}

	75% {
		-webkit-transform: translate(15px, 0);
		transform: translate(15px, 0);
	}
}

@keyframes animation5shape1 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(0, 15px);
		transform: translate(0, 15px);
	}

	50% {
		-webkit-transform: translate(15px, 15px);
		transform: translate(15px, 15px);
	}

	75% {
		-webkit-transform: translate(15px, 0);
		transform: translate(15px, 0);
	}
}

.animation-5 .shape2 {
	animation: animation5shape2 2s ease 0s infinite reverse;
}

@-webkit-keyframes animation5shape2 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(-15px, 0);
		transform: translate(-15px, 0);
	}

	50% {
		-webkit-transform: translate(-15px, 15px);
		transform: translate(-15px, 15px);
	}

	75% {
		-webkit-transform: translate(0, 15px);
		transform: translate(0, 15px);
	}
}

@keyframes animation5shape2 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(-15px, 0);
		transform: translate(-15px, 0);
	}

	50% {
		-webkit-transform: translate(-15px, 15px);
		transform: translate(-15px, 15px);
	}

	75% {
		-webkit-transform: translate(0, 15px);
		transform: translate(0, 15px);
	}
}

.animation-5 .shape3 {
	animation: animation5shape3 2s ease 0s infinite reverse;
}

@-webkit-keyframes animation5shape3 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(15px, 0);
		transform: translate(15px, 0);
	}

	50% {
		-webkit-transform: translate(15px, -15px);
		transform: translate(15px, -15px);
	}

	75% {
		-webkit-transform: translate(0, -15px);
		transform: translate(0, -15px);
	}
}

@keyframes animation5shape3 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(15px, 0);
		transform: translate(15px, 0);
	}

	50% {
		-webkit-transform: translate(15px, -15px);
		transform: translate(15px, -15px);
	}

	75% {
		-webkit-transform: translate(0, -15px);
		transform: translate(0, -15px);
	}
}

.animation-5 .shape4 {
	animation: animation5shape4 2s ease 0s infinite reverse;
}

@-webkit-keyframes animation5shape4 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(0, -15px);
		transform: translate(0, -15px);
	}

	50% {
		-webkit-transform: translate(-15px, -15px);
		transform: translate(-15px, -15px);
	}

	75% {
		-webkit-transform: translate(-15px, 0);
		transform: translate(-15px, 0);
	}
}

@keyframes animation5shape4 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(0, -15px);
		transform: translate(0, -15px);
	}

	50% {
		-webkit-transform: translate(-15px, -15px);
		transform: translate(-15px, -15px);
	}

	75% {
		-webkit-transform: translate(-15px, 0);
		transform: translate(-15px, 0);
	}
}

.animation-6 .shape1 {
	-webkit-animation: animation6shape1 2s linear 0s infinite normal;
	animation: animation6shape1 2s linear 0s infinite normal;
}

@-webkit-keyframes animation6shape1 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(0, 18px);
		transform: translate(0, 18px);
	}

	50% {
		-webkit-transform: translate(18px, 18px);
		transform: translate(18px, 18px);
	}

	75% {
		-webkit-transform: translate(18px, 0);
		transform: translate(18px, 0);
	}
}

@keyframes animation6shape1 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(0, 18px);
		transform: translate(0, 18px);
	}

	50% {
		-webkit-transform: translate(18px, 18px);
		transform: translate(18px, 18px);
	}

	75% {
		-webkit-transform: translate(18px, 0);
		transform: translate(18px, 0);
	}
}

.animation-6 .shape2 {
	-webkit-animation: animation6shape2 2s linear 0s infinite normal;
	animation: animation6shape2 2s linear 0s infinite normal;
}

@-webkit-keyframes animation6shape2 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(-18px, 0);
		transform: translate(-18px, 0);
	}

	50% {
		-webkit-transform: translate(-18px, 18px);
		transform: translate(-18px, 18px);
	}

	75% {
		-webkit-transform: translate(0, 18px);
		transform: translate(0, 18px);
	}
}

@keyframes animation6shape2 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(-18px, 0);
		transform: translate(-18px, 0);
	}

	50% {
		-webkit-transform: translate(-18px, 18px);
		transform: translate(-18px, 18px);
	}

	75% {
		-webkit-transform: translate(0, 18px);
		transform: translate(0, 18px);
	}
}

.animation-6 .shape3 {
	-webkit-animation: animation6shape3 2s linear 0s infinite normal;
	animation: animation6shape3 2s linear 0s infinite normal;
}

@-webkit-keyframes animation6shape3 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(18px, 0);
		transform: translate(18px, 0);
	}

	50% {
		-webkit-transform: translate(18px, -18px);
		transform: translate(18px, -18px);
	}

	75% {
		-webkit-transform: translate(0, -18px);
		transform: translate(0, -18px);
	}
}

@keyframes animation6shape3 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(18px, 0);
		transform: translate(18px, 0);
	}

	50% {
		-webkit-transform: translate(18px, -18px);
		transform: translate(18px, -18px);
	}

	75% {
		-webkit-transform: translate(0, -18px);
		transform: translate(0, -18px);
	}
}

.animation-6 .shape4 {
	-webkit-animation: animation6shape4 2s linear 0s infinite normal;
	animation: animation6shape4 2s linear 0s infinite normal;
}

@-webkit-keyframes animation6shape4 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(0, -18px);
		transform: translate(0, -18px);
	}

	50% {
		-webkit-transform: translate(-18px, -18px);
		transform: translate(-18px, -18px);
	}

	75% {
		-webkit-transform: translate(-18px, 0);
		transform: translate(-18px, 0);
	}
}

@keyframes animation6shape4 {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0);
	}

	25% {
		-webkit-transform: translate(0, -18px);
		transform: translate(0, -18px);
	}

	50% {
		-webkit-transform: translate(-18px, -18px);
		transform: translate(-18px, -18px);
	}

	75% {
		-webkit-transform: translate(-18px, 0);
		transform: translate(-18px, 0);
	}
}

@media screen and (max-width: 600px) {
	.content {
		-ms-flex-line-pack: start;
		align-content: flex-start;
	}

	.content .column {
		width: calc(50% - 30px);
	}
}

@media screen and (max-width: 400px) {
	.content {
		-ms-flex-line-pack: start;
		align-content: flex-start;
	}

	.content .column {
		width: calc(100% - 30px);
	}
}

/*--------------------------------------------------------------
  ##  Header
  --------------------------------------------------------------*/
.site-header {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 999;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.site-header .site-logo .site-title {
	font-size: 34px;
	font-weight: 500;
	padding: 8px 0;
	margin: 0;
}

.site-header .site-logo a {
	font-size: 30px;
	font-weight: 700;
	padding: 23px 0;
	display: block;
	color: #1c1e21;
}

.site-header .site-logo a img {
	min-width: 150px;
	width: 158px;
}

.site-header .site-logo a h3 {
	margin: 0;
	font-size: 30px;
	font-weight: 700;
	color: #fff;
}

.site-header .site-logo .logo-sticky {
	display: none;
}

.error404 .site-header .site-logo a {
	color: #1c1e21;
}

.error404 .site-header .site-logo a h3 {
	color: #1c1e21;
}

.site-header .tt-hamburger {
	width: 24px;
	cursor: pointer;
}

.site-header .tt-hamburger.active {
	opacity: 0;
	visibility: hidden;
}

@media (min-width: 992px) {
	.site-header .tt-hamburger {
		display: none;
	}
}

.site-header .tt-hamburger:focus {
	outline: 0;
}

.site-header .tt-hamburger .bar {
	background: #fff;
	height: 2px;
	display: block;
}

.site-header .tt-hamburger .bar:not(:last-child) {
	margin-bottom: 5px;
}

.site-header.header-1 .tt-hamburger .bar,
.site-header.header-8 .tt-hamburger .bar {
	background: #1c1e21;
}

.site-header .nav-right .search-btn {
	margin-right: 20px;
}

.site-header .nav-right .search-btn svg {
	width: 20px;
}

.site-header .nav-right .nav-link i {
	margin-right: 10px;
	text-transform: capitalize;
}

.site-header .nav-right.style2 .tt__btn {
	border-radius: 6px;
}

.site-header .nav-right .phone {
	color: #fff;
	font-size: 15px;
	font-weight: 500;
}

.site-header .nav-right .phone i {
	margin-right: 8px;
}

.site-header.showed {
	position: fixed;
	width: 100%;
	left: 0;
	top: 0;
	z-index: 9999;
	background: #fff;
	-webkit-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
	-webkit-animation: stickySlideDown 0.65s cubic-bezier(0.23, 1, 0.32, 1) both;
	animation: stickySlideDown 0.65s cubic-bezier(0.23, 1, 0.32, 1) both;
}

.header-position .site-header.showed {
	top: 32px;
}

@media (max-width: 782px) {
	.header-position .site-header.showed {
		top: 46px;
	}
}

.site-header.showed .site-logo .main-logo {
	display: none;
}

.site-header.showed .site-logo a {
	color: #1c1e21;
}

.site-header.showed .site-logo h3 {
	color: #1c1e21;
}

.site-header.showed .add-menu li a {
	color: #1c1e21;
}

.site-header.showed .tt-hamburger .bar {
	background: #1c1e21;
}

.site-header.showed .nav-right .phone {
	color: #1c1e21;
}

.site-header.showed .nav-right .phone:hover {
	color: #3283fd;
}

@media (min-width: 992px) {
	.site-header .container {
		position: relative;
	}
}

.site-header .nav-right {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

@media (max-width: 991px) {
	.site-header .nav-right {
		margin-left: 25px;
	}
}

.site-header .nav-right .nav-btn {
	padding: 11px 26px;
}

.site-header .nav-right .nav-link {
	padding: 0;
	margin-right: 30px;
	color: #1c1e21;
	font-weight: 500;
}

.site-header .nav-right .nav-link:hover {
	color: #3283fd;
}

.site-header .nav-right.style3 .tt__btn {
	border-radius: 6px;
	border-color: #fa5441;
}

.site-header .nav-right.style3 .tt__btn:before {
	background-color: #fa5441;
}

.site-header .nav-right.style3 .tt__btn:hover {
	border-color: rgba(255, 255, 255, 0.4);
	color: #fff;
}

.site-header.header-2:not(.showed) .nav-right .nav-link,
.site-header.header-2:not(.mobile-header) .nav-right .nav-link {
	color: #fff;
}

.site-header.header-2.mobile-header .nav-right .nav-link {
	color: #1c1e21;
}

.site-header.header-8 .nav-right .nav-link {
	border-radius: 4px;
}

.site-header:not(.mobile-header) .main-nav-container {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-left: 80px;
	-webkit-box-flex: 2;
	-ms-flex: 2;
	flex: 2;
}

.site-header:not(.mobile-header) .menu {
	margin: 0;
	padding: 0;
	list-style: none;
}

.site-header:not(.mobile-header) .menu>li {
	padding: 25px 0;
}

.site-header:not(.mobile-header) .menu>li>a {
	font-weight: 500;
	font-size: 15px;
	color: #fff;
	line-height: 1;
}

.site-header:not(.mobile-header) .menu>li>a:after {
	display: none;
	visibility: hidden;
	opacity: 0;
}

.site-header:not(.mobile-header) .menu>li>a:hover {
	color: rgba(255, 255, 255, 0.7);
}

.site-header:not(.mobile-header) .menu>li.current_page a {
	color: rgba(255, 255, 255, 0.7);
}

.site-header:not(.mobile-header) .menu>li:not(.mega-menu) {
	position: relative;
}

.menu-light .site-header:not(.mobile-header) .menu>li>a {
	color: #fff;
}

.site-header:not(.mobile-header) .menu li {
	display: inline-block;
}

.site-header:not(.mobile-header) .menu li i {
	display: none;
}

.site-header:not(.mobile-header) .menu li:not(:last-child) {
	margin-right: 35px;
}

@media (max-width: 1200px) {
	.site-header:not(.mobile-header) .menu li:not(:last-child) {
		margin-right: 25px;
	}
}

.site-header:not(.mobile-header) .menu li.has-submenu {
	position: relative;
}

.site-header:not(.mobile-header) .menu li.has-submenu i {
	position: absolute;
	top: 10px;
	right: 20px;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	font-size: 12px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.site-header:not(.mobile-header) .menu li.has-submenu .sub-menu {
	margin: 0;
	padding: 15px 0;
	position: absolute;
	top: 115%;
	left: 0;
	opacity: 0;
	visibility: hidden;
	background: #fff;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	border-radius: 4px;
	min-width: 220px;
	-webkit-box-shadow: 0px 20px 30px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 20px 30px 0px rgba(0, 9, 40, 0.1);
	z-index: 2222;
	display: block !important;
}

.site-header:not(.mobile-header) .menu li.has-submenu .sub-menu.sub-menu-wide {
	width: 100%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	padding: 40px 80px 25px;
}

.site-header:not(.mobile-header) .menu li.has-submenu .sub-menu.sub-menu-wide>.mega-menu-item>a.menu-link {
	font-size: 16px;
	text-transform: uppercase;
	font-weight: 500;
	display: inline-block;
	color: #1c1e21;
}

.site-header:not(.mobile-header) .menu li.has-submenu .sub-menu.sub-menu-wide>.mega-menu-item>a.menu-link:after {
	display: none;
}

.site-header:not(.mobile-header) .menu li.has-submenu .sub-menu.sub-menu-wide .sub-menu {
	position: unset;
	-webkit-box-shadow: unset;
	box-shadow: unset;
	visibility: unset;
	opacity: unset;
}

.site-header:not(.mobile-header) .menu li.has-submenu .sub-menu li {
	display: block;
	position: relative;
	margin-right: 0;
	line-height: 36px;
}

.site-header:not(.mobile-header) .menu li.has-submenu .sub-menu li a {
	font-size: 14px;
	padding: 7px 25px;
	display: block;
	line-height: 1.5;
	color: #0c1636;
	font-weight: 500;
}

.site-header:not(.mobile-header) .menu li.has-submenu .sub-menu li a:hover {
	color: #3283fd;
}

.site-header:not(.mobile-header) .menu li.has-submenu .sub-menu li ul {
	padding: 15px 0;
	position: absolute;
	left: 100%;
	top: 0;
	background: #fff;
	min-width: 220px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	opacity: 0;
	visibility: hidden;
	-webkit-box-shadow: 0 5px 10px #14303a15;
	box-shadow: 0 5px 10px #14303a15;
}

.site-header:not(.mobile-header) .menu li.has-submenu .sub-menu li.current-menu-item a {
	color: #3283fd;
}

.site-header:not(.mobile-header) .menu li.has-submenu .sub-menu li.has-submenu:hover ul {
	opacity: 1;
	visibility: visible;
	top: -15px;
}

.site-header:not(.mobile-header) .menu li.has-submenu:hover ul {
	opacity: 1;
	visibility: visible;
	top: 100%;
}

.site-header:not(.mobile-header).header-1 .menu>li>a,
.site-header:not(.mobile-header).header-8 .menu>li>a {
	color: #1c1e21;
}

.site-header:not(.mobile-header).header-1 .menu>li>a:hover,
.site-header:not(.mobile-header).header-8 .menu>li>a:hover {
	color: #3283fd;
}

.site-header:not(.mobile-header).header-5 .menu li a {
	color: #1c1e21;
}

.site-header:not(.mobile-header).header-5 .menu li a:hover {
	color: #fb514d;
}

.site-header:not(.mobile-header).header-5 .menu li.has-submenu .sub-menu li a:hover {
	color: #fb514d;
}

.site-header:not(.mobile-header).header-5 .nav-right .nav-link:hover {
	color: #fb514d;
}

.site-header:not(.mobile-header).header-5 .nav-right .nav-btn {
	border-color: #fb514d;
}

.site-header:not(.mobile-header).header-5 .nav-right .nav-btn:before {
	background-color: #fb514d;
}

.site-header:not(.mobile-header).header-5 .nav-right .nav-btn:hover {
	color: #fb514d;
}

.site-header:not(.mobile-header).header-6 .menu li a {
	color: #1c1e21;
}

.site-header:not(.mobile-header).header-6 .menu li a:hover {
	color: #089df1;
}

.site-header:not(.mobile-header).header-6 .menu li.has-submenu .sub-menu li a:hover {
	color: #089df1;
}

.site-header:not(.mobile-header).header-6 .nav-right .nav-btn {
	border-color: #089df1;
	border-radius: 0;
}

.site-header:not(.mobile-header).header-6 .nav-right .nav-btn:before {
	background-color: #089df1;
}

.site-header:not(.mobile-header).header-7 {
	background-color: #fff;
	-webkit-box-shadow: 0 10px 20px 0 rgba(0, 9, 40, 0.1);
	box-shadow: 0 10px 20px 0 rgba(0, 9, 40, 0.1);
}

.site-header:not(.mobile-header).header-7 .menu li a {
	color: #1c1e21;
}

.site-header:not(.mobile-header).header-7 .menu li a:hover {
	color: #089df1;
}

.site-header:not(.mobile-header).header-7 .menu li.has-submenu .sub-menu li a:hover {
	color: #089df1;
}

.site-header:not(.mobile-header).header-7 .nav-right .nav-btn {
	border-color: #089df1;
	border-radius: 0;
}

.site-header:not(.mobile-header).header-7 .nav-right .nav-btn:before {
	background-color: #089df1;
}

.site-header:not(.mobile-header).header-4 .main-nav-container {
	-webkit-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end;
}

.site-header:not(.mobile-header).header-4 .menu-wrapper {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.site-header:not(.mobile-header).header-4 .nav-right {
	margin-left: 50px;
}

.site-header:not(.mobile-header).header-4 .nav-right.style3 .tt__btn {
	border-color: #f63a6f;
}

.site-header:not(.mobile-header).header-4 .nav-right.style3 .tt__btn:before {
	background-color: #f63a6f;
}

.site-header:not(.mobile-header).header-4 .nav-right.style3 .tt__btn:hover {
	border-color: rgba(255, 255, 255, 0.4);
	color: #fff;
}

.site-header:not(.mobile-header).showed .menu li a.current_page {
	color: #3283fd;
}

.site-header:not(.mobile-header).showed.header-4 .menu li a:hover {
	color: #f63a6f;
}

.site-header:not(.mobile-header).showed.header-4 .menu li.has-submenu .sub-menu li a:hover {
	color: #f63a6f;
}

.site-header:not(.mobile-header).showed.header-4 .nav-right.style3 .tt__btn:hover {
	border-color: #f63a6f;
	color: #f63a6f;
}

.single-portfolio .site-header:not(.mobile-header) .menu li a {
	color: #1c1e21;
}

.single-portfolio .site-header:not(.mobile-header) .menu li a:hover {
	color: #3283fd;
}

.single-portfolio .site-header:not(.mobile-header) .nav-right .nav-link {
	color: #1c1e21;
}

.single-portfolio .site-header:not(.mobile-header) .nav-right .nav-link:hover {
	color: #3283fd;
}

.single-portfolio .site-header:not(.mobile-header) .tt-hamburger .bar {
	background: #1c1e21;
}

.header-static .site-header .menu>li a {
	color: #0c1636;
}

.header-static .site-header .menu>li a:hover {
	color: #3283fd;
}

.header-static .site-header:not(.mobile-header) .menu>li.current_page_item a,
.header-static .site-header:not(.mobile-header) .menu>li.current-menu-parent a {
	color: #3283fd;
}

.header-static .site-header:not(.showed) .nav-right .nav-link {
	color: #0c1636;
}

.header-static .site-header:not(.showed) .nav-right .nav-link:hover {
	color: #3283fd;
}

.header-static .site-header .site-logo a {
	color: #1c1e21;
}

.header-static .site-header .site-logo a h3 {
	color: #1c1e21;
}

.header-static .site-header .site-logo .main-logo {
	display: none;
}

.header-static .site-header .site-logo .logo-sticky {
	display: block;
}

.header-static .site-header .tt-hamburger .bar {
	background: #1c1e21;
}

.header-static .site-header:not(.showed) .nav-right.style2 .tt__btn {
	color: #fff;
	border-color: #3283fd;
}

.header-static .site-header:not(.showed) .nav-right.style2 .tt__btn:before {
	background-color: #3283fd;
}

.header-static .site-header:not(.showed) .nav-right.style2 .tt__btn:hover {
	color: #3283fd;
	border-color: #3283fd;
}

.site-header:not(.showed) .site-logo .sticky-logo {
	display: block;
}

.site-header:not(.showed) .nav-right.style2 .tt__btn {
	border-color: #fff;
	color: #3283fd;
}

.site-header:not(.showed) .nav-right.style2 .tt__btn:before {
	background-color: #fff;
}

@media (max-width: 991px) {
	.site-header:not(.showed) .nav-right.style2 .tt__btn {
		color: #fff;
		border-color: #3283fd;
	}

	.site-header:not(.showed) .nav-right.style2 .tt__btn:before {
		background-color: #3283fd;
	}
}

.site-header:not(.showed) .nav-right.style2 .tt__btn:hover {
	color: #fff;
	border-color: rgba(255, 255, 255, 0.4);
}

@media (max-width: 991px) {
	.site-header:not(.showed) .nav-right.style2 .tt__btn:hover {
		color: #3283fd;
		border-color: #3283fd;
	}
}

.site-header.showed {
	position: fixed;
	width: 100%;
	left: 0;
	top: 0;
	z-index: 999;
	background: #fff;
	-webkit-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
	-webkit-animation: stickySlideDown 0.65s cubic-bezier(0.23, 1, 0.32, 1) both;
	animation: stickySlideDown 0.65s cubic-bezier(0.23, 1, 0.32, 1) both;
}

.header-position .site-header.showed {
	top: 32px;
}

@media (max-width: 782px) {
	.header-position .site-header.showed {
		top: 46px;
	}
}

.header-position .site-header.showed .fullscreen-menu {
	top: 0;
}

.site-header.showed .site-logo .main-logo {
	display: none;
}

.site-header.showed .site-logo .logo-sticky {
	display: block;
}

.site-header.showed .menu-trigger .dot_icon .dot {
	background: #1c1e21;
}

.site-header.showed .nav-right.style2 .tt__btn {
	border-color: #3283fd;
	color: #fff;
}

.site-header.showed .nav-right.style2 .tt__btn:before {
	background-color: #3283fd;
}

.site-header.showed .nav-right.style2 .tt__btn:hover {
	color: #3283fd;
}

.site-header.showed .nav-right.style2 .nav-link {
	color: #1c1e21;
}

.site-header.showed .nav-right.style2 .nav-link:hover {
	color: #3283fd;
}

.site-header.showed .nav-right.style3 .tt__btn:hover {
	border-color: #fa5441;
	color: #fa5441;
}

.site-header.showed .menu li a {
	color: #1c1e21;
}

.site-header.showed .menu li a:hover {
	color: #3283fd;
}

.single-portfolio .site-header:not(.showed) .nav-right.style2 .tt__btn {
	border-color: #3283fd;
	color: #fff;
}

.single-portfolio .site-header:not(.showed) .nav-right.style2 .tt__btn:before {
	background-color: #3283fd;
}

.single-portfolio .site-header:not(.showed) .nav-right.style2 .tt__btn:hover {
	color: #3283fd;
}

.single-portfolio .site-header .site-logo .logo-sticky {
	display: block;
}

.single-portfolio .site-header .site-logo .main-logo {
	display: none;
}

.single-portfolio .site-header .tt-hamburger .bar {
	background: #1c1e21;
}

@-webkit-keyframes stickySlideDown {
	from {
		-webkit-transform: translateY(-100%);
		transform: translateY(-100%);
	}

	to {
		-webkit-transform: translateY(0);
		transform: translateY(0);
	}
}

@keyframes stickySlideDown {
	from {
		-webkit-transform: translateY(-100%);
		transform: translateY(-100%);
	}

	to {
		-webkit-transform: translateY(0);
		transform: translateY(0);
	}
}

.menu-trigger {
	overflow: hidden;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	cursor: pointer;
	background-color: transparent;
	border: 0px solid transparent;
}

@media (max-width: 991px) {
	.menu-trigger {
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		display: block !important;
	}
}

.menu-trigger .dot_icon {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	width: 24px;
	height: 24px;
	margin: -2px;
	overflow: hidden;
}

.menu-trigger .dot_icon .dot {
	width: 4px;
	height: 4px;
	background: #1c1e21;
	margin: 2px;
	-webkit-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	transition: all 0.3s linear;
}

.menu-light .menu-trigger .dot_icon .dot {
	background: #fff;
}

.menu-trigger:hover .dot_icon .dot:nth-child(odd) {
	-webkit-transform: translate(8px, 8px);
	-ms-transform: translate(8px, 8px);
	transform: translate(8px, 8px);
}

.menu-trigger:hover .dot_icon .dot:nth-child(5) {
	opacity: 0;
}

#site-navigation {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

#site-navigation.menu-position-right {
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

.site-header:not(.mobile-header) #site-navigation.menu-position-right .main-nav-container {
	-webkit-box-flex: inherit;
	-ms-flex: inherit;
	flex: inherit;
}

@media (min-width: 992px) {
	.close-menu {
		display: none;
	}
}

.close-menu {
	height: 50px;
	width: 50px;
	background: rgba(136, 136, 136, 0.1);
	border-radius: 50%;
	text-align: center;
	line-height: 54px;
	position: absolute;
	top: 25px;
	left: 20px;
	color: #051441;
	font-size: 22px;
}

.menu {
	list-style: none;
}

.menu ul {
	list-style: none;
}

/*--------------------------------------------------------------
### Main Nav Mobile
--------------------------------------------------------------*/
.mobile-header {
	height: auto;
	/*--------------------------------------------------------------
	### Main Nav
	--------------------------------------------------------------*/
}

.mobile-header .main-nav-container {
	display: block;
}

.mobile-header .site-logo .logo-sticky {
	display: none;
}

.mobile-header .tt-hamburger {
	display: block;
}

.mobile-header.header-5 .tt-hamburger .bar,
.mobile-header.header-7 .tt-hamburger .bar {
	background: #1c1e21;
}

.mobile-header.header-fixed.showed .logo-sticky {
	display: block;
}

.mobile-header.header-fixed.showed .main-nav-container .menu-item-depth-0>a.active {
	color: #fff;
}

.mobile-header .site-logo a {
	padding: 13px 0;
}

.mobile-header .nav-right.style3 .tt__btn:hover {
	border-color: #fa5441;
	color: #fa5441;
}

.mobile-header .main-nav-container {
	position: fixed;
	top: 0;
	height: 100vh;
	background: #fff;
	max-width: 400px;
	width: 100%;
	text-align: left;
	overflow-y: auto;
	padding: 94px 0;
	left: -100px;
	-webkit-transition-duration: 0.3s;
	-o-transition-duration: 0.3s;
	transition-duration: 0.3s;
	opacity: 0;
	visibility: hidden;
	z-index: 999;
}

.header-position .mobile-header .main-nav-container {
	top: 32px;
}

@media (max-width: 780px) {
	.header-position .mobile-header .main-nav-container {
		top: 46px;
	}
}

@media (max-width: 420px) {
	.mobile-header .main-nav-container {
		max-width: 100%;
	}
}

.mobile-header .main-nav-container.open {
	opacity: 1;
	left: 0;
	visibility: visible;
}

.mobile-header .main-nav-container li {
	border-bottom: 1px solid #edeef1;
}

.mobile-header .main-nav-container li a {
	font-size: 15px;
	color: #1c1e21;
	position: relative;
	z-index: 1;
	padding: 11px 0;
	display: block;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	text-transform: capitalize;
}

.mobile-header .main-nav-container li.current-menu-parent a {
	color: #3283fd;
}

.mobile-header .main-nav-container li.current-menu-item a {
	color: #3283fd;
}

.mobile-header .main-nav-container li .sub-menu li {
	border: 0;
}

.mobile-header .main-nav-container li .sub-menu li.current-menu-item a {
	color: #3283fd;
}

.mobile-header .main-nav-container li .sub-menu li a {
	padding: 9px 0;
}

.mobile-header .main-nav-container li .sub-menu li:last-child {
	margin-bottom: 10px;
}

.mobile-header .main-nav-container li .sub-menu li ul li:last-child {
	margin-bottom: 0;
}

.mobile-header .main-nav-container li.has-submenu {
	position: relative;
}

.mobile-header .main-nav-container li.has-submenu>a {
	position: relative;
}

.mobile-header .main-nav-container li.has-submenu>a.active:after {
	content: "";
}

.mobile-header .main-nav-container li.has-submenu i {
	position: absolute;
	top: 12px;
	right: 0;
	z-index: 2;
	font-size: 15px;
	width: 30px;
	text-align: right;
	height: 20px;
	color: #52525c;
	display: block;
}

.mobile-header .main-nav-container li.has-submenu li a {
	font-size: 14px;
	color: #4f5158;
}

.mobile-header .main-nav-container .menu-item-depth-0>a.active {
	color: #fff;
	background: #3283fd;
}

.header-position .mobile-header.header-fixed.showed .main-nav-container {
	top: 0;
}

.mobile-header .main-nav {
	position: static;
	z-index: 3333;
	line-height: 1.2;
}

.mobile-header .main-nav .menu {
	padding: 0 30px;
}

.mobile-header .main-nav .sub-menu {
	padding-left: 15px;
	margin-left: 0;
	margin-bottom: 0;
	display: none;
}

.mobile-header .main-nav .sub-menu.active .main-item>.menu-link {
	background: #f8f9fa;
}

.mobile-header .main-nav .sub-menu li a {
	border-top: 0;
}

.mobile-header .main-nav .sub-menu li.active a.active .ti-plus {
	color: #3283fd;
}

.mobile-header .main-nav .sub-menu li.current-menu-item a.active {
	color: #3283fd;
}

.mobile-header .main-nav .nav-item.active-main-item>.menu-link {
	color: #3283fd;
	background: #f8f9fa;
	border-color: #3283fd;
}

.mobile-header .main-nav .sub-menu {
	padding-left: 20px;
	margin-bottom: 0;
}

.mobile-header .main-nav {
	cursor: default;
	position: relative;
	z-index: 10;
	text-align: left;
	font-weight: 500;
	height: 100%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

.mobile-header .main-nav .close-bar {
	display: none;
}

.mobile-header .main-nav .site-logo a {
	font-size: 40px;
}

.mobile-header .main-nav .menu,
.mobile-header .main-nav .menu ul {
	list-style: none;
}

.mobile-header .main-nav .nav-item:before,
.mobile-header .main-nav .nav-item.current-menu-item:before,
.mobile-header .main-nav .nav-item.current-menu-ancestor:before {
	background-color: #3283fd;
}

.mobile-header .main-nav .nav-item.active-main-item>.menu-link {
	color: #3283fd;
}

.mobile-header .main-nav.hidden {
	display: none;
}

@media (max-width: 991px) {
	.mobile-header .main-nav .close-menu {
		display: block;
	}
}

.mask-overlay {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 222;
}

/*--------------------------------------------------------------
  ##  Banner
  --------------------------------------------------------------*/
.banner {
	height: 950px;
	position: relative;
	overflow: hidden;
	background-color: #f7f8fb;
}

@media (max-width: 1024px) {
	.banner {
		height: auto;
		padding: 140px 0 80px;
	}
}

.banner__content,
.banner__feature-image {
	position: relative;
	z-index: 3;
}

.banner__feature-image {
	position: relative;
	z-index: 4;
}

.banner__feature-image img {
	position: relative;
	z-index: 2;
}

@media (max-width: 1024px) {
	.banner__feature-image {
		margin-bottom: 50px;
	}
}

.banner__title {
	font-size: 56px;
	font-weight: 900;
	color: #1c1e21;
	margin-bottom: 10px;
	overflow: hidden;
	display: inline-block;
	padding-right: 0.05em;
}

@media (max-width: 1200px) {
	.banner__title {
		font-size: 50px;
	}
}

@media (max-width: 991px) {
	.banner__title {
		font-size: 40px;
	}

	.banner__title br {
		display: none;
	}
}

@media (max-width: 767px) {
	.banner__title {
		font-size: 34px;
	}
}

.banner__title .text-wrapper {
	position: relative;
	display: inline-block;
}

.banner__title .text-wrapper .letter {
	display: inline-block;
	line-height: 1em;
}

.banner__subtitle {
	font-size: 12px;
	text-transform: uppercase;
	font-weight: 500;
	color: #1c1e21;
	margin-bottom: 5px;
}

.banner__description {
	margin-bottom: 43px;
	font-size: 18px;
	line-height: 26px;
	color: #4f5158;
}

@media (max-width: 1200px) {
	.banner__description br {
		display: none;
	}
}

.banner__btns .banner-btn {
	padding: 14px 30px;
}

.banner__newsletter-form {
	max-width: 420px;
}

@media (max-width: 991px) {
	.banner__newsletter-form {
		margin: 0 auto;
	}
}

.banner__newsletter-form .newsletter-inner {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	border: 2px solid #3283fd;
	border-radius: 30px;
	overflow: hidden;
}

.banner__newsletter-form .newsletter-inner input {
	background-color: transparent !important;
	border: 0 !important;
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
	margin-bottom: 0 !important;
	height: 50px;
	border-radius: 30px !important;
}

.banner__newsletter-form .newsletter-inner input::-webkit-input-placeholder {
	font-size: 14px;
	color: #7e8086;
}

.banner__newsletter-form .newsletter-inner input::-moz-placeholder {
	font-size: 14px;
	color: #7e8086;
}

.banner__newsletter-form .newsletter-inner input:-ms-input-placeholder {
	font-size: 14px;
	color: #7e8086;
}

.banner__newsletter-form .newsletter-inner input::-ms-input-placeholder {
	font-size: 14px;
	color: #7e8086;
}

.banner__newsletter-form .newsletter-inner input::placeholder {
	font-size: 14px;
	color: #7e8086;
}

.banner__newsletter-form .form-result {
	margin-bottom: 0;
	margin-top: 20px;
}

.banner__newsletter-form .newsletter-submit {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
	outline: 0;
	background-color: #3283fd;
	border: 0;
	min-width: 124px;
}

.banner__newsletter-form .newsletter-submit i {
	display: none;
}

.banner__newsletter-form .newsletter-submit.clicked i {
	display: block;
}

.banner__newsletter-form .newsletter-submit.clicked span {
	display: none;
}

.banner__newsletter-form .newsletter-submit:hover {
	background-color: #1974fd;
	color: #fff;
}

.banner .shape-image {
	position: absolute;
	top: -16%;
	left: 50px;
	z-index: 0;
}

@media (max-width: 767px) {
	.banner .shape-image {
		left: 0;
	}
}

.banner__shape {
	position: absolute;
}

.banner__feature {
	margin: 17px 0 0;
	padding: 0;
	list-style: none;
}

.banner__feature li {
	display: inline-block;
	font-size: 14px;
	color: #4f5158;
}

.banner__feature li:not(:last-child) {
	margin-right: 30px;
}

.banner__feature li i {
	display: inline-block;
	text-align: center;
	font-size: 14px;
	height: 20px;
	width: 20px;
	line-height: 20px;
	border-radius: 50%;
	background-color: rgba(42, 92, 255, 0.102);
	margin-right: 10px;
	color: #3283fd;
}

.banner__image-lists {
	margin: 0;
	padding: 0;
	list-style: none;
	text-align: center;
	max-width: 712px;
	position: relative;
}

@media (max-width: 991px) {
	.banner__image-lists {
		max-width: 500px;
		margin: 0 auto;
	}
}

@media (max-width: 767px) {
	.banner__image-lists {
		max-width: 300px;
		margin: 0 auto;
	}
}

@media (max-width: 1200px) {
	.banner__image-lists li:nth-child(1) {
		margin: 0 auto;
		max-width: 400px;
	}
}

@media (max-width: 991px) {
	.banner__image-lists li:nth-child(1) {
		margin: 0 auto;
		max-width: 360px;
	}
}

.banner__image-lists li:not(:first-child) {
	position: absolute;
}

.banner__image-lists li img {
	border-radius: 6px;
	background-color: white;
	-webkit-box-shadow: 0px 40px 70px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 40px 70px 0px rgba(0, 9, 40, 0.1);
}

.banner__image-lists li:nth-child(2) {
	top: 70px;
	left: 55px;
}

.banner__image-lists li:nth-child(3) {
	bottom: -100px;
	left: 7px;
}

.banner__image-lists li:nth-child(4) {
	right: 0;
	top: 30%;
}

.banner__image-lists li:nth-child(5) {
	right: 74px;
	bottom: -35px;
}

.banner__feature-multiimage {
	max-width: 710px;
	position: relative;
}

@media (max-width: 991px) {
	.banner__feature-multiimage {
		max-width: 500px;
		margin: 0 auto 50px;
		min-height: -webkit-max-content;
		min-height: -moz-max-content;
		min-height: max-content;
	}
}

.banner__feature-multiimage.image-two {
	top: 0;
}

@media (max-width: 991px) {
	.banner__feature-multiimage li:nth-child(2) {
		top: 15px;
		left: 30px;
	}

	.banner__feature-multiimage li:nth-child(3) {
		top: 200px;
		left: -20px;
		width: 165px;
	}

	.banner__feature-multiimage li:nth-child(4) {
		top: 20px;
		width: 140px;
	}

	.banner__feature-multiimage li:nth-child(5) {
		top: 210px;
		left: 365px;
		width: 130px;
	}
}

@media (max-width: 767px) {
	.banner__feature-multiimage li:nth-child(2) {
		top: 20px;
		left: -15px;
		width: 60px;
	}

	.banner__feature-multiimage li:nth-child(3) {
		top: 180px;
		left: -20px;
		width: 110px;
	}

	.banner__feature-multiimage li:nth-child(4) {
		left: 210px;
		width: 100px;
	}

	.banner__feature-multiimage li:nth-child(5) {
		top: 190px;
		left: 225px;
		width: 95px;
	}
}

.banner .animate-element {
	margin: 0;
	padding: 0;
	list-style: none;
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
}

@media (max-width: 576px) {
	.banner .animate-element {
		display: none;
	}
}

.banner .animate-element li {
	position: absolute;
	height: 100%;
	width: 100%;
}

.banner .animate-element li .inner {
	position: absolute;
}

.banner .animate-element li:nth-child(1) .inner {
	top: 26%;
	left: 10%;
}

@media (max-width: 991px) {
	.banner .animate-element li:nth-child(1) .inner {
		left: 5%;
	}
}

.banner .animate-element li:nth-child(2) .inner {
	bottom: 180px;
	left: 100px;
}

@media (max-width: 991px) {
	.banner .animate-element li:nth-child(2) .inner {
		left: 30px;
	}
}

.banner .animate-element li:nth-child(3) .inner {
	bottom: 150px;
	right: 25%;
}

@media (max-width: 991px) {
	.banner .animate-element li:nth-child(3) .inner {
		right: 7%;
	}
}

@media (max-width: 576px) {
	.banner .animate-element.banner-two-element {
		display: none;
	}
}

.banner .animate-element.banner-two-element li:nth-child(1) .inner {
	top: 120px;
	left: -50px;
}

@media (max-width: 991px) {
	.banner .animate-element.banner-two-element li:nth-child(1) .inner {
		width: 120px;
	}
}

.banner .animate-element.banner-two-element li:nth-child(2) .inner {
	top: 47%;
	left: 10%;
}

@media (max-width: 991px) {
	.banner .animate-element.banner-two-element li:nth-child(2) .inner {
		left: 4%;
	}
}

.banner .animate-element.banner-two-element li:nth-child(3) .inner {
	bottom: 160px;
	left: 90px;
}

@media (max-width: 991px) {
	.banner .animate-element.banner-two-element li:nth-child(3) .inner {
		left: 10px;
		width: 45px;
	}
}

.banner .animate-element.banner-two-element li:nth-child(4) .inner {
	bottom: 230px;
	right: 0;
}

.banner.banner--two {
	background-color: #832fff;
	height: 100vh;
	padding: 160px 0 150px;
	overflow: hidden;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

@media (max-width: 991px) {
	.banner.banner--two {
		height: auto;
		padding: 120px 0 100px;
	}
}

.banner.banner--two .banner__feature-image {
	margin-right: -220px;
	position: relative;
	z-index: 2;
}

@media (max-width: 1024px) {
	.banner.banner--two .banner__feature-image {
		margin-right: 0;
	}
}

.banner.banner--two .banner__subtitle {
	color: rgba(255, 255, 255, 0.8);
	letter-spacing: 1px;
}

.banner.banner--two .banner__title {
	font-weight: 800;
	color: #fff;
	margin-bottom: 10px;
}

.banner.banner--two .banner__description {
	color: rgba(255, 255, 255, 0.8);
	font-size: 18px;
	line-height: 26px;
}

.banner.banner--two .banner__shape {
	left: 29px;
	bottom: -55px;
}

@media (max-width: 767px) {
	.banner.banner--two .banner__shape {
		left: 0;
		bottom: -20px;
	}
}

@media (max-width: 991px) {
	.banner.banner--two .banner__shape svg {
		max-width: 530px;
	}
}

.banner__counter {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	margin-bottom: 50px;
}

.banner__counter__item {
	margin-right: 50px;
}

.banner__counter__count {
	color: #fff;
	font-size: 36px;
	font-weight: 700;
	margin-bottom: 0;
	line-height: 1.5;
}

.banner__counter__count .suffix_title {
	font-size: 14px;
	font-weight: 400;
}

.banner__counter__title {
	color: #fff;
	font-size: 12px;
	font-weight: 500;
	text-transform: uppercase;
	margin: 0;
	line-height: 1.2;
}

.banner--one {
	margin-top: -32px;
}

.banner--one .banner__content {
	z-index: 5;
}

@media (max-width: 991px) {
	.banner--one .banner__content {
		text-align: center;
	}
}

.banner--one .banner__feature-image {
	margin-right: -155px;
}

@media (max-width: 991px) {
	.banner--one .banner__feature-image {
		margin: 0;
	}
}

.banner--three {
	background-image: linear-gradient(21deg, #4D82FF 10%, #E269DF 40%, #FBC065 100%);
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	position: relative;
	overflow: visible;
	height: 1070px;
}

@media (max-width: 1400px) {
	.banner--three {
		height: 800px;
	}
}

@media (max-width: 991px) {
	.banner--three {
		height: auto;
		padding: 140px 0 130px;
		overflow: hidden;
	}
}

.banner--three:after {
	content: "";
	position: absolute;
	bottom: 0;
	right: 0;
	width: 0;
	height: 0;
	border-top: 200px solid transparent;
	border-right: 1920px solid #fff;
	border-bottom: 0 solid #fff;
}

@media (max-width: 991px) {
	.banner--three:after {
		border-top: 60px solid transparent;
	}
}

.banner--three .banner__content {
	margin-top: -45px;
}

.banner--three .banner__title {
	font-weight: 800;
	color: #fff;
	margin-bottom: 15px;
}

.banner--three .banner__description {
	color: rgba(255, 255, 255, 0.8);
	font-size: 18px;
	line-height: 26px;
	margin-bottom: 22px;
}

.banner--three .banner__btns {
	margin-top: 39px;
}

.banner--three .banner__btns .banner-btn {
	padding: 12px 37px;
}

.banner--three .banner__feature-image {
	bottom: -190px;
}

@media (max-width: 1400px) {
	.banner--three .banner__feature-image {
		bottom: -100px;
	}

	.banner--three .banner__feature-image .banner__image-three {
		width: 700px;
	}

	.banner--three .banner__feature-image .banner__image-four {
		width: 290px;
	}
}

@media (max-width: 576px) {
	.banner--three .banner__feature-image .banner__image-three {
		width: 280px;
	}
}

@media (max-width: 1200px) {
	.banner--three .banner__feature-image .banner__image-four {
		width: 270px;
		right: 0;
	}
}

@media (max-width: 576px) {
	.banner--three .banner__feature-image .banner__image-four {
		width: 240px;
		right: -90px;
	}
}

@media (max-width: 991px) {
	.banner--three .banner__feature-image {
		bottom: 0;
		max-width: 500px;
		margin: 0 auto 30px;
	}
}

@media (max-width: 576px) {
	.banner--three .banner__feature-image {
		width: 330px;
	}
}

@media (max-width: 420px) {
	.banner--three .banner__feature-image {
		width: 300px;
		margin-left: 0px;
	}

	.banner--three .banner__feature-image .banner__image-three {
		width: 220px;
	}

	.banner--three .banner__feature-image .banner__image-four {
		width: 175px;
		right: 0;
	}
}

.banner--three .banner__shape {
	left: 30px;
	width: 605px;
	bottom: 0;
}

@media (max-width: 1400px) {
	.banner--three .banner__shape {
		width: 470px;
	}
}

@media (max-width: 576px) {
	.banner--three .banner__shape {
		width: 300px;
	}
}

.banner--three .shape__item-one,
.banner--three .shape__item-two {
	position: absolute;
}

.banner--three .shape__item-one {
	right: -80px;
	top: 12%;
}

@media (max-width: 1400px) {
	.banner--three .shape__item-one {
		right: 0;
	}
}

@media (max-width: 576px) {
	.banner--three .shape__item-one {
		display: none;
	}
}

.banner--three .shape__item-two {
	left: -10px;
	bottom: 51%;
}

@media (max-width: 576px) {
	.banner--three .shape__item-two {
		display: none;
	}
}

.banner--three .shape-left {
	position: absolute;
	left: 0;
	top: 230px;
}

.banner__feature-list {
	margin: 0;
	padding: 0;
	list-style: none;
}

.banner__feature-list li {
	color: rgba(254, 254, 254, 0.702);
	font-size: 16px;
	line-height: 34px;
}

.banner__feature-list li i {
	margin-right: 7px;
}

.banner__image-three {
	position: relative;
	z-index: 2;
}

.banner__image-four {
	position: absolute !important;
	right: -88px;
	z-index: 1 !important;
	top: -30px;
}

@media (max-width: 1400px) {
	.banner__image-four {
		right: 100px;
		top: -20px;
	}
}

.banner--four {
	height: 1050px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	background-color: transparent;
	background-image: url(../../media/banner/banner-four/banner_bg_four.png);
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
}

.banner--four .banner__title {
	color: #fff;
	font-size: 60px;
	font-weight: 800;
	line-height: 1.15;
	margin-bottom: 12px;
}

@media (max-width: 1200px) {
	.banner--four .banner__title {
		font-size: 50px;
	}
}

@media (max-width: 767px) {
	.banner--four .banner__title {
		font-size: 40px;
	}
}

@media (max-width: 576px) {
	.banner--four .banner__title {
		font-size: 32px;
	}
}

.banner--four .banner__description {
	color: #fff;
	margin-bottom: 32px;
}

.banner--four .banner-wave-shape {
	position: absolute;
	bottom: 0;
	left: 0;
}

@media (max-width: 991px) {
	.banner--four {
		height: auto;
	}

	.banner--four .banner__feature-image {
		max-width: 500px;
		margin: 0 auto 50px;
		padding: 0 30px;
	}

	.banner--four .banner__content {
		text-align: center;
	}

	.banner--four .banner__counter {
		width: -webkit-max-content;
		width: -moz-max-content;
		width: max-content;
		margin: 0 auto 50px;
	}
}

@media (max-width: 1024px) {
	.banner.style-five {
		padding: inherit;
	}
}

.banner--six {
	background-color: transparent;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
}

@media (max-width: 1400px) {
	.banner--six {
		height: 700px;
	}
}

@media (max-width: 991px) {
	.banner--six {
		height: auto;
	}
}

.banner--six .bottom-shape {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
}

.banner--six .banner__subtitle {
	font-size: 16px;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.banner--six .banner__title {
	color: #1a82f5;
	font-size: 60px;
}

@media (max-width: 1200px) {
	.banner--six .banner__title {
		font-size: 50px;
	}
}

@media (max-width: 991px) {
	.banner--six .banner__title {
		font-size: 40px;
	}
}

@media (max-width: 767px) {
	.banner--six .banner__title {
		font-size: 34px;
	}
}

.banner--six .banner__feature-image {
	margin-right: -130px;
}

@media (max-width: 1400px) {
	.banner--six .banner__feature-image {
		margin-right: 0;
	}
}

.banner--six .tt__btn {
	border-color: #fb514d;
}

.banner--six .tt__btn:before {
	background-color: #fb514d;
}

.banner--six .tt__btn:hover {
	color: #fb514d;
}

.banner--seven {
	background-color: #f0f1f5;
	overflow: hidden;
}

@media (max-width: 1200px) {
	.banner--seven {
		height: 750px;
	}
}

@media (max-width: 991px) {
	.banner--seven {
		height: auto;
	}
}

.banner--seven .banner__content {
	margin-top: 90px;
}

@media (max-width: 991px) {
	.banner--seven .banner__content {
		max-width: 550px;
		margin: 40px auto 0;
		text-align: center;
	}
}

.banner--seven .banner__feature-image {
	margin: 0 -230px -175px -70px;
}

@media (max-width: 1200px) {
	.banner--seven .banner__feature-image {
		margin: 0 -150px -215px 0px;
	}
}

@media (max-width: 991px) {
	.banner--seven .banner__feature-image {
		margin: 0 auto;
		max-width: 500px;
	}
}

.banner--seven .banner__title {
	margin-bottom: 13px;
}

.banner--seven .banner__title span {
	font-weight: 300;
	display: block;
}

@media (max-width: 1200px) {
	.banner--seven .banner__title {
		font-size: 44px;
	}
}

@media (max-width: 576px) {
	.banner--seven .banner__title {
		font-size: 32px;
	}
}

.banner--seven .banner-btn {
	border-radius: 0;
	font-size: 15px;
	font-weight: 500;
	padding: 14px 28px;
	border-color: #089df1;
}

.banner--seven .banner-btn:before {
	background-color: #089df1;
}

.banner--seven .banner-btn i {
	margin-left: 0;
	margin-right: 10px;
	font-size: 14px;
}

.banner--seven .banner-btn.btn-outline {
	color: #11266d;
	border-color: #dbdde3;
	margin-left: 15px;
}

.banner--seven .banner-btn.btn-outline:hover {
	border-color: #089df1;
	color: #fff;
}

.banner--seven .banner-btn:hover {
	color: #089df1;
}

.banner--seven .banner-bg-shape {
	margin: 0;
	padding: 0;
	list-style: none;
}

.banner--seven .banner-bg-shape li {
	position: absolute;
}

.banner--seven .banner-bg-shape li:nth-child(1) {
	left: 0;
	top: 100px;
}

.banner--seven .banner-bg-shape li:nth-child(2) {
	left: 13%;
	top: 29%;
}

.banner--seven .banner-bg-shape li:nth-child(3) {
	left: 45%;
	bottom: 150px;
}

.banner--seven .banner-bg-shape li:nth-child(4) {
	right: 120px;
	top: 30%;
}

.banner--seven .banner-bg-shape li:nth-child(5) {
	right: -55px;
	bottom: 40%;
}

.banner--eight {
	background-color: #eff2f8;
	overflow: hidden;
}

@media (max-width: 1200px) {
	.banner--eight {
		height: 750px;
	}
}

@media (max-width: 991px) {
	.banner--eight {
		height: auto;
	}
}

.banner--eight .banner__content {
	margin-top: 90px;
}

@media (max-width: 991px) {
	.banner--eight .banner__content {
		max-width: 550px;
		margin: 40px auto 0;
		text-align: center;
	}
}

.banner--eight .banner__feature-image {
	margin: 0 -170px -185px 0;
	position: relative;
}

@media (max-width: 1200px) {
	.banner--eight .banner__feature-image {
		margin: 0 -150px -215px 0px;
	}
}

@media (max-width: 991px) {
	.banner--eight .banner__feature-image {
		margin: 0 auto;
		max-width: 500px;
	}
}

.banner--eight .banner__title {
	margin-bottom: 13px;
}

.banner--eight .banner__title span {
	font-weight: 300;
	color: #285dfc;
}

@media (max-width: 1200px) {
	.banner--eight .banner__title {
		font-size: 44px;
	}
}

@media (max-width: 576px) {
	.banner--eight .banner__title {
		font-size: 32px;
	}
}

.banner--eight .course-search-form {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	background-color: #fff;
	border-left: 4px solid #285dfc;
	-webkit-box-shadow: 0 30px 50px 0 rgba(0, 9, 40, 0.06);
	box-shadow: 0 30px 50px 0 rgba(0, 9, 40, 0.06);
	max-width: 470px;
}

@media (max-width: 991px) {
	.banner--eight .course-search-form {
		max-width: 100%;
	}
}

.banner--eight .course-search-form .search-field-wrap {
	position: relative;
	width: 100%;
}

.banner--eight .course-search-form .feather-search {
	position: absolute;
	left: 15px;
	top: 49%;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	color: #77797e;
	font-size: 20px;
}

.banner--eight .course-search-form input {
	border: 0;
	background-color: transparent;
	width: 100%;
	padding: 10px 30px 10px 40px;
	font-size: 15px;
	height: 70px;
}

.banner--eight .course-search-form input::-webkit-input-placeholder {
	color: #77797e;
}

.banner--eight .course-search-form input::-moz-placeholder {
	color: #77797e;
}

.banner--eight .course-search-form input:-ms-input-placeholder {
	color: #77797e;
}

.banner--eight .course-search-form input::-ms-input-placeholder {
	color: #77797e;
}

.banner--eight .course-search-form input::placeholder {
	color: #77797e;
}

.banner--eight .course-search-form button {
	background-color: transparent;
	padding: 0;
	border: 0;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	color: #285dfc;
	font-weight: 500;
	font-size: 15px;
	min-width: 120px;
}

.banner--eight .course-search-form button i {
	margin-left: 3px;
}

.banner--eight .banner-bg-shape {
	margin: 0;
	padding: 0;
	list-style: none;
}

.banner--eight .banner-bg-shape li {
	position: absolute;
}

.banner--eight .banner-bg-shape li:nth-child(1) {
	left: 0;
	top: 50%;
}

.banner--eight .banner-bg-shape li:nth-child(2) {
	left: 150px;
	top: 31%;
}

.banner--eight .banner-bg-shape li:nth-child(3) {
	right: 170px;
	top: 31%;
}

.banner--eight .banner-bg-shape li:nth-child(4) {
	right: 120px;
	bottom: 90px;
}

.banner--eight .group-image-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-top: 60px;
}

.banner--eight .group-image-wrap .group-text {
	margin: 0;
	color: #0c1636;
	font-size: 15px;
	font-weight: 500;
}

.banner--eight .group-image {
	margin: 0 10px 0 0;
	padding: 0;
	list-style: none;
}

.banner--eight .group-image li {
	display: inline-block;
	border: 3px solid #fff;
	border-radius: 50%;
	overflow: hidden;
	height: 46px;
	width: 46px;
	text-align: center;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	-webkit-box-shadow: 0 10px 20px rgba(0, 9, 40, 0.1);
	box-shadow: 0 10px 20px rgba(0, 9, 40, 0.1);
}

.banner--eight .group-image li:not(:first-child) {
	margin-left: -25px;
}

.banner--eight .group-image:hover li:not(:first-child) {
	margin-left: 0;
}

.banner--nine {
	background-image: url(../../media/banner/banner-nine/banner-nine-bg.png);
	background-size: cover;
	background-position: center center;
}

.banner--nine .banner__title {
	font-size: 60px;
	color: #fff;
}

@media (max-width: 1200px) {
	.banner--nine .banner__title {
		font-size: 44px;
	}
}

.banner--nine .banner__title span {
	font-weight: 400;
}

.banner--nine .banner__description {
	color: #fff;
	font-size: 18px;
}

.banner--nine .banner__feature-image {
	margin-right: -170px;
}

@media (max-width: 1200px) {
	.banner--nine .banner__feature-image {
		margin-right: 0;
		text-align: center;
	}
}

.banner--nine .banner-btn {
	margin-right: 15px;
}

.banner--nine .video-btn {
	color: #fff;
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	font-size: 15px;
}

.banner--nine .video-btn i {
	display: inline-block;
	border: 1.5px solid rgba(255, 255, 255, 0.161);
	height: 45px;
	width: 45px;
	line-height: 44px;
	text-align: center;
	color: #fff;
	border-radius: 50%;
	font-size: 14px;
	margin-right: 10px;
}

.banner__animate-element {
	margin: 0;
	padding: 0;
	list-style: none;
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 2;
	top: 0;
	left: 0;
}

.banner__animate-element li {
	position: absolute;
}

.banner__animate-element li img {
	-webkit-box-shadow: 0 30px 50px 0 rgba(0, 9, 40, 0.1);
	box-shadow: 0 30px 50px 0 rgba(0, 9, 40, 0.1);
}

.banner__animate-element li:nth-child(1) {
	top: 21%;
	left: 75px;
}

.banner__animate-element li:nth-child(2) {
	bottom: 150px;
	left: -20px;
}

.banner__animate-element li:nth-child(3) {
	bottom: 40%;
	right: -40px;
}

.banner--ten {
	background-color: #eff1f5;
	background-image: url(../../media/banner/banner-ten/banner_bg.jpg);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

.banner--ten .banner__title {
	font-size: 54px;
	color: #11266d;
}

@media (max-width: 991px) {
	.banner--ten .banner__title {
		font-size: 40px;
	}
}

.banner--ten .banner__title span {
	font-weight: 300;
	display: block;
}

.banner--ten .banner__feature {
	margin-bottom: 35px;
}

@media (min-width: 1024px) {
	.banner--ten .banner__feature-image {
		margin-right: -167px;
	}
}

.banner--ten .banner__feature li {
	color: #3c435b;
}

.banner--ten .banner__feature li i {
	background-color: rgba(246, 58, 111, 0.102);
	color: #f63a6f;
}

.banner__feature-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	margin-bottom: 27px;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
}

.banner__feature-item {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	background-color: #fff;
	padding: 15px;
	margin-right: 10px;
	border-radius: 6px;
	min-width: 150px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	margin-bottom: 10px;
}

.banner__feature-item:hover {
	-webkit-box-shadow: 0px 20px 20px 0px rgba(2, 19, 79, 0.1);
	box-shadow: 0px 20px 20px 0px rgba(2, 19, 79, 0.1);
	-webkit-transform: translateY(-5px);
	-ms-transform: translateY(-5px);
	transform: translateY(-5px);
}

.banner__feature-icon {
	margin-right: 20px;
	line-height: 1;
}

.banner__feature-title {
	font-size: 14px;
	font-weight: 500;
	margin-bottom: 0;
}

.banner__slider {
	position: relative;
}

.banner__slider .banner__slide {
	position: relative;
	overflow: hidden;
}

.banner__slider .banner__content {
	position: absolute;
	width: 100%;
	left: 0;
	top: 54%;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	z-index: 2;
}

@media (max-width: 991px) {
	.banner__slider .banner__content {
		left: 50%;
		-webkit-transform: translate(-50%, -50%);
		-ms-transform: translate(-50%, -50%);
		transform: translate(-50%, -50%);
		max-width: 80%;
	}
}

.banner__slider .banner__content .banner__subtitle {
	color: #fff;
	font-size: 14px;
	text-transform: uppercase;
	font-weight: 500;
	letter-spacing: 0.4em;
	margin-bottom: 10px;
}

.banner__slider .banner__content .banner__title {
	color: #fff;
	font-size: 70px;
	font-weight: 800;
}

@media (max-width: 1024px) {
	.banner__slider .banner__content .banner__title {
		font-size: 45px;
	}
}

@media (max-width: 767px) {
	.banner__slider .banner__content .banner__title {
		font-size: 38px;
	}
}

.banner__slider .banner__content .banner__description {
	color: rgba(255, 255, 255, 0.7);
}

@media (max-width: 767px) {
	.banner__slider .banner__content .banner__description {
		font-size: 15px;
	}
}

.banner__slider .banner__content .banner-btn {
	border-color: rgba(255, 255, 255, 0.3);
	color: #fff;
	padding: 14px 31px;
	border-radius: 4px;
}

.banner__slider .banner__content .banner-btn:before {
	background-color: #fff;
}

.banner__slider .banner__content .banner-btn:hover {
	border-color: #fff;
	color: #1c1e21;
}

.banner__slider .banner__slide {
	position: relative;
	height: 950px;
}

@media (max-width: 1024px) {
	.banner__slider .banner__slide {
		height: 700px;
	}
}

@media (max-width: 767px) {
	.banner__slider .banner__slide {
		height: 550px;
	}
}

.banner__slider .banner__slide .banner__image {
	height: 950px;
}

.banner__slider .banner__slide img {
	height: 100%;
	width: 100%;
	-o-object-fit: cover;
	object-fit: cover;
}

.banner__slider .banner__slide:after {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(12, 22, 54, 0.8);
}

.banner__slider .swiper-slide-active .banner__subtitle {
	-webkit-animation: fadeInUp 1s both 1s;
	animation: fadeInUp 1s both 1s;
}

.banner__slider .swiper-slide-active .banner__title {
	-webkit-animation: fadeInUp 1.2s both 1.2s;
	animation: fadeInUp 1.2s both 1.2s;
}

.banner__slider .swiper-slide-active .banner__description {
	-webkit-animation: fadeInUp 1.4s both 1.4s;
	animation: fadeInUp 1.4s both 1.4s;
}

.banner__slider .swiper-slide-active .banner-btn {
	-webkit-animation: fadeInUp 1.6s both 1.6s;
	animation: fadeInUp 1.6s both 1.6s;
}

@media (max-width: 991px) {
	.tt-order-lg-2 {
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		order: 2;
	}
}

.banner-control {
	position: absolute;
	top: 50%;
	left: 0;
	width: 100%;
	z-index: 2;
}

@media (max-width: 480px) {
	.banner-control {
		display: none;
	}
}

.banner-control>div {
	height: 60px;
	width: 40px;
	line-height: 60px;
	border: 2px solid rgba(255, 255, 255, 0.102);
	text-align: center;
	color: #fff;
	position: absolute;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	cursor: pointer;
	font-size: 20px;
}

.banner-control>div:hover {
	background-color: #fff;
	border-color: #fff;
	color: #f63a6f;
}

.banner-control .slider-prev {
	left: 50px;
}

@media (max-width: 991px) {
	.banner-control .slider-prev {
		left: 20px;
	}
}

@media (max-width: 767px) {
	.banner-control .slider-prev {
		left: 10px;
	}
}

.banner-control .slider-next {
	right: 50px;
}

@media (max-width: 991px) {
	.banner-control .slider-next {
		right: 20px;
	}
}

@media (max-width: 767px) {
	.banner-control .slider-next {
		right: 10px;
	}
}

/*--------------------------------------------------------------
 ##  About
 --------------------------------------------------------------*/
.about-section {
	background-color: #233675;
	position: relative;
}

.about-section>div .tt__btn {
	margin-top: 31px;
}

.about-section .section-heading {
	margin-bottom: 0;
}

.about-section .section-heading .subtitle {
	color: #fff;
}

.about-section .section-heading .section-title {
	color: #fff;
}

@media (max-width: 1200px) {
	.about-section .section-heading .section-title {
		font-size: 34px;
	}
}

.about-section .about-content-left {
	padding: 163px 0;
}

@media (max-width: 1200px) {
	.about-section .about-content-left {
		padding: 100px 0;
	}
}

@media (max-width: 991px) {
	.about-section .about-content-left {
		padding: 80px 0;
	}
}

.about-section .about-content-right {
	background-image: url(../../media/background/about_bg.jpg);
	background-size: cover;
	background-position: center center;
	position: absolute;
	right: 0;
	top: 0;
	width: 50%;
	padding: 163px 0 163px 100px;
}

@media (max-width: 1200px) {
	.about-section .about-content-right {
		padding: 100px 0 100px 100px;
	}
}

@media (max-width: 991px) {
	.about-section .about-content-right {
		width: 100%;
		position: static;
		padding: 80px 0 80px 10%;
	}
}

@media (max-width: 575px) {
	.about-section .about-content-right {
		padding: 80px 15px;
	}
}

.about-section .about-content-right .tt__btn {
	border-color: #f63a6f;
}

.about-section .about-content-right .tt__btn:before {
	background-color: #f63a6f;
}

.about-section .about-content-right .tt__btn:hover {
	color: #fff;
	border-color: rgba(255, 255, 255, 0.3);
}

.about-doctor {
	padding: 160px 0 140px;
}

@media (max-width: 991px) {
	.about-doctor {
		padding: 80px 0 100px;
	}
}

.about-doctor .section-heading.style-two {
	margin-bottom: 33px;
}

.about-doctor .tt-list {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
}

.about-doctor .tt-list li {
	width: 50%;
}

@media (max-width: 991px) {
	.about-doctor-content {
		margin-top: 40px;
	}
}

.about-education {
	padding: 100px 0 120px;
}

@media (max-width: 991px) {
	.about-education {
		padding: 100px 0 80px;
	}
}

@media (max-width: 991px) {
	.about-education-content {
		margin-top: 50px;
	}
}

.about-education-content .section-heading {
	margin-bottom: 37px;
}

.about-education-content .section-heading .section-title {
	font-size: 40px;
}

.about-education-content .title {
	font-size: 24px;
	margin-bottom: 25px;
}

.about-education-content .tt-list.style-two li {
	font-size: 16px;
}

.about-education-content .tt-list.style-two li i {
	background-color: rgba(13, 181, 13, 0.102);
	color: #0db50d;
	margin-right: 10px;
}

.about-education-content .tt-list.style-two li .list-text {
	font-size: 16px;
	margin-bottom: 0;
}

/* Data Science Content */
@media (max-width: 991px) {
	.about-science-content {
		margin-top: 40px;
	}
}

.about-science-content .section-heading {
	margin-bottom: 30px;
}

.about-science-content .section-heading .section-title {
	font-size: 40px;
	font-weight: 800;
}

.about-science-content .section-heading .lead {
	font-size: 20px;
	font-weight: 500;
	color: #1c1e21;
	margin-top: 13px;
	margin-bottom: 25px;
}

/*--------------------------------------------------------------
  ##  Logo Carousel
  --------------------------------------------------------------*/
.logo-carousel {
	padding-top: 50px;
}

.logo-carousel .section-heading {
	margin-bottom: 63px;
}

@media (max-width: 991px) {
	.logo-carousel .section-heading {
		text-align: center;
		margin-bottom: 30px;
	}
}

@media (max-width: 991px) {
	.logo-carousel {
		padding-top: 0;
	}

	.logo-carousel .btn__link {
		text-align: center;
		display: block;
		margin-bottom: 40px;
	}
}

.tt-logo-carousel .swiper-wrapper {
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

.logo-slider-wrapper .section--heading {
	margin-bottom: 40px;
}

.logo-slider-wrapper .section--heading .section-title-sm {
	font-size: 16px;
	font-weight: 500;
}

.client_logo {
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.client_logo img {
	-webkit-filter: grayscale(100%);
	filter: grayscale(100%);
	opacity: 0.7;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	max-width: 130px !important;
}

.client_logo:hover img {
	opacity: 1;
	-webkit-filter: grayscale(0);
	filter: grayscale(0);
}

.logo-list {
	margin: 0;
	padding: 0;
	list-style: none;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

.logo-list li:not(:last-child) {
	margin-right: 20px;
	text-align: center;
}

.logo-list li .tt-star-rating span {
	font-size: 10px;
}

.client-logo-list {
	margin: 0;
	padding: 0;
	list-style: none;
}

.client-logo-list li {
	display: inline-block;
	margin-right: 60px;
	margin-bottom: 20px;
}

.client-logo-list li img {
	-webkit-filter: grayscale(100%);
	filter: grayscale(100%);
	opacity: 0.7;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.client-logo-list li img:hover {
	-webkit-filter: grayscale(0);
	filter: grayscale(0);
	opacity: 1;
}

.client-logo-list.style_two {
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.client-logo-list.style_two li {
	margin-bottom: 30px;
	padding-right: 30px;
	margin-right: 0 !important;
}

.client-logo-list.style_two li img {
	-webkit-filter: grayscale(100%);
	filter: grayscale(100%);
	opacity: 0.7;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.client-logo-list.style_two li:hover img {
	-webkit-filter: grayscale(0);
	filter: grayscale(0);
	opacity: 1;
}

.client-logo-list.column_3 li {
	width: 33.33%;
	text-align: left;
}

@media (max-width: 767px) {
	.client-logo-list.column_3 li {
		text-align: center;
	}
}

@media (max-width: 480px) {
	.client-logo-list.column_3 li {
		width: 50%;
		text-align: center;
	}
}

.client-logo-list.column_4 li {
	width: 25%;
}

@media (max-width: 640px) {
	.client-logo-list.column_4 li {
		width: 33.33%;
	}
}

@media (max-width: 480px) {
	.client-logo-list.column_4 li {
		width: 50%;
	}
}

.client-logo-list.column_5 li {
	width: 20%;
}

@media (max-width: 767px) {
	.client-logo-list.column_5 li {
		text-align: center;
	}
}

@media (max-width: 480px) {
	.client-logo-list.column_5 li {
		width: 50%;
		text-align: center;
	}
}

/*--------------------------------------------------------------
##  Feature
--------------------------------------------------------------*/
.feature-area {
	padding: 115px 0 90px;
}

.feature-area .section--heading {
	margin-bottom: 26px;
}

.signature {
	margin-bottom: 80px;
}

.signature .name {
	font-size: 16px;
	font-weight: 500;
	margin-bottom: 20px;
}

.gp-feature {
	text-align: center;
	padding: 35px 15px;
	background-image: -o-linear-gradient(210deg, #996dfe 0%, #6a3bef 100%);
	background-image: linear-gradient(-120deg, #996dfe 0%, #6a3bef 100%);
	border-radius: 6px;
	-webkit-box-shadow: 0 20px 30px 0 rgba(132, 86, 247, 0.2);
	box-shadow: 0 20px 30px 0 rgba(132, 86, 247, 0.2);
}

.gp-feature__icon-container {
	margin-bottom: 23px;
}

.gp-feature__title {
	font-size: 16px;
	font-size: 1em;
	color: #fff;
	font-weight: 700;
	margin-bottom: 0;
	line-height: 1.1;
}

.gp-feature--colortwo {
	background-image: -o-linear-gradient(210deg, #fdc46b 0%, #f98c25 100%);
	background-image: linear-gradient(-120deg, #fdc46b 0%, #f98c25 100%);
	-webkit-box-shadow: 0 20px 30px 0 rgba(234, 157, 21, 0.2);
	box-shadow: 0 20px 30px 0 rgba(234, 157, 21, 0.2);
}

.gp-feature--colorthree {
	background-image: -o-linear-gradient(210deg, #2fc0f3 0%, #1c86e3 100%);
	background-image: linear-gradient(-120deg, #2fc0f3 0%, #1c86e3 100%);
	-webkit-box-shadow: 0 20px 30px 0 rgba(41, 174, 238, 0.2);
	box-shadow: 0 20px 30px 0 rgba(41, 174, 238, 0.2);
}

.gp-feature--colorfour {
	background-image: -o-linear-gradient(210deg, #fe7353 0%, #fc5933 100%);
	background-image: linear-gradient(-120deg, #fe7353 0%, #fc5933 100%);
	-webkit-box-shadow: 0 20px 30px 0 rgba(253, 104, 71, 0.2);
	box-shadow: 0 20px 30px 0 rgba(253, 104, 71, 0.2);
}

/*--------------------------------------------------------------
##  List
--------------------------------------------------------------*/
.tt-list {
	margin: 0;
	padding: 0;
	list-style: none;
}

.tt-list li {
	color: #0c1636;
	line-height: 36px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: baseline;
	-ms-flex-align: baseline;
	align-items: baseline;
	position: relative;
	font-size: 15px;
}

.tt-list li span {
	font-weight: 400;
}

.tt-list li p {
	margin: 0;
	font-size: 15px;
	line-height: 24px;
}

.tt-list li i {
	display: inline-block;
	font-size: 14px;
	color: #3283fd;
	height: 20px;
	width: 20px;
	line-height: 20px;
	text-align: center;
	border-radius: 30px;
	background-color: #e9eeff;
	margin-right: 10px;
}

.tt-list li a {
	color: #1c1e21;
	display: block;
}

.tt-list li a:hover {
	color: #3283fd;
}

.tt-list li.style_disk {
	position: relative;
	-webkit-box-align: unset;
	-ms-flex-align: unset;
	align-items: unset;
}

.tt-list li.style_disk .desk {
	height: 16px;
	width: 16px;
	border: 2px solid #572aff;
	display: inline-block;
	border-radius: 50%;
	position: relative;
	margin-right: 10px;
}

.tt-list li.style_disk .desk:after {
	content: "";
	position: absolute;
	height: 6px;
	width: 6px;
	border: 2px solid #572aff;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}

.tt-list li.style_disk .list-text {
	line-height: 1;
}

.tt-list.inline-items li {
	display: inline-block;
	margin: 0 10px;
}

.tt-list.inline-items li:first-child {
	margin-left: 0 !important;
}

.tt-list.inline-flex {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

@media (max-width: 991px) {
	.tt-list.inline-flex {
		margin-top: 40px;
	}
}

.tt-list.inline-flex li {
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	width: 33.33%;
}

@media (max-width: 991px) {
	.tt-list.inline-flex li {
		width: 50%;
		margin-bottom: 30px;
	}
}

@media (max-width: 767px) {
	.tt-list.inline-flex li {
		width: 100%;
		margin-bottom: 30px;
	}
}

.tt-list.inline-flex li .list-content {
	-webkit-box-flex: 2;
	-ms-flex: 2;
	flex: 2;
	padding-right: 40px;
}

.tt-list.style-two .list_item {
	padding-bottom: 30px;
}

@media (max-width: 991px) {
	.tt-list.style-two.inline-flex .list_item {
		padding-bottom: 0;
	}
}

.tt-list.style-two i {
	margin-right: 20px;
}

.tt-list.style-two .list-text {
	font-size: 18px;
	margin-bottom: 10px;
}

.tt-list.style-three li {
	font-size: 17px;
	font-weight: 500;
	line-height: 38px;
}

@media (max-width: 576px) {
	.tt-list.style-three li {
		width: 100%;
	}
}

@media (max-width: 991px) {
	.tt-list.style-three li {
		font-size: 16px;
	}
}

.tt-list.style-three i {
	background-color: #27b261;
	color: #fff;
}

.tt-list.style-four li {
	position: relative;
	padding-left: 13px;
	font-size: 16px;
	line-height: 36px;
	margin-bottom: 0;
}

.tt-list.style-four li:before {
	content: "";
	position: absolute;
	left: 0;
	top: 14px;
	height: 6px;
	width: 6px;
	background-color: #ff393e;
	border-radius: 10px;
}

.tt-list.style-five li {
	padding: 10px 20px;
	background-color: #fff;
	border-radius: 6px;
	-webkit-box-shadow: 0 20px 20px 0 rgba(2, 19, 79, 0.1);
	box-shadow: 0 20px 20px 0 rgba(2, 19, 79, 0.1);
	margin-bottom: 20px;
	font-weight: 500;
	color: #11266d;
}

.tt-list.style-five li i {
	background-color: rgba(39, 178, 97, 0.102);
	color: #27b261;
	height: 24px;
	width: 24px;
	font-size: 16px;
	line-height: 24px;
}

/* List With icon image*/
.list-with-icon {
	margin: 0;
	padding: 0;
	list-style: none;
}

.list-with-icon li {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 18px;
}

.list-with-icon li .list-icon {
	padding: 0.688em;
	display: inline-block;
	background-color: rgba(255, 57, 62, 0.078);
	border-radius: 50%;
	margin-right: 10px;
}

.list-with-icon li .list-icon.color-two {
	background-color: rgba(14, 184, 236, 0.078);
}

.list-with-icon li .list-icon img,
.list-with-icon li .list-icon svg {
	width: 1em;
	height: 1em;
	position: relative;
	display: block;
	font-size: 18px;
}

.list-with-icon li .list-title {
	margin-bottom: 0;
	font-size: 18px;
	font-weight: 500;
}

.ellipse {
	border-radius: 50%;
	background-color: rgba(14, 184, 236, 0.078);
	opacity: 0.078;
	position: absolute;
	left: 375px;
	top: 3605px;
	width: 40px;
	height: 40px;
	z-index: 216;
}

/*--------------------------------------------------------------
##  Icon Box
--------------------------------------------------------------*/
.service {
	padding: 110px 0 90px;
}

@media (max-width: 991px) {
	.service {
		padding: 70px 0 50px;
	}
}

.service-three {
	padding: 110px 0 60px;
}

@media (max-width: 991px) {
	.service-three {
		padding: 70px 0 60px;
	}
}

.service-four-area {
	padding-top: 120px;
}

.service-five {
	padding: 120px 0 60px;
	position: relative;
}

@media (max-width: 991px) {
	.service-five {
		padding: 70px 0 30px;
	}
}

.service-five .section-heading {
	margin-bottom: 50px;
}

@media (max-width: 991px) {
	.service-five .text-right {
		text-align: left !important;
		margin-bottom: 40px;
	}
}

.service-five .image__content-six {
	position: absolute;
	left: 145px;
	bottom: 0;
}

@media (max-width: 1800px) {
	.service-five .image__content-six img {
		max-width: 500px;
	}
}

@media (max-width: 1600px) {
	.service-five .image__content-six {
		left: 80px;
	}

	.service-five .image__content-six img {
		max-width: 450px;
	}
}

@media (max-width: 1400px) {
	.service-five .image__content-six {
		left: 30px;
	}

	.service-five .image__content-six img {
		max-width: 400px;
	}
}

@media (max-width: 1200px) {
	.service-five .image__content-six {
		display: none;
	}
}

.service-five .tt__btn {
	border-color: #f63a6f;
}

.service-five .tt__btn:before {
	background-color: #f63a6f;
}

.service-five .tt__btn:hover {
	color: #f63a6f;
}

.service-six {
	padding: 110px 0 60px;
	position: relative;
}

@media (max-width: 991px) {
	.service-six {
		padding: 70px 0 60px;
	}
}

.service-seven {
	padding-top: 110px;
}

@media (max-width: 991px) {
	.service-seven {
		padding: 70px 0 80px;
	}
}

.service-eight {
	padding: 111px 0 90px;
}

@media (max-width: 991px) {
	.service-eight {
		padding: 71px 0 50px;
	}
}

.feature-element {
	margin: 0;
	padding: 0;
	list-style: none;
}

.feature-element li {
	position: absolute;
}

.feature-element li:nth-child(1) {
	top: 50%;
	left: 5%;
	width: 76px;
}

.feature-element li:nth-child(2) {
	right: 0;
	top: 40%;
}

.feature-element li:nth-child(3) {
	right: 10%;
	bottom: 30%;
	height: 26px;
	width: 26px;
	border: 3px solid #fb514d;
	border-radius: 50%;
}

@media (max-width: 1600px) {
	.feature-element li:nth-child(3) {
		right: 5%;
	}
}

.tt-icon-box {
	margin-bottom: 30px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

@media (max-width: 991px) {
	.tt-icon-box {
		margin-bottom: 100px;
	}
}

.tt-icon-box .icon-container {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	text-align: center;
	font-size: 30px;
	margin-bottom: 35px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-icon-box .box-title {
	font-size: 20px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	margin-bottom: 15px;
}

.tt-icon-box .box-title a {
	color: #1c1e21;
}

.tt-icon-box .box-title a:hover {
	color: #3283fd;
}

.tt-icon-box .description {
	margin: 0;
}

@media (max-width: 1200px) {
	.tt-icon-box .description br {
		display: none;
	}
}

.tt-icon-box.style-one {
	margin-bottom: 73px;
}

@media (max-width: 767px) {
	.tt-icon-box.style-one {
		text-align: center;
		max-width: 350px;
		margin: 0 auto 40px;
	}
}

.tt-icon-box.style-one .icon-container {
	height: 70px;
	width: 70px;
	border-radius: 50%;
	color: #fff;
	background-color: #3283fd;
	background-image: -o-linear-gradient(30deg, #2a5cff 0%, #54a0ff 100%);
	background-image: linear-gradient(60deg, #2a5cff 0%, #54a0ff 100%);
}

.tt-icon-box.style-one .icon-container svg,
.tt-icon-box.style-one .icon-container img {
	width: 35px;
}

@media (max-width: 767px) {
	.tt-icon-box.style-one .icon-container {
		margin: 0 auto 35px;
	}
}

.tt-icon-box.style-one .description {
	line-height: 24px;
}

.tt-icon-box.style-one:hover .icon-container {
	-webkit-box-shadow: 0 20px 30px 0 rgba(5, 30, 115, 0.3);
	box-shadow: 0 20px 30px 0 rgba(5, 30, 115, 0.3);
}

.tt-icon-box.style-two {
	padding: 40px;
	border-radius: 6px;
}

@media (max-width: 767px) {
	.tt-icon-box.style-two {
		-webkit-box-shadow: 0px 10px 30px 0px rgba(0, 9, 40, 0.05);
		box-shadow: 0px 10px 30px 0px rgba(0, 9, 40, 0.05);
	}
}

.tt-icon-box.style-two .icon-container {
	height: 60px;
	width: 60px;
	background-color: #fff5f4;
	border-radius: 6px;
	margin-bottom: 45px;
}

.tt-icon-box.style-two .icon-container img,
.tt-icon-box.style-two .icon-container svg {
	width: 30px;
}

.tt-icon-box.style-two .icon-container.color__two {
	background-color: #f6f1fc;
}

.tt-icon-box.style-two .icon-container.color__three {
	background-color: #f2faf1;
}

.tt-icon-box.style-two .icon-container.color__four {
	background-color: #f1fafa;
}

.tt-icon-box.style-two .icon-container.color__five {
	background-color: #fcf1f6;
}

.tt-icon-box.style-two .icon-container.color__six {
	background-color: #f4f4ff;
}

.tt-icon-box.style-two .box-title:hover {
	color: #572aff;
}

.tt-icon-box.style-two .box-title a:hover {
	color: #572aff;
}

.tt-icon-box.style-two .description {
	margin-bottom: 16px;
}

.tt-icon-box.style-two .tt-more-link {
	opacity: 0;
}

.tt-icon-box.style-two .tt-more-link:hover {
	color: #572aff;
}

.tt-icon-box.style-two:hover {
	background-color: #fff;
	-webkit-box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.1);
}

.tt-icon-box.style-two:hover .tt-more-link {
	opacity: 1;
}

.tt-icon-box .tt-more-link {
	font-size: 15px;
	font-weight: 500;
	color: #4f5158;
}

.tt-icon-box .tt-more-link i {
	font-size: 15px;
	margin-left: 5px;
}

.tt-icon-box.style-three {
	background: #fff;
	-webkit-box-shadow: 0 30px 70px 0 rgba(0, 9, 40, 0.1);
	box-shadow: 0 30px 70px 0 rgba(0, 9, 40, 0.1);
	border-radius: 6px;
	padding: 40px 40px 37px;
}

.tt-icon-box.style-three .icon-container {
	height: 60px;
	width: 60px;
	background-color: #fff5f4;
	border-radius: 6px;
	margin-bottom: 33px;
	color: #fa5441;
}

.tt-icon-box.style-three .icon-container img,
.tt-icon-box.style-three .icon-container svg {
	width: 30px;
}

.tt-icon-box.style-three .icon-container.color__two {
	background-color: #f6f1fc;
}

.tt-icon-box.style-three .icon-container.color__three {
	background-color: #f2faf1;
}

.tt-icon-box.style-three .box-title {
	margin-bottom: 13px;
}

.tt-icon-box.style-three .box-title a:hover {
	color: #fa5441;
}

.tt-icon-box.style-three .description {
	color: #4f5158;
}

.tt-icon-box.style-three .tt-more-link {
	margin-top: 25px;
	font-size: 15px;
	font-weight: 500;
	display: block;
	color: #1c1e21;
}

.tt-icon-box.style-three .tt-more-link:hover {
	color: #fa5441;
}

.tt-icon-box.style-four {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	max-width: 380px;
}

.tt-icon-box.style-four .icon-container {
	height: 50px;
	width: 50px;
	background-color: #0cb934;
	color: #fff;
	font-size: 20px;
	border-radius: 6px;
	margin-right: 20px;
}

.tt-icon-box.style-four .icon-container.color__one {
	background-color: #0cb934;
}

.tt-icon-box.style-four .icon-container.color__two {
	background-color: #6353fe;
}

.tt-icon-box.style-four .box-content {
	-webkit-box-flex: 2;
	-ms-flex: 2;
	flex: 2;
}

.tt-icon-box.style-four .box-title {
	font-size: 18px;
	font-weight: 700;
	margin-bottom: 6px;
}

.tt-icon-box.style-four .box-title a:hover {
	color: #fa5441;
}

.tt-icon-box.style-four p {
	margin: 0;
}

.tt-icon-box.style-five {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	border-radius: 6px;
	background-color: #fff;
	-webkit-box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.12);
	box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.12);
	padding: 40px 60px;
}

@media (max-width: 480px) {
	.tt-icon-box.style-five {
		padding: 40px;
	}
}

@media (max-width: 360px) {
	.tt-icon-box.style-five {
		display: block;
		text-align: center;
	}
}

.tt-icon-box.style-five .icon-container {
	margin-right: 30px;
	min-width: 40px;
}

@media (max-width: 360px) {
	.tt-icon-box.style-five .icon-container {
		margin-right: 0;
	}
}

.tt-icon-box.style-five .icon-container img {
	max-height: 55px;
}

.tt-icon-box.style-five .box-title {
	margin-bottom: 6px;
}

.tt-icon-box.style-five:hover {
	-webkit-transform: translateY(-5px);
	-ms-transform: translateY(-5px);
	transform: translateY(-5px);
}

.tt-icon-box.style-six {
	border-radius: 4px;
	padding: 30px 27px;
	border-top: 3px solid transparent;
}

.tt-icon-box.style-six .icon-container {
	max-width: 50px;
	margin: 0 0 22px;
	position: relative;
}

.tt-icon-box.style-six .icon-container:before {
	content: "";
	position: absolute;
	width: 30px;
	height: 30px;
	background-color: #e9eeff;
	border-radius: 50%;
	right: 0;
	bottom: 0;
}

.tt-icon-box.style-six .icon-container img,
.tt-icon-box.style-six .icon-container i,
.tt-icon-box.style-six .icon-container svg {
	position: relative;
	z-index: 2;
	max-width: 42px;
}

.tt-icon-box.style-six .box-title {
	margin-bottom: 7px;
}

.tt-icon-box.style-six .box-title a:hover {
	color: #3283fd;
}

.tt-icon-box.style-six .description {
	font-size: 15px;
}

.tt-icon-box.style-six:hover {
	-webkit-box-shadow: 0px -3px 0px 0px rgba(246, 58, 111, 0.004), 0px 30px 30px 0px rgba(0, 9, 40, 0.12);
	box-shadow: 0px -3px 0px 0px rgba(246, 58, 111, 0.004), 0px 30px 30px 0px rgba(0, 9, 40, 0.12);
	background-color: #fff;
	border-top-color: #3283fd;
}

.tt-icon-box.style-seven {
	border: 2px solid #e7e9f0;
	padding: 40px 30px 30px;
	text-align: center;
	position: relative;
	z-index: 2;
	background-color: #fff;
}

.tt-icon-box.style-seven .icon-container {
	margin-bottom: 27px;
}

.tt-icon-box.style-seven .icon-container img,
.tt-icon-box.style-seven .icon-container svg {
	max-height: 170px;
}

.tt-icon-box.style-seven .box-title {
	margin-bottom: 7px;
	font-weight: 700;
}

.tt-icon-box.style-seven .read-more-btn {
	color: #1c1e21;
	border: 2px solid #ebedf3;
	border-radius: 6px;
	padding: 2px 30px;
	display: inline-block;
	margin-top: 33px;
}

.tt-icon-box.style-seven .read-more-btn:hover {
	color: #fff;
	background-color: #fb514d;
	border-color: #fb514d;
}

.tt-icon-box.style-seven:hover {
	background-color: #fff;
	-webkit-box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.1);
	border-color: #fff;
}

.tt-icon-box.style-eight {
	background-color: #fff;
	-webkit-box-shadow: 0px 30px 50px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 30px 50px 0px rgba(0, 9, 40, 0.1);
	padding: 50px 30px;
}

.tt-icon-box.style-eight .icon-container {
	margin-bottom: 35px;
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.tt-icon-box.style-eight .icon-container img,
.tt-icon-box.style-eight .icon-container svg {
	max-height: 60px;
}

.tt-icon-box.style-eight .box-title {
	margin-bottom: 7px;
	font-weight: 700;
}

.tt-icon-box.style-eight .read-more-btn {
	color: #1c1e21;
	display: inline-block;
	margin-top: 17px;
}

.tt-icon-box.style-eight .read-more-btn:hover {
	color: #1a82f5;
}

.tt-icon-box.style-eight:hover {
	-webkit-transform: translateY(-5px);
	-ms-transform: translateY(-5px);
	transform: translateY(-5px);
}

.tt-icon-box.style-nine {
	background-color: #fff;
	-webkit-box-shadow: 0 30px 50px 0 rgba(0, 9, 41, 0.1);
	box-shadow: 0 30px 50px 0 rgba(0, 9, 41, 0.1);
	padding: 40px;
}

.tt-icon-box.style-nine .icon-container {
	display: block;
	text-align: left;
	margin-bottom: 25px;
}

.tt-icon-box.style-nine .icon-container img,
.tt-icon-box.style-nine .icon-container svg {
	height: 58px;
}

.tt-icon-box.style-nine .box-title {
	font-weight: 700;
	margin-bottom: 7px;
}

.tt-icon-box.style-nine .box-title a:hover {
	color: #089df1;
}

.tt-icon-box.style-ten {
	-webkit-box-shadow: 0px 1px 2px 0px rgba(0, 9, 41, 0.1);
	box-shadow: 0px 1px 2px 0px rgba(0, 9, 41, 0.1);
	border-radius: 6px;
	margin-bottom: 10px;
	padding: 50px 30px 35px 30px;
	background-color: #fff;
}

.tt-icon-box.style-ten .icon-container {
	text-align: left;
	display: block;
	height: 50px;
}

.tt-icon-box.style-ten .icon-container img,
.tt-icon-box.style-ten .icon-container svg {
	height: 50px;
}

.tt-icon-box.style-ten .box-title {
	margin-bottom: 6px;
}

.tt-icon-box.style-ten .tt-more-link {
	display: block;
	margin-top: 16px;
}

.tt-icon-box.style-ten:hover {
	-webkit-box-shadow: 0px 30px 50px 0px rgba(0, 9, 41, 0.1);
	box-shadow: 0px 30px 50px 0px rgba(0, 9, 41, 0.1);
	-webkit-transform: translateY(-5px);
	-ms-transform: translateY(-5px);
	transform: translateY(-5px);
	position: relative;
	z-index: 2;
}

.tt-icon-box.style-eleven {
	border-radius: 6px;
	background-color: #fff;
	-webkit-box-shadow: 0 30px 70px 0 rgba(0, 9, 41, 0.1);
	box-shadow: 0 30px 70px 0 rgba(0, 9, 41, 0.1);
	padding: 45px 30px 46px;
	position: relative;
}

.tt-icon-box.style-eleven:before {
	content: "";
	position: absolute;
	left: 50%;
	-webkit-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	transform: translateX(-50%);
	height: 80px;
	top: -80px;
	border-left: 2px dotted #b4b6ba;
	z-index: -1;
}

.tt-icon-box.style-eleven .process-count {
	position: absolute;
	top: -75px;
	left: 50%;
	-webkit-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	transform: translateX(-50%);
	text-align: center;
	line-height: 40px;
	height: 40px;
	width: 40px;
	border: 1px solid #e8e8ec;
	border-radius: 50%;
	font-size: 15px;
	color: #111a3b;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	font-weight: 500;
	background-color: #fff;
	z-index: 2;
}

.tt-icon-box.style-eleven .icon-container {
	height: 74px;
	width: -webkit-max-content;
	width: -moz-max-content;
	width: max-content;
	-webkit-box-align: end;
	-ms-flex-align: end;
	align-items: flex-end;
}

.tt-icon-box.style-eleven p {
	color: #444856;
}

.tt-icon-box.style-eleven:hover .process-count {
	background-color: #3283fd;
	border-color: #3283fd;
	color: #fff;
	-webkit-box-shadow: 0px 10px 20px 0px rgba(4, 40, 156, 0.2);
	box-shadow: 0px 10px 20px 0px rgba(4, 40, 156, 0.2);
}

.tt-icon-box.style-twelve {
	text-align: center;
	background-color: rgba(8, 29, 100, 0.851);
	border: 1px solid rgba(255, 255, 255, 0.08);
	padding: 40px;
	border-radius: 6px;
}

@media (max-width: 991px) {
	.tt-icon-box.style-twelve {
		margin-bottom: 30px;
	}
}

.tt-icon-box.style-twelve .icon-container {
	height: 70px;
	width: 70px;
	line-height: 70px;
	text-align: center;
	border-radius: 50%;
	margin: 0 auto 34px;
	border: 2px solid rgba(255, 255, 255, 0.502);
}

.tt-icon-box.style-twelve .icon-container img {
	-webkit-filter: brightness(0) invert(1);
	filter: brightness(0) invert(1);
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-icon-box.style-twelve .box-title {
	color: #fff;
	font-size: 26px;
	font-weight: 700;
	margin-bottom: 12px;
}

.tt-icon-box.style-twelve .box-title a {
	color: #fff;
}

.tt-icon-box.style-twelve .box-title a:hover {
	color: #3283fd;
}

.tt-icon-box.style-twelve .description {
	color: rgba(255, 255, 255, 0.502);
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-icon-box.style-twelve .tt-more-link {
	color: #fff;
	font-weight: 500;
	font-size: 16px;
	margin-top: 24px;
	display: inline-block;
}

.tt-icon-box.style-twelve .tt-more-link:hover {
	color: #3283fd;
}

.tt-icon-box.style-twelve:hover {
	background-color: #fff;
	border-color: #fff;
}

.tt-icon-box.style-twelve:hover .icon-container {
	border-color: #3283fd;
}

.tt-icon-box.style-twelve:hover .icon-container img {
	-webkit-filter: none;
	filter: none;
}

.tt-icon-box.style-twelve:hover .box-title,
.tt-icon-box.style-twelve:hover a {
	color: #1c1e21;
}

.tt-icon-box.style-twelve:hover .description {
	color: #3c435b;
}

.process-box-wrapper {
	padding-top: 75px;
	position: relative;
}

.process-box-wrapper:after {
	content: "";
	position: absolute;
	width: 85%;
	border-bottom: 2px dotted #b4b6ba;
	height: 2px;
	top: 19px;
	left: 50%;
	-webkit-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	transform: translateX(-50%);
}

.row.gutter-5 .col-lg-3 {
	padding: 0 5px !important;
}

/* Service Nine */
.service-nine {
	background-color: #f2f3f5;
	padding: 115px 0 430px;
}

@media (max-width: 991px) {
	.service-nine {
		padding: 75px 0 70px;
	}
}

.service-nine .col-lg-3,
.service-nine .col-md-4 {
	padding: 0 5px;
}

/* Service Ten */
.service-ten-area {
	padding: 110px 0 90px;
	background-image: url(../../media/background/service_bg.jpg);
	background-size: cover;
	background-position: center center;
}

@media (max-width: 991px) {
	.service-ten-area {
		padding: 70px 0 50px;
	}
}

/*--------------------------------------------------------------
  ##  Icon Box List
  --------------------------------------------------------------*/
.feature-security-section {
	padding-top: 110px;
}

@media (max-width: 991px) {
	.feature-security-section {
		padding-top: 70px;
	}
}

.feature-security-wrapper {
	padding: 90px 0 70px;
	position: relative;
}

@media (max-width: 991px) {
	.feature-security-wrapper {
		padding: 60px 0 40px;
	}
}

.feature-security-wrapper:before {
	content: "";
	position: absolute;
	top: 0;
	bottom: 0;
	left: 50px;
	right: 50px;
	background-color: #f3f4f7;
	border-radius: 10px;
	background-image: url(../../media/background/feature-bg3.jpg);
	background-position: center center;
	background-repeat: no-repeat;
	background-size: cover;
}

.tt-icon-box-list {
	background-color: #fff;
	-webkit-box-shadow: 0 30px 40px 0 rgba(2, 19, 79, 0.1);
	box-shadow: 0 30px 40px 0 rgba(2, 19, 79, 0.1);
	border-radius: 6px;
	padding: 40px 30px 38px;
	margin-bottom: 30px;
}

.tt-icon-box-list .icon-container {
	margin-bottom: 30px;
	height: 45px;
}

.tt-icon-box-list .box-title {
	color: #11266d;
	font-size: 20px;
	font-weight: 800;
	margin-bottom: 16px;
}

.tt-icon-box-list .tt-icon-box-list-item {
	margin-bottom: 25px;
	padding: 0;
	list-style: none;
}

.tt-icon-box-list .tt-icon-box-list-item li {
	color: #11266d;
	font-size: 15px;
	line-height: 36px;
	font-weight: 500;
}

.tt-icon-box-list .tt-icon-box-list-item li i {
	margin-right: 10px;
	color: #3283fd;
}

.tt-icon-box-list .feature-link {
	font-size: 15px;
	font-weight: 500;
	color: #3283fd;
}

.tt-icon-box-list.color-two .tt-icon-box-list-item li i {
	color: #f63a6f;
}

.tt-icon-box-list.color-two .feature-link {
	color: #f63a6f;
}

.tt-icon-box-list.color-three .tt-icon-box-list-item li i {
	color: #27b261;
}

.tt-icon-box-list.color-three .feature-link {
	color: #27b261;
}

.tt-icon-box-list.color-four .tt-icon-box-list-item li i {
	color: #d31cf9;
}

.tt-icon-box-list.color-four .feature-link {
	color: #d31cf9;
}

/*--------------------------------------------------------------
##  Call To Action
--------------------------------------------------------------*/
.call-to-action {
	padding: 130px 0;
	background-color: #0c1636;
	position: relative;
	overflow: hidden;
}

@media (max-width: 991px) {
	.call-to-action {
		padding: 80px 0;
	}
}

.call-to-action .actions-content {
	max-width: 630px;
	margin: 0 auto;
	text-align: center;
	position: relative;
	z-index: 2;
}

.call-to-action .actions-content .action-title {
	font-size: 44px;
	font-weight: 700;
	color: #fff;
	margin-bottom: 22px;
}

@media (max-width: 767px) {
	.call-to-action .actions-content .action-title {
		font-size: 32px;
	}
}

.call-to-action .actions-content p {
	color: rgba(255, 255, 255, 0.502);
	margin-bottom: 51px;
}

.call-to-action .button-wrapper .tt__btn {
	max-width: 300px;
	width: 100%;
	padding: 20px;
	font-size: 16px;
}

.author-info-box {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	max-width: 280px;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	text-align: left;
	margin: 20px auto 0;
}

.author-info-box .avatar {
	height: 50px;
	width: 50px;
	border-radius: 50%;
	overflow: hidden;
	margin-right: 10px;
}

.author-info-box .avatar img {
	width: 100%;
	height: 100%;
	-o-object-fit: cover;
	object-fit: cover;
}

.author-info-box .author-info {
	-webkit-box-flex: 2;
	-ms-flex: 2;
	flex: 2;
	font-size: 16px;
	color: #fff;
	margin: 0;
	font-weight: 400;
	line-height: 24px;
}

.author-info-box.style-two {
	max-width: inherit;
}

.author-info-box.style-two .author-info {
	color: #1c1e21;
	font-size: 18px;
	font-weight: 700;
}

.author-info-box.style-two p {
	margin: 0;
}

.animated-bg-shap {
	margin: 0;
	padding: 0;
	list-style: none;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

@media (max-width: 991px) {
	.animated-bg-shap {
		display: none;
	}
}

.animated-bg-shap li {
	position: absolute;
	width: 100%;
	height: 100%;
}

.animated-bg-shap li .image-wrap {
	position: absolute;
}

.animated-bg-shap li:nth-child(1) .image-wrap {
	top: 150px;
	left: 102px;
}

.animated-bg-shap li:nth-child(2) .image-wrap {
	top: 63%;
	left: 15.5%;
}

.animated-bg-shap li:nth-child(3) .image-wrap {
	top: 0;
	right: 100px;
}

.animated-bg-shap li:nth-child(4) .image-wrap {
	top: 40%;
	right: 18%;
}

.animated-bg-shap li:nth-child(5) .image-wrap {
	bottom: 100px;
	right: 10%;
}

.call-to-action-two {
	background-color: #3283fd;
	background-image: url(../../media/background/call_to_action_two.png);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
	padding: 55px 0 64px;
}

@media (max-width: 991px) {
	.call-to-action-two .action-thumbnail {
		text-align: center;
	}
}

@media (max-width: 991px) {
	.actions-content-two {
		text-align: center;
		margin-top: 40px;
	}
}

.actions-content-two .action-title {
	font-size: 40px;
	font-weight: 800;
	color: #fff;
	margin-bottom: 18px;
}

.actions-content-two .description {
	font-size: 20px;
	color: rgba(255, 255, 255, 0.8);
	line-height: 1.5;
	margin-bottom: 33px;
}

/* Call To action Wrapper */
.call-to-action-three {
	position: relative;
	z-index: 2;
	margin-bottom: -120px;
}

.call-action-wrapper {
	background-image: url(../../media/background/call_to_action_three.png);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
	padding: 70px 100px 66px;
}

.call-action-wrapper .action-title {
	margin: 0;
	font-size: 44px;
}

@media (max-width: 1200px) {
	.call-action-wrapper .action-title {
		font-size: 36px;
	}
}

@media (max-width: 991px) {
	.call-action-wrapper .action-title {
		text-align: center;
	}
}

.call-action-wrapper .button-wrapper {
	text-align: right;
}

@media (max-width: 991px) {
	.call-action-wrapper .button-wrapper {
		text-align: center;
		margin-top: 40px;
	}
}

.call-action-wrapper .tt__btn {
	padding: 14px 25px;
	border: 2px solid rgba(255, 255, 255, 0.302);
}

/*--------------------------------------------------------------
##  Skill
--------------------------------------------------------------*/
.skill-area {
	padding: 150px 0;
}

@media (max-width: 991px) {
	.skill-area {
		padding: 80px 0;
	}
}

@media (min-width: 992px) {
	.skills-wrap {
		padding-right: 30px;
	}
}

.skills-wrap .section-heading {
	margin-bottom: 42px;
}

@media (max-width: 767px) {
	.skill-image-wrapper {
		margin-bottom: 40px;
	}
}

.progress-box {
	margin-bottom: 32px;
}

.progress-box p {
	font-family: "Inter", sans-serif;
	font-size: 16px;
	font-weight: 500;
	color: #1c1e21;
	margin-bottom: 8px;
}

.progress-box .bar-inner {
	position: relative;
	display: block;
	width: 0px;
	height: 6px;
	-webkit-transition: all 1500ms ease;
	-o-transition: all 1500ms ease;
	transition: all 1500ms ease;
	background-color: #25c7c8;
}

.progress-box .bar-inner.color-two {
	background-color: #2c51c7;
}

.progress-box .bar-inner.color-three {
	background-color: #fd4285;
}

.progress-box .bar {
	position: relative;
	width: 100%;
	height: 6px;
	background: #e6e7ed;
}

.progress-box .count-text {
	position: absolute;
	top: -33px;
	right: 0px;
	font-size: 16px;
	font-weight: 500;
	color: #222;
	opacity: 1;
	-webkit-transition: all 500ms ease;
	-o-transition: all 500ms ease;
	transition: all 500ms ease;
}

.progress-box.style-two .bar {
	border-radius: 3px;
	background-color: #fff;
}

.progress-box.style-two .bar-inner {
	border-radius: 3px;
	background-color: #f63a6f;
}

.faq-skill-area {
	background-color: #f2f4f7;
	padding: 120px 0 360px;
}

@media (max-width: 991px) {
	.faq-skill-area {
		padding-top: 70px;
	}
}

@media (min-width: 768px) {
	.faq-skill-area .skills-wrap {
		padding-right: 70px;
	}
}

@media (max-width: 991px) {
	.tt-accordian_faq.style-two {
		margin-top: 40px;
	}
}

/*--------------------------------------------------------------
## Tabs
--------------------------------------------------------------*/
.feature-tabs-area {
	padding-top: 110px;
	background-color: #f6f6f7;
}

@media (max-width: 991px) {
	.feature-tabs-area {
		padding-top: 70px;
	}
}

.feature-tabs-two {
	padding: 110px 0 120px;
}

@media (max-width: 991px) {
	.feature-tabs-two {
		padding: 70px 0;
	}
}

.feature-tabs-three {
	padding: 110px 0 120px;
}

@media (max-width: 991px) {
	.feature-tabs-three {
		padding: 70px 0 50px;
	}
}

.tabs-wrapper {
	position: relative;
}

#ultraland-tabs {
	position: relative;
	z-index: 2;
}

#ultraland-tabs #ultraland-tabs-nav {
	margin-bottom: 30px;
	padding: 0;
	list-style: none;
}

#ultraland-tabs #ultraland-tabs-nav li {
	display: inline-block;
}

#ultraland-tabs #ultraland-tabs-nav li:not(:last-child) {
	margin-right: 5px;
}

@media (max-width: 480px) {
	#ultraland-tabs #ultraland-tabs-nav li:not(:last-child) {
		margin-bottom: 10px;
	}
}

#ultraland-tabs #ultraland-tabs-nav li a {
	font-size: 14px;
	font-weight: 500;
	padding: 6px 22px;
	line-height: 1;
	display: block;
	border: 2px solid rgba(12, 22, 54, 0.102);
	border-radius: 30px;
	background: transparent;
}

#ultraland-tabs #ultraland-tabs-nav li a:hover,
#ultraland-tabs #ultraland-tabs-nav li a:focus {
	color: #1c1e21;
}

#ultraland-tabs #ultraland-tabs-nav li.active a {
	background-color: #3283fd;
	border-color: #3283fd;
	color: #fff;
}

.ultraland-tabs {
	padding: 50px 70px 70px 100px;
	background-color: #f9e8dd;
	border-top-left-radius: 10px;
	border-top-right-radius: 10px;
}

@media (max-width: 576px) {
	.ultraland-tabs {
		padding: 50px 30px 70px 50px;
	}
}

.ultraland-tabs-wrapper .content {
	position: relative;
}

.ultraland-tabs-wrapper .content:not(:first-child) {
	display: none;
}

.ultraland-tabs-wrapper .tab-image {
	border-radius: 6px;
	overflow: hidden;
	-webkit-box-shadow: 0 30px 40px 0 rgba(41, 16, 0, 0.1);
	box-shadow: 0 30px 40px 0 rgba(41, 16, 0, 0.1);
}

.ultraland-tabs-wrapper .tab-image img {
	border-radius: 6px;
	width: 100%;
}

.ultraland-tabs-wrapper .tab-image-two {
	position: absolute;
	bottom: -20px;
	left: -40px;
	width: 200px;
	height: 190px;
}

@media (max-width: 576px) {
	.ultraland-tabs-wrapper .tab-image-two {
		width: 130px;
		bottom: -40px;
	}
}

@media (max-width: 420px) {
	.ultraland-tabs-wrapper .tab-image-two {
		width: 100px;
		bottom: -60px;
	}
}

/* Feature Tab */
.feature-tab {
	background-color: #eff2f9;
	height: 450px !important;
	padding: 40px 0;
	border-radius: 6px;
}

@media (max-width: 767px) {
	.feature-tab {
		padding: 40px;
		height: auto !important;
	}
}

.tt-tabs-content .tt-tab-item {
	display: none;
}

.tt-tabs-content .tt-tab-item .feature-tab__image {
	-webkit-transition: all 0.4s ease-in-out;
	-o-transition: all 0.4s ease-in-out;
	transition: all 0.4s ease-in-out;
	opacity: 0;
	text-align: center;
}

.tt-tabs-content .tt-tab-item.active-tab {
	display: block;
}

.tt-tabs-content .tt-tab-item.active-tab .feature-tab__image {
	opacity: 1;
}

.feature-tab-nav {
	max-width: 400px;
	margin: 0 auto;
}

@media (max-width: 991px) {
	.feature-tab-nav {
		max-width: 100%;
		margin-top: 30px;
	}
}

.feature-tab-nav .feature_progress {
	position: absolute;
	width: 3px;
	background-color: #d4cfe6;
	left: 15px;
	top: 7px;
	bottom: 7px;
}

.feature-tab-nav .feature_progress:before {
	content: "";
	position: absolute;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	background-color: #fff;
	-webkit-transform: scaleY(0);
	-ms-transform: scaleY(0);
	transform: scaleY(0);
	-webkit-transform-origin: 0 0;
	-ms-transform-origin: 0 0;
	transform-origin: 0 0;
	-webkit-animation-duration: 7000ms;
	animation-duration: 7000ms;
}

.feature-tab-nav__item {
	border-radius: 6px;
	padding: 5px 15px 6px 40px;
	margin-bottom: 20px;
	position: relative;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.feature-tab-nav__item:hover {
	background-color: #f6f5fa;
}

.feature-tab-nav__item.active .feature-tab-nav__description {
	display: block;
}

.feature-tab-nav__item.active-tab {
	background-color: #572aff;
	padding: 15px 15px 25px 40px;
}

.feature-tab-nav__item.active-tab .feature-tab-nav__title {
	color: #fff;
}

.feature-tab-nav__item.active-tab .feature-tab-nav__description {
	color: rgba(255, 255, 255, 0.702);
}

.feature-tab-nav__item.active-tab .feature_progress {
	background-color: rgba(255, 255, 255, 0.102);
	top: 16px;
	bottom: 16px;
}

.feature-tab-nav__item.active-tab .feature_progress:before {
	-webkit-animation: featureProgressScale 10s linear forwards;
	animation: featureProgressScale 10s linear forwards;
}

.feature-tab-nav__title {
	font-size: 20px;
	font-weight: 700;
	margin-bottom: 0;
	padding: 8px 0 7px;
}

.feature-tab-nav__description {
	margin: 0;
	display: none;
	font-size: 16px;
	line-height: 24px;
}

@media (max-width: 991px) {
	.feature-tab-content {
		margin-bottom: 50px;
	}

	.feature-tab-content .section-heading .section-title {
		font-size: 26px !important;
	}

	.feature-tab-content .section-heading .section-title br {
		display: none;
	}
}

@media (min-width: 768px) {
	.feature-tab-content {
		padding-right: 100px;
	}
}

.feature-tab-content .section-heading {
	margin-bottom: 40px;
}

.feature-tab-content .section-heading .section-title {
	font-size: 30px;
}

.feature-tab-content .tt__btn {
	margin-top: 45px;
	padding: 11px 40px;
}

@-webkit-keyframes featureProgressScale {
	0% {
		-webkit-transform: scaleY(0);
		transform: scaleY(0);
	}

	to {
		-webkit-transform: scaleY(1);
		transform: scaleY(1);
	}
}

@keyframes featureProgressScale {
	0% {
		-webkit-transform: scaleY(0);
		transform: scaleY(0);
	}

	to {
		-webkit-transform: scaleY(1);
		transform: scaleY(1);
	}
}

@-webkit-keyframes featureProgressScaleX {
	0% {
		-webkit-transform: scaleX(0);
		transform: scaleX(0);
	}

	to {
		-webkit-transform: scaleX(1);
		transform: scaleX(1);
	}
}

@keyframes featureProgressScaleX {
	0% {
		-webkit-transform: scaleX(0);
		transform: scaleX(0);
	}

	to {
		-webkit-transform: scaleX(1);
		transform: scaleX(1);
	}
}

/* Feature Tab Two */
.tt-feature-tab-two {
	max-width: 970px;
	margin: 0 auto;
	position: relative;
}

.tt-feature-tab-two .bg-shape {
	position: absolute;
	bottom: -90px;
	right: -30px;
	width: 601px;
}

@media (max-width: 576px) {
	.tt-feature-tab-two .bg-shape {
		display: none;
	}
}

.feature-tab-two-nav {
	margin: 0;
	padding: 0;
	list-style: none;
}

.feature-tab-two-nav li {
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	-webkit-box-align: baseline;
	-ms-flex-align: baseline;
	align-items: baseline;
	padding: 20px 30px;
	background-color: #f6f5fa;
	border-top-left-radius: 6px;
	border-top-right-radius: 6px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.feature-tab-two-nav li:not(:last-child) {
	margin-right: 15px;
}

@media (max-width: 576px) {
	.feature-tab-two-nav li {
		width: 100%;
		margin-right: 0;
		margin-bottom: 20px;
	}
}

.feature-tab-two-nav li .tab-icon {
	margin-right: 10px;
	color: #3283fd;
}

.feature-tab-two-nav li .tab-icon svg,
.feature-tab-two-nav li .tab-icon img {
	width: 17px;
}

.feature-tab-two-nav li .feature-tab-two-nav__title {
	font-size: 18px;
	font-weight: 700;
	margin-bottom: 10px;
}

.feature-tab-two-nav li .feature_progress {
	height: 3px;
	width: 100%;
	background-color: #d4cfe5;
	position: relative;
}

.feature-tab-two-nav li .feature_progress:before {
	content: "";
	position: absolute;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	background-color: #3283fd;
	-webkit-transform: scaleX(0);
	-ms-transform: scaleX(0);
	transform: scaleX(0);
	-webkit-transform-origin: 0 0;
	-ms-transform-origin: 0 0;
	transform-origin: 0 0;
	-webkit-animation-duration: 7000ms;
	animation-duration: 7000ms;
}

.feature-tab-two-nav li.active-tab {
	background-color: #fff;
	-webkit-box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.1);
}

.feature-tab-two-nav li.active-tab .feature_progress:before {
	-webkit-animation: featureProgressScaleX 10s linear forwards;
	animation: featureProgressScaleX 10s linear forwards;
}

.feature-tab-two {
	background-color: #fff;
	padding: 35px 50px;
	-webkit-box-shadow: 0 30px 70px 0 rgba(0, 9, 40, 0.1);
	box-shadow: 0 30px 70px 0 rgba(0, 9, 40, 0.1);
	position: relative;
	z-index: 1;
	border-bottom-left-radius: 6px;
	border-bottom-right-radius: 6px;
}

@media (max-width: 767px) {
	.feature-tab-two {
		padding: 30px;
	}
}

.feature-tab-two__content-wrap,
.feature-tab-two__image {
	opacity: 0;
	-webkit-transition: all 0.5s ease-in-out;
	-o-transition: all 0.5s ease-in-out;
	transition: all 0.5s ease-in-out;
}

.feature-tab-two .tt-tab-item.active-tab {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

@media (max-width: 767px) {
	.feature-tab-two .tt-tab-item.active-tab {
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
	}
}

.feature-tab-two .tt-tab-item.active-tab .feature-tab-two__content-wrap,
.feature-tab-two .tt-tab-item.active-tab .feature-tab-two__image {
	opacity: 1;
}

.feature-tab-two .feature-tab-two__item {
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
}

.feature-tab-two .feature-tab-two__item>div {
	width: 50%;
}

@media (max-width: 767px) {
	.feature-tab-two .feature-tab-two__item>div {
		width: 100%;
	}
}

@media (max-width: 767px) {
	.feature-tab-two .feature-tab-two__item .feature-tab-two__content-wrap {
		margin-top: 40px;
	}
}

@media (max-width: 767px) {
	.feature-tab-two .feature-tab-two__item .ultraland-tabs-contents {
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		order: 2;
	}
}

.feature-tab-two__title {
	font-size: 24px;
	font-weight: 800;
}

.feature-tab-two__image {
	text-align: right;
	height: 350px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end;
}

.feature-tab-two .tt__btn {
	margin-top: 14px;
	padding: 11px 27px;
	border-color: #572aff;
	color: #572aff;
}

.feature-tab-two .tt__btn:before {
	background-color: #572aff;
}

.feature-tab-two .tt__btn i {
	margin-left: 5px;
	font-size: 20px;
}

/* Tab Four */
.feature-tabs-area-four {
	padding: 110px 0 120px;
}

@media (max-width: 991px) {
	.feature-tabs-area-four {
		padding: 70px 0 80px;
	}
}

.tabs-four-wrapper {
	background-color: #f6f7fa;
	border-radius: 30px;
	padding: 100px 70px;
}

@media (max-width: 991px) {
	.tabs-four-wrapper {
		padding: 70px 50px;
	}
}

.tabs-four-wrapper .ultraland-tabs {
	background-color: transparent;
	padding: 0;
}

.tabs-four-wrapper .ultraland-tab-contents .tab-image img {
	-webkit-box-shadow: 0 20px 20px 0 rgba(2, 19, 79, 0.1);
	box-shadow: 0 20px 20px 0 rgba(2, 19, 79, 0.1);
	width: 100%;
	border-radius: 6px;
}

@media (min-width: 992px) {
	.tabs-four-wrapper #ultraland-tabs #ultraland-tabs-nav {
		margin-bottom: 0;
	}
}

.tabs-four-wrapper #ultraland-tabs #ultraland-tabs-nav li {
	display: block;
	margin-bottom: 10px;
}

.tabs-four-wrapper #ultraland-tabs #ultraland-tabs-nav li a {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	background-color: #fff;
	border-radius: 6px;
	-webkit-box-shadow: 0 1px 2px 0 rgba(2, 19, 79, 0.16);
	box-shadow: 0 1px 2px 0 rgba(2, 19, 79, 0.16);
	border: 0;
	font-size: 18px;
	font-weight: 600;
	line-height: 26px;
	color: #0c1636;
	padding: 24px 40px;
}

.tabs-four-wrapper #ultraland-tabs #ultraland-tabs-nav li a .tab-icon {
	display: inline-block;
	margin-right: 20px;
}

.tabs-four-wrapper #ultraland-tabs #ultraland-tabs-nav li.active {
	position: relative;
	z-index: 2;
}

.tabs-four-wrapper #ultraland-tabs #ultraland-tabs-nav li.active a {
	-webkit-box-shadow: 0px 20px 20px 0px rgba(2, 19, 79, 0.1);
	box-shadow: 0px 20px 20px 0px rgba(2, 19, 79, 0.1);
}

/*--------------------------------------------------------------
##  Image Content
--------------------------------------------------------------*/
.image-content-area {
	padding-top: 140px;
	padding-bottom: 80px;
	overflow: hidden;
}

@media (max-width: 991px) {
	.image-content-area {
		padding-top: 60px;
	}
}

.image-content-area-two {
	padding: 115px 0 140px;
	overflow: hidden;
}

@media (max-width: 991px) {
	.image-content-area-two {
		padding: 30px 0 80px;
	}

	.image-content-area-two .image-content {
		margin-top: 100px;
	}
}

.parallax-image-content-area {
	background-image: linear-gradient(21deg, #4D82FF 20%, #E269DF 80%, #E269DF 100%);
	padding-top: 120px;
	overflow: hidden;
}

@media (max-width: 991px) {
	.parallax-image-content-area {
		padding-top: 60px;
	}
}

.parallax-image-content-section {
	padding: 150px 0 165px;
	overflow: hidden;
}

@media (max-width: 991px) {
	.parallax-image-content-section {
		padding: 100px 0 80px;
	}
}

.image-content-area-five {
	padding: 90px 0 210px;
	overflow: hidden;
}

@media (max-width: 1200px) {
	.image-content-area-five {
		padding: 90px 0 100px;
	}
}

@media (max-width: 991px) {
	.image-content-area-five {
		padding: 20px 0 110px;
	}
}

.image-content-area-five .image-content {
	margin-top: 50px;
}

@media (max-width: 991px) {
	.image-content-area-five .image-content {
		margin-top: 0;
	}
}

@media (max-width: 767px) {
	.image-content-area-five .image-content {
		margin-top: 110px;
	}
}

@media (max-width: 767px) {
	.image-content-area-five {
		padding: 30px 0 50px;
	}

	.image-content-area-five .shape-image {
		display: none;
	}
}

.tt-parallax__image {
	position: relative;
}

@media (max-width: 1200px) {
	.tt-parallax__image--one {
		max-width: 540px;
		margin: 0 auto 70px;
	}
}

.tt-parallax__image .parallax-image {
	position: relative;
	z-index: 2;
	margin: 0 -160px 0 -105px;
	text-align: left;
	max-width: 700px;
	margin: 0 auto;
}

.tt-parallax__image .shape-image {
	position: absolute;
	top: -45px;
	left: -55px;
}

@media (max-width: 420px) {
	.tt-parallax__image .shape-image {
		top: -20px;
	}
}

.tt-parallax__image .shape-image svg {
	width: 557px;
}

@media (max-width: 767px) {
	.tt-parallax__image .shape-image svg {
		width: 400px;
	}
}

@media (max-width: 420px) {
	.tt-parallax__image .shape-image svg {
		width: 270px;
	}
}

@media (max-width: 1024px) {
	.tt-parallax__image .shape-image {
		left: 50%;
		-webkit-transform: translateX(-50%);
		-ms-transform: translateX(-50%);
		transform: translateX(-50%);
	}
}

.tt-parallax__image--two .shape-image {
	right: 0;
	top: -31px;
}

.tt-parallax__image--two .shape-image svg {
	width: 496px;
	margin-top: -14px;
}

@media (max-width: 991px) {
	.tt-parallax__image--two .shape-image {
		right: auto;
		left: 50%;
		top: 0;
		-webkit-transform: translateX(-50%);
		-ms-transform: translateX(-50%);
		transform: translateX(-50%);
	}
}

@media (max-width: 767px) {
	.tt-parallax__image--two .shape-image {
		right: auto;
		left: 50%;
		top: 0;
		-webkit-transform: translateX(-50%);
		-ms-transform: translateX(-50%);
		transform: translateX(-50%);
	}

	.tt-parallax__image--two .shape-image svg {
		width: 370px;
	}
}

@media (max-width: 420px) {
	.tt-parallax__image--two .shape-image svg {
		width: 300px;
	}
}

.tt-parallax__image--two .parallax-image {
	margin: 0px -77px 0px -53px;
}

@media (max-width: 1024px) {
	.tt-parallax__image--two .parallax-image {
		margin: 0;
	}
}

.tt-parallax__image--three {
	max-width: 430px;
	margin: 0 auto;
}

.tt-parallax__image--three .image-one img,
.tt-parallax__image--three .image-two img {
	border-radius: 6px;
	-webkit-box-shadow: 0px 40px 70px 0px rgba(0, 9, 40, 0.16);
	box-shadow: 0px 40px 70px 0px rgba(0, 9, 40, 0.16);
}

.tt-parallax__image--three .image-two {
	position: absolute;
	right: -20px;
	top: 42%;
}

.tt-parallax__image--three .shape-image {
	position: absolute;
	top: auto;
	left: auto;
	bottom: -85px;
	right: -40px;
}

@media (max-width: 767px) {
	.tt-parallax__image--three .shape-image {
		left: 23%;
		bottom: -20px;
		-webkit-transform: translateX(-50%);
		-ms-transform: translateX(-50%);
		transform: translateX(-50%);
	}

	.tt-parallax__image--three .shape-image svg {
		width: 500px;
	}
}

@media (max-width: 767px) {
	.tt-parallax__image--three .image-one img {
		max-width: 270px;
	}

	.tt-parallax__image--three .image-two {
		right: 20px;
	}

	.tt-parallax__image--three .image-two img {
		max-width: 200px;
	}
}

@media (max-width: 767px) {
	.tt-parallax__image--four {
		margin-top: 50px;
	}
}

.tt-parallax__image--four .parallax-image {
	margin: 0;
	text-align: right;
	max-width: 472px;
}

.tt-parallax__image--four .shape-image {
	top: 80px;
	left: 50%;
	-webkit-transform: translateX(-55%);
	-ms-transform: translateX(-55%);
	transform: translateX(-55%);
}

.tt-parallax__image--four .image-two {
	position: absolute;
	left: 0;
	bottom: 170px;
}

.tt-parallax__image--four .image-two img {
	border-radius: 6px;
	-webkit-box-shadow: 0px 40px 50px 0px rgba(0, 9, 40, 0.2);
	box-shadow: 0px 40px 50px 0px rgba(0, 9, 40, 0.2);
}

.tt-parallax__image--five {
	max-width: 575px;
	margin: 0 auto;
}

.tt-parallax__image--five .shape-image {
	top: 28px;
	left: -58px;
}

@media (max-width: 620px) {
	.tt-parallax__image--five .shape-image {
		left: 0;
		top: 0;
	}
}

@media (max-width: 991px) {
	.tt-parallax__image--five .shape-wrapper {
		display: none;
	}
}

.tt-parallax__image--five .parallax-image {
	margin: 0px;
}

.tt-parallax__image--five .image-two {
	position: absolute;
	right: 0;
	top: 0;
}

@media (min-width: 768px) {
	.tt-parallax__image--five .image-two {
		width: 50%;
		padding-left: 15px;
	}
}

.tt-parallax__image--five .image-one img,
.tt-parallax__image--five .image-two img {
	border-radius: 6px;
}

@media (max-width: 767px) {

	.tt-parallax__image--five .image-one,
	.tt-parallax__image--five .image-two {
		width: 48%;
	}
}

@media (min-width: 768px) {
	.tt-parallax__image--five .image-one {
		width: 50%;
		padding-right: 15px;
	}
}

@media (min-width: 992px) {
	.tt-parallax__image--six {
		padding: 0 40px;
	}
}

@media (max-width: 768px) {
	.tt-parallax__image--six {
		margin-bottom: 30px;
	}
}

.tt-parallax__image--seven {
	max-width: 545px;
}

.tt-parallax__image--seven .shape-image {
	top: -15px;
	left: -120px;
}

@media (max-width: 767px) {
	.tt-parallax__image--seven .shape-image {
		left: 100px;
	}
}

.tt-parallax__image--seven .image-one {
	position: relative;
	z-index: 2;
	padding-top: 65px;
}

.tt-parallax__image--seven .image-two,
.tt-parallax__image--seven .image-three {
	position: absolute;
}

.tt-parallax__image--seven .image-two {
	right: 0;
	top: 0;
}

.tt-parallax__image--seven .image-three {
	left: -160px;
	bottom: 115px;
	-webkit-box-shadow: 0px 30px 70px 0px rgba(1, 9, 63, 0.14);
	box-shadow: 0px 30px 70px 0px rgba(1, 9, 63, 0.14);
	z-index: 3;
}

@media (max-width: 767px) {
	.tt-parallax__image--seven .image-three {
		left: -60px;
	}
}

.tt-parallax__image--eight {
	margin-left: -70px;
}

.tt-parallax__image--eight .image-two,
.tt-parallax__image--eight .image-three {
	position: absolute;
}

.tt-parallax__image--eight .image-two {
	top: 30px;
	left: 50%;
	-webkit-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	transform: translateX(-50%);
}

.tt-parallax__image--eight .image-two img {
	top: 0;
	border-radius: 10px;
	-webkit-box-shadow: 0 30px 50px 0 rgba(12, 2, 79, 0.14);
	box-shadow: 0 30px 50px 0 rgba(12, 2, 79, 0.14);
}

.tt-parallax__image--eight .image-three {
	right: 40px;
	bottom: 0;
}

.tt-parallax__image--eight .image-three img {
	border-radius: 10px;
	-webkit-box-shadow: 0 30px 50px 0 rgba(12, 2, 79, 0.14);
	box-shadow: 0 30px 50px 0 rgba(12, 2, 79, 0.14);
}

.tt-parallax__image--nine {
	margin-right: -185px;
}

@media (max-width: 1400px) {
	.tt-parallax__image--nine {
		margin-right: 0;
	}
}

.tt-parallax__image--nine .image-two {
	position: absolute;
	bottom: 55px;
	left: 170px;
}

.tt-parallax__image--nine .image-two img {
	border-radius: 10px;
	-webkit-box-shadow: 0 30px 50px 0 rgba(12, 2, 79, 0.14);
	box-shadow: 0 30px 50px 0 rgba(12, 2, 79, 0.14);
}

.tt-parallax__image--ten .parallax-image {
	position: relative;
	margin: 0;
	padding: 130px 0 70px;
}

.tt-parallax__image--ten:before {
	content: "";
	position: absolute;
	background-color: #f3f4f7;
	top: 0;
	bottom: 0;
	left: 50px;
	right: 20px;
	border-radius: 10px;
	background-image: url(../../media/background/feature-bg2.jpg);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

@media (max-width: 991px) {
	.tt-parallax__image--ten:before {
		right: 30px;
		left: 30px;
	}
}

.tt-parallax__image--ten .parallax-image {
	margin: 0;
}

.tt-parallax__image--ten img {
	border-radius: 10px;
	-webkit-box-shadow: 0 30px 60px 0 rgba(2, 19, 79, 0.1);
	box-shadow: 0 30px 60px 0 rgba(2, 19, 79, 0.1);
}

.tt-parallax__image--ten .image-two {
	position: absolute;
	top: 70px;
	left: 150px;
}

.tt-parallax__image--ten .image-three {
	position: absolute;
	right: 0;
	bottom: 0;
}

.parallax-image-list {
	margin: 0;
	padding: 0;
	list-style: none;
	position: relative;
	width: 620px;
	height: 500px;
	left: -50px;
}

@media (max-width: 1023px) {
	.parallax-image-list {
		margin: 0 auto;
		left: auto;
	}
}

@media (max-width: 767px) {
	.parallax-image-list {
		max-width: 420px;
		height: 355px;
	}
}

@media (max-width: 420px) {
	.parallax-image-list {
		max-width: 300px;
		height: 290px;
	}
}

.parallax-image-list li {
	position: absolute;
}

.parallax-image-list li img {
	-webkit-box-shadow: 0px 40px 70px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 40px 70px 0px rgba(0, 9, 40, 0.1);
	border-radius: 6px;
}

.parallax-image-list li:nth-child(1) {
	top: 30px;
	left: 0;
}

@media (max-width: 767px) {
	.parallax-image-list li:nth-child(1) {
		width: 175px;
	}
}

@media (max-width: 420px) {
	.parallax-image-list li:nth-child(1) {
		width: 120px;
	}
}

.parallax-image-list li:nth-child(2) {
	right: 40px;
	top: 0;
}

@media (max-width: 767px) {
	.parallax-image-list li:nth-child(2) {
		width: 190px;
	}
}

@media (max-width: 420px) {
	.parallax-image-list li:nth-child(2) {
		width: 150px;
		right: 20px;
	}
}

.parallax-image-list li:nth-child(3) {
	left: 50px;
	bottom: 0;
}

@media (max-width: 767px) {
	.parallax-image-list li:nth-child(3) {
		width: 120px;
		left: 55px;
	}
}

@media (max-width: 420px) {
	.parallax-image-list li:nth-child(3) {
		width: 100px;
		left: 20px;
	}
}

.parallax-image-list li:nth-child(4) {
	right: 0;
	bottom: 30px;
}

@media (min-width: 1024px) and (max-width: 1200px) {
	.parallax-image-list li:nth-child(4) {
		right: 50px;
		max-width: 320px;
		bottom: 65px;
	}
}

@media (max-width: 767px) {
	.parallax-image-list li:nth-child(4) {
		width: 230px;
		bottom: 65px;
	}
}

@media (max-width: 420px) {
	.parallax-image-list li:nth-child(4) {
		width: 170px;
		bottom: 70px;
	}
}

.parallax-image-list_two {
	position: relative;
	width: 570px;
	height: 510px;
	list-style: none;
	margin-left: 40px;
}

@media (max-width: 767px) {
	.parallax-image-list_two {
		margin: 0 auto;
		width: 420px;
		height: 390px;
		padding-left: 0;
	}
}

@media (max-width: 420px) {
	.parallax-image-list_two {
		width: 290px;
		height: 270px;
	}
}

.parallax-image-list_two li {
	position: absolute;
}

.parallax-image-list_two li img {
	border-radius: 6px;
	-webkit-box-shadow: 0 40px 70px 0 rgba(0, 9, 40, 0.1);
	box-shadow: 0 40px 70px 0 rgba(0, 9, 40, 0.1);
}

.parallax-image-list_two li:nth-child(1) {
	left: 0;
	top: 40px;
}

@media (max-width: 767px) {
	.parallax-image-list_two li:nth-child(1) {
		width: 200px;
		top: auto;
		bottom: 0;
	}
}

@media (max-width: 420px) {
	.parallax-image-list_two li:nth-child(1) {
		width: 150px;
	}
}

.parallax-image-list_two li:nth-child(2) {
	top: 0;
	right: 0;
}

@media (max-width: 767px) {
	.parallax-image-list_two li:nth-child(2) {
		width: 200px;
	}
}

@media (max-width: 420px) {
	.parallax-image-list_two li:nth-child(2) {
		width: 120px;
	}
}

.parallax-image-list_two li:nth-child(3) {
	bottom: 50px;
	right: 50px;
}

@media (max-width: 767px) {
	.parallax-image-list_two li:nth-child(3) {
		width: 150px;
		bottom: 30px;
	}
}

@media (max-width: 420px) {
	.parallax-image-list_two li:nth-child(3) {
		width: 100px;
		bottom: 35px;
		right: 20px;
	}
}

.image-content .section-heading {
	margin-bottom: 30px;
}

.image-content .section-heading .section-title {
	font-size: 40px;
}

.image-content .title {
	font-size: 18px;
	font-weight: 700;
	margin-bottom: 20px;
}

.image-content .tt__btn {
	margin-top: 15px;
	padding: 11px 34px;
}

@media (max-width: 991px) {
	.parallax-image-content {
		margin-top: 40px;
	}
}

.parallax-image-content .section-heading {
	margin-bottom: 42px;
}

.parallax-image-content .section-heading .section-title {
	margin-bottom: 11px;
}

.parallax-image-content .tt__btn {
	padding: 14px 23px;
	margin-top: 10px;
}

.parallax-image-content-two .section-heading {
	margin-bottom: 15px;
}

.parallax-image-content-two .section-heading .section-title {
	color: #fff;
	font-size: 40px;
}

.parallax-image-content-two .section-heading .description {
	color: rgba(255, 255, 255, 0.702);
}

.parallax-image-content-two .tt-list {
	margin-bottom: 42px;
}

.parallax-image-content-two .tt-list li {
	color: #fff;
}

.parallax-image-content-two .tt-list li i {
	background-color: #0cb934;
	color: #fff;
}

.parallax-image-content-two .tt__btn {
	padding: 14px 31px;
	border-color: #fa5441;
}

.parallax-image-content-two .tt__btn:before {
	background-color: #fa5441;
}

.parallax-image-content-two .tt__btn:hover {
	border-color: rgba(255, 255, 255, 0.502);
	color: #fff;
}

.tt-parallax__image.tt-parallax__image--four .shape-image svg {
	width: auto;
}

.image-content-six-area {
	padding: 115px 0 98px;
}

.image-content-six .section-heading {
	margin-bottom: 30px;
}

.image-content-six .section-heading .section-title {
	font-size: 40px;
	margin-bottom: 15px;
}

.image-content-six .tt__btn {
	padding: 11px 38px;
}

/*--------------------------------------------------------------
  ##  Service
  --------------------------------------------------------------*/
.service-area {
	background: #fafbff;
	padding: 120px 0;
}

.service-list-area .tt__btn {
	border-color: #572aff;
}

.service-list-area .tt__btn::before {
	background-color: #572aff;
}

.service-list-area .tt__btn:hover {
	color: #572aff;
}

.service-list-wrap {
	background-color: #f6f5fa;
	padding: 70px 70px 30px 70px;
	border-radius: 6px;
}

@media (max-width: 991px) {
	.service-list-wrap {
		padding: 50px 50px 30px 50px;
	}
}

@media (max-width: 991px) {
	.service-list-wrap {
		padding: 50px 30px 30px 30px;
	}
}

.service-list-wrap .section-heading {
	margin-bottom: 0;
}

.service-list-wrap .section-heading .section-title {
	font-size: 30px;
	line-height: 34px;
	margin-bottom: 16px;
}

.service-list-wrap .section-heading .tt__btn {
	margin-top: 32px;
}

@media (max-width: 767px) {
	.service-list-image {
		margin-top: 40px;
	}
}

/*--------------------------------------------------------------
 ##  Pricing
 --------------------------------------------------------------*/
.pricing {
	padding: 115px 0 90px;
}

@media (max-width: 991px) {
	.pricing {
		padding: 75px 0 30px;
	}
}

.pricing-two {
	margin-top: -205px;
	padding-bottom: 120px;
}

@media (max-width: 991px) {
	.pricing-two {
		padding-bottom: 70px;
	}
}

@media (max-width: 768px) {
	.pricing-two {
		margin-top: -160px;
	}
}

.yearly-price {
	display: none;
}

.form-switch {
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	position: relative;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	margin-bottom: 50px;
}

.form-switch span {
	font-size: 14px;
	font-weight: 500;
	display: inline-block;
	text-align: center;
	z-index: 2;
	position: relative;
	line-height: 35px;
	color: #6a6971;
	cursor: pointer;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.form-switch .switch-icon {
	position: relative;
	width: 65px;
	background: #f0f4f7;
	border-radius: 30px;
	-webkit-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	transition: all 0.3s linear;
	height: 30px;
	margin: 0 20px;
}

.form-switch .switch-icon:after {
	content: "";
	position: absolute;
	top: 50%;
	left: 11px;
	width: 16px;
	height: 16px;
	background: #3283fd;
	border-radius: 30px;
	-webkit-transform: translate3d(-4px, 0, 0) translateY(-50%);
	transform: translate3d(-4px, 0, 0) translateY(-50%);
	-webkit-transition: all 0.4s ease-in-out;
	-o-transition: all 0.4s ease-in-out;
	transition: all 0.4s ease-in-out;
}

.form-switch .switch-icon.yearly:after {
	-webkit-transform: translate3d(32px, 0, 0) translateY(-50%);
	transform: translate3d(32px, 0, 0) translateY(-50%);
}

.form-switch.monthly .beforeinput {
	color: #3283fd;
}

.form-switch.yearly .afterinput {
	color: #3283fd;
}

.form-switch.yearly .switch-icon:after {
	-webkit-transform: translate3d(32px, 0, 0) translateY(-50%);
	transform: translate3d(32px, 0, 0) translateY(-50%);
}

.form-switch.switcher-light.monthly .beforeinput,
.form-switch.switcher-light.yearly .afterinput {
	color: #fff;
}

.form-switch.switcher-light span {
	color: rgba(255, 255, 255, 0.502);
}

.pricing-table {
	position: relative;
	overflow: hidden;
	padding: 46px 0 50px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	margin-bottom: 30px;
	background: #fff;
	border-radius: 10px;
	-webkit-box-shadow: 0 20px 50px 0 rgba(9, 11, 41, 0.1);
	box-shadow: 0 20px 50px 0 rgba(9, 11, 41, 0.1);
}

.pricing-table .pricing-header {
	position: relative;
	margin-bottom: 27px;
	border-bottom: 1px solid #ebedf3;
	padding: 0 40px 25px;
}

.pricing-table .pricing-header .price-subtitle {
	font-size: 14px;
	font-weight: 400;
	color: #686979;
	margin-bottom: 0;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.pricing-table .pricing-header .price-title {
	font-size: 20px;
	margin-bottom: 20px;
	font-weight: 500;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.pricing-table .pricing-header .price {
	font-size: 40px;
	font-weight: 700;
	color: #1c1e21;
	line-height: 1;
	margin-bottom: 11px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.pricing-table .pricing-header .price .period {
	font-size: 14px;
	font-weight: 400;
	color: #4f5158;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.pricing-table .price-feture {
	margin: 0 0 37px;
	padding: 0 40px;
	list-style: none;
}

.pricing-table .price-feture li {
	display: block;
	font-size: 15px;
	color: #4f5158;
	font-weight: 400;
	position: relative;
	padding-left: 30px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	margin-bottom: 9px;
}

.pricing-table .price-feture li:before {
	content: "N";
	position: absolute;
	font-family: eleganticons;
	left: 0;
	top: 3px;
	font-size: 14px;
	color: #3283fd;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	height: 20px;
	width: 20px;
	line-height: 20px;
	text-align: center;
	border-radius: 50%;
	background-color: #e9eeff;
}

.pricing-table .price-feture li span {
	color: #1c1e21;
	font-weight: 700;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.pricing-table .feature-label {
	position: absolute;
	right: 0;
	-webkit-transform: rotate(90deg);
	-ms-transform: rotate(90deg);
	transform: rotate(90deg);
	top: 30px;
	background-color: #4a47df;
	padding: 0 23px 0 10px;
	color: #fff;
	font-size: 14px;
	font-weight: 500;
	line-height: 30px;
}

.pricing-table .feature-label:before {
	content: "";
	position: absolute;
	width: 0;
	height: 0;
	border-top: 15px solid transparent;
	border-bottom: 15px solid transparent;
	border-right: 10px solid #fff;
	right: -1px;
	bottom: 0;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.pricing-table .action {
	padding: 0 40px;
}

.pricing-table .action .tt__btn {
	display: block;
	text-align: center;
}

.pricing-table.style_one {
	border-top: 3px solid transparent;
}

.pricing-table.style_one:hover {
	border-color: #3283fd;
}

.pricing-two-area {
	padding-bottom: 120px;
}

@media (max-width: 991px) {
	.pricing-two-area {
		padding-bottom: 80px;
	}
}

.priceing-tab.style-two .form-switch.yearly .afterinput,
.priceing-tab.style-two .form-switch.monthly .beforeinput {
	color: #f63a6f;
}

.priceing-tab.style-two .form-switch .switch-icon:after {
	background: #f63a6f;
}

.tt-pricing-plan {
	max-width: 1030px;
	margin: 0 auto;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

@media (max-width: 991px) {
	.tt-pricing-plan {
		display: block;
	}
}

.tt-pricing-plan .tt-price-info {
	background-color: #f1f3f6;
	padding: 78px 40px 85px;
	text-align: center;
	width: 420px;
	margin-right: 10px;
}

@media (max-width: 991px) {
	.tt-pricing-plan .tt-price-info {
		width: 100%;
		margin-right: 0;
		margin-bottom: 10px;
	}
}

.tt-pricing-plan .price-title {
	text-transform: uppercase;
	font-size: 14px;
	font-weight: 600;
	color: #1c1e21;
	margin-bottom: 49px;
}

.tt-pricing-plan .tt-price {
	font-size: 60px;
	font-weight: 700;
	margin: 0;
	line-height: 1;
	margin-bottom: 3px;
}

.tt-pricing-plan .tt-price .suffix {
	font-weight: 300;
}

.tt-pricing-plan .tt-price .cent {
	font-size: 24px;
	font-weight: 500;
}

.tt-pricing-plan .period {
	color: #4f5158;
	margin-bottom: 32px;
}

.tt-pricing-plan .tt__btn {
	padding: 14px 30px;
	width: 200px;
	border-color: #f63a6f;
}

.tt-pricing-plan .tt__btn:before {
	background-color: #f63a6f;
}

.tt-pricing-plan .tt__btn:hover {
	color: #f63a6f;
}

.tt-pricing-plan .price-feature-info {
	border-radius: 4px;
	-webkit-box-shadow: 0px 2px 4px 0px rgba(0, 9, 40, 0.16);
	box-shadow: 0px 2px 4px 0px rgba(0, 9, 40, 0.16);
	-webkit-box-flex: 2;
	-ms-flex: 2;
	flex: 2;
}

@media (max-width: 991px) {
	.tt-pricing-plan .price-feature-info {
		width: 100%;
	}
}

.tt-pricing-plan .tt-price-feature-title {
	font-size: 24px;
	margin-bottom: 25px;
}

.tt-pricing-plan .feature-list {
	margin: 0;
	padding: 0;
	list-style: none;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
}

.tt-pricing-plan .feature-list li {
	width: 50%;
	line-height: 34px;
}

@media (max-width: 576px) {
	.tt-pricing-plan .feature-list li {
		width: 100%;
	}
}

.tt-pricing-plan .feature-list li i {
	height: 20px;
	width: 20px;
	line-height: 20px;
	text-align: center;
	border-radius: 50%;
	margin-right: 10px;
	background-color: #27b261;
	color: #fff;
	font-size: 14px;
	display: inline-block;
}

.tt-pricing-plan .pricing-content {
	border-top: 1px solid #ebedf3;
	padding: 20px 50px 20px;
}

.tt-pricing-plan .pricing-content p {
	margin-bottom: 0;
	font-size: 14px;
	color: #6e6f75;
}

.tt-pricing-plan .feature-list-wrapper {
	padding: 45px 50px 43px;
}

@media (min-width: 992px) and (max-width: 1024px) {
	.tt-pricing-plan .feature-list-wrapper {
		padding: 45px 30px 43px;
	}
}

@media (max-width: 576px) {
	.tt-pricing-plan .feature-list-wrapper {
		padding: 45px 30px 43px;
	}
}

.pricing-section-four {
	padding: 115px 0 90px;
}

@media (max-width: 991px) {
	.pricing-section-four {
		padding: 75px 0 50px;
	}
}

.tt-pricing {
	padding: 40px 40px 35px;
	border: 1px solid #e8e8ee;
	border-radius: 6px;
	margin-bottom: 30px;
}

.tt-pricing__icon {
	margin-bottom: 31px;
}

.tt-pricing__title {
	font-size: 20px;
	font-weight: 800;
	margin-bottom: 15px;
}

.tt-pricing__feature-list {
	margin: 0 0 24px 0;
	padding: 0;
	list-style: none;
}

.tt-pricing__feature-list li {
	font-size: 15px;
	color: #444856;
	line-height: 36px;
}

.tt-pricing__feature-list li i {
	color: #2a5cff;
	margin-right: 8px;
}

.tt-pricing__btn-link {
	color: #1c1e21;
	display: block;
	font-size: 15px;
	font-weight: 500;
}

.tt-pricing__btn-link i {
	margin-left: 3px;
	font-size: 16px;
}

.tt-pricing.featured {
	-webkit-box-shadow: 0 30px 70px 0 rgba(2, 19, 79, 0.14);
	box-shadow: 0 30px 70px 0 rgba(2, 19, 79, 0.14);
	background-color: #fff;
	border: 0;
	position: relative;
	padding: 70px 40px 60px;
}

.tt-pricing.featured .tt-pricing__feature-list {
	margin: 0 0 40px 0;
}

.tt-pricing.featured .tt-pricing__feature-list li i {
	color: #ff393e;
}

.tt-pricing.featured .tt__btn {
	border-color: #ff393e;
}

.tt-pricing.featured .tt__btn:before {
	background-color: #ff393e;
}

.tt-pricing.featured .tt__btn:hover {
	color: #ff393e;
}

.tt-pricing .feature-text {
	background-color: #ff393e;
	color: #fff;
	position: absolute;
	top: 0;
	left: 50%;
	-webkit-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	transform: translateX(-50%);
	font-size: 14px;
	font-weight: 500;
	padding: 4px 11px;
	line-height: 1;
	border-bottom-left-radius: 6px;
	border-bottom-right-radius: 6px;
}

/*--------------------------------------------------------------
##  Accordion
--------------------------------------------------------------*/
.tt-accordian {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	max-width: 970px;
	margin: 0 auto;
}

.tt-accordian__nav {
	width: 300px;
	-webkit-box-shadow: 0px 40px 70px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 40px 70px 0px rgba(0, 9, 40, 0.1);
	background-color: #fff;
	border-radius: 6px;
	padding: 30px 0;
	display: block;
	height: auto;
}

@media (max-width: 767px) {
	.tt-accordian__nav {
		width: 100%;
		margin-bottom: 40px;
	}
}

.tt-accordian__nav li {
	display: inline-block;
}

@media (max-width: 767px) {
	.tt-accordian__nav li {
		width: 90%;
	}
}

.tt-accordian__nav li a {
	font-size: 16px;
	font-weight: 500;
	padding: 7px 40px;
	min-width: 230px;
	border-top-right-radius: 6px;
	border-bottom-right-radius: 6px;
	display: block;
	color: #777580;
}

.tt-accordian__nav li a.active {
	background-color: #3283fd;
	color: #fff;
}

.tt-accordian__content {
	max-width: 570px;
	width: 100%;
}

@media (max-width: 991px) {
	.tt-accordian__content {
		-webkit-box-flex: 2;
		-ms-flex: 2;
		flex: 2;
		margin-left: 20px;
	}
}

@media (max-width: 767px) {
	.tt-accordian__content {
		margin-left: 0;
		width: 100%;
	}
}

.tt-accordian .card {
	border: 0;
	margin-bottom: 10px;
	width: 100%;
}

.tt-accordian .card-header {
	background-color: transparent;
	padding: 0;
	border: 0;
}

.tt-accordian h5 {
	margin-bottom: 0;
	font-size: 16px;
	font-weight: 600;
	background-color: #fff;
	padding: 25px 30px;
	border-radius: 6px;
	-webkit-box-shadow: 0px 30px 50px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 30px 50px 0px rgba(0, 9, 40, 0.1);
	line-height: 1.3;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	position: relative;
}

.tt-accordian h5 i {
	position: absolute;
	right: 30px;
	top: 50%;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	font-size: 26px;
	color: #3283fd;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

@media (max-width: 991px) {
	.tt-accordian h5 i {
		right: 15px;
	}
}

.tt-accordian h5.collapsed {
	background-color: #f1f3f7;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.tt-accordian h5.collapsed i {
	color: #1c1e21;
	-webkit-transform: translateY(-50%) rotate(-180deg);
	-ms-transform: translateY(-50%) rotate(-180deg);
	transform: translateY(-50%) rotate(-180deg);
}

.tt-accordian .card-body {
	padding: 21px 30px 30px;
}

.tt-accordian .card-body p {
	margin: 0;
}

.tt-accordian.style-two .card {
	background-color: #fff;
	border-radius: 4px;
}

.tt-accordian.style-two .card.active {
	-webkit-box-shadow: 0px 30px 50px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 30px 50px 0px rgba(0, 9, 40, 0.1);
	margin-bottom: 20px;
}

.tt-accordian.style-two h5 {
	-webkit-box-shadow: none;
	box-shadow: none;
}

.tt-accordian.style-two h5.collapsed {
	background-color: transparent;
}

.tt-accordian.style-two h5.collapsed i {
	color: #1c1e21;
}

.tt-accordian.style-two h5 i {
	color: #f63a6f;
}

.tt-accordian.style-two .card-body {
	padding: 0 30px 30px;
}

.tt-accordian.style-three {
	display: block;
}

.tt-accordian.style-three .card {
	background-color: #fff;
	border-radius: 0;
}

.tt-accordian.style-three .card.active {
	-webkit-box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.12);
	box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.12);
}

.tt-accordian.style-three .card.active h5 {
	color: #089df1;
	border-radius: 0;
}

.tt-accordian.style-three .card-header {
	background-color: #f1f2f3;
	color: #089df1;
}

.tt-accordian.style-three h5 {
	-webkit-box-shadow: none;
	box-shadow: none;
	padding: 20px 30px;
}

.tt-accordian.style-three h5.collapsed {
	background-color: transparent;
	color: #1c1e21;
}

.tt-accordian.style-three h5.collapsed i {
	color: #1c1e21;
}

.tt-accordian.style-three h5 i {
	color: #089df1;
	right: 20px;
}

.tt-accordian.style-three .card-body {
	padding: 8px 26px 30px;
}

.tt-accordian.style-three .card-body .feature-list {
	margin: 0;
	padding: 0;
	list-style: none;
	margin-top: 22px;
}

.tt-accordian.style-three .card-body .feature-list li {
	color: #111a3b;
	line-height: 28px;
}

.tt-accordian.style-three .card-body .feature-list li i {
	margin-right: 10px;
	color: #089df1;
}

.faq-section {
	padding: 100px 0 110px;
}

@media (max-width: 991px) {
	.faq-section {
		padding: 70px 0;
	}
}

.faq-section .tt-accordian__nav li a.active {
	background: #fa5441;
}

.faq-section .tt-accordian h5 i {
	color: #fa5441;
}

.faq-section .tt-accordian h5.collapsed i {
	color: #1c1e21;
}

.faq-page-section-two {
	padding-bottom: 110px;
}

@media (max-width: 991px) {
	.faq-page-section-two {
		padding-bottom: 70px;
	}
}

.faq-medical-section {
	padding-top: 120px;
}

.faq-medical-section .section-heading.style-two {
	margin-bottom: 42px;
}

/*--------------------------------------------------------------
 ##  Testimonial
 --------------------------------------------------------------*/
.testimonial-two-section {
	background-color: #ebeef2;
	padding: 115px 0 60px 0;
}

@media (max-width: 991px) {
	.testimonial-two-section {
		padding-top: 75px;
	}
}

@media (max-width: 767px) {
	.testimonial-two-section .section-heading {
		text-align: center;
		margin-bottom: 30px;
	}
}

@media (max-width: 767px) {
	.testimonial-two-section .testimonial-control {
		margin: 0 auto 40px;
	}
}

.testimonial-section {
	background-color: #ebeef2;
	padding: 120px 0 90px;
}

@media (max-width: 991px) {
	.testimonial-section {
		padding: 80px 0 50px;
	}
}

.slider-pagination {
	position: absolute;
	left: 50%;
	bottom: 0;
	-webkit-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	transform: translateX(-50%);
}

.slider-pagination .swiper-pagination-bullet {
	margin: 0 5px !important;
	height: 10px;
	width: 10px;
	background: #dbd9e3;
	opacity: 1;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.slider-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
	background: #572aff;
}

.testimonial-control {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	width: 90px;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	margin-left: auto;
}

.testimonial-control>div {
	width: 40px;
	height: 40px;
	line-height: 42px;
	border-radius: 50%;
	background: #fff;
	text-align: center;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	outline: 0;
	cursor: pointer;
	font-size: 20px;
}

.testimonial-control>div:hover {
	background: #fa5441;
	color: #fff;
}

.testimonial-area {
	padding: 50px 0 110px;
}

@media (max-width: 991px) {
	.testimonial-area {
		padding: 20px 0 60px;
	}
}

.testimonial-six-section {
	padding: 140px 0 130px;
	background-image: url(../../media/background/testimonial_four.jpg);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

@media (max-width: 991px) {
	.testimonial-six-section {
		padding: 100px 0 90px;
	}
}

@media (max-width: 991px) {
	.learn-info-content-wrap {
		margin-bottom: 40px;
	}
}

.testimonial-seven-section {
	padding: 140px 0 130px;
	background-image: url(../../media/background/testimonial_five.png);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

@media (max-width: 991px) {
	.testimonial-seven-section {
		padding: 80px 0 70px;
	}
}

.testimonial--one {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	max-width: 830px;
	margin: 0 auto;
}

@media (max-width: 1024px) {
	.testimonial--one {
		display: block;
	}
}

.testimonial--one .testimonial__info-wrapper {
	width: 200px;
}

@media (max-width: 1024px) {
	.testimonial--one .testimonial__info-wrapper {
		width: 100%;
		margin-bottom: 25px;
	}
}

.testimonial--one .testimonial__info {
	text-align: center;
}

.testimonial--one .testimonial__info .avatar {
	margin: 0 auto 12px;
}

.testimonial--one .testimonial__content {
	max-width: 570px;
	padding-left: 20px;
	position: relative;
}

@media (max-width: 1024px) {
	.testimonial--one .testimonial__content {
		text-align: center;
		padding-left: 0;
		margin: 0 auto;
	}
}

.testimonial--one .testimonial__content:after {
	content: "";
	position: absolute;
	width: 3px;
	height: 120px;
	background: #e7e9ed;
	left: 0;
	top: 42px;
}

@media (max-width: 1024px) {
	.testimonial--one .testimonial__content:after {
		display: none;
	}
}

.testimonial__info .avatar {
	height: 50px;
	width: 50px;
	border-radius: 50%;
}

.testimonial__info .avatar img {
	border-radius: 50%;
	height: 100%;
	width: 100%;
	-o-object-fit: cover;
	object-fit: cover;
}

.testimonial__content p {
	font-size: 20px;
	line-height: 34px;
	color: #0c1636;
}

@media (max-width: 991px) {
	.testimonial__content p {
		font-size: 16px;
		line-height: 28px;
	}
}

.testimonial__date {
	font-size: 14px;
	color: #4f5158;
	display: block;
}

.testimonial .tt-star-rating {
	margin-bottom: 7px;
}

.testimonial__name {
	font-size: 16px;
	font-weight: 700;
	color: #1c1e21;
	margin: 0;
}

.testimonial__designation {
	font-size: 14px;
	color: #4f5158;
	display: block;
	line-height: 20px;
}

.testimonial--two {
	background-color: #fff;
	border-radius: 10px;
	padding: 40px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.testimonial--two .testimonial__info {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 21px;
}

.testimonial--two .testimonial__info .avatar {
	margin-right: 10px;
}

.testimonial--two .testimonial__content p {
	font-size: 16px;
	line-height: 28px;
	color: #4f5158;
	margin: 0;
}

.testimonial--two .testimonial__content .review-link {
	display: inline-block;
	margin-top: 22px;
	color: #0c1636;
	font-size: 15px;
	font-weight: 500;
}

.testimonial--two .testimonial__content .review-link:hover {
	color: #fa5441;
}

.testimonial--two:hover {
	-webkit-box-shadow: 0px 40px 40px 0px rgba(0, 9, 40, 0.08);
	box-shadow: 0px 40px 40px 0px rgba(0, 9, 40, 0.08);
}

.testimonial--three {
	background-color: #fff;
	border-radius: 6px;
	padding: 50px;
}

.testimonial--three .quote {
	margin-bottom: 20px;
}

.testimonial--three .quote img {
	text-shadow: 0px 10px 20px rgba(194, 10, 77, 0.2);
}

.testimonial--three .testimonial__content p {
	color: #4f5158;
	font-size: 18px;
	line-height: 30px;
	margin-bottom: 28px;
}

.testimonial--three .testimonial__name {
	font-size: 18px;
}

.testimonial--four .testimonial__avater {
	width: 70px;
	height: 70px;
	border: 3px solid #fff;
	border-radius: 50%;
	-webkit-box-shadow: 0px 30px 40px 0px rgba(0, 9, 40, 0.14);
	box-shadow: 0px 30px 40px 0px rgba(0, 9, 40, 0.14);
	margin-bottom: 25px;
}

.testimonial--four .testimonial__avater img {
	border-radius: 50%;
	height: 100%;
	width: 100%;
	-o-object-fit: cover;
	object-fit: cover;
}

.testimonial--four .testimonial__title {
	color: #fff;
	font-size: 20px;
	margin-bottom: 13px;
}

.testimonial--four .testimonial__content p {
	font-size: 18px;
	color: rgba(255, 255, 255, 0.902);
	line-height: 30px;
	margin-bottom: 30px;
}

.testimonial--four .testimonial__bio .testimonial__name {
	font-size: 18px;
	color: #fff;
	margin-bottom: 2px;
}

.testimonial--four .testimonial__bio .testimonial__designation {
	color: #d9dce7;
}

.testimonial--five {
	text-align: center;
}

.testimonial--five .testimonial__content .title {
	font-size: 16px;
	font-weight: 700;
	margin-bottom: 18px;
	color: #ffff;
}

.testimonial--five .testimonial__content p {
	font-size: 24px;
	line-height: 36px;
	max-width: 620px;
	margin: 0 auto 40px;
	color: #ffff;
}

@media (max-width: 767px) {
	.testimonial--five .testimonial__content p {
		font-size: 18px;
		line-height: 28px;
	}
}

.testimonial--five .testimonial__info .avatar {
	margin: 0 auto 20px;
	height: 65px;
	width: 65px;
	-webkit-box-shadow: 0 20px 20px 0 rgba(2, 19, 79, 0.1);
	box-shadow: 0 20px 20px 0 rgba(2, 19, 79, 0.1);
	border: 3px solid #fff;
}

.testimonial--five .testimonial__name {
	font-size: 20px;
	color: #fff;
	margin-bottom: 3px;
}

.testimonial--five .testimonial__designation {
	color: rgba(255, 255, 255, 0.8);
}

.testimonial--six {
	background-color: #fff;
	padding: 30px;
}

.testimonial--six .testimonial__info-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.testimonial--six .testimonial__avater {
	height: 45px;
	width: 45px;
	border-radius: 50%;
	overflow: hidden;
	margin-right: 15px;
}

.testimonial--six .testimonial__content {
	margin-bottom: 25px;
}

.testimonial--six .testimonial__content p {
	font-size: 24px;
	line-height: 36px;
	margin-bottom: 0;
}

.testimonial--seven .testimonial__info-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 22px;
}

.testimonial--seven .testimonial__avater {
	margin-right: 20px;
	border-radius: 50%;
	height: 60px;
	width: 60px;
	border: 1px solid rgba(255, 255, 255, 0.3);
}

.testimonial--seven .testimonial__avater img {
	border-radius: 50%;
	height: 100%;
	width: 100%;
	-o-object-fit: cover;
	object-fit: cover;
}

.testimonial--seven .testimonial__name {
	font-size: 18px;
	font-weight: 700;
	color: #fff;
	margin-bottom: 5px;
}

.testimonial--seven .testimonial__designation {
	color: rgba(255, 255, 255, 0.6);
}

.testimonial--seven .testimonial__content p {
	color: #fff;
	font-size: 16px;
	line-height: 26px;
	margin-bottom: 0;
}

.testimonial-control-two {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	margin-top: 28px;
}

.testimonial-control-two .testi-prev,
.testimonial-control-two .testi-next {
	color: rgba(255, 255, 255, 0.302);
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	font-size: 24px;
}

.testimonial-control-two .testi-prev:hover,
.testimonial-control-two .testi-next:hover {
	color: #fff;
}

.testimonial-control-two .testi-prev {
	margin-right: 10px;
}

.rating {
	margin-bottom: 4px;
	padding: 0;
	list-style: none;
}

.rating li {
	display: inline-block;
	color: #ffa32b;
	font-size: 14px;
}

.tt-testimonial.style-1 {
	padding-top: 40px;
}

@media (max-width: 1024px) {
	.tt-testimonial.style-1 {
		padding-bottom: 50px;
	}
}

.tt-testimonial.style-1 .testimonial-pagination {
	bottom: auto;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	top: 50%;
	width: -webkit-fit-content;
	width: -moz-fit-content;
	width: fit-content;
	z-index: 2;
}

@media (max-width: 991px) {
	.tt-testimonial.style-1 .testimonial-pagination {
		bottom: 0;
	}
}

@media (max-width: 1024px) {
	.tt-testimonial.style-1 .testimonial-pagination {
		top: auto;
		left: 50%;
		-webkit-transform: translateX(-50%);
		-ms-transform: translateX(-50%);
		transform: translateX(-50%);
	}
}

.tt-testimonial.style-1 .testimonial-pagination .swiper-pagination-bullet {
	display: block;
	margin: 10px 0 !important;
}

@media (max-width: 1024px) {
	.tt-testimonial.style-1 .testimonial-pagination .swiper-pagination-bullet {
		display: inline-block;
		margin: 0 5px !important;
	}
}

.tt-testimonial.style-1 .swiper-slide .testimonial__info .avatar,
.tt-testimonial.style-1 .swiper-slide .testimonial__info .testimonial__bio {
	-webkit-transform: translateY(-20px);
	-ms-transform: translateY(-20px);
	transform: translateY(-20px);
	-webkit-transition: all 0.5s;
	-o-transition: all 0.5s;
	transition: all 0.5s;
	opacity: 0;
}

.tt-testimonial.style-1 .swiper-slide .testimonial__info .testimonial__bio {
	-webkit-transition-delay: 0.3s;
	-o-transition-delay: 0.3s;
	transition-delay: 0.3s;
}

.tt-testimonial.style-1 .swiper-slide .testimonial__content .tt-star-rating,
.tt-testimonial.style-1 .swiper-slide .testimonial__content p,
.tt-testimonial.style-1 .swiper-slide .testimonial__content .testimonial__date {
	-webkit-transform: translateY(-10px);
	-ms-transform: translateY(-10px);
	transform: translateY(-10px);
	-webkit-transition: all 0.5s;
	-o-transition: all 0.5s;
	transition: all 0.5s;
	opacity: 0;
}

.tt-testimonial.style-1 .swiper-slide .testimonial__content p {
	-webkit-transition-delay: 0.3s;
	-o-transition-delay: 0.3s;
	transition-delay: 0.3s;
}

.tt-testimonial.style-1 .swiper-slide.swiper-slide-active .testimonial__info .avatar,
.tt-testimonial.style-1 .swiper-slide.swiper-slide-active .testimonial__info .testimonial__bio {
	-webkit-transform: translateY(0);
	-ms-transform: translateY(0);
	transform: translateY(0);
	opacity: 1;
}

.tt-testimonial.style-1 .swiper-slide.swiper-slide-active .testimonial__content .tt-star-rating,
.tt-testimonial.style-1 .swiper-slide.swiper-slide-active .testimonial__content p,
.tt-testimonial.style-1 .swiper-slide.swiper-slide-active .testimonial__content .testimonial__date {
	-webkit-transform: translateY(0);
	-ms-transform: translateY(0);
	transform: translateY(0);
	opacity: 1;
}

.tt-testimonial.style-2 {
	padding: 20px 0 70px;
}

.tt-testimonial.style-3 {
	-webkit-box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.12);
	box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.12);
}

.tt-testimonial.style-5 .testimonial-control {
	z-index: 2;
	width: 100%;
	position: absolute;
	top: 50%;
	left: 0;
}

.tt-testimonial.style-5 .testimonial-control>div {
	background-color: transparent;
	border: 1px solid rgba(255, 255, 255, 0.141);
	color: #fff;
}

.tt-testimonial.style-5 .testimonial-control>div:hover {
	background-color: #fff;
	border-color: #fff;
	color: #000;
}

.testimonial-thumb {
	margin-top: 40px;
	max-width: 170px;
	margin-left: 30px;
}

.testimonial-thumb .swiper-slide .testiminial-nav-thumb {
	-webkit-transform: scale(0.7);
	-ms-transform: scale(0.7);
	transform: scale(0.7);
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	cursor: pointer;
}

.testimonial-thumb .swiper-slide .testiminial-nav-thumb img {
	border: 2px solid #fff;
	background-color: #fff;
	border-radius: 50%;
}

.testimonial-thumb .swiper-slide.swiper-slide-thumb-active .testiminial-nav-thumb {
	-webkit-transform: scale(1);
	-ms-transform: scale(1);
	transform: scale(1);
}

.tt-testimonial-single-two {
	border-radius: 4px;
	background-color: #3283fd;
	padding: 50px;
	-webkit-box-shadow: 0px 30px 60px 0px rgba(2, 36, 145, 0.2);
	box-shadow: 0px 30px 60px 0px rgba(2, 36, 145, 0.2);
}

@media (max-width: 991px) {
	.tt-testimonial-single-two {
		margin-top: 30px;
	}
}

.testimonial-pagination-three {
	text-align: center;
}

.testimonial-pagination-three.testi-pagi-two {
	margin-top: 14px;
}

.testimonial-pagination-three.testi-pagi-two .swiper-pagination-bullet {
	background-color: #8d9bc2;
	border-color: #8d9bc2;
	height: 10px;
	width: 10px;
}

.testimonial--grid {
	margin-bottom: 30px;
}

.testimonial-area-five {
	background-color: #13318f;
	padding: 120px 0;
	background-image: url(../../media/background/testimonial_three.png);
	background-position: bottom center;
	background-repeat: no-repeat;
}

@media (max-width: 991px) {
	.testimonial-area-five {
		padding: 80px 0;
	}
}

.tt-testimonials-wrapper-seven {
	background-color: rgba(0, 9, 42, 0.949);
	padding: 60px 50px 49px;
	border-radius: 10px;
}

/* Rating Style */
.tt-star-1:before,
.tt-star-2:before,
.tt-star-3:before,
.tt-star-4:before,
.tt-star-5:before {
	content: "";
	color: #ff9915;
}

.tt-star-05 .tt-star-1:before,
.tt-star-15 .tt-star-2:before,
.tt-star-25 .tt-star-3:before,
.tt-star-35 .tt-star-4:before,
.tt-star-45 .tt-star-5:before {
	content: "";
	color: #ff9915;
}

.tt-star-10 .tt-star-1:before,
.tt-star-15 .tt-star-1:before,
.tt-star-20 .tt-star-1:before,
.tt-star-20 .tt-star-2:before,
.tt-star-25 .tt-star-1:before,
.tt-star-25 .tt-star-2:before,
.tt-star-30 .tt-star-1:before,
.tt-star-30 .tt-star-2:before,
.tt-star-30 .tt-star-3:before,
.tt-star-35 .tt-star-1:before,
.tt-star-35 .tt-star-2:before,
.tt-star-35 .tt-star-3:before,
.tt-star-40 .tt-star-1:before,
.tt-star-40 .tt-star-2:before,
.tt-star-40 .tt-star-3:before,
.tt-star-40 .tt-star-4:before,
.tt-star-45 .tt-star-1:before,
.tt-star-45 .tt-star-2:before,
.tt-star-45 .tt-star-3:before,
.tt-star-45 .tt-star-4:before,
.tt-star-50 .tt-star-1:before,
.tt-star-50 .tt-star-2:before,
.tt-star-50 .tt-star-3:before,
.tt-star-50 .tt-star-4:before,
.tt-star-50 .tt-star-5:before {
	content: "";
	color: #ff9915;
}

.testimonial-left-content .section-heading {
	margin-bottom: 33px;
}

.testimonial-left-content .section-heading .section-title {
	margin-bottom: 32px;
}

.testimonial-left-content .section-heading .description {
	font-weight: 500;
}

.testimonial-three-section {
	padding: 140px 0;
	background-color: #eff1f6;
	background-image: url(../../media/background/testimonial_bg.png);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

@media (max-width: 991px) {
	.testimonial-three-section {
		padding: 80px 0;
	}
}

@media (max-width: 767px) {
	.testimonial-left-content {
		margin-bottom: 40px;
	}
}

.testimonial-left-content-two .section-heading {
	margin-bottom: 23px;
}

.testimonial-left-content-two .section-heading .section-title {
	color: #fff;
	margin-bottom: 34px;
}

.testimonial-left-content-two .section-heading .description {
	color: #fff;
	font-weight: 600;
}

.testimonial-left-content-two .popup-play-btn {
	margin-bottom: 28px;
}

.testimonial-left-content-two .popup-play-btn:before {
	display: none;
}

.testimonial-four-section {
	position: relative;
	background-image: url(../../media/background/testimonial_bg_two.jpg);
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
	padding-top: 120px;
	background-attachment: fixed;
}

@media (max-width: 991px) {
	.testimonial-four-section {
		padding-top: 80px;
	}
}

.testimonial-four-section:before {
	content: "";
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background-color: rgba(14, 26, 64, 0.7);
}

.testimonial-four-section:after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 50px;
	background-color: #fff;
}

.testi-pagi-two {
	margin-top: 41px;
}

.testi-pagi-two .swiper-pagination-bullet {
	opacity: 1;
	background: transparent;
	border: 2px solid rgba(255, 255, 255, 0.2);
	width: 14px;
	height: 14px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.testi-pagi-two .swiper-pagination-bullet.swiper-pagination-bullet-active {
	background: #fff;
	border-color: #fff;
}

/* Testimonial Eight */
.testimonial-area-eight {
	background-color: #f3f4f7;
	padding: 120px 0;
	background-image: url(../../media/background/testimonial_eight.png);
	background-position: bottom center;
	background-repeat: no-repeat;
}

@media (max-width: 991px) {
	.testimonial-area-eight {
		padding: 80px 0;
	}
}

.tt-testimonial.style-8 .testimonial--five .testimonial__content .title {
	color: #11266d;
}

.tt-testimonial.style-8 .testimonial--five .testimonial__content p {
	color: #11266d;
}

.tt-testimonial.style-8 .testimonial--five .testimonial__name {
	color: #11266d;
}

.tt-testimonial.style-8 .testimonial--five .testimonial__designation {
	color: #3c435b;
}

.tt-testimonial.style-8 .testimonial-control {
	position: absolute;
	width: 100%;
	left: 0;
	top: 50%;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	z-index: 2;
}

.tt-testimonial.style-8 .testimonial-control>div:hover {
	background-color: #3283fd;
}

/*--------------------------------------------------------------
 ##  Support
 --------------------------------------------------------------*/
@media (min-width: 992px) {
	.support {
		padding-top: 70px;
	}
}

.support .title {
	font-size: 16px;
	font-weight: 500;
	color: #1c1e21;
	margin-bottom: 40px;
	text-align: center;
}

.support .section-heading {
	margin-bottom: 50px;
}

.support .section-heading .section-title {
	margin-bottom: 31px;
}

/*--------------------------------------------------------------
 ##  Promo
 --------------------------------------------------------------*/
.promo-area {
	padding: 140px 0 90px;
}

@media (max-width: 991px) {
	.promo-area {
		padding: 80px 0 50px;
	}
}

.promo-about-area {
	padding-bottom: 90px;
}

@media (max-width: 991px) {
	.promo-about-area {
		padding-bottom: 50px;
	}
}

.tt-promo {
	background-color: #a32061;
	padding: 65px 50px 70px;
	border-radius: 10px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 30px;
}

.tt-promo.color-two {
	background-color: #1f5ab4;
}

.tt-promo__content {
	width: 250px;
}

.tt-promo__title {
	color: #fff;
	font-weight: 700;
	font-size: 26px;
}

.tt-promo__description {
	color: #fff;
	margin-bottom: 30px;
}

.tt-promo .btn-outline {
	border-color: rgba(255, 255, 255, 0.4);
	color: #fff;
}

.tt-promo .btn-outline:before {
	background: #fff;
}

.tt-promo .btn-outline:hover {
	color: #3283fd;
	background-color: #ffffff;
	border-color: #fff;
	-webkit-box-shadow: 0px 10px 30px 0px rgba(1, 24, 99, 0.2);
	box-shadow: 0px 10px 30px 0px rgba(1, 24, 99, 0.2);
}

.tt-promo__image {
	position: relative;
	-webkit-box-flex: 2;
	-ms-flex: 2;
	flex: 2;
	text-align: right;
}

.tt-promo__image img {
	position: absolute;
	top: 50%;
	right: 0;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
}

.promo-section-area {
	padding: 57px 0 90px;
}

@media (max-width: 991px) {
	.promo-section-area {
		padding: 57px 0 50px;
	}
}

.promo-content-wrap {
	max-width: 670px;
	margin: 0 auto 140px;
	position: relative;
}

@media (max-width: 991px) {
	.promo-content-wrap {
		margin: 0 auto 80px;
	}
}

.promo-content-wrap .section-heading {
	margin-bottom: 0;
}

.promo-content-wrap .section-heading .section-title {
	font-size: 50px;
	margin-bottom: 11px;
	background: -webkit-gradient(linear, left top, right top, from(#fb514d), to(#1a82f5));
	background: -o-linear-gradient(left, #fb514d, #1a82f5);
	background: linear-gradient(90deg, #fb514d, #1a82f5);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.promo-content-wrap .section-heading .description {
	font-size: 18px;
	color: #4f5158;
	margin-bottom: 31px;
}

.promo-content-wrap .phone-info {
	font-size: 30px;
	color: #1c1e21;
	font-weight: 700;
	margin-bottom: 38px;
}

.promo-content-wrap .phone-info img {
	max-width: 28px;
	margin-right: 10px;
}

.promo-content-wrap .tt__btn {
	border-color: #fb514d;
	color: #fb514d;
	padding: 14px 34px;
}

.promo-content-wrap .tt__btn:before {
	background-color: #fb514d;
}

.promo-content-wrap .badge-wrap {
	position: absolute;
	bottom: -40px;
	width: 100%;
}

.promo-content-wrap .badge-wrap img {
	position: absolute;
	bottom: 0;
}

.promo-content-wrap .badge-wrap img.badge-right {
	right: 0;
}

.promo-box-two {
	position: relative;
	margin-bottom: 30px;
}

.promo-box-two:before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(12, 22, 54, 0.8);
}

.promo-box-two .box-image img {
	width: 100%;
}

.promo-box-two .promo-content {
	position: absolute;
	width: 100%;
	bottom: 0;
	left: 0;
	padding: 0 50px 60px;
}

@media (max-width: 420px) {
	.promo-box-two .promo-content {
		padding: 0 30px 20px;
	}
}

.promo-box-two .promo-content .box-title {
	font-size: 24px;
	color: #fff;
	margin-bottom: 11px;
}

.promo-box-two .promo-content .description {
	color: rgba(255, 255, 255, 0.8);
	margin-bottom: 18px;
}

@media (max-width: 420px) {
	.promo-box-two .promo-content .description {
		font-size: 14px;
	}

	.promo-box-two .promo-content .description br {
		display: none;
	}
}

.promo-box-two .promo-content .more-link {
	color: #fff;
	font-size: 14px;
}

/*--------------------------------------------------------------
##  Funfact
--------------------------------------------------------------*/
.funfact-section {
	background-color: #fff;
	padding: 65px 0 100px;
}

@media (max-width: 991px) {
	.funfact-section {
		padding: 65px 0 60px;
	}
}

.funfact-section .title {
	font-size: 20px;
	font-weight: 500;
	margin-bottom: 50px;
}

.counter-area-two {
	padding: 110px 0 120px;
}

@media (max-width: 991px) {
	.counter-area-two {
		padding: 80px 0 50px;
	}
}

.counter-area {
	padding: 265px 0 132px;
	background-image: url(../../media/background/counter_bg.png);
	overflow: hidden;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
}

@media (max-width: 991px) {
	.counter-area {
		padding: 220px 0 70px;
	}
}

@media (max-width: 767px) {
	.counter-area {
		padding: 100px 0 70px;
	}
}

.counter-area-three {
	background-image: url(../../media/background/counter_bg_two.png);
	background-size: cover;
	background-position: center center;
	padding-top: 140px;
	background-repeat: no-repeat;
}

@media (max-width: 767px) {
	.counter-area-three {
		padding: 0 0 60px;
	}
}

.counter-image-wrap {
	margin-left: -215px;
}

@media (max-width: 767px) {
	.counter-image-wrap {
		margin-left: 0;
	}
}

.counter-content-wrap {
	padding-top: 25px;
}

.counter-content-wrap .section-heading {
	margin-bottom: 62px;
}

.counter-content-wrap .section-heading .section-title {
	margin-bottom: 24px;
}

.counter-content-wrap .section-heading .description {
	font-size: 18px;
	line-height: 28px;
	font-weight: 500;
}

.tt-counter {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

@media (max-width: 991px) {
	.tt-counter {
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
	}
}

.tt-counter__count {
	color: #5f15cd;
	font-size: 44px;
	font-weight: 500;
	margin-bottom: 0;
	line-height: 1;
}

.tt-counter__count.color__two {
	color: #f97136;
}

.tt-counter__count.color__three {
	color: #10bd96;
}

.tt-counter__count.color__four {
	color: #f94495;
}

@media (max-width: 991px) {
	.tt-counter .tt-counter__item {
		width: 50%;
		margin-bottom: 40px;
	}
}

.tt-counter .tt-counter__item.style-two {
	padding: 0 15px;
	position: relative;
}

.tt-counter .tt-counter__item.style-two:not(:last-child):after {
	content: "";
	position: absolute;
	right: -60%;
	top: 50%;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	height: 140px;
	width: 1px;
	background-color: rgba(255, 255, 255, 0.14);
}

@media (max-width: 991px) {
	.tt-counter .tt-counter__item.style-two {
		text-align: center;
	}

	.tt-counter .tt-counter__item.style-two:after {
		right: 0 !important;
	}

	.tt-counter .tt-counter__item.style-two:nth-child(2):after {
		display: none;
	}
}

.tt-counter .tt-counter__item.style-two .tt-counter__count {
	color: #fff;
	font-weight: 800;
	font-size: 40px;
}

.tt-counter .tt-counter__item.style-two .tt-counter__title {
	color: rgba(255, 255, 255, 0.5);
}

.tt-counter .tt-counter__item.style-two .suffix {
	font-weight: 500;
	margin-left: -8px;
}

.tt-counter__title {
	font-size: 16px;
	color: #75777d;
	font-weight: 400;
	margin: 0;
}

.tt-counter .suffix {
	display: inline-block;
	margin-left: -10px;
}

.tt-counter.style-three {
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-box-pack: inherit;
	-ms-flex-pack: inherit;
	justify-content: inherit;
}

.tt-counter.style-three .tt-counter__item {
	width: 33.33%;
	padding: 60px 70px;
	background-color: #3283fd;
}

@media (max-width: 991px) {
	.tt-counter.style-three .tt-counter__item {
		padding: 50px 40px;
	}
}

@media (max-width: 767px) {
	.tt-counter.style-three .tt-counter__item {
		width: 100%;
		margin-bottom: 30px;
	}
}

.tt-counter.style-three .tt-counter__item .tt-counter__count {
	color: #fff;
	font-weight: 800;
	font-size: 50px;
}

@media (max-width: 991px) {
	.tt-counter.style-three .tt-counter__item .tt-counter__count {
		font-size: 34px;
	}
}

.tt-counter.style-three .tt-counter__item .tt-counter__count .suffix {
	font-weight: 400;
}

.tt-counter.style-three .tt-counter__item .tt-counter__title {
	font-size: 14px;
	color: #fff;
	margin-bottom: 5px;
}

.tt-counter.style-three .tt-counter__item.color-two {
	background-color: #121d41;
}

.tt-counter.style-three .tt-counter__item.color-theree {
	background-color: #f63a6f;
}

.tt-counter.style-four {
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	max-width: 460px;
}

.tt-counter.style-four .tt-counter__item {
	background-color: #fff;
	-webkit-box-shadow: 0 30px 50px 0 rgba(0, 9, 40, 0.1);
	box-shadow: 0 30px 50px 0 rgba(0, 9, 40, 0.1);
	padding: 14px 15px;
	width: 24%;
	text-align: center;
}

@media (max-width: 480px) {
	.tt-counter.style-four .tt-counter__item {
		width: 49%;
		margin-bottom: 10px;
	}
}

.tt-counter.style-four .tt-counter__item .counter {
	font-size: 40px;
	color: #3283fd;
}

.tt-counter.style-four .tt-counter__item .tt-counter__title {
	font-size: 14px;
	font-weight: 500;
	color: #1c1e21;
}

.tt-counter-box {
	background-color: #0257b6;
	padding: 56px 50px 60px;
	background-image: url(../../media/background/counter_bg_three.jpg);
	background-size: cover;
	background-position: center center;
	margin-bottom: 40px;
}

.tt-counter-box__subtitle {
	font-size: 15px;
	color: rgba(255, 255, 255, 0.8);
	margin-bottom: 14px;
}

.tt-counter-box__title {
	font-size: 26px;
	line-height: 32px;
	color: #fff;
	margin-bottom: 43px;
}

.tt-counter-box__item {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	margin-bottom: 29px;
}

.tt-counter-box__item.style-two .tt-counter-box__count {
	color: #11266d;
}

.tt-counter-box__item.style-two .tt-counter-box__count-title {
	color: #11266d;
	font-size: 15px;
	font-weight: 500;
}

.tt-counter-box__icon {
	margin-right: 20px;
}

.tt-counter-box__count {
	font-size: 30px;
	font-weight: 700;
	color: #fff;
	margin-bottom: 0;
	line-height: 1.1;
}

.tt-counter-box__count-title {
	color: rgba(255, 255, 255, 0.8);
	font-size: 14px;
	margin-bottom: 0;
}

.tt-counter-box .tt__btn {
	margin-top: 23px;
	border-radius: 6px;
	padding: 9px 34px;
}

/*--------------------------------------------------------------
 ##  Newsletter
 --------------------------------------------------------------*/
.newsletter {
	padding: 116px 0 115px;
	background-color: #3283fd;
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
	background-image: url(../../media/background/call_to_action.png);
}

@media (max-width: 991px) {
	.newsletter {
		padding: 80px 0;
	}
}

.newsletter .section-heading {
	margin-bottom: 0;
}

@media (max-width: 991px) {
	.newsletter .section-heading {
		text-align: center;
		margin-bottom: 40px;
	}
}

.newsletter .section-heading .section-title {
	color: #fff;
	font-size: 40px;
	margin-bottom: 13px;
	font-weight: 800;
}

@media (max-width: 991px) {
	.newsletter .section-heading .section-title {
		font-size: 30px;
	}
}

.newsletter .section-heading .description {
	font-size: 18px;
	color: rgba(255, 255, 255, 0.702);
	margin-bottom: 0;
	font-weight: 400;
}

@media (max-width: 768px) {
	.newsletter .section-heading {
		text-align: center;
		margin-bottom: 40px;
	}
}

.newsletter-three {
	padding: 94px 0;
	background-color: #fb514d;
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
	background-image: url(../../media/background/newsletter-bg.jpg);
	background-attachment: fixed;
}

.newsletter-three .section-heading {
	margin-bottom: 0;
}

.newsletter-three .section-heading .section-title {
	color: #fff;
	font-size: 44px;
}

@media (max-width: 991px) {
	.newsletter-three .section-heading {
		margin-bottom: 30px;
	}
}

.newsletter-three .info-text {
	color: #fff;
	font-size: 14px;
	margin-top: 10px;
	margin-bottom: 0;
}

.newsletter-three .info-text a {
	color: #fff;
	font-weight: 500;
}

.form-result {
	display: none;
}

.newsletter-form {
	max-width: 1050px;
}

@media (max-width: 991px) {
	.newsletter-form {
		max-width: 500px;
		margin: 0 auto;
	}
}

.newsletter-form .newsletter-inner {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.newsletter-form .newsletter-inner.style_one {
	border-radius: 30px;
	border: 2px solid rgba(255, 255, 255, 0.502);
}

.newsletter-form .newsletter-inner.style_one:focus {
	border-color: #fff;
}

.newsletter-form .newsletter-inner.style_one .newsletter-submit {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
	min-width: 161px;
}

.newsletter-form .newsletter-inner.style_one .newsletter-submit:after {
	content: "";
	position: absolute;
	top: -2px;
	right: -2px;
	bottom: -2px;
	border: 2px solid #fff;
	left: -2px;
	border-top-right-radius: 30px;
	border-bottom-right-radius: 30px;
}

.newsletter-form .newsletter-inner.style_one .newsletter-submit:before {
	display: none;
}

.newsletter-form .newsletter-inner.style_one .newsletter-submit.clicked i {
	display: block;
}

.newsletter-form .newsletter-inner.style_two input:not([type=checkbox]):not([type=submit]) {
	border: 2px solid #5839cc;
	border-radius: 6px;
	background-color: #2e1781;
	margin-right: 10px;
}

.newsletter-form .newsletter-inner.style_two .newsletter-submit {
	overflow: hidden;
	border-radius: 6px;
}

.newsletter-form .newsletter-inner.style_two .newsletter-submit:before {
	background: #fff;
}

.newsletter-form .newsletter-inner.style_two .newsletter-submit.clicked i {
	display: block;
}

.newsletter-form .form-result {
	margin: 20px 5px 0;
}

.newsletter-form input:not([type=checkbox]):not([type=submit]) {
	border: 0;
	padding: 17px 25px;
	line-height: 19px;
	font-size: 14px;
	height: 50px;
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
	background: transparent;
	margin: 0;
	color: #fff;
	border-top-left-radius: 30px;
	border-bottom-left-radius: 30px;
}

.newsletter-form input:not([type=checkbox]):not([type=submit])::-webkit-input-placeholder {
	color: rgba(255, 255, 255, 0.502);
}

.newsletter-form input:not([type=checkbox]):not([type=submit])::-moz-placeholder {
	color: rgba(255, 255, 255, 0.502);
}

.newsletter-form input:not([type=checkbox]):not([type=submit]):-ms-input-placeholder {
	color: rgba(255, 255, 255, 0.502);
}

.newsletter-form input:not([type=checkbox]):not([type=submit])::-ms-input-placeholder {
	color: rgba(255, 255, 255, 0.502);
}

.newsletter-form input:not([type=checkbox]):not([type=submit])::placeholder {
	color: rgba(255, 255, 255, 0.502);
}

.newsletter-form input:not([type=checkbox]):not([type=submit]):focus {
	border-color: #fff;
	-webkit-box-shadow: none;
	box-shadow: none;
}

@media (max-width: 420px) {
	.newsletter-form input:not([type=checkbox]):not([type=submit]) {
		padding: 10px 15px;
	}
}

.newsletter-form .newsletter-submit {
	font-size: 15px;
	font-weight: 500;
	height: 50px;
	border: 0;
	padding: 0 26px;
	outline: 0;
	background-color: #fff;
	color: #1c1e21;
	overflow: visible;
}

@media (max-width: 420px) {
	.newsletter-form .newsletter-submit {
		padding: 10px 15px;
	}
}

.newsletter-form .newsletter-submit .fa-spin {
	display: none;
}

.newsletter-form .newsletter-submit.clicked .fa-spin {
	display: block;
}

.newsletter-form .newsletter-submit.clicked span {
	display: none;
}

.newsletter-info {
	margin: 10px 0 0;
	padding: 0;
	list-style: none;
}

@media (max-width: 991px) {
	.newsletter-info {
		text-align: center;
	}
}

.newsletter-info li {
	display: inline-block;
	color: #fff;
	font-size: 14px;
	color: rgba(255, 255, 255, 0.6);
}

.newsletter-info li i {
	color: #fff;
	margin-right: 10px;
}

.newsletter-info li:not(:last-child) {
	margin-right: 15px;
}

.newsletter-form-three .newsletter-inner {
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	background-color: #fff;
	border-radius: 6px;
}

.newsletter-form-three input {
	height: 60px;
	border: 0;
	background-color: transparent;
}

.newsletter-form-three input:focus {
	outline: 0;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.newsletter-form-three .newsletter-submit {
	border-radius: 6px;
	min-width: 140px;
	height: 60px;
	border-color: #0c1636;
}

.newsletter-form-three .newsletter-submit:before {
	background-color: #0c1636;
}

.newsletter-form-three .newsletter-submit i {
	display: none;
}

.newsletter-form-three .newsletter-submit:hover {
	color: #1c1e21;
}

.newsletter-four-section {
	margin-top: -320px;
}

@media (max-width: 991px) {
	.newsletter-four-section {
		margin-top: 0;
		padding: 80px 0;
	}

	.newsletter-four-section .newsletter-wrapper {
		max-width: 95%;
		margin: 0 auto;
	}
}

.newsletter-wrapper {
	border-radius: 10px;
	background-color: #2a5cff;
	-webkit-box-shadow: 0px 40px 60px 0px rgba(17, 26, 59, 0.2);
	box-shadow: 0px 40px 60px 0px rgba(17, 26, 59, 0.2);
	max-width: 970px;
	margin: 0 auto;
	padding: 90px 50px 75px;
	background-image: url(../../media/background/newsletter-four.png);
	background-size: cover;
	background-position: center center;
}

.newsletter-wrapper .section-heading {
	margin-bottom: 40px;
}

.newsletter-wrapper .section-heading .section-title {
	color: #fff;
	font-size: 40px;
	font-weight: 800;
}

.newsletter-wrapper .newsletter-form {
	max-width: 570px;
	margin: 0 auto;
}

.newsletter-wrapper .newsletter-form input:not([type=checkbox]):not([type=submit]) {
	color: #1c1e21;
	padding: 10px 15px;
}

.newsletter-wrapper .newsletter-form input:not([type=checkbox]):not([type=submit])::-webkit-input-placeholder {
	color: #84878f;
}

.newsletter-wrapper .newsletter-form input:not([type=checkbox]):not([type=submit])::-moz-placeholder {
	color: #84878f;
}

.newsletter-wrapper .newsletter-form input:not([type=checkbox]):not([type=submit]):-ms-input-placeholder {
	color: #84878f;
}

.newsletter-wrapper .newsletter-form input:not([type=checkbox]):not([type=submit])::-ms-input-placeholder {
	color: #84878f;
}

.newsletter-wrapper .newsletter-form input:not([type=checkbox]):not([type=submit])::placeholder {
	color: #84878f;
}

.newsletter-wrapper .newsletter-inner {
	padding: 10px;
}

.newsletter-wrapper .newsletter-inner.style_four {
	background-color: #fff;
	border-radius: 6px;
}

.newsletter-wrapper .newsletter-inner .newsletter-submit {
	background-color: #ff393e;
	color: #fff;
	border-radius: 6px;
}

.newsletter-wrapper .newsletter-inner .newsletter-submit:hover {
	background-color: #d20005;
}

.newsletter-wrapper .newsletter-inner .form-control {
	color: #000;
}

.newsletter-wrapper .description {
	color: rgba(255, 255, 255, 0.702);
	text-align: center;
	margin-top: 17px;
}

/*--------------------------------------------------------------
##  SEO Score
--------------------------------------------------------------*/
.seo-score-wrapper {
	padding: 71px 50px 74px;
	background: #fff;
	-webkit-box-shadow: 0px 30px 80px 0px rgba(0, 9, 40, 0.14);
	box-shadow: 0px 30px 80px 0px rgba(0, 9, 40, 0.14);
	border-radius: 6px;
	position: relative;
	z-index: 2;
	background-image: url(../../media/background/seo_score.png);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

.seo-score-wrapper .section-heading {
	margin-bottom: 50px;
}

.seo-score-wrapper .bottom-content {
	margin-top: 23px;
	text-align: center;
}

.seo-score-wrapper .bottom-content p {
	font-size: 14px;
	line-height: 22px;
	color: #7b7d84;
	margin: 0;
}

@media (max-width: 576px) {
	.seo-score-wrapper .bottom-content p br {
		display: none;
	}
}

.score-form {
	max-width: 770px;
	margin: 0 auto;
}

.scoreform-inner {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
}

.scoreform-inner input:not([type=checkbox]):not([type=submit]) {
	margin-right: 10px;
	height: 50px;
	border: 0;
	background-color: #f1f2f5;
	font-size: 14px;
	border-radius: 6px;
	padding: 15px 30px;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.scoreform-inner input:not([type=checkbox]):not([type=submit])::-webkit-input-placeholder {
	color: #83858c;
}

.scoreform-inner input:not([type=checkbox]):not([type=submit])::-moz-placeholder {
	color: #83858c;
}

.scoreform-inner input:not([type=checkbox]):not([type=submit]):-ms-input-placeholder {
	color: #83858c;
}

.scoreform-inner input:not([type=checkbox]):not([type=submit])::-ms-input-placeholder {
	color: #83858c;
}

.scoreform-inner input:not([type=checkbox]):not([type=submit])::placeholder {
	color: #83858c;
}

.scoreform-inner #score-form-url {
	max-width: 300px;
}

@media (max-width: 767px) {
	.scoreform-inner #score-form-url {
		max-width: 100%;
		-webkit-box-flex: 1;
		-ms-flex: auto;
		flex: auto;
		margin-right: 0;
		margin-bottom: 10px;
	}
}

.scoreform-inner #score-form-email {
	max-width: 300px;
}

@media (max-width: 420px) {
	.scoreform-inner #score-form-email {
		width: 100%;
		-webkit-box-flex: 1;
		-ms-flex: auto;
		flex: auto;
		margin-right: 0;
		margin-bottom: 30px;
		max-width: 100%;
	}
}

.scoreform-inner .score-submit {
	font-size: 15px;
	padding: 14px 25px;
}

@media (max-width: 991px) {
	.scoreform-inner .score-submit {
		margin-top: 10px;
	}
}

.dot-right-shape {
	position: absolute;
	right: 0;
	bottom: -21px;
}

/*--------------------------------------------------------------
 ## Feature Slider
 --------------------------------------------------------------*/
.feature-two-section {
	padding-top: 130px;
	margin-bottom: -140px;
}

@media (max-width: 767px) {
	.feature-two-section {
		margin-bottom: 0;
		padding: 80px 0;
	}
}

.feature-two-section .section-heading {
	margin-bottom: 62px;
}

.feature-slider-two {
	padding: 110px 0 80px;
}

@media (max-width: 991px) {
	.feature-slider-two {
		padding: 70px 0 40px;
	}
}

.tt-feature__image {
	position: relative;
	border-radius: 6px;
	overflow: hidden;
}

.tt-feature__image img {
	width: 100%;
}

.tt-feature__image:after {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image: -webkit-gradient(linear, left bottom, left top, from(#000822), to(rgba(0, 8, 34, 0)));
	background-image: -o-linear-gradient(bottom, #000822 0%, rgba(0, 8, 34, 0) 100%);
	background-image: linear-gradient(0deg, #000822 0%, rgba(0, 8, 34, 0) 100%);
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-feature__image img {
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-feature__info {
	position: absolute;
	bottom: 0;
	left: 0;
	padding: 30px;
}

.tt-feature__title {
	color: #fff;
	font-size: 20px;
}

.tt-feature__cat {
	color: #fff;
	font-size: 14px;
	font-weight: 400;
}

.tt-feature:hover .tt-feature__image img {
	-webkit-transform: scale(1.1);
	-ms-transform: scale(1.1);
	transform: scale(1.1);
}

.tt-feature--two {
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-feature--two .tt-feature__image:after {
	background-image: -webkit-gradient(linear, left bottom, left top, from(#1a82f5), to(rgba(26, 130, 245, 0)));
	background-image: -o-linear-gradient(bottom, #1a82f5 0%, rgba(26, 130, 245, 0) 100%);
	background-image: linear-gradient(0deg, #1a82f5 0%, rgba(26, 130, 245, 0) 100%);
	opacity: 0.949;
}

.tt-feature--two:hover {
	-webkit-box-shadow: 0px 30px 50px 0px rgba(10, 96, 194, 0.3);
	box-shadow: 0px 30px 50px 0px rgba(10, 96, 194, 0.3);
}

.feature-control {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	width: 80px;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

@media (min-width: 768px) {
	.feature-control {
		margin-left: auto;
	}
}

@media (max-width: 767px) {
	.feature-control {
		margin-bottom: 30px;
	}
}

.feature-control .testi-next,
.feature-control .testi-prev {
	width: 30px;
}

.feature-control .testi-next svg,
.feature-control .testi-prev svg {
	fill: #b3b5bd;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.feature-control .testi-next svg:hover,
.feature-control .testi-prev svg:hover {
	fill: #3283fd;
}

.feature-pagination {
	margin-top: 43px;
	text-align: center;
}

.feature-pagination .swiper-pagination-bullet {
	opacity: 1;
	background: transparent;
	border: 2px solid #e3e4e9;
	width: 14px;
	height: 14px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.feature-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
	background: #fb514d;
	border-color: #fb514d;
}

/*--------------------------------------------------------------
 ##  Intro Video
 --------------------------------------------------------------*/
.intro-video {
	background-image: url(../../media/background/intro_video_bg.png);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
	padding: 171px 0 185px;
	background-attachment: fixed;
}

@media (max-width: 991px) {
	.intro-video {
		padding: 80px 0 100px;
	}
}

.intro-video-two {
	padding: 150px 0;
	background-image: url(../../media/background/intro_video_bg_two.jpg);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

@media (max-width: 991px) {
	.intro-video-two {
		padding: 80px 0;
	}
}

.video-content .section-heading {
	margin: 0;
}

@media (max-width: 991px) {
	.video-content .section-heading {
		text-align: center;
	}
}

.video-content .section-heading .section-title {
	color: #fff;
	font-size: 50px;
	line-height: 1.2;
}

.video-content .section-heading .description {
	color: #fff;
}

.video-content .section-heading .tt__btn {
	margin-top: 33px;
}

.video-content.style-two {
	border-radius: 10px;
	background-color: rgba(26, 13, 82, 0.4);
	padding: 70px 100px;
	max-width: 770px;
	margin: 0 auto;
	border: 3px solid rgba(255, 255, 255, 0.14);
}

@media (max-width: 991px) {
	.video-content.style-two {
		padding: 60px 80px;
	}
}

@media (max-width: 576px) {
	.video-content.style-two {
		padding: 50px 40px;
	}
}

.video-content.style-two .play-button {
	margin-bottom: 50px;
}

.video-content.style-two .play-button .popup-play-btn:before {
	display: none;
}

.video-content.style-two .section-heading .section-title {
	font-size: 40px;
	margin-bottom: 13px;
}

.video-content.style-three {
	padding: 0 28% 0 12%;
}

@media (max-width: 1600px) {
	.video-content.style-three {
		padding: 0 6% 0 6%;
	}
}

.video-content.style-three .section-heading {
	margin-bottom: 52px;
}

@media (max-width: 1280px) {
	.video-content.style-three .section-heading {
		margin-bottom: 30px;
	}
}

.video-content.style-three .section-heading .section-title {
	color: #1c1e21;
}

@media (max-width: 1280px) {
	.video-content.style-three .section-heading .section-title {
		font-size: 40px;
	}
}

@media (max-width: 1280px) {
	.video-content.style-three .section-heading .section-subtitle {
		font-size: 30px;
	}
}

.video-content.style-three .section-heading .description {
	color: #3c435b;
}

@media (min-width: 992px) and (max-width: 1200px) {
	.video-content.style-three .section-heading .description {
		font-size: 14px;
	}
}

@media (max-width: 991px) {
	.video-content.style-three {
		padding: 10% 14% 10%;
	}
}

@media (max-width: 991px) {
	.play-button.text-right {
		text-align: center !important;
		margin-top: 50px;
	}
}

.intro-video-three {
	background-color: #0c1636;
	padding: 360px 0 120px;
	background-image: url(../../media/background/intro_video_bg_three.jpg);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

@media (max-width: 991px) {
	.intro-video-three {
		padding: 70px 0 80px;
	}
}

.video-thumbnail {
	position: relative;
	margin-bottom: -360px;
	z-index: 2;
	margin-top: 130px;
}

@media (max-width: 991px) {
	.video-thumbnail {
		margin: 20px 0 0;
	}
}

.video-thumbnail .play-button {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}

.video-content-wrapper {
	max-width: 670px;
	margin: 62px auto 0;
}

@media (max-width: 991px) {
	.video-content-wrapper {
		margin: 0;
	}
}

.video-content-wrapper .video-title {
	color: #fff;
	font-size: 40px;
	font-weight: 800;
	margin-bottom: 23px;
}

.video-content-wrapper .description {
	color: rgba(255, 255, 255, 0.702);
	margin-bottom: 51px;
}

.video-content-wrapper .tt__btn {
	border-color: #fb514d;
	padding: 14px 27px;
}

.video-content-wrapper .tt__btn:before {
	background-color: #fb514d;
}

.video-content-wrapper .tt__btn:hover {
	color: #fff;
	border-color: rgba(255, 255, 255, 0.2);
}

.video-content-wrapper .tt__btn.btn-outline {
	border-color: rgba(255, 255, 255, 0.2);
	color: #fff;
}

.video-content-wrapper .tt__btn.btn-outline:hover {
	border-color: #fb514d;
}

.video-content-wrapper .button-container>.tt__btn {
	margin-right: 20px;
}

@media (max-width: 420px) {
	.video-content-wrapper .button-container .btn-outline {
		margin-top: 10px;
	}
}

.intro-video-four {
	background-color: #f0f1f5;
	background-image: url(../../media/background/intro_video_bg_five.jpg);
	background-position: center center;
	background-repeat: no-repeat;
	background-size: cover;
}

.video-intro-box .play-button {
	position: absolute;
	left: 50%;
	top: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}

.tt-chamber-time-box {
	background-color: #fff;
	padding: 45px 30px;
}

@media (min-width: 992px) and (max-width: 1200px) {
	.tt-chamber-time-box {
		padding: 30px 21px;
	}
}

@media (max-width: 767px) {
	.tt-chamber-time-box {
		margin-bottom: 30px;
	}
}

.tt-chamber-time-box .chamber-icon {
	margin-bottom: 30px;
	position: relative;
	width: -webkit-max-content;
	width: -moz-max-content;
	width: max-content;
}

.tt-chamber-time-box .hour-time {
	position: absolute;
	top: -3px;
	right: -20px;
	background-color: #27b261;
	border-radius: 20px;
	display: block;
	font-size: 12px;
	line-height: 1;
	padding: 3px 9px;
	color: #fff;
}

.tt-chamber-time-box .hour-time.color-two {
	background-color: #6c5dd3;
}

.tt-chamber-time-box .chamber-title {
	font-size: 18px;
	font-weight: 600;
	margin-bottom: 11px;
}

@media (min-width: 992px) and (max-width: 1200px) {
	.tt-chamber-time-box .chamber-title {
		font-size: 15px;
	}
}

.tt-chamber-time-box .chamber-description {
	font-size: 15px;
	line-height: 24px;
	margin-bottom: 0;
}

@media (min-width: 992px) and (max-width: 1200px) {
	.tt-chamber-time-box .chamber-description {
		font-size: 12px;
		line-height: 22px;
	}
}

/*--------------------------------------------------------------
 ##  Screenshot
 --------------------------------------------------------------*/
.screenshot-area {
	padding: 115px 0 32px;
}

@media (max-width: 991px) {
	.screenshot-area {
		padding: 75px 0 0;
	}
}

.tt-screenshots {
	padding: 20px 0 82px;
}

.tt-screenshots .slider-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
	background-color: #fa5441;
}

.screenshot-slider img {
	-webkit-box-shadow: 0 30px 30px 0 rgba(0, 9, 40, 0.09) !important;
	box-shadow: 0 30px 30px 0 rgba(0, 9, 40, 0.09) !important;
	border-radius: 6px !important;
	width: 100%;
}

.slider-pagination {
	-webkit-transform: translateX(0);
	-ms-transform: translateX(0);
	transform: translateX(0);
	left: 0;
	text-align: center;
}

/*--------------------------------------------------------------
##  Download
--------------------------------------------------------------*/
.download-area {
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
	padding: 120px 0;
	margin-top: 35px;
	background-image: linear-gradient(21deg, #4D82FF 20%, #E269DF 80%, #E269DF 100%);
}

@media (max-width: 991px) {
	.download-area {
		padding: 70px 0 80px;
	}
}

.download-area .section-heading {
	margin: 0;
}

@media (max-width: 767px) {
	.download-area .section-heading {
		text-align: center;
		margin-bottom: 40px;
	}
}

.download-area .section-heading .section-title {
	color: #fff;
	font-size: 40px;
}

.download-area .section-heading .description {
	color: rgba(255, 255, 255, 0.702);
}

.download-area .tt-app-btn {
	border-color: rgba(255, 255, 255, 0.2);
	color: #fff;
}

.download-area .tt-app-btn:hover {
	background-color: #fff;
	color: #5f15cd;
	border-color: #fff;
}

/*--------------------------------------------------------------
 ##  Team
 --------------------------------------------------------------*/
.team-section {
	padding: 120px 0 90px;
}

@media (max-width: 991px) {
	.team-section {
		padding: 80px 0 50px;
	}
}

.team-section-two {
	padding: 115px 0 120px;
}

@media (max-width: 991px) {
	.team-section-two {
		padding: 75px 0 80px;
	}
}

.team-section-three {
	padding-bottom: 90px;
	margin-top: -280px;
}

.team-section-three .team-title {
	font-size: 16px;
	font-weight: 500;
	color: #4f5158;
	margin-bottom: 26px;
}

.team-section-five {
	padding: 100px 0 120px;
}

@media (max-width: 991px) {
	.team-section-five {
		padding: 0 0 80px;
	}
}

.doctors {
	padding-top: 111px;
}

@media (max-width: 991px) {
	.doctors {
		padding-top: 71px;
	}
}

.tt-team {
	position: relative;
	margin-bottom: 30px;
}

.tt-team__avater {
	position: relative;
	border-radius: 6px;
	overflow: hidden;
}

.tt-team__avater img {
	width: 100%;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-team__avater:before {
	content: "";
	position: absolute;
	width: 100%;
	height: 100%;
	background-color: rgba(50, 131, 253, 0.8);
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	opacity: 0;
	z-index: 2;
	top: 0;
	left: 0;
}

.tt-team__social {
	margin: 0;
	padding: 0;
	list-style: none;
	position: absolute;
	bottom: 30px;
	left: 40px;
	z-index: 2;
	height: 36px;
	overflow: hidden;
}

.tt-team__social li {
	display: inline-block;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	-webkit-transform: translateY(20px);
	-ms-transform: translateY(20px);
	transform: translateY(20px);
	opacity: 0;
}

.tt-team__social li:not(:last-child) {
	margin-right: 7px;
}

.tt-team__social li:nth-child(2) {
	-webkit-transition-delay: 0.1s;
	-o-transition-delay: 0.1s;
	transition-delay: 0.1s;
}

.tt-team__social li:nth-child(3) {
	-webkit-transition-delay: 0.2s;
	-o-transition-delay: 0.2s;
	transition-delay: 0.2s;
}

.tt-team__social li:nth-child(4) {
	-webkit-transition-delay: 0.3s;
	-o-transition-delay: 0.3s;
	transition-delay: 0.3s;
}

.tt-team__social li:nth-child(5) {
	-webkit-transition-delay: 0.4s;
	-o-transition-delay: 0.4s;
	transition-delay: 0.4s;
}

.tt-team__social li:nth-child(6) {
	-webkit-transition-delay: 0.5s;
	-o-transition-delay: 0.5s;
	transition-delay: 0.5s;
}

.tt-team__social li a {
	display: block;
	height: 36px;
	width: 36px;
	line-height: 33px;
	text-align: center;
	border-radius: 6px;
	border: 2px solid rgba(255, 255, 255, 0.102);
	color: #fff;
	font-size: 14px;
}

.tt-team__social li a:hover {
	background-color: #fff;
	border-color: #fff;
	color: #3283fd;
}

.tt-team__info {
	padding: 15px 30px 0;
}

.tt-team__name {
	font-size: 18px;
	font-weight: 700;
	margin-bottom: 3px;
}

.tt-team__designation {
	font-size: 14px;
	color: #4f5158;
	font-weight: 400;
}

.tt-team:hover .tt-team__avater {
	-webkit-box-shadow: 0 30px 30px 0 rgba(8, 37, 132, 0.2);
	box-shadow: 0 30px 30px 0 rgba(8, 37, 132, 0.2);
}

.tt-team:hover .tt-team__avater:before {
	opacity: 1;
}

.tt-team:hover .tt-team__avater img {
	-webkit-transform: scale(1.05) rotate(3deg);
	-ms-transform: scale(1.05) rotate(3deg);
	transform: scale(1.05) rotate(3deg);
}

.tt-team:hover .tt-team__social li {
	-webkit-transform: translateY(0);
	-ms-transform: translateY(0);
	transform: translateY(0);
	opacity: 1;
}

.tt-team.style-two {
	border: 2px solid #ecedf0;
	padding: 50px 45px;
	border-radius: 6px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

@media (max-width: 991px) {
	.tt-team.style-two {
		padding: 35px;
	}
}

.tt-team.style-two .tt-team__avater {
	max-width: 190px;
	max-height: 190px;
	border-radius: 50%;
	margin: 0 auto 26px;
	width: 100%;
	height: 100%;
}

.tt-team.style-two .tt-team__avater:before {
	display: none;
}

.tt-team.style-two .tt-team__info {
	text-align: center;
	padding: 0;
}

.tt-team.style-two:hover {
	-webkit-box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.12);
	box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.12);
	border-color: transparent;
}

.tt-team.style-two:hover .tt-team__avater {
	-webkit-box-shadow: none;
	box-shadow: none;
}

.tt-team.style-two.team-box {
	border-radius: 4px;
	padding: 0;
	-webkit-box-shadow: 0px 30px 50px 0px rgba(0, 9, 40, 0.12);
	box-shadow: 0px 30px 50px 0px rgba(0, 9, 40, 0.12);
	background-color: #fff;
	border: 0;
}

.tt-team.style-two.team-box .tt-team__avater-wrap {
	max-width: inherit;
}

.tt-team.style-two.team-box .social-wrap {
	bottom: 20px;
}

.tt-team.style-two.team-box .tt-team__avater {
	max-width: inherit;
	max-height: inherit;
	border-radius: 0;
	margin-bottom: 0;
}

.tt-team.style-two.team-box .tt-team__info {
	padding: 25px 30px 18px;
	text-align: left;
}

.tt-team.style-three {
	border: 2px solid #ecedf0;
	padding: 20px 20px 25px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-team.style-three .tt-team__avater-wrap {
	max-width: 100%;
}

.tt-team.style-three .tt-team__avater-wrap .tt-team__avater {
	border-radius: 0;
}

.tt-team.style-three .tt-team__avater-wrap .tt-team__avater:before {
	display: none;
}

.tt-team.style-three .social-wrap {
	left: 50%;
	bottom: -21px;
	-webkit-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	transform: translateX(-50%);
}

.tt-team.style-three .social-wrap .phone-number {
	font-size: 16px;
	background-color: #089df1;
	-webkit-box-shadow: 0 14px 24px 0 rgba(0, 88, 138, 0.3);
	box-shadow: 0 14px 24px 0 rgba(0, 88, 138, 0.3);
}

.tt-team.style-three .tt-team__info {
	text-align: center;
	padding: 30px 0 0;
}

.tt-team.style-three .tt-team__name {
	margin-bottom: 9px;
}

.tt-team.style-three:hover {
	-webkit-box-shadow: 0 30px 70px 0 rgba(0, 9, 40, 0.12);
	box-shadow: 0 30px 70px 0 rgba(0, 9, 40, 0.12);
	border-color: #fff;
}

.tt-team.style-three:hover .tt-team__avater {
	-webkit-box-shadow: none;
	box-shadow: none;
}

.tt-team.style-five .tt-team__avater-wrap {
	max-width: 100%;
	position: relative;
}

.tt-team.style-five .tt-team__avater {
	border-radius: 0;
}

.tt-team.style-five .tt-team__avater:before {
	background-color: #3283fd;
}

.tt-team.style-five .tt-team__info-top {
	position: absolute;
	top: 0;
	left: 0;
	padding: 35px 30px;
	z-index: 2;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	opacity: 0;
	visibility: hidden;
}

.tt-team.style-five .tt-team__info {
	position: absolute;
	left: 0;
	bottom: 0;
	padding: 0 30px 26px 30px;
	z-index: 2;
}

.tt-team.style-five .tt-team__social-two {
	margin: 0;
	padding: 0;
	list-style: none;
	position: absolute;
	top: 5px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	opacity: 0;
	visibility: hidden;
}

.tt-team.style-five .tt-team__social-two li {
	display: inline-block;
}

.tt-team.style-five .tt-team__social-two li:not(:last-child) {
	margin-right: 5px;
}

.tt-team.style-five .tt-team__social-two li a {
	display: block;
	text-align: center;
	line-height: 33px;
	height: 36px;
	width: 36px;
	border: 2px solid rgba(255, 255, 255, 0.302);
	color: #fff;
	font-size: 15px;
}

.tt-team.style-five .tt-team__social-two li a:hover {
	background-color: #fff;
	color: #3283fd;
}

.tt-team.style-five .tt-team__name {
	font-size: 20px;
	font-weight: 600;
	color: #fff;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-team.style-five .tt-team__designation {
	font-size: 15px;
	font-weight: 400;
	color: #fff;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	margin-bottom: 0;
}

.tt-team.style-five:hover .tt-team__avater {
	-webkit-box-shadow: none;
	box-shadow: none;
}

.tt-team.style-five:hover .tt-team__info-top,
.tt-team.style-five:hover .tt-team__social-two {
	opacity: 1;
	visibility: visible;
}

.tt-team.style-five:hover .tt-team__info .tt-team__name,
.tt-team.style-five:hover .tt-team__info .tt-team__designation {
	opacity: 0;
	visibility: hidden;
}

.tt-team__avater-wrap {
	position: relative;
	max-width: 190px;
	margin: 0 auto;
}

.social-wrap {
	position: absolute;
	right: 20px;
	bottom: 0;
	width: 40px;
}

.social-wrap .link-expand,
.social-wrap .phone-number {
	width: 40px;
	height: 40px;
	-webkit-box-shadow: 0px 14px 24px 0px rgba(10, 53, 194, 0.3);
	box-shadow: 0px 14px 24px 0px rgba(10, 53, 194, 0.3);
	background-color: #3283fd;
	border-radius: 50%;
	text-align: center;
	color: #fff;
	line-height: 42px;
	font-size: 26px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	display: block;
}

.social-wrap .tt-team__social-two {
	margin: 0;
	padding: 0;
	list-style: none;
	position: absolute;
	right: 0;
	bottom: 0;
	margin-bottom: 40px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-box-orient: vertical;
	-webkit-box-direction: reverse;
	-ms-flex-direction: column-reverse;
	flex-direction: column-reverse;
}

.social-wrap .tt-team__social-two li {
	margin-bottom: 10px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	-webkit-transform: translateY(-10px);
	-ms-transform: translateY(-10px);
	transform: translateY(-10px);
	opacity: 0;
}

.social-wrap .tt-team__social-two li:nth-child(2) {
	-webkit-transition-delay: 0.2s;
	-o-transition-delay: 0.2s;
	transition-delay: 0.2s;
}

.social-wrap .tt-team__social-two li:nth-child(3) {
	-webkit-transition-delay: 0.3s;
	-o-transition-delay: 0.3s;
	transition-delay: 0.3s;
}

.social-wrap .tt-team__social-two li:nth-child(4) {
	-webkit-transition-delay: 0.4s;
	-o-transition-delay: 0.4s;
	transition-delay: 0.4s;
}

.social-wrap .tt-team__social-two li a {
	display: inline-block;
	height: 40px;
	width: 40px;
	line-height: 40px;
	text-align: center;
	color: #1c1e21;
	background-color: #fff;
	border-radius: 50%;
	-webkit-box-shadow: 0px 14px 24px 0px rgba(10, 53, 194, 0.3);
	box-shadow: 0px 14px 24px 0px rgba(10, 53, 194, 0.3);
}

.social-wrap.active .link-expand {
	-webkit-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	transform: rotate(45deg);
}

.social-wrap.active .tt-team__social-two li {
	-webkit-transform: translateY(0);
	-ms-transform: translateY(0);
	transform: translateY(0);
	opacity: 1;
}

/*--------------------------------------------------------------
 ##  Category Box
 --------------------------------------------------------------*/
.category-section {
	padding-top: 115px;
}

@media (max-width: 991px) {
	.category-section {
		padding-top: 75px;
	}

	.category-section .section-heading {
		text-align: center;
	}

	.category-section .text-right {
		text-align: center !important;
		margin-bottom: 40px;
	}
}

.tt-category-box {
	-webkit-box-shadow: 0 30px 70px 0 rgba(0, 9, 40, 0.12);
	box-shadow: 0 30px 70px 0 rgba(0, 9, 40, 0.12);
	background-color: #fff;
	padding: 40px 35px;
	margin-bottom: 30px;
}

.tt-category-box__icon {
	margin-bottom: 35px;
}

.tt-category-box__title {
	font-size: 18px;
	font-weight: 500;
	margin-bottom: 3px;
}

.tt-category-box__title a {
	color: #1c1e21;
}

.tt-category-box__posts {
	margin-bottom: 0;
	font-size: 14px;
	color: #4f5158;
}

/* Info Box */
.learn-info-title {
	color: #fff;
	font-size: 30px;
	font-weight: 600;
	margin-bottom: 52px;
}

.learn-info-box-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

@media (max-width: 991px) {
	.learn-info-box-wrap {
		display: block;
	}
}

.learn-info-box {
	margin-right: 100px;
}

@media (max-width: 991px) {
	.learn-info-box {
		margin: 0 0 30px;
	}
}

.learn-info-box .info-title {
	font-size: 50px;
	font-weight: 600;
	color: #fff;
	margin-bottom: 13px;
}

.learn-info-box .info {
	color: #fff;
}

/*--------------------------------------------------------------
##  Portfolio
--------------------------------------------------------------*/
.portfolio-section {
	padding: 115px 0 80px;
}

@media (max-width: 991px) {
	.portfolio-section {
		padding: 75px 0 50px;
	}
}

.tt-portfolio__filter {
	margin-bottom: 61px;
	padding: 0 0 6px;
	list-style: none;
	position: relative;
	border-bottom: 1px solid #eff1f6;
}

.tt-portfolio__filter li {
	display: inline-block;
	font-size: 14px;
	font-weight: 600;
	color: #777580;
}

.tt-portfolio__filter li:not(:last-child) {
	margin-right: 29px;
}

.tt-portfolio__filter li a {
	color: #777580;
	padding-right: 7px;
}

.tt-portfolio__filter li a.isActive {
	color: #3283fd;
}

.tt-portfolio__filter li a:hover {
	color: #3283fd;
}

.tt-portfolio__item {
	border-radius: 10px;
	position: relative;
	margin-bottom: 40px;
}

.tt-portfolio__item:hover .tt-portfolio__thumbnail-wrapper {
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-portfolio__item:hover .tt-portfolio__thumbnail-wrapper:after {
	opacity: 1;
	visibility: visible;
}

.tt-portfolio__item:hover .tt-portfolio__thumbnail-wrapper img {
	-webkit-transform: scale(1);
	-ms-transform: scale(1);
	transform: scale(1);
}

.tt-portfolio__item:hover .tt-portfolio__info .tt-portfolio__title,
.tt-portfolio__item:hover .tt-portfolio__info .tt-portfolio__cat span {
	-webkit-transform: translateY(0);
	-ms-transform: translateY(0);
	transform: translateY(0);
	opacity: 1;
	visibility: visible;
}

.tt-portfolio__item:hover .tt-portfolio__info-bottom {
	opacity: 0;
}

.tt-portfolio__item:hover .tt-portfolio__title-bottom,
.tt-portfolio__item:hover .tt-portfolio__cat-bottom {
	opacity: 0;
}

.tt-portfolio__item:hover .tt-portfolio__topinfo {
	opacity: 1;
}

.tt-portfolio__item:hover .tt-portfolio__topinfo a {
	-webkit-transform: scale(1);
	-ms-transform: scale(1);
	transform: scale(1);
}

.tt-portfolio__post-thumbnail {
	overflow: hidden;
	border-radius: 10px;
	position: relative;
}

.tt-portfolio__thumbnail-wrapper {
	position: relative;
	overflow: hidden;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-portfolio__thumbnail-wrapper:after {
	content: "";
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	background-color: rgba(50, 131, 253, 0.8);
	opacity: 0;
	visibility: hidden;
	-webkit-transition: all cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
	-o-transition: all cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
	transition: all cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
	border-radius: 10px;
}

.tt-portfolio__thumbnail-wrapper img {
	-webkit-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
	-o-transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
	transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0s;
	width: 100%;
}

.tt-portfolio__topinfo {
	position: absolute;
	left: 40px;
	top: 30px;
	z-index: 2;
	opacity: 0;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-portfolio__topinfo a {
	display: inline-block;
	width: 32px;
	height: 32px;
	line-height: 32px;
	border: 2px solid rgba(255, 255, 255, 0.102);
	border-radius: 6px;
	color: #fff;
	text-align: center;
	font-size: 14px;
	margin-right: 5px;
	-webkit-transform: scale(0.5);
	-ms-transform: scale(0.5);
	transform: scale(0.5);
}

.tt-portfolio__topinfo a i.ei-icon_search {
	-webkit-transform: rotate(270deg);
	-ms-transform: rotate(270deg);
	transform: rotate(270deg);
	display: inline-block;
}

.tt-portfolio__topinfo a:hover {
	background-color: #fff;
	color: #1c1e21;
}

.tt-portfolio__info {
	position: absolute;
	bottom: 37px;
	left: 0;
	padding: 0 40px;
	z-index: 2;
	height: -webkit-max-content;
	height: -moz-max-content;
	height: max-content;
	-webkit-transform: translateY(10px);
	-ms-transform: translateY(10px);
	transform: translateY(10px);
	overflow: hidden;
}

.tt-portfolio__title {
	color: #fff;
	font-size: 18px;
	font-weight: 700;
	margin: 0;
	-webkit-transition: all cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
	-o-transition: all cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
	transition: all cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
	overflow: hidden;
	height: -webkit-max-content;
	height: -moz-max-content;
	height: max-content;
	-webkit-transform: translateY(-20px);
	-ms-transform: translateY(-20px);
	transform: translateY(-20px);
	opacity: 0;
	visibility: hidden;
}

.tt-portfolio__title:hover {
	color: rgba(255, 255, 255, 0.9);
}

.tt-portfolio__cat {
	height: -webkit-max-content;
	height: -moz-max-content;
	height: max-content;
	visibility: hidden;
	font-size: 14px;
}

.tt-portfolio__cat span {
	color: rgba(255, 255, 255, 0.502);
	-webkit-transform: translateY(20px);
	-ms-transform: translateY(20px);
	transform: translateY(20px);
	opacity: 0;
	-webkit-transition: all cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
	-o-transition: all cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
	transition: all cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
	display: inline-block;
}

.tt-portfolio__info-bottom {
	padding: 15px 30px 0;
}

.tt-portfolio__title-bottom {
	font-size: 18px;
	font-weight: 700;
	color: #1c1e21;
	margin: 0;
}

.tt-portfolio__cat-bottom {
	font-size: 14px;
	line-height: 1.4;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-course__filter {
	margin-bottom: 0;
}

.tt-course__filter li:not(:last-child) {
	margin-right: 0;
}

.tt-course__filter li a {
	padding: 3px 16px;
	display: block;
	font-size: 15px;
	font-weight: 500;
	color: #505056;
}

.tt-course__filter li.current a {
	background-color: #089df1;
	color: #fff;
}

.indicator {
	background: #3283fd;
	position: absolute;
	left: 0;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	height: 2px;
	bottom: 0;
}

/* Portfolio Single */
.portfolio-header {
	min-height: 500px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	position: relative;
}

.portfolio-header .container {
	margin-top: 87px;
}

.portfolio-header .portfolio-cat {
	font-size: 14px;
	font-weight: 400;
	color: #fff;
	background-color: #3283fd;
	padding: 2px 14px 3px;
	border-radius: 6px;
	line-height: 1;
	margin-bottom: 11px;
}

.portfolio-header .portfolio-title {
	font-size: 60px;
	font-weight: 800;
	margin-bottom: 7px;
}

@media (max-width: 767px) {
	.portfolio-header .portfolio-title {
		font-size: 46px;
	}
}

.portfolio-header .info-description {
	font-size: 24px;
	color: #1c1e21;
	margin: 0;
	font-weight: 500;
}

@media (max-width: 767px) {
	.portfolio-header .info-description {
		font-size: 18px;
	}
}

.portfolio-header .portfolio-info-list {
	margin: 0;
	padding: 0;
	list-style: none;
}

@media (max-width: 991px) {
	.portfolio-header .portfolio-info-list {
		margin-top: 40px;
	}
}

.portfolio-header .portfolio-info-list li {
	display: inline-block;
	font-size: 14px;
	font-weight: 500;
	line-height: 24px;
}

.portfolio-header .portfolio-info-list li:not(:last-child) {
	margin-right: 90px;
}

@media (max-width: 1200px) {
	.portfolio-header .portfolio-info-list li:not(:last-child) {
		margin-right: 50px;
		margin-bottom: 30px;
	}
}

.portfolio-header .portfolio-info-list li span {
	display: block;
	font-weight: 600;
	text-transform: uppercase;
	color: #1c1e21;
	line-height: 1;
}

.portfolio-header .animated-shape {
	margin: 0;
	padding: 0;
	list-style: none;
}

@media (max-width: 576px) {
	.portfolio-header .animated-shape {
		display: none;
	}
}

.portfolio-header .animated-shape li {
	position: absolute;
}

.portfolio-header .animated-shape li:nth-child(1) {
	left: 0;
	top: 45%;
}

.portfolio-header .animated-shape li:nth-child(2) {
	right: 175px;
	bottom: 100px;
}

@media (max-width: 767px) {
	.portfolio-header .animated-shape li:nth-child(2) {
		right: 70px;
	}
}

.portfolio-header .animated-shape li:nth-child(3) {
	right: 115px;
	top: 175px;
}

@media (max-width: 767px) {
	.portfolio-header .animated-shape li:nth-child(3) {
		right: 50px;
	}
}

.portfolio_details_area {
	padding-bottom: 115px;
}

.portfolio-content {
	position: relative;
}

@media (min-width: 992px) and (max-width: 1200px) {
	.portfolio-content .container {
		max-width: 850px;
	}
}

.portfolio-content .portfolio-share {
	top: 8px;
	left: 220px;
}

@media (max-width: 1600px) {
	.portfolio-content .portfolio-share {
		left: 8%;
	}
}

@media (max-width: 1440px) {
	.portfolio-content .portfolio-share {
		left: 5%;
	}
}

@media (max-width: 1200px) {
	.portfolio-content .portfolio-share {
		left: 20px;
	}
}

@media (max-width: 767px) {
	.portfolio-content .portfolio-share {
		left: 20px;
	}
}

@media (max-width: 640px) {
	.portfolio-content .portfolio-share {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		margin: 30px 0;
	}
}

@media (max-width: 640px) {
	.portfolio-content .portfolio-share {
		margin: 30px auto;
		width: -webkit-max-content;
		width: -moz-max-content;
		width: max-content;
	}
}

@media (min-width: 641px) {
	.portfolio-content .portfolio-share {
		position: absolute;
		width: 34px;
	}
}

.portfolio-content .share-title {
	font-size: 14px;
	font-weight: 600;
	display: block;
	text-transform: uppercase;
	margin: 0;
}

@media (min-width: 641px) {
	.portfolio-content .share-title {
		-webkit-transform: rotate(-90deg);
		-ms-transform: rotate(-90deg);
		transform: rotate(-90deg);
	}
}

@media (max-width: 640px) {
	.portfolio-content .share_social-wpapper {
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		order: 2;
	}
}

.portfolio-content .social-share-link {
	margin-bottom: 20px;
}

@media (max-width: 640px) {
	.portfolio-content .social-share-link {
		margin-right: 0;
		margin-left: 20px;
		margin-bottom: 0;
	}
}

.portfolio-content .social-share-link li {
	margin-bottom: 10px;
}

@media (min-width: 641px) {
	.portfolio-content .social-share-link li:not(:last-child) {
		margin-right: 0;
	}
}

@media (max-width: 640px) {
	.portfolio-content .social-share-link li {
		display: inline-block;
	}
}

.portfolio-content .social-share-link li a {
	height: 34px;
	width: 34px;
	line-height: 34px;
	font-size: 13px;
	border: 0;
}

@media (max-width: 640px) {
	.portfolio-content .social-share-link li a {
		display: block;
	}
}

.portfolio-content .social-share-link li a:hover {
	background-color: transparent;
}

.portfolio-content .social-share-link li a i {
	display: block;
	height: 100%;
	width: 100%;
	text-align: center;
	line-height: 34px;
	color: #fff;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	position: relative;
}

.portfolio-content .social-share-link li a i:after {
	content: "";
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: -1;
	border-radius: 6px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.portfolio-content .social-share-link li a i.fa-facebook-f:after {
	background-color: #37589e;
}

.portfolio-content .social-share-link li a i.fa-twitter:after {
	background-color: #119ff6;
}

.portfolio-content .social-share-link li a i.fa-pinterest-p:after {
	background-color: #db172a;
}

.portfolio-content .social-share-link li a i.fa-linkedin-in:after {
	background-color: #0584c6;
}

.portfolio-content .social-share-link li a i:hover:after {
	-webkit-transform: scale(1.15);
	-ms-transform: scale(1.15);
	transform: scale(1.15);
}

.related-portfolio {
	margin-top: 98px;
	padding-bottom: 76px;
}

@media (max-width: 767px) {
	.related-portfolio {
		padding-bottom: 20px;
	}
}

.portfolio-related-post .tt-portfolio__post-thumbnail {
	border-radius: 6px;
}

.portfolio-related-post .tt-portfolio__post-thumbnail img {
	height: 385px;
	-o-object-fit: cover;
	object-fit: cover;
	width: 100%;
}

.portfolio-feature-image {
	margin-bottom: 92px;
}

@media (max-width: 767px) {
	.portfolio-feature-image {
		margin-bottom: 40px;
	}
}

.content-wrap {
	max-width: 960px;
	margin: 0 auto;
}

.content-wrap .lead {
	font-size: 20px;
	color: #0c1636;
	margin-top: 26px;
	margin-bottom: 90px;
}

.social-share-link {
	margin-bottom: 20px;
	padding: 0;
	list-style: none;
}

@media (max-width: 640px) {
	.social-share-link {
		margin-right: 0;
		margin-left: 20px;
		margin-bottom: 0;
	}
}

.social-share-link li {
	margin-bottom: 10px;
}

@media (min-width: 641px) {
	.social-share-link li:not(:last-child) {
		margin-right: 0;
	}
}

.social-share-link li a {
	height: 34px;
	width: 34px;
	line-height: 34px;
	font-size: 13px;
	border: 0;
}

.social-share-link li a:hover {
	background-color: transparent;
}

.social-share-link li a i {
	display: block;
	height: 100%;
	width: 100%;
	text-align: center;
	line-height: 34px;
	color: #fff;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	position: relative;
}

.social-share-link li a i:after {
	content: "";
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: -1;
	border-radius: 6px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.social-share-link li a i.fa-facebook-f:after {
	background-color: #37589e;
}

.social-share-link li a i.fa-twitter:after {
	background-color: #119ff6;
}

.social-share-link li a i.fa-pinterest-p:after {
	background-color: #db172a;
}

.social-share-link li a i.fa-linkedin-in:after {
	background-color: #0584c6;
}

.social-share-link li a i:hover:after {
	-webkit-transform: scale(1.15);
	-ms-transform: scale(1.15);
	transform: scale(1.15);
}

.course-buttons {
	margin-top: 20px;
}

.course-buttons .btn-outline {
	border-color: #d7d7d9;
	color: #0b1536;
}

/*--------------------------------------------------------------
 ##  Courses
 --------------------------------------------------------------*/
.courses-section {
	padding: 110px 0 90px;
	background-color: #f0f3fa;
}

@media (max-width: 991px) {
	.courses-section {
		padding: 70px 0 80px;
	}
}

.tt-course {
	background-color: #fff;
	-webkit-box-shadow: 0 30px 70px 0 rgba(1, 9, 63, 0.14);
	box-shadow: 0 30px 70px 0 rgba(1, 9, 63, 0.14);
	padding: 30px 30px 21px 30px;
	border-radius: 0;
}

.tt-course__post-thumbnail {
	margin-bottom: 26px;
}

.tt-course__post-thumbnail img {
	width: 100%;
}

.tt-course__meta-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	margin-bottom: 16px;
}

.tt-course__rating {
	color: #505056;
	font-size: 14px;
	font-weight: 500;
}

.tt-course__rating i {
	color: #ff9a17;
}

.tt-course__cate {
	padding: 5px 9px;
	font-size: 12px;
	font-weight: 600;
	background-color: #3283fd;
	color: #fff;
	line-height: 1;
}

.tt-course__cate:hover {
	color: #fff;
}

.tt-course__title {
	font-size: 18px;
	line-height: 26px;
	font-weight: 700;
	margin-bottom: 11px;
}

.tt-course__lessons {
	font-size: 14px;
	color: #505056;
}

.tt-course__lessons i {
	margin-right: 5px;
}

.tt-course__footer {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-top: 19px;
}

.tt-course__price {
	font-size: 20px;
	color: #0b1536;
	margin-bottom: 0;
}

.tt-course__price .old-price {
	color: #929295;
	font-size: 14px;
	text-decoration: line-through;
}

.tt-course .more-link {
	border: 2px solid #e8e9eb;
	display: block;
	height: 46px;
	width: 46px;
	line-height: 44px;
	text-align: center;
	border-radius: 50%;
	color: #1c1e21;
}

.tt-course .more-link:hover {
	background-color: #3283fd;
	color: #fff;
	border-color: #3283fd;
}

/*--------------------------------------------------------------
 ##  Dashboard Preview
 --------------------------------------------------------------*/
.dashboard-preview-section {
	padding: 100px 0;
}

@media (max-width: 991px) {
	.dashboard-preview-section {
		padding: 0;
	}
}

.dashboard-preview-wrapper {
	background-color: #f2f4f7;
	text-align: center;
	border-radius: 10px;
	padding-top: 70px;
}

@media (max-width: 991px) {
	.dashboard-preview-wrapper {
		padding: 50px 30px 0;
	}
}

.dashboard-preview-wrapper .section-heading .tt__btn {
	margin-top: 34px;
}

.dashboard-preview-wrapper .dashboard-image img {
	border-top-left-radius: 10px;
	border-top-right-radius: 10px;
}

/*--------------------------------------------------------------
 ##  Process Box
 --------------------------------------------------------------*/
.process-section {
	padding: 115px 0 0;
}

@media (max-width: 991px) {
	.process-section {
		padding: 75px 0 0;
	}
}

/*--------------------------------------------------------------
 ##  Contact
 --------------------------------------------------------------*/
.contact-page {
	padding: 120px 0;
}

@media (max-width: 991px) {
	.contact-page {
		padding: 80px 0;
	}
}

.contact-page .anemate-element {
	margin: 0;
	padding: 0;
	list-style: none;
	list-style: none;
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
}

@media (max-width: 991px) {
	.contact-page .anemate-element {
		display: none;
	}
}

.contact-page .anemate-element li {
	position: absolute;
	width: 100%;
	height: 100%;
}

.contact-page .anemate-element li .inner {
	position: absolute;
}

.contact-page .anemate-element li:nth-child(1) .inner {
	top: 36%;
	left: -14%;
}

.contact-page .anemate-element li:nth-child(2) .inner {
	top: 28%;
	right: -2%;
}

.contact-page .anemate-element li:nth-child(3) .inner {
	bottom: 20%;
	right: -10%;
}

.gmap3-area {
	height: 650px;
	border-radius: 10px;
	border: 6px solid #fff;
	-webkit-box-shadow: 0px 30px 50px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 30px 50px 0px rgba(0, 9, 40, 0.1);
}

.google-map {
	padding-left: 40px;
}

@media (max-width: 768px) {
	.google-map {
		padding-left: 0;
		margin-top: 60px;
	}
}

.contact-content {
	margin-bottom: 43px;
}

.contact-content .contact-title {
	font-size: 44px;
	font-weight: 700;
	color: #1c1e21;
}

@media (max-width: 991px) {
	.contact-content .contact-title {
		font-size: 32px;
	}
}

.contact-form .input-wrap {
	margin-bottom: 21px;
}

.contact-form .form-result {
	margin-top: 10px;
}

.input-checkbox {
	margin-bottom: 20px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.input-checkbox p {
	margin: 0;
	font-size: 14px;
}

.input-checkbox p a {
	color: #1c1e21;
}

.input-checkbox p a:hover {
	color: #3283fd;
}

.input-checkbox input {
	height: 16px;
	width: 16px;
	margin-right: 10px;
}

.contact-info-section {
	padding: 83px 0 155px;
}

@media (max-width: 991px) {
	.contact-info-section {
		padding: 50px 0 50px;
	}
}

.tt-contact-info {
	border-radius: 10px;
	background-color: white;
	-webkit-box-shadow: 0px 50px 70px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 50px 70px 0px rgba(0, 9, 40, 0.1);
	padding: 50px 30px;
	text-align: center;
	margin-bottom: 30px;
}

@media (max-width: 1024px) and (min-width: 768px) {
	.tt-contact-info {
		padding: 50px 15px;
	}
}

.tt-contact-info .icon-container {
	margin-bottom: 25px;
	min-height: 65px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: end;
	-ms-flex-align: end;
	align-items: flex-end;
}

.tt-contact-info .box-title {
	font-weight: 500;
	font-size: 20px;
}

.tt-contact-info .box-title a {
	color: #1c1e21;
}

.tt-contact-info .box-title a:hover {
	color: #3283fd;
}

@media (max-width: 1024px) and (min-width: 768px) {
	.tt-contact-info .box-title {
		font-size: 16px;
	}
}

.tt-contact-info.style-two {
	-webkit-box-shadow: none;
	box-shadow: none;
	background-color: #f0f1f5;
	text-align: left;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	border-radius: 0;
	padding: 45px 40px;
}

.tt-contact-info__icon {
	width: 70px;
	margin-right: 20px;
}

.tt-contact-info__title {
	font-size: 20px;
	margin-bottom: 3px;
}

.tt-contact-info__description {
	margin-bottom: 0;
}

.contact-info-section-two {
	padding-top: 20px;
	padding-bottom: 90px;
}

.contact-info-section-two .col-md-4 {
	padding: 0 5px;
}

.bg-wrap {
	position: relative;
}

.bg-wrap:after {
	position: absolute;
	content: "";
	width: 80%;
	height: 350px;
	background: #f6f7f9;
	top: 44%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	z-index: -1;
	border-radius: 10px;
}

@media (max-width: 767px) {
	.bg-wrap:after {
		height: 90%;
		width: 50%;
	}
}

/*--------------------------------------------------------------
  ##  Blog
  --------------------------------------------------------------*/
.recent-blog {
	padding: 110px 0 90px;
	border-top: 1px solid #e7e9ed;
}

@media (max-width: 991px) {
	.recent-blog {
		padding: 70px 0 50px;
	}
}

.recent-blog-two {
	padding-top: 60px;
}

.blog-grid-three {
	padding: 110px 0 90px;
}

@media (max-width: 991px) {
	.blog-grid-three {
		padding: 70px 0 50px;
	}
}

.blog-post-four {
	padding: 115px 0 90px;
}

@media (max-width: 991px) {
	.blog-post-four {
		padding: 70px 0 50px;
	}
}

.tt-blog-grid {
	background-color: #fff;
	border-radius: 6px;
	padding: 35px;
	-webkit-box-shadow: 0 30px 70px 0 rgba(0, 9, 40, 0.1);
	box-shadow: 0 30px 70px 0 rgba(0, 9, 40, 0.1);
	margin-bottom: 30px;
}

@media (max-width: 576px) {
	.tt-blog-grid {
		padding: 25px;
	}
}

.tt-blog-grid .feature-image {
	margin-bottom: 18px;
}

.tt-blog-grid .feature-image img {
	min-height: 200px;
	-o-object-fit: cover;
	object-fit: cover;
	width: 100%;
}

.tt-blog-grid .entry-title {
	font-size: 18px;
	line-height: 26px;
	margin-bottom: 10px;
}

.tt-blog-grid .entry-title a {
	color: #1c1e21;
}

.tt-blog-grid .entry-title a:hover {
	color: #572aff;
}

.tt-blog-grid .entry-content .entry-meta {
	margin-bottom: 5px;
}

.tt-blog-grid .entry-content .entry-meta a {
	text-transform: capitalize;
	font-size: 14px;
}

.tt-blog-grid .entry-content p {
	margin-bottom: 18px;
}

.tt-blog-grid.style-two {
	padding: 0;
	-webkit-box-shadow: 0px 2px 4px 0px rgba(0, 9, 40, 0.16);
	box-shadow: 0px 2px 4px 0px rgba(0, 9, 40, 0.16);
	border-radius: 4px;
	background: #fff;
	overflow: hidden;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

.tt-blog-grid.style-two .post_content {
	padding: 15px 40px 40px;
}

.tt-blog-grid.style-two .post_content p {
	margin-bottom: 13px;
}

.tt-blog-grid.style-two .entry-meta {
	color: #70737a;
	font-size: 14px;
	margin-bottom: 7px;
}

.tt-blog-grid.style-two .entry-meta i {
	color: #3283fd;
}

.tt-blog-grid.style-two .entry-title {
	font-size: 20px;
	font-weight: 700;
	margin-bottom: 7px;
}

.tt-blog-grid.style-two .entry-title a:hover {
	color: #f63a6f;
}

.tt-blog-grid.style-two .read-more {
	font-size: 15px;
	font-weight: 500;
	color: #1c1e21;
}

.tt-blog-grid.style-two .read-more i {
	font-size: 15px;
	vertical-align: -1px;
}

.tt-blog-grid.style-two .read-more:hover {
	color: #f63a6f;
}

.tt-blog-grid.style-two:hover {
	-webkit-box-shadow: 0px 30px 50px 0px rgba(0, 9, 40, 0.12);
	box-shadow: 0px 30px 50px 0px rgba(0, 9, 40, 0.12);
}

.tt-blog-grid.style-three {
	padding: 0;
	-webkit-box-shadow: 0 20px 50px 0 rgba(0, 9, 41, 0.08);
	box-shadow: 0 20px 50px 0 rgba(0, 9, 41, 0.08);
}

.tt-blog-grid.style-three .post-meta {
	padding-top: 0;
	margin-bottom: 13px;
}

.tt-blog-grid.style-three .entry-title {
	font-size: 20px;
	margin-bottom: 7px;
}

.tt-blog-grid.style-three .blog-content {
	padding: 30px 40px;
}

.tt-blog-grid.style-three .entry-content {
	margin-bottom: 12px;
}

.tt-blog-grid.style-three .tt__btn-link {
	color: #1c1e21;
	font-size: 15px;
}

.tt-blog-grid.style-four {
	-webkit-box-shadow: 0 20px 50px 0 rgba(0, 9, 41, 0.08);
	box-shadow: 0 20px 50px 0 rgba(0, 9, 41, 0.08);
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	padding: 0;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

@media (max-width: 420px) {
	.tt-blog-grid.style-four {
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
	}

	.tt-blog-grid.style-four .blog-content {
		width: 100%;
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		order: 2;
	}

	.tt-blog-grid.style-four .feature-image {
		width: 100%;
		margin-bottom: 10px;
	}
}

.tt-blog-grid.style-four .feature-image {
	margin: 0;
	overflow: hidden;
}

.tt-blog-grid.style-four .blog-content {
	padding: 15px 30px;
	max-width: 359px;
}

@media (max-width: 1200px) {
	.tt-blog-grid.style-four .blog-content {
		max-width: 285px;
	}
}

@media (max-width: 991px) {
	.tt-blog-grid.style-four .blog-content {
		max-width: initial;
	}
}

@media (max-width: 420px) {
	.tt-blog-grid.style-four .blog-content {
		padding: 25px 30px;
	}
}

.tt-blog-grid.style-four .post-meta {
	padding-top: 0;
	margin-bottom: 13px;
}

@media (max-width: 480px) {
	.tt-blog-grid.style-four .post-meta li:not(:last-child) {
		margin-bottom: 0;
	}
}

.tt-blog-grid.style-four .entry-title {
	margin-bottom: 5px;
}

.tt-blog-grid.style-four .entry-content {
	margin-bottom: 9px;
}

.tt-blog-grid.style-four .tt__btn-link {
	color: #1c1e21;
	font-size: 15px;
}

.tt-blog-grid.style-five {
	padding: 0;
	-webkit-box-shadow: 0 30px 70px 0 rgba(1, 9, 63, 0.1);
	box-shadow: 0 30px 70px 0 rgba(1, 9, 63, 0.1);
}

.tt-blog-grid.style-five .feature-image {
	margin-bottom: 0;
}

.tt-blog-grid.style-five .post-meta {
	margin-bottom: 23px;
	padding-top: 0;
}

.tt-blog-grid.style-five .post-meta li {
	font-size: 14px;
	color: #505056;
}

.tt-blog-grid.style-five .post-meta li i {
	margin-right: 6px;
}

.tt-blog-grid.style-five .post-meta li:after {
	display: none;
}

.tt-blog-grid.style-five .post-meta li:not(:last-child) {
	margin-right: 35px;
}

.tt-blog-grid.style-five .entry-content {
	padding: 30px;
}

.tt-blog-grid.style-five .entry-title {
	margin-bottom: 18px;
	font-weight: 700;
}

.tt-blog-grid.style-five .read-more {
	color: #505056;
	font-size: 15px;
	font-weight: 500;
}

.post-meta li .author a {
	color: #0c1636;
	font-weight: 700;
	font-size: 14px;
	text-transform: capitalize;
}

.post-meta {
	margin: 0;
	padding: 5px 0 0;
	line-height: 1;
}

.post-meta li {
	display: inline-block;
	position: relative;
	font-size: 14px;
	color: #696969;
}

.post-meta li:not(:last-child) {
	margin-right: 10px;
}

.post-meta li:not(:last-child):after {
	content: "";
	position: absolute;
	right: -8px;
	bottom: 41%;
	height: 2px;
	width: 2px;
	background-color: #97999c;
}

@media (max-width: 480px) {
	.post-meta li:not(:last-child) {
		margin-bottom: 15px;
	}
}

.post-meta li.author {
	margin-left: 5px;
}

.post-meta li.author .avatar {
	height: 36px;
	width: 36px;
	margin-right: 12px;
}

.post-meta li.author a {
	color: #1c1e21;
	font-weight: 700;
	font-size: 14px;
	text-transform: capitalize;
}

.post-meta li.author a:hover {
	color: #572aff;
}

.post-meta li a {
	margin: 0;
	color: #76787d;
	line-height: normal;
}

.post-meta li a:hover {
	color: #3283fd;
}

.entry-meta a {
	color: #1c1e21;
}

.entry-meta a.color__one {
	color: #1ca30d;
}

.entry-meta a.color__two {
	color: #fa5441;
}

.entry-meta a.color__three {
	color: #572aff;
}

.blog-post-archive {
	padding: 120px 0;
}

@media (max-width: 991px) {
	.blog-post-archive {
		padding: 80px 0 30px;
	}
}

.post-wrapper .post {
	margin-bottom: 60px;
	padding-bottom: 32px;
	border-bottom: 1px solid #e7e9ed;
}

.post-wrapper .post .feature-image {
	position: relative;
	overflow: hidden;
}

.post-wrapper .post .feature-image img {
	border-radius: 6px;
}

.post-wrapper .post .post-meta-tags {
	margin-bottom: 3px;
}

.post-wrapper .post .post-meta-tags a {
	font-size: 14px;
	color: #1ca30d;
}

.post-wrapper .post .blog-content {
	padding-top: 20px;
}

.post-wrapper .post .blog-content .entry-title {
	font-size: 30px;
	font-weight: 700;
	margin-bottom: 12px;
}

.post-wrapper .post .blog-content .entry-title a {
	color: #1c1e21;
}

.post-wrapper .post .blog-content .entry-title a:hover {
	color: #3283fd;
}

.post-wrapper .post .blog-content p {
	margin-bottom: 19px;
}

.post-wrapper .post .post-meta {
	margin-bottom: 24px;
}

.post-wrapper .post .read-more {
	font-size: 15px;
	font-weight: 500;
	color: #4f5158;
}

.post-wrapper .post .read-more:hover {
	color: #3283fd;
}

.post-wrapper .post.video-post {
	position: relative;
}

.post-wrapper .post.video-post .feature-image:after {
	content: "";
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	background-color: rgba(42, 92, 255, 0.5);
	border-radius: 6px;
}

.post-wrapper .post.video-post .popup-video {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	height: 100px;
	width: 100px;
	border-radius: 50%;
	background-color: #fff;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	color: #2a5cff;
	font-size: 24px;
	z-index: 2;
}

.post-wrapper .post.video-post .popup-video:after {
	content: "";
	height: 120px;
	width: 120px;
	position: absolute;
	left: -10px;
	top: -10px;
	border: 1px solid rgba(255, 255, 255, 0.302);
	border-radius: 50%;
	-webkit-animation: videoBtnAnim 3s linear infinite;
	animation: videoBtnAnim 3s linear infinite;
	display: block;
}

.post-wrapper .post.link-post {
	background-color: #f5f6fa;
	padding: 41px 50px;
	border-bottom: 0 !important;
	border-radius: 6px;
	overflow: hidden;
	position: relative;
}

.post-wrapper .post.link-post a {
	font-size: 24px;
	line-height: 34px;
	font-weight: 400;
	color: #0c1636;
}

.post-wrapper .post.link-post a:hover {
	color: #3283fd;
}

.post-wrapper .post.link-post i {
	position: absolute;
	-webkit-transform: rotate(90deg);
	-ms-transform: rotate(90deg);
	transform: rotate(90deg);
}

.post-wrapper .post.link-post .link-left {
	font-size: 24px;
	color: #dedee2;
	top: -8px;
}

.post-wrapper .post.link-post .link-right {
	right: 45px;
	font-size: 55px;
	color: #dedee2;
}

blockquote {
	background-color: #f5f6fa;
	padding: 42px 50px 52px;
	position: relative;
	margin: 54px 0 52px;
	overflow: hidden;
	border-radius: 6px;
}

blockquote p {
	font-size: 24px;
	color: #0c1636;
	font-style: normal;
	margin-bottom: 4px;
	line-height: 34px;
	font-weight: 400;
	padding: 0;
}

blockquote cite {
	font-family: "Inter", sans-serif;
	font-style: normal;
	font-weight: 700;
	font-size: 20px;
	line-height: 28px;
	color: #0c1636;
	padding-top: 15px;
	display: block;
	padding-left: 35px;
	position: relative;
}

blockquote cite:before {
	content: "";
	position: absolute;
	left: 0;
	top: 27px;
	height: 3px;
	width: 20px;
	background: #2a5cff;
}

blockquote img {
	position: absolute;
	right: 40px;
}

.post-navigation {
	margin: 0;
	padding: 0;
	list-style: none;
}

.post-navigation li {
	display: inline-block;
}

.post-navigation li a,
.post-navigation li.active {
	display: inline-block;
	height: 40px;
	width: 40px;
	line-height: 36px;
	border: 2px solid #e3e5ee;
	text-align: center;
	color: #0c1636;
	border-radius: 4px;
	margin-right: 5px;
	font-size: 15px;
	font-weight: 500;
}

.post-navigation li.active {
	background-color: #3283fd;
	border-color: #3283fd;
	color: #fff;
}

.post-navigation li.next a {
	border: 0;
}

.post-navigation li.next a i {
	margin-left: 3px;
}

.post-navigation li.next a:hover {
	color: #3283fd;
}

.blog-single {
	padding: 120px 0;
}

@media (max-width: 991px) {
	.blog-single {
		padding: 80px 0 50px;
	}
}

.blog-single-content .details-image {
	margin: 40px 0 52px;
}

.blog-single-content ul {
	margin: 0 0 20px;
	padding-left: 17px;
}

.blog-single-content .title {
	font-size: 30px;
	font-weight: 700;
	margin: 0;
	margin-bottom: 25px;
}

.blog-share-wrap .title {
	font-size: 16px;
	margin-bottom: 20px;
}

.blog-share-sociali-link {
	margin: 0;
	padding: 0;
	list-style: none;
}

.blog-share-sociali-link li {
	display: inline-block;
}

.blog-share-sociali-link li a {
	display: inline-block;
	height: 40px;
	width: 40px;
	line-height: 40px;
	color: #4f5158;
	border: 2px solid #e6e7eb;
	border-radius: 6px;
	text-align: center;
	font-size: 14px;
}

.blog-share-sociali-link li a:hover {
	background-color: #3283fd;
	border-color: #3283fd;
	color: #fff;
}

.blog-share-wrap {
	margin-top: 47px;
}

.blog-single-footer {
	border-top: 1px solid #e7e9ed;
	padding-top: 25px;
	margin-top: 40px;
}

.blog-single-footer .taglist {
	margin: 0;
	padding: 0;
	list-style: none;
}

.blog-single-footer .taglist li {
	display: inline-block;
	margin-right: 5px;
}

.blog-single-footer .taglist li a {
	font-size: 12px;
	font-weight: 500;
	display: inline-block;
	border: 2px solid #e3e5ee;
	color: #1c1e21;
	border-radius: 6px;
	line-height: 1;
	padding: 4px 17px;
}

.blog-single-footer .taglist li a:hover {
	background-color: #3283fd;
	color: #fff;
	border-color: #3283fd;
}

.single-post-navigation {
	margin-top: 80px;
	border: 2px solid #e7e9ed;
	padding: 0 25px;
	border-radius: 6px;
	position: relative;
}

@media (max-width: 420px) {
	.single-post-navigation {
		padding: 0 15px;
	}
}

.single-post-nav {
	max-width: 270px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.single-post-nav .post-nav-wrapper {
	-webkit-box-flex: 2;
	-ms-flex: 2;
	flex: 2;
	padding: 21px 0;
}

.single-post-nav .post-nav-title {
	font-size: 13px;
	font-weight: 400;
	color: #76787d;
	margin-bottom: 6px;
	line-height: 1;
}

.single-post-nav .post-title {
	font-size: 14px;
	-webkit-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	margin: 0;
	color: #0c1636;
	font-weight: 500;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
	display: block;
	white-space: nowrap;
	max-width: 270px;
}

.post-next .post-nav-wrapper {
	text-align: right;
}

.post-next .single-post-nav {
	margin-left: auto;
}

.post-next:before {
	content: "";
	position: absolute;
	width: 2px;
	height: 100%;
	left: 0;
	top: 0;
	background-color: #e7e9ed;
}

@media (max-width: 767px) {
	.post-next:before {
		display: none;
	}
}

.related-post-wrapper {
	margin-top: 95px;
}

.related-post-wrapper .related-title {
	font-size: 20px;
	margin-bottom: 21px;
}

.related-post {
	background-color: #fff;
	-webkit-box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.1);
	box-shadow: 0px 30px 70px 0px rgba(0, 9, 40, 0.1);
	border-radius: 6px;
	overflow: hidden;
	padding: 20px;
}

@media (max-width: 767px) {
	.related-post {
		margin-bottom: 30px;
	}
}

.related-post .feature-image img {
	min-height: 160px;
	width: 100%;
	-o-object-fit: cover;
	object-fit: cover;
}

.related-post .blog-content {
	padding-top: 22px;
}

.related-post .blog-content .post-title {
	font-size: 14px;
	margin-bottom: 11px;
	line-height: 20px;
}

.related-post .blog-content .post-title a {
	color: #0c1636;
}

.related-post .blog-content .post-title a:hover {
	color: #3283fd;
}

.related-post .post-meta {
	margin-bottom: 7px;
	padding-top: 0;
}

.related-post .post-meta li a {
	font-size: 13px;
	color: #2a5cff;
}

.related-post .post-meta li .posted-on a {
	color: #76787d;
}

@media (min-width: 992px) {
	.sidebar {
		padding-left: 70px;
	}
}

@media (max-width: 991px) {
	.sidebar {
		margin-top: 70px;
	}
}

.sidebar .widget-title {
	position: relative;
	display: inline-block;
	margin-bottom: 35px;
	font-size: 20px;
}

.widget {
	margin-bottom: 44px;
}

.search-form {
	border-radius: 6px;
	position: relative;
	background-color: #f0f1f5;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	overflow: hidden;
}

.search-form input {
	padding: 11px 30px;
	border-radius: 0;
	border: 0;
	width: 100%;
	color: #696969;
	height: 55px;
	margin: 0;
	background: transparent;
	font-size: 16px;
}

.search-form button {
	border: 0;
	padding: 0 15px;
	font-size: 20px;
	line-height: 40px;
	color: #0c1636;
	background: transparent;
}

.ultraland-widget-recent-posts {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	margin-bottom: 20px;
}

.ultraland-widget-recent-posts .recent-posts-image_wrapper {
	width: 70px;
	margin-right: 20px;
	border-radius: 6px;
	overflow: hidden;
}

.ultraland-widget-recent-posts .recent-posts-image_wrapper img {
	border-radius: 6px;
}

.ultraland-widget-recent-posts .recent-posts-content_wrapper {
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
}

.ultraland-widget-recent-posts .post-title {
	font-weight: 600;
	margin-bottom: 0;
	font-size: 16px;
	line-height: 22px;
}

.ultraland-widget-recent-posts .post-title a {
	color: #0c1636;
	line-height: 1.2;
}

.ultraland-widget-recent-posts .post-title a:hover {
	color: #3283fd;
}

.ultraland-widget-recent-posts .date a {
	color: #4f5158;
	font-size: 14px;
}

.blog-category {
	margin: 0;
	padding: 0;
	list-style: none;
}

.blog-category li {
	position: relative;
	padding-left: 20px;
}

.blog-category li:not(:last-child) {
	margin-bottom: 13px;
}

.blog-category li:before {
	content: "";
	position: absolute;
	left: 0;
	top: 10px;
	height: 6px;
	width: 6px;
	border-radius: 6px;
	background-color: #acadba;
}

.blog-category li a {
	color: #4f5158;
}

.blog-category li a:hover {
	color: #3283fd;
}

.tag-list {
	margin: 0;
	padding: 0;
	list-style: none;
}

.tag-list li {
	display: inline-block;
}

.tag-list li a {
	padding: 4px 18px;
	font-size: 13px;
	font-weight: 500;
	color: #1c1e21;
	border: 2px solid #e3e5ee;
	border-radius: 6px;
	display: inline-block;
	margin-bottom: 9px;
	margin-right: 5px;
}

.tag-list li a:hover {
	color: #fff;
	background-color: #3283fd;
	border-color: #3283fd;
}

.comments-section {
	padding: 80px 0;
}

.comment-wrapper {
	margin-top: 55px;
}

.comment-content a {
	word-wrap: break-word;
}

.bypostauthor {
	display: block;
}

#comments {
	margin-top: 98px;
}

#comments .comments-title {
	font-size: 20px;
	margin-bottom: 26px;
}

.comments-area .comment-inner {
	margin-bottom: 50px;
}

.comments-area .reply-title {
	font-size: 24px;
	font-weight: 600;
	color: #1c1e21;
	margin-bottom: 30px;
}

.comments-area .no-comments {
	margin: 0;
	line-height: 11px;
}

.page .comment-list-wrapper {
	margin-top: 40px;
}

.page .comment-respond .comment-reply-title,
.page .comment-respond .comment-notes,
.page .comment-respond .logged-in-as {
	text-align: left;
}

.page .comment-list {
	margin-bottom: 0;
}

.page .comment-respond {
	margin-top: 60px;
}

.page .comment-list .comment .comment-body .comment_info .meta-wrapper {
	color: #bbb;
}

.comments-section .comment-list-wrapper {
	padding-top: 0;
	border: 0;
}

.comment-list {
	padding: 0;
	list-style: none;
	overflow: auto;
	margin-bottom: 0;
}

.comment-list .comment-body {
	margin-bottom: 30px;
}

.comment-list .comment .comment-body {
	overflow: auto;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	position: relative;
	margin-bottom: 23px;
	border: 1px solid #e7e9ed;
	padding: 28px 30px;
	border-radius: 6px;
}

.comment-list .comment .comment-body .comment-avatar {
	margin-right: 20px;
	border-radius: 50%;
	height: 50px;
	width: 50px;
}

.comment-list .comment .comment-body .comment-avatar img {
	border-radius: 50%;
}

.comment-list .comment .comment-body .comment_content {
	margin-top: 5px;
}

.comment-list .comment .comment-body .comment_content p {
	font-size: 14px;
	color: #4f5158;
	margin-bottom: 14px;
	line-height: 24px;
}

.comment-list .comment .comment-body .comment_info {
	position: relative;
	-webkit-box-flex: 2;
	-ms-flex: 2;
	flex: 2;
}

.comment-list .comment .comment-body .comment_info .comment_author_says {
	margin-top: 0;
	margin-bottom: 0;
	font-size: 14px;
	font-weight: 700;
	line-height: 1.2;
	color: #1c1e21;
	font-family: "Inter", sans-serif;
	display: inline-block;
	margin-right: 13px;
	position: relative;
}

.comment-list .comment .comment-body .comment_info .comment_author_says:after {
	content: "";
	position: absolute;
	right: -10px;
	bottom: 3px;
	height: 3px;
	width: 3px;
	border-radius: 10px;
	background-color: #76787d;
}

.comment-list .comment .comment-body .comment_info .meta-wrapper {
	display: inline-block;
	font-weight: 400;
	color: #76787d;
	font-size: 14px;
}

.comment-list .comment .comment-body .comment_info .comment-reply-wrapper {
	position: absolute;
	right: 0;
	top: 0;
}

.comment-list .comment .comment-body .comment_info p img {
	margin: 15px 0 5px;
}

.comment-list .comment .comment-body .comment-reply-link {
	color: #76787d;
	font-weight: 400;
	font-size: 14px;
	display: block;
	line-height: 1;
}

.comment-list .comment .comment-body .comment-reply-link i {
	margin-right: 5px;
}

.comment-list .comment .comment-body .comment-reply-link:hover {
	color: #3283fd;
}

.comment-list .comment .children {
	margin: 0;
	padding-left: 100px;
	list-style: none;
}

#comments .comment-list .comment-respond {
	margin: 20px 0;
	max-width: 100%;
}

#comments .comment-list .comment-respond #cancel-comment-reply-link {
	color: #3283fd;
	font-size: 14px;
	margin-left: 10px;
	font-weight: 600;
}

.children .comment-respond {
	margin-left: 80px !important;
}

.comment-respond .comment-reply-title {
	font-size: 20px;
	font-weight: 700;
	margin-bottom: 0;
	line-height: 20px;
}

.comment-respond .comment-notes,
.comment-respond .logged-in-as {
	margin-bottom: 30px;
	font-size: 14px;
}

#review_form .comment-respond .comment-notes,
#review_form .comment-respond .logged-in-as {
	text-align: left;
}

.comment-form .comment-form-author,
.comment-form .comment-form-email {
	width: 50%;
	display: inline-block;
	margin-bottom: 0;
}

.comment-form .comment-form-author {
	padding-right: 15px;
}

.comment-form .comment-form-email {
	padding-left: 15px;
	float: right;
}

.comment-form input {
	height: 50px;
}

.comment-form input,
.comment-form textarea {
	background-color: #f5f6fa;
	border-color: #f5f6fa;
	font-size: 14px;
	border: 2px solid #f5f6fa;
	border-radius: 6px;
	padding: 10px 20px;
}

.comment-form input::-webkit-input-placeholder,
.comment-form textarea::-webkit-input-placeholder {
	color: #76787d;
}

.comment-form input::-moz-placeholder,
.comment-form textarea::-moz-placeholder {
	color: #76787d;
}

.comment-form input:-ms-input-placeholder,
.comment-form textarea:-ms-input-placeholder {
	color: #76787d;
}

.comment-form input::-ms-input-placeholder,
.comment-form textarea::-ms-input-placeholder {
	color: #76787d;
}

.comment-form input::placeholder,
.comment-form textarea::placeholder {
	color: #76787d;
}

.comment-form input:focus,
.comment-form textarea:focus {
	background: transparent;
	border-color: #3283fd;
}

.comment-form textarea {
	height: 200px;
	margin-bottom: 14px;
}

.comment-form #submit {
	margin-bottom: 0;
	background-color: #3283fd;
	color: #fff;
	border: 2px solid transparent;
	padding: 12px 0;
	width: 100%;
	font-size: 15px;
	font-weight: 500;
	max-width: 180px;
	text-align: center;
	border-radius: 6px;
	font-family: "Inter", sans-serif;
	line-height: 1;
}

.comment-form #submit:hover {
	color: #3283fd;
	border-color: #3283fd;
	background: transparent;
}

.comment-form .comment-form-cookies-consent {
	margin-bottom: 24px !important;
	font-size: 14px;
	color: #797986;
	position: relative;
}

.comment-form .comment-form-cookies-consent:after {
	content: "";
	display: block;
	clear: both;
}

.comment-form .comment-form-cookies-consent #wp-comment-cookies-consent {
	width: auto;
	height: auto;
}

.comment-form .comment-form-cookies-consent label {
	cursor: pointer;
	display: inline;
	line-height: 1.25em;
	vertical-align: top;
	clear: both;
	padding-left: 1px;
}

.comment-form .comment-form-cookies-consent label:not(:empty) {
	padding-left: 0.75em;
}

.comment-form .comment-form-cookies-consent label:before,
.comment-form .comment-form-cookies-consent label:after {
	content: "";
	position: absolute;
	left: 0;
	top: 2px;
	font-size: 16px;
}

.comment-form .comment-form-cookies-consent label:before {
	width: 14px;
	height: 14px;
	border: 2px solid rgba(5, 20, 65, 0.2);
	cursor: pointer;
	-webkit-transition: background 0.3s;
	-o-transition: background 0.3s;
	transition: background 0.3s;
}

.comment-form .comment-form-cookies-consent input[type=checkbox] {
	outline: 0;
	visibility: hidden;
	width: 1.25em;
	margin: 0;
	display: block;
	float: left;
	font-size: inherit;
}

.comment-form .comment-form-cookies-consent input[type=checkbox]:checked+label:before {
	background: #3283fd;
	border: none;
}

.comment-form .comment-form-cookies-consent input[type=checkbox]:checked+label:after {
	-webkit-transform: translate(0.17em, 0.25em) rotate(-45deg);
	-ms-transform: translate(0.17em, 0.25em) rotate(-45deg);
	transform: translate(0.17em, 0.25em) rotate(-45deg);
	width: 0.6em;
	height: 0.25em;
	border: 0.125em solid #fff;
	border-top-style: none;
	border-right-style: none;
}

#review_form input[type=text],
#review_form textarea {
	border: 1px solid rgba(5, 20, 65, 0.2);
}

#review_form input[type=text]::-webkit-input-placeholder,
#review_form textarea::-webkit-input-placeholder {
	color: #75757d;
}

#review_form input[type=text]::-moz-placeholder,
#review_form textarea::-moz-placeholder {
	color: #75757d;
}

#review_form input[type=text]:-ms-input-placeholder,
#review_form textarea:-ms-input-placeholder {
	color: #75757d;
}

#review_form input[type=text]::-ms-input-placeholder,
#review_form textarea::-ms-input-placeholder {
	color: #75757d;
}

#review_form input[type=text]::placeholder,
#review_form textarea::placeholder {
	color: #75757d;
}

#review_form input[type=text]:focus,
#review_form textarea:focus {
	border-color: rgba(50, 131, 253, 0.5);
}

@media (max-width: 991px) {
	.comment-list .comment .children {
		padding-left: 50px;
	}
}

@media (max-width: 576px) {
	.comment-list .comment .comment-body .comment-avatar {
		width: 55px;
		height: 55px;
	}

	.comment-list .comment .children {
		padding-left: 20px;
	}

	.comment-list .comment .children .comment .comment-body .comment-avatar {
		width: 40px;
		height: 40px;
	}
}

/*--------------------------------------------------------------
  ##  Event
  --------------------------------------------------------------*/
.event-section {
	padding: 115px 0;
}

@media (max-width: 991px) {
	.event-section {
		padding: 70px 0;
	}

	.event-section .section-heading {
		text-align: center;
	}

	.event-section .btn-container {
		margin-bottom: 50px;
	}

	.event-section .btn-container.text-right {
		text-align: center !important;
	}
}

.tt-event {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-shadow: 0 30px 70px 0 rgba(1, 9, 63, 0.14);
	box-shadow: 0 30px 70px 0 rgba(1, 9, 63, 0.14);
	border-radius: 6px;
	overflow: hidden;
	margin-bottom: 30px;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
}

@media (max-width: 767px) {
	.tt-event {
		display: block;
	}
}

@media (max-width: 991px) {
	.tt-event__thumbnail {
		width: 240px;
	}
}

@media (max-width: 767px) {
	.tt-event__thumbnail {
		width: 100%;
	}

	.tt-event__thumbnail img {
		width: 100%;
	}
}

.tt-event__contents {
	padding: 0 50px 0 80px;
	-webkit-box-flex: 2;
	-ms-flex: 2;
	flex: 2;
}

@media (max-width: 991px) {
	.tt-event__contents {
		padding: 30px;
	}
}

.tt-event__title {
	font-size: 24px;
	margin-bottom: 15px;
}

.tt-event__title a {
	color: #1c1e21;
}

.tt-event__title a:hover {
	color: #3283fd;
}

.tt-event__content {
	margin-bottom: 32px;
}

.tt-event__info {
	margin: 0;
	padding: 0;
	list-style: none;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	padding-top: 15px;
	border-top: 1px solid #d6d6e1;
}

.tt-event__info li {
	width: 50%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: baseline;
	-ms-flex-align: baseline;
	align-items: baseline;
}

.tt-event__info li i {
	color: #3283fd;
	margin-right: 12px;
}

.tt-event__info-left,
.tt-event__info-right {
	width: 50%;
}

@media (max-width: 767px) {

	.tt-event__info-left,
	.tt-event__info-right {
		width: 100%;
	}
}

.tt-event__info-left p,
.tt-event__info-right p {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: baseline;
	-ms-flex-align: baseline;
	align-items: baseline;
	margin-bottom: 7px;
}

@media (max-width: 1200px) and (min-width: 991px) {

	.tt-event__info-left p,
	.tt-event__info-right p {
		font-size: 13px;
	}
}

.tt-event__info-left p i,
.tt-event__info-right p i {
	color: #3283fd;
	margin-right: 12px;
}

@media (max-width: 991px) {
	.courses-section .tt-portfolio__filter-wrapper.text-right {
		text-align: center !important;
		margin-bottom: 40px;
	}
}

@media (max-width: 991px) {
	.courses-section .section-heading {
		text-align: center !important;
	}
}

/*--------------------------------------------------------------
  ##  Home Security
  --------------------------------------------------------------*/
.home-security-section {
	padding: 120px 0 24px;
}

.home-security-wrapper {
	background-color: #082da5;
	padding: 111px 150px 93px;
	-webkit-box-shadow: 0 40px 70px 0 rgba(1, 37, 158, 0.2);
	box-shadow: 0 40px 70px 0 rgba(1, 37, 158, 0.2);
	border-radius: 10px;
	background-image: url(../../media/background/service_bg.jpg);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

@media (max-width: 991px) {
	.home-security-wrapper {
		padding: 71px 50px 63px;
	}
}

.home-security-wrapper .section-heading {
	margin-bottom: 42px;
}

.home-security-wrapper .section-heading .section-title {
	margin-bottom: 18px;
	font-size: 40px;
}

.home-security-wrapper .description {
	font-size: 20px;
	line-height: 30px;
}

/*--------------------------------------------------------------
  ##  Security Feature
  --------------------------------------------------------------*/
.security-feature-section {
	padding: 110px 0 120px;
}

@media (max-width: 991px) {
	.security-feature-section {
		padding: 70px 0 80px;
	}
}

.security-content .section-heading {
	margin-bottom: 33px;
}

.security-content .section-heading .section-title {
	margin-bottom: 16px;
}

.security-content .tt-counter-box__item {
	margin-bottom: 40px;
}

.security-content .tt-counter-box__item .tt-counter-box__count {
	font-weight: 800;
}

.security-feature-list-wrapper {
	position: relative;
	padding: 59px 0;
}

@media (max-width: 991px) {
	.security-feature-list-wrapper {
		margin-top: 50px;
	}
}

.security-feature-list-wrapper:before {
	content: "";
	position: absolute;
	background-color: #f3f4f7;
	top: 0;
	bottom: 0;
	right: 50px;
	left: 50px;
	border-radius: 10px;
	z-index: -1;
	background-image: url(../../media/background/feature-bg.jpg);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

.security-feature-list-wrapper .col-md-6 {
	padding: 0 10px;
}

.security-feature-title {
	font-size: 26px;
	line-height: 36px;
	margin-bottom: 32px;
	color: #11266d;
	padding: 0 100px;
}

.security-image-content {
	padding-top: 200px;
}

@media (max-width: 991px) {
	.security-image-content {
		padding-top: 100px;
	}
}

@media (min-width: 992px) {
	.security-image-content-wrapper {
		padding-left: 100px;
	}
}

.security-image-content-wrapper .section-heading {
	margin-bottom: 31px;
}

.security-image-content-wrapper .section-heading .section-title {
	margin-bottom: 17px;
}

.security-image-content-wrapper .section-heading .lead {
	font-size: 20px;
	line-height: 30px;
	color: #11266d;
	font-weight: 500;
	margin-bottom: 24px;
}

.security-image-content-wrapper .tt-counter-box__item .tt-counter-box__count {
	font-weight: 800;
}

.security-image-content-wrapper .tt__btn {
	padding: 14px 41px;
	margin-top: 12px;
}

@media (max-width: 991px) {
	.tt-parallax__image--ten {
		margin-top: 50px;
	}
}

/*--------------------------------------------------------------
 ##  Page Header
 --------------------------------------------------------------*/
.page-header {
	text-align: center;
	height: 450px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	position: relative;
	overflow: hidden;
	background-size: cover;
	background-position: center center;
	background-color: #3283fd;
	background-image: url(../../media/background/page_banner_bg.png);
}

@media (max-width: 1024px) {
	.page-header {
		min-height: 350px !important;
	}
}

@media (max-width: 768px) {
	.page-header {
		height: auto !important;
		padding: 50px 0;
	}
}

.page-header.pricing-page-header {
	height: 600px;
	background-image: url(../../media/background/page_banner_pricing_bg.png);
}

@media (max-width: 1024px) {
	.page-header.pricing-page-header {
		min-height: 450px !important;
	}
}

.page-header.pricing-page-header .page-header_title {
	margin-top: 0;
	line-height: 1.1;
	margin-bottom: 6px;
}

@media (max-width: 576px) {
	.page-header.pricing-page-header .page-header_title br {
		display: none;
	}
}

.page-header.pricing-page-header .description {
	margin-bottom: 73px;
}

.page-header .page-header_wrapper {
	position: relative;
	z-index: 22;
}

.page-header .page-header_title {
	font-size: 60px;
	font-weight: 800;
	position: relative;
	z-index: 2;
	line-height: 1.2;
	margin-bottom: 0;
	color: #fff;
	margin-top: 35px;
}

@media (max-width: 991px) {
	.page-header .page-header_title {
		font-size: 40px;
	}
}

.page-header .description {
	color: #fff;
}

.page-header .breadcrumbs {
	font-size: 14px;
	font-weight: 500;
	color: #fff;
	line-height: 1.4;
}

.page-header .breadcrumbs a {
	color: #fff;
}

.page-header .breadcrumbs a:hover {
	color: rgba(255, 255, 255, 0.7);
}

.page-header .breadcrumbs .separator {
	display: inline-block;
	margin: 0 8px;
	height: 5px;
	width: 5px;
	border-radius: 20px;
	background-color: #fff;
}

.menu-transparent~.page-header .page-header_wrapper {
	margin-top: 85px;
}

.single-post-header-bg {
	text-align: left;
	position: relative;
	height: 650px;
	background-image: url(../../media/background/blog_single.png);
}

@media (max-width: 991px) {
	.single-post-header-bg {
		height: auto;
		padding: 120px 0 70px;
	}
}

@media (max-width: 767px) {
	.single-post-header-bg {
		padding: 120px 0 70px;
	}
}

.single-post-header-bg .overlay-bg {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(12, 22, 54, 0.7);
}

.single-post-header-bg .single-post-header {
	position: relative;
}

.single-post-header-bg .single-post-title {
	font-size: 60px;
	color: #fff;
	line-height: 1.2;
	margin: 8px 0 11px;
	max-width: 780px;
	-ms-word-wrap: break-word;
	word-wrap: break-word;
}

@media (max-width: 991px) {
	.single-post-header-bg .single-post-title {
		font-size: 40px;
	}
}

@media (max-width: 576px) {
	.single-post-header-bg .single-post-title {
		font-size: 30px;
	}
}

.single-post-header-bg .post-meta-wrapper .tt-blog-meta-category {
	color: #1ca30d;
	margin-right: 10px;
}

.single-post-header-bg .post-meta li a {
	color: #fff;
}

.single-post-header-bg .post-meta li:not(:last-child):after {
	right: -13px;
}

.single-post-header-bg .post-meta li:not(:last-child) {
	margin-right: 18px;
}

.single-post-header-bg .post-author img {
	border-radius: 50% !important;
	margin-right: 15px;
}

/*--------------------------------------------------------------
##  Error Page
--------------------------------------------------------------*/
.error_page {
	position: relative;
	height: 100vh;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
}

.error_page .error-page-content {
	position: relative;
	z-index: 2;
	max-width: 990px;
	margin: 0 auto;
	padding: 0 15px;
}

.error_page .error-page-content .error-image {
	margin-bottom: 32px;
}

.error_page .error-page-content .error-title {
	font-size: 100px;
	margin-bottom: 3px;
	line-height: 1.1;
}

@media (max-width: 991px) {
	.error_page .error-page-content .error-title {
		font-size: 70px;
	}
}

@media (max-width: 576px) {
	.error_page .error-page-content .error-title {
		font-size: 50px;
	}
}

.error_page .error-page-content p {
	margin-bottom: 32px;
	font-size: 18px;
	font-weight: 400;
}

.error_page .error-info {
	max-width: 600px;
	margin: 0 auto;
}

.error-animate-element {
	margin: 0;
	padding: 0;
	list-style: none;
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
}

@media (max-width: 991px) {
	.error-animate-element {
		display: none;
	}
}

.error-animate-element li img {
	position: absolute;
}

.error-animate-element li:nth-child(1) img {
	left: 10%;
	bottom: 35%;
}

.error-animate-element li:nth-child(2) img {
	left: 17%;
	top: 22%;
}

.error-animate-element li:nth-child(3) img {
	right: 12%;
	bottom: 44%;
}

/*--------------------------------------------------------------
## Form
--------------------------------------------------------------*/
.login-form-area {
	background-color: #ebeef2;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	padding: 200px 0 140px;
}

@media (max-width: 991px) {
	.login-form-area {
		padding: 150px 0 80px;
	}
}

.login-form-area .anemate-element {
	margin: 0;
	padding: 0;
	list-style: none;
	list-style: none;
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
}

@media (max-width: 767px) {
	.login-form-area .anemate-element {
		display: none;
	}
}

.login-form-area .anemate-element li {
	position: absolute;
	width: 100%;
	height: 100%;
}

.login-form-area .anemate-element li .inner {
	position: absolute;
}

.login-form-area .anemate-element li:nth-child(1) .inner {
	top: 22%;
	left: 18%;
}

@media (max-width: 1200px) {
	.login-form-area .anemate-element li:nth-child(1) .inner {
		left: 13%;
	}
}

@media (max-width: 991px) {
	.login-form-area .anemate-element li:nth-child(1) .inner {
		left: 0%;
	}
}

.login-form-area .anemate-element li:nth-child(2) .inner {
	bottom: 22%;
	left: 23%;
}

@media (max-width: 1200px) {
	.login-form-area .anemate-element li:nth-child(2) .inner {
		left: 15%;
	}
}

@media (max-width: 991px) {
	.login-form-area .anemate-element li:nth-child(2) .inner {
		left: 2%;
	}
}

.login-form-area .anemate-element li:nth-child(3) .inner {
	top: 38%;
	right: 24%;
}

@media (max-width: 1200px) {
	.login-form-area .anemate-element li:nth-child(3) .inner {
		right: 16%;
	}
}

@media (max-width: 991px) {
	.login-form-area .anemate-element li:nth-child(3) .inner {
		right: 5%;
	}
}

.login-form-area .anemate-element li:nth-child(4) .inner {
	bottom: 28%;
	right: 19%;
}

@media (max-width: 1200px) {
	.login-form-area .anemate-element li:nth-child(4) .inner {
		right: 11%;
	}
}

@media (max-width: 991px) {
	.login-form-area .anemate-element li:nth-child(4) .inner {
		right: 0%;
	}
}

.tt-form-heading {
	margin-bottom: 50px;
}

.tt-form-heading .form-title {
	font-size: 44px;
	margin: 0;
	font-weight: 700;
	margin-bottom: 6px;
}

.tt-form-heading p {
	font-size: 16px;
	color: #4f5158;
}

.tt-form-wrapper {
	border-radius: 6px;
	background-color: white;
	max-width: 550px;
	margin: 0 auto;
	padding: 50px 70px;
	position: relative;
	z-index: 2;
}

@media (max-width: 420px) {
	.tt-form-wrapper {
		padding: 40px 25px;
	}
}

.tt-form-wrapper .input-checkbox {
	margin: 0;
}

.tt-form-wrapper .input-checkbox span {
	font-size: 14px;
}

.tt-form-wrapper .input-wrap {
	margin-bottom: 17px;
}

.tt-form-wrapper .tt__btn {
	display: block;
	padding: 14px 20px;
	width: 100%;
	margin-top: 33px;
}

.input-wrap label {
	color: #1c1e21;
	font-weight: 500;
}

.condition-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

@media (max-width: 480px) {
	.condition-wrap {
		display: block;
	}
}

.condition-wrap a {
	font-size: 14px;
}

.input-field {
	position: relative;
}

.input-field input,
.input-field textarea {
	border-radius: 6px;
	background-color: #f1f2f3;
	border: 2px solid #f1f2f3;
	padding-left: 50px;
}

.input-field input::-webkit-input-placeholder,
.input-field textarea::-webkit-input-placeholder {
	color: #83858c;
}

.input-field input::-moz-placeholder,
.input-field textarea::-moz-placeholder {
	color: #83858c;
}

.input-field input:-ms-input-placeholder,
.input-field textarea:-ms-input-placeholder {
	color: #83858c;
}

.input-field input::-ms-input-placeholder,
.input-field textarea::-ms-input-placeholder {
	color: #83858c;
}

.input-field input::placeholder,
.input-field textarea::placeholder {
	color: #83858c;
}

.input-field input:focus,
.input-field textarea:focus {
	border-color: #3283fd;
	background: transparent;
}

.input-field input {
	height: 60px;
}

.input-field textarea {
	height: 120px;
}

.input-field i {
	position: absolute;
	left: 25px;
	top: 50%;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	color: #83858c;
}

.input-field.message-field i {
	top: 15px;
	-webkit-transform: translate(0);
	-ms-transform: translate(0);
	transform: translate(0);
}

.mb-9 {
	margin-bottom: 9px !important;
}

.facebook-login {
	border-radius: 6px;
	background-color: #ebeef2;
	max-width: 300px;
	margin: 0 auto 15px;
}

.facebook-login i {
	display: inline-block;
	height: 50px;
	width: 50px;
	line-height: 50px;
	text-align: center;
	background-color: #3360bd;
	color: #fff;
	margin-right: 15px;
	border-bottom-left-radius: 6px;
	border-top-left-radius: 6px;
}

.facebook-login a {
	color: #4f5158;
}

.facebook-login a:hover {
	color: #3283fd;
}

.switch-access {
	font-size: 14px;
	color: #55545b;
	display: block;
	margin-bottom: 23px;
}

.more-info {
	font-size: 14px;
	color: #4f5158;
	text-align: center;
	margin: 21px 0 0;
}

.more-info a {
	color: #3283fd;
}

/*--------------------------------------------------------------
 ## Form
 --------------------------------------------------------------*/
.appointment-section {
	background-color: #f1f2f3;
	padding: 115px 0 120px;
	background-image: url(../../media/background/appointment_bg.jpg);
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
}

.appointment-section .section-heading {
	margin-bottom: 0;
}

.appointment-section .section-heading .description {
	font-size: 20px;
	line-height: 30px;
}

.appointment-form .input-field input,
.appointment-form .input-field select {
	height: 50px;
}

.appointment-form .input-field select {
	border: 2px solid #fff;
	color: #83858c;
}

.appointment-form .input-field select:focus {
	border-color: #089df1;
	border-radius: 0;
}

.appointment-form .input-field select:focus-visible {
	border-radius: 0;
}

.appointment-form .input-field input,
.appointment-form .input-field select,
.appointment-form .input-field textarea {
	background-color: #fff;
	padding: 10px 25px;
	margin-bottom: 16px;
	width: 100%;
	border-radius: 0;
	font-size: 14px;
}

.appointment-form .input-field input::-webkit-input-placeholder,
.appointment-form .input-field select::-webkit-input-placeholder,
.appointment-form .input-field textarea::-webkit-input-placeholder {
	color: #83858c;
}

.appointment-form .input-field input::-moz-placeholder,
.appointment-form .input-field select::-moz-placeholder,
.appointment-form .input-field textarea::-moz-placeholder {
	color: #83858c;
}

.appointment-form .input-field input:-ms-input-placeholder,
.appointment-form .input-field select:-ms-input-placeholder,
.appointment-form .input-field textarea:-ms-input-placeholder {
	color: #83858c;
}

.appointment-form .input-field input::-ms-input-placeholder,
.appointment-form .input-field select::-ms-input-placeholder,
.appointment-form .input-field textarea::-ms-input-placeholder {
	color: #83858c;
}

.appointment-form .input-field input::placeholder,
.appointment-form .input-field select::placeholder,
.appointment-form .input-field textarea::placeholder {
	color: #83858c;
}

.appointment-form .input-field input:focus,
.appointment-form .input-field select:focus,
.appointment-form .input-field textarea:focus {
	border-color: #089df1;
}

.appointment-form .input-field textarea {
	margin-bottom: 9px;
	height: 130px;
}

.appointment-form .col-md-6,
.appointment-form .col-md-12 {
	padding: 0 8px;
}

.appointment-form .tt__btn.submit-btn {
	width: auto;
	border-radius: 0;
	padding: 14px 35px;
}

.appointment-contact-info {
	margin-top: 28px;
}

.appointment-contact-info .appointment-contact-info-title {
	font-size: 16px;
	margin-bottom: 0;
}

.appointment-contact-info .appointment-contact-info-number {
	font-size: 20px;
}

/*--------------------------------------------------------------
  ##  Footer
  --------------------------------------------------------------*/
#footer {
	background: #f3f6fd;
}

#footer .footer-widget-wrapper {
	padding: 95px 0 25px;
}

#footer .footer-widget-wrapper .footer-logo {
	display: inline-block;
	margin-bottom: 30px;
}

#footer .footer-widget-wrapper .widget:last-child {
	margin-bottom: 65px;
}

#footer .widget-title {
	font-size: 20px;
	font-weight: 700;
	color: #1c1e21;
	margin-bottom: 37px;
}

#footer.footer-style-two {
	background-color: #17285f;
}

#footer.footer-style-two .footer-widget-wrapper {
	padding: 95px 0 58px;
}

@media (max-width: 991px) {
	#footer.footer-style-two .footer-widget-wrapper {
		padding: 70px 0 20px;
	}
}

#footer.footer-style-two .widget-title {
	color: #fff;
	margin-bottom: 27px;
}

#footer.footer-style-two .footer-menu li {
	margin-bottom: 5px;
}

#footer.footer-style-two .footer-menu li a {
	color: #acb1c2;
}

#footer.footer-style-two .footer-menu li a:hover {
	color: #fff;
}

#footer.footer-style-two .about-widget_wrapper p {
	color: #fff;
}

#footer.footer-style-two .footer-contact-info li {
	color: #acb1c2;
}

#footer.footer-style-two .ultraland-widget-recent-posts .post-title a {
	color: #fff;
}

#footer.footer-style-two .ultraland-widget-recent-posts .date a {
	color: #acb1c2;
}

#footer.footer-style-two .ultraland-widget-recent-posts .date a:hover {
	color: #fff;
}

#footer.footer-style-two .site-info {
	border-color: rgba(255, 255, 255, 0.059);
}

#footer.footer-style-two .site-info .copy-right {
	color: #99a0b8;
}

#footer.footer-style-two .site-info .copy-right a {
	color: #fff;
}

#footer .footer-menu {
	margin-bottom: 30px;
	padding: 0;
	list-style: none;
}

#footer .footer-menu li {
	display: block;
	margin-bottom: 13px;
}

#footer .footer-menu li a {
	display: inline-block;
	color: #595959;
	font-weight: 500;
	font-size: 15px;
	position: relative;
}

#footer .footer-menu li a:hover {
	color: #3283fd;
}

#footer .footer-social-link {
	margin: 0;
	padding: 0;
	list-style: none;
}

#footer .footer-social-link li {
	display: inline-block;
	margin: 0 5px;
}

#footer .footer-social-link li a {
	display: block;
	color: #595959;
	font-size: 15px;
}

#footer .footer-social-link li a:hover {
	background: #3283fd;
	border-color: #3283fd;
}

#footer .site-info {
	padding: 23px 0 22px;
	text-align: center;
	border-top: 1px solid #ece9fc;
}

#footer .site-info .copy-right {
	margin-bottom: 0;
	color: #595959;
	font-size: 14px;
	font-weight: 500;
	line-height: 24px;
}

#footer .site-info .copy-right a {
	color: #1c1e21;
}

#footer .site-info .copy-right a:hover {
	color: #0265fa;
}

#footer.footer-dark {
	background-color: #0c1636;
}

#footer.footer-dark .widget-title,
#footer.footer-dark .social-title,
#footer.footer-dark .about-widget_wrapper p {
	color: #fff;
}

#footer.footer-dark .footer-contact-info li {
	color: #acadb4;
}

#footer.footer-dark .footer-menu li a {
	color: #acadb4;
}

#footer.footer-dark .footer-menu li a:hover {
	color: #fff;
}

#footer.footer-dark .footer-social-link li a {
	color: #fff;
	border-color: rgba(255, 255, 255, 0.102);
}

#footer.footer-dark .footer-social-link li a:hover {
	border-color: #3283fd;
}

#footer.footer-dark .site-info {
	border-color: rgba(255, 255, 255, 0.078);
}

#footer.footer-dark .site-info .copy-right {
	color: #87888e;
}

#footer.footer-dark .site-info .copy-right a {
	color: #fff;
}

#footer.footer-dark .site-info .copy-right a:hover {
	color: #3283fd;
}

#footer.footer-medical {
	background-color: #1a2e6b;
	background-image: url(../../media/background/footer_medical_bg.jpg);
	background-size: cover;
	background-position: center center;
}

#footer.footer-medical .footer-widget-wrapper {
	padding: 65px 0 25px;
}

#footer.footer-medical p {
	color: #bfc1c8;
}

#footer.footer-medical .widget-title {
	color: #fff;
	margin-bottom: 27px;
}

#footer.footer-medical .about-widget_wrapper .about_text {
	color: #fff;
}

#footer.footer-medical .footer-contact-info li {
	color: #bfc1c8;
}

#footer.footer-medical .footer-menu li {
	margin-bottom: 0;
	line-height: 30px;
}

#footer.footer-medical .footer-menu li a {
	color: #bfc1c8;
	font-size: 15px;
	font-weight: 400;
}

#footer.footer-medical .footer-newsletter-form .newsletter-inner {
	background-color: #fff;
}

#footer.footer-medical .footer-newsletter-form .newsletter-inner .form-control {
	border: 0;
	background-color: #fff;
	height: 54px;
	padding: 15px 20px;
}

#footer.footer-medical .footer-newsletter-form .newsletter-inner .form-control:focus {
	-webkit-box-shadow: none;
	box-shadow: none;
}

#footer.footer-medical .footer-newsletter-form .newsletter-inner .form-control::-webkit-input-placeholder {
	color: #666974;
	font-size: 15px;
}

#footer.footer-medical .footer-newsletter-form .newsletter-inner .form-control::-moz-placeholder {
	color: #666974;
	font-size: 15px;
}

#footer.footer-medical .footer-newsletter-form .newsletter-inner .form-control:-ms-input-placeholder {
	color: #666974;
	font-size: 15px;
}

#footer.footer-medical .footer-newsletter-form .newsletter-inner .form-control::-ms-input-placeholder {
	color: #666974;
	font-size: 15px;
}

#footer.footer-medical .footer-newsletter-form .newsletter-inner .form-control::placeholder {
	color: #666974;
	font-size: 15px;
}

#footer.footer-medical .footer-newsletter-form .newsletter-inner .newsletter-submit {
	border-radius: 0;
}

#footer.footer-medical .footer-newsletter-form .newsletter-inner .newsletter-submit .fa-spinner {
	display: none;
}

#footer.footer-medical .footer-newsletter-form .newsletter-inner .newsletter-submit.clicked .fa-spinner {
	display: block;
}

#footer.footer-medical .footer-newsletter-form .newsletter-inner .newsletter-submit:hover {
	background-color: #3283fd;
	color: #fff;
}

#footer.footer-science {
	background-color: #eff1f5;
}

#footer.footer-science .widget-title {
	color: #11266d;
	margin-bottom: 24px;
}

#footer.footer-science .footer-menu li {
	margin-bottom: 0;
	line-height: 30px;
}

#footer.footer-science .footer-menu li a {
	font-size: 15px;
	color: #3c435b;
	font-weight: 400;
}

#footer.footer-science .footer-menu li a:hover {
	color: #2a5cff;
}

#footer.footer-science .footer-contact-info li i {
	color: #2a5cff;
}

@media (min-width: 991px) {
	#footer.footer-science .footer-social-link {
		text-align: right;
	}
}

#footer.footer-science .footer-social-link li {
	margin-left: 0 !important;
}

#footer.footer-science .footer-social-link li:not(:last-child) {
	margin-right: 5px !important;
}

#footer.footer-science .footer-social-link li a {
	display: block;
	height: 40px;
	width: 40px;
	line-height: 40px;
	text-align: center;
	background-color: #fff;
	border-radius: 50%;
	color: #3c435b;
}

#footer.footer-science .footer-social-link li a:hover {
	color: #fff;
	background-color: #2a5cff;
}

#footer.footer-science .site-info {
	border-color: #e2e3e7;
}

@media (min-width: 991px) {
	#footer.footer-science .site-info {
		text-align: left;
	}
}

#footer.footer-science .site-info .copy-right {
	color: #666974;
	font-weight: 400;
}

#footer.footer-education {
	background-color: #f0f3fa;
}

#footer.footer-education .footer-widget-wrapper {
	padding: 220px 0 25px;
}

#footer.footer-education .widget-title {
	margin-bottom: 21px;
}

#footer.footer-education .about-widget_wrapper p {
	font-weight: 400;
	color: #505056;
}

#footer.footer-education .footer-contact-info li {
	color: #505056;
}

#footer.footer-education .footer-contact-info li i {
	color: #505056;
}

#footer.footer-education .footer-menu li {
	margin-bottom: 5px;
}

#footer.footer-education .footer-menu li a {
	font-size: 15px;
	font-weight: 400;
}

#footer.footer-education .site-info {
	text-align: left;
	border-color: #e0e2e9;
}

@media (max-width: 767px) {
	#footer.footer-education .site-info {
		text-align: center;
	}
}

#footer.footer-education .site-info .copy-right {
	color: #505056;
	font-weight: 400;
}

#footer.footer-education .footer-social-link {
	text-align: right;
}

@media (max-width: 767px) {
	#footer.footer-education .footer-social-link {
		text-align: center;
		margin-top: 20px;
	}
}

#footer.footer-education .footer-social-link li a {
	display: block;
	height: 40px;
	width: 40px;
	line-height: 35px;
	text-align: center;
	border: 2px solid #d0d2d9;
	color: #8b8d92;
	font-size: 14px;
}

#footer.footer-education .footer-social-link li a:hover {
	color: #fff;
	border-color: #3283fd;
}

#footer .footer-newsletter-form .newsletter-inner {
	background-color: #fff;
	border-radius: 6px;
	overflow: hidden;
}

#footer .footer-newsletter-form .newsletter-inner .form-control {
	border: 0;
	background-color: #fff;
	height: 54px;
	padding: 15px 20px;
}

#footer .footer-newsletter-form .newsletter-inner .form-control:focus {
	-webkit-box-shadow: none;
	box-shadow: none;
}

#footer .footer-newsletter-form .newsletter-inner .form-control::-webkit-input-placeholder {
	color: #666974;
	font-size: 15px;
}

#footer .footer-newsletter-form .newsletter-inner .form-control::-moz-placeholder {
	color: #666974;
	font-size: 15px;
}

#footer .footer-newsletter-form .newsletter-inner .form-control:-ms-input-placeholder {
	color: #666974;
	font-size: 15px;
}

#footer .footer-newsletter-form .newsletter-inner .form-control::-ms-input-placeholder {
	color: #666974;
	font-size: 15px;
}

#footer .footer-newsletter-form .newsletter-inner .form-control::placeholder {
	color: #666974;
	font-size: 15px;
}

#footer .footer-newsletter-form .newsletter-inner .newsletter-submit {
	border-radius: 0;
	border-color: #2a5cff;
	background-color: #2a5cff;
}

#footer .footer-newsletter-form .newsletter-inner .newsletter-submit:before {
	background-color: #2a5cff;
}

#footer .footer-newsletter-form .newsletter-inner .newsletter-submit i {
	font-size: 20px;
}

#footer .footer-newsletter-form .newsletter-inner .newsletter-submit .fa-spinner {
	display: none;
}

#footer .footer-newsletter-form .newsletter-inner .newsletter-submit.clicked .fa-spinner {
	display: block;
}

#footer .footer-newsletter-form .newsletter-inner .newsletter-submit:hover {
	background-color: #2a5cff;
	color: #fff;
}

#footer.footer-home-security {
	background-color: transparent;
}

#footer.footer-home-security .footer-newsletter-form .newsletter-inner {
	background-color: #eff1f5;
}

#footer.footer-home-security .footer-newsletter-form .newsletter-inner .form-control {
	background-color: #eff1f5;
}

#footer.footer-home-security .footer-newsletter-form .newsletter-inner .form-control::-webkit-input-placeholder {
	color: #666974;
}

#footer.footer-home-security .footer-newsletter-form .newsletter-inner .form-control::-moz-placeholder {
	color: #666974;
}

#footer.footer-home-security .footer-newsletter-form .newsletter-inner .form-control:-ms-input-placeholder {
	color: #666974;
}

#footer.footer-home-security .footer-newsletter-form .newsletter-inner .form-control::-ms-input-placeholder {
	color: #666974;
}

#footer.footer-home-security .footer-newsletter-form .newsletter-inner .form-control::placeholder {
	color: #666974;
}

#footer.footer-home-security .footer-menu li {
	margin-bottom: 3px;
}

#footer.footer-home-security .site-info {
	background-color: #eff1f5;
	border-top: 0;
	background-image: url(../../media/background/footer_copright.png);
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
}

#footer.footer-home-security .copy-right {
	text-align: left;
}

@media (max-width: 767px) {
	#footer.footer-home-security .copy-right {
		text-align: center;
	}
}

#footer.footer-home-security .footer-contact-info li i {
	color: #fb514d;
}

#footer.footer-home-security .widget-title {
	color: #11266d;
	margin-bottom: 27px;
}

#footer.footer-home-security .footer-social-link li {
	margin: 0;
}

#footer.footer-home-security .footer-social-link li:not(:last-child) {
	margin-right: 5px;
}

#footer.footer-home-security .footer-social-link li a {
	display: block;
	height: 40px;
	width: 40px;
	line-height: 41px;
	text-align: center;
	background-color: #fff;
	border-radius: 50%;
}

#footer.footer-home-security .footer-social-link li a:hover {
	background-color: #3283fd;
	color: #fff;
}

.footer-social-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	width: -webkit-max-content;
	width: -moz-max-content;
	width: max-content;
	margin-left: auto;
}

@media (max-width: 991px) {
	.footer-social-wrap {
		margin: 15px auto 0;
	}
}

.footer-social-wrap .social-title {
	font-size: 15px;
	font-weight: 500;
	color: #11266d;
	margin-right: 15px;
	margin-bottom: 0;
}

.footer-top {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	padding-top: 38px;
	padding-bottom: 40px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.102);
}

@media (max-width: 991px) {
	.footer-top {
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
	}
}

.footer-top .footer-phone-info {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

@media (max-width: 991px) {
	.footer-top .footer-phone-info {
		width: -webkit-max-content;
		width: -moz-max-content;
		width: max-content;
		margin: 0 auto;
	}
}

.footer-top .image-wrap {
	margin-right: 20px;
}

.footer-top .phone-info {
	font-size: 15px;
	line-height: 24px;
	margin: 0;
}

@media (max-width: 991px) {
	.footer-top .phone-info {
		width: 100%;
	}
}

@media (max-width: 767px) {
	.footer-top .phone-info br {
		display: none;
	}
}

.footer-top .phone-info span {
	color: #fff;
}

.footer-top .footer-social-link-top {
	margin: 0;
	padding: 0;
	list-style: none;
}

.footer-top .footer-social-link-top li {
	display: inline-block;
}

.footer-top .footer-social-link-top li:not(:last-child) {
	margin-right: 7px;
}

.footer-top .footer-social-link-top li a {
	width: 40px;
	height: 40px;
	line-height: 40px;
	background-color: rgba(255, 255, 255, 0.102);
	color: #fff;
	display: inline-block;
	text-align: center;
}

.footer-top .footer-social-top {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

@media (max-width: 991px) {
	.footer-top .footer-social-top {
		margin: 20px auto 0;
		width: -webkit-max-content;
		width: -moz-max-content;
		width: max-content;
	}
}

.footer-top .social-title {
	color: #fff;
	font-size: 15px;
	font-weight: 500;
	margin-bottom: 0;
	margin-right: 20px;
}

.footer-shape-element {
	margin: 0;
	padding: 0;
	list-style: none;
	position: relative;
	z-index: 2;
}

@media (max-width: 640px) {
	.footer-shape-element {
		display: none;
	}
}

.footer-shape-element li {
	position: absolute;
}

.footer-shape-element li.element-left {
	left: 40px;
	bottom: 0;
}

.footer-shape-element li.element-left img {
	max-width: 209px;
	opacity: 0.6;
}

.footer-shape-element li.element-right {
	right: 55px;
	bottom: 0;
}

.footer-shape-element li.element-right img {
	width: 152px;
}

.footer-shape-element li.element {
	bottom: 170px;
	right: 100px;
}

.footer-shape-element li.element img {
	width: 182px;
}

.about-widget_wrapper {
	max-width: 290px;
}

.about-widget_wrapper p {
	color: #1c1e21;
	margin-bottom: 20px;
	font-size: 16px;
	font-weight: 600;
}

.about-widget_wrapper .footer-logo {
	margin-bottom: 15px;
	max-width: 150px;
}

.about-widget_wrapper .footer-logo a {
	display: block;
	max-width: 150px;
}

.about-widget_wrapper .footer-logo a img {
	height: 52px;
	max-width: 150px;
}

.about-widget_wrapper h4 {
	color: #fff;
	font-size: 16px;
	font-weight: 400;
	margin-bottom: 0;
}

.ultraland-contact-widget .about_text {
	color: #4f5158;
	margin-bottom: 20px;
}

.ultraland-contact-widget .social-title {
	font-size: 15px;
	font-weight: 600;
	margin-top: 20px;
	margin-bottom: 15px;
}

.ultraland-contact-widget .footer-social-link {
	margin-left: 0;
}

.ultraland-contact-widget .footer-social-link li:not(:last-child) {
	margin-right: 7px;
}

.ultraland-contact-widget .footer-social-link li a {
	font-size: 14px;
	display: inline-block;
	width: 36px;
	height: 36px;
	line-height: 32px;
	border: 2px solid #d8dbe2;
	text-align: center;
	border-radius: 6px;
}

.ultraland-contact-widget .footer-social-link li a:hover {
	color: #fff !important;
}

.footer-contact-info {
	margin: 0;
	padding: 0;
	list-style: none;
}

.footer-contact-info li {
	margin-bottom: 10px;
	color: #1c1e21;
}

.footer-contact-info li i {
	margin-right: 10px;
	font-size: 17px;
	color: #3283fd;
}

.screenshot-slider {
	height: 543px;
	width: 220px !important;
	margin-right: 10px !important;

}

.screenshot-slider img {
	margin-top: -20px;
}

/* mdy styles */

.download-app__btns {
	display: flex;
	gap: 10px;
}
.cursor-pointer {
	cursor: pointer;
}