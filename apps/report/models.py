from django.db import models
from django.utils.translation import gettext as _
from dj_language.field import LanguageField



class Service(models.Model):
    class Service(models.TextChoices):
        hussainiyah = 'hussainiyah', '<PERSON><PERSON>h'
        quran = 'quran', 'Quran'
        mafatih = 'mafatih', 'Ma<PERSON>tih'
        qiblah_finder = 'qiblah', '<PERSON><PERSON><PERSON> Finder'
        ahkam = 'ahkam', 'Ahkam'
        calendar = 'calendar', 'Calendar'
        talk = 'talk', 'Talk'
        meet = 'meet', 'Meet'
        library = 'library', 'Library'
        hadis = 'hadis', 'Hadis'
        habit = 'habit', 'Habit'
        
    name = models.CharField(max_length=255,choices=Service.choices, verbose_name=_('service'))
    slug = models.SlugField(verbose_name=_('slug'))    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))
    
    def __str__(self):
        return self.get_name_display()
    

class Report(models.Model):

    user = models.ForeignKey("account.User", on_delete=models.SET_NULL, verbose_name=_('user'), null=True, blank=True)
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='reports')
    
    subject = models.ForeignKey('Subject', on_delete=models.CASCADE, related_name='subject_reports', null=True, blank=True)    
    text = models.CharField(max_length=255, verbose_name=_('Report Text'))
    object_id = models.PositiveBigIntegerField(verbose_name=_('object id'), null=True, blank=True)
    description = models.TextField(null=True, blank=True, verbose_name=_('description'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))

    def __str__(self):
        return str(self.id)

    class Meta:
        verbose_name = _('report')
        verbose_name_plural = _('reports')
        ordering = ('-id',)

class Subject(models.Model):
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='subjects')
    name = models.CharField(max_length=255)
    slug = models.SlugField(verbose_name=_('slug'))    
    language = LanguageField(verbose_name=_('language'))

    def __str__(self):
        return f"{self.name} - {self.service.name}"
    
    

class ReportComment(models.Model):
    class Status(models.TextChoices):
        Rejected = 'rejected', 'Rejected'
        Solved = 'solved', 'Solved'
        InProgress = 'inProgress', 'In Progress'
        initial = 'initial', 'Initial'

    report = models.ForeignKey(Report, on_delete=models.CASCADE, verbose_name=_('report'))
    comment = models.TextField(verbose_name=_('comment'))
    admin = models.ForeignKey("account.User", on_delete=models.SET_NULL, verbose_name=_('user'), null=True, limit_choices_to={
        'is_staff': True,
    })
    status = models.CharField(
        choices=Status.choices, verbose_name=_('status'), max_length=10, default=Status.initial
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))

    class Meta:
        verbose_name = _('comment')
        verbose_name_plural = _('report comments')
        ordering = ('-id',)
