from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin

from apps.report.models import Report, ReportComment, Subject, Service



# @admin.register(Service)
# class ServiceAdmin(AjaxDatatable):
#     list_display = ('name', 'slug','created_at')
#     search_fields = ('name', 'slug')
#     list_filter = ('name', 'slug')
#     prepopulated_fields = {"slug": ("name",)}


@admin.register(Subject)
class SubjectAdmin(AjaxDatatable):
    list_display = ('name', 'slug', 'language', 'service')
    search_fields = ('name', 'slug','service__name')
    list_filter = ('service', 'language')
    prepopulated_fields = {"slug": ("name",)}

    autocomplete_fields = ('language', )
    

class ReportCommentInline(admin.TabularInline):
    model = ReportComment
    extra = 1
    readonly_fields = ('admin',)


@admin.register(Report)
class ReportAdmin(AjaxDatatable):
    list_display = ('user', 'service', 'subject','created_at',)
    list_filter = (
        'service', 'created_at'
    )
    search_fields = ('user__username', 'service__name', 'subject__name', 'description')
    latest_by = 'created_at'
    inlines = [
        ReportCommentInline,
    ]
    autocomplete_fields = ('user',)

    def save_related(self, request, form, formsets, change):
        for formset in formsets:
            for _form in formset.forms:
                if not _form.instance.pk:
                    _form.instance.admin = request.user

        return super().save_related(request, form, formsets, change)
