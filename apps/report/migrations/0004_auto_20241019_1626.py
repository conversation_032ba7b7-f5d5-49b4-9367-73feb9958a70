# Generated by Django 3.2.25 on 2024-10-19 16:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('aboutservice', '0007_alter_aboutservice_service'),
        ('report', '0003_reportcomment_comment'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='report',
            name='type',
        ),
        migrations.AlterField(
            model_name='report',
            name='service',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='aboutservice.aboutservice'),
        ),
        migrations.AlterField(
            model_name='report',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='user'),
        ),
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subjects', to='aboutservice.aboutservice')),
            ],
        ),
        migrations.AddField(
            model_name='report',
            name='subject',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subject_reports', to='report.subject'),
        ),
    ]
