# Generated by Django 3.2.22 on 2023-11-02 13:43

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('report', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='reportcomment',
            options={'ordering': ('-id',), 'verbose_name': 'comment', 'verbose_name_plural': 'report comments'},
        ),
        migrations.AddField(
            model_name='reportcomment',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='created at'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='report',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='created at'),
        ),
    ]
