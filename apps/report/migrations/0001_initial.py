# Generated by Django 3.2.22 on 2023-11-02 13:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service', models.CharField(choices=[('quran', 'Quran'), ('mafatih', 'Mafatih'), ('eightmag', 'Eightmag'), ('adhan', '<PERSON>han'), ('donate', 'Donate'), ('elalhabib', '<PERSON><PERSON>hab<PERSON>'), ('habibnet', 'Habibnet'), ('hadis', 'Hadis'), ('hussainiya', 'Hussainiya'), ('calendar', 'Calendar'), ('library', 'Library'), ('ahkam', 'Ahkam'), ('account', 'Account')], max_length=10, verbose_name='service')),
                ('type', models.CharField(choices=[('typo', 'Typo'), ('IC', 'Inappropriate content'), ('Bug', 'Software Error'), ('UserReport', 'User report')], max_length=10, verbose_name='type')),
                ('object_id', models.PositiveBigIntegerField(blank=True, null=True, verbose_name='object id')),
                ('description', models.TextField(blank=True, null=True, verbose_name='description')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'report',
                'verbose_name_plural': 'reports',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='ReportComment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('rejected', 'Rejected'), ('solved', 'Solved'), ('inProgress', 'In Progress'), ('initial', 'Initial')], default='initial', max_length=10, verbose_name='status')),
                ('admin', models.ForeignKey(limit_choices_to={'is_staff': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='user')),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='report.report', verbose_name='report')),
            ],
        ),
    ]
