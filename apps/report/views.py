from rest_framework.generics import C<PERSON>AP<PERSON>View, ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.shortcuts import get_object_or_404

from apps.report.models import Report, Subject, Service
from apps.report.serializer import ReportSerializer, SubjectSerializer



class ReportCreateAPIView(CreateAPIView):
    serializer_class = ReportSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def perform_create(self, serializer):
        serializer.save()



class SubjectListAPIView(ListAPIView):
    serializer_class = SubjectSerializer

    @swagger_auto_schema(manual_parameters=[
            openapi.Parameter(
                'service', openapi.IN_QUERY, 
                description="service to filter subjects", 
                type=openapi.TYPE_STRING
            )]
    )
    def get_queryset(self):
        service_slug = self.request.query_params.get('service')
        language_code = self.request.LANGUAGE_CODE  

        queryset = Subject.objects.all()

        if service_slug:
            service = get_object_or_404(Service, slug=service_slug)
            queryset = queryset.filter(service=service, language__code=language_code)

        else:
            queryset = queryset.filter(language__code=language_code)
        
        return queryset