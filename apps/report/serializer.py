from rest_framework import serializers, generics
from .models import Report, Service, Subject
from django.shortcuts import get_object_or_404
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticatedOrReadOnly



class SubjectSerializer(serializers.ModelSerializer):
    service = serializers.SerializerMethodField()
    
    class Meta:
        model = Subject
        fields = ('id', 'name', 'slug', 'service')
        
    def get_service(self, obj):
        return {
            "name": obj.service.name,
            "slug": obj.service.slug
        }

        


class ReportSerializer(serializers.ModelSerializer):
    service_slug = serializers.ChoiceField(choices=Service.Service.choices, write_only=True)
    description = serializers.CharField(required=False, allow_blank=True)
    
    class Meta:
        model = Report
        fields = ('service_slug', 'description', 'description', 'text', "object_id")
        read_only_fields = ('user',)
        
        
    def create(self, validated_data):
        service_slug = validated_data.pop('service_slug')
        service = get_object_or_404(Service, slug=service_slug)
        user = self.context['request'].user if self.context['request'].user.is_authenticated else None

        report = Report.objects.create(
            user=user,
            service=service,
            **validated_data
        )
        return report
    