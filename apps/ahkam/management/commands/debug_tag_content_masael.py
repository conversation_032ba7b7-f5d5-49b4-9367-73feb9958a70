from django.core.management.base import BaseCommand
from django.db.models import Subquery, OuterRef, Q

from apps.ahkam.models import <PERSON><PERSON><PERSON>, MasaelCategory
from django.db import transaction
import re
from lxml import html


class Command(BaseCommand):
    help = 'Remove <br> tags from <div class="q-header"> in Masael content'

    def handle(self, *args, **kwargs):
        category_id = 503
        updated_count = 0
        category = MasaelCategory.objects.get(id=category_id)
        self.stdout.write(self.style.SUCCESS(f'cate = {category}'))
        categories = category.get_descendants(include_self=True)
        self.stdout.write(self.style.SUCCESS(f'children all = {categories.count()}'))
        masaels = Masael.objects.filter(category__in=categories)
        self.stdout.write(self.style.SUCCESS(f'masaels = {masaels.count()}'))

        with transaction.atomic():
            for masael in masaels:
                original_content = masael.content
                if original_content:
                    self.stdout.write(self.style.SUCCESS(f'Masael = {masael}'))
                    self.stdout.write(f'Original content for Masael ID {masael.id}: {original_content}')

                    updated_content = re.sub(r'(<div class="q-header">[^<]*)<br>', r'\1', original_content)
                    if original_content != updated_content:
                        self.stdout.write(self.style.SUCCESS(f'element = {updated_content}'))
                        masael.content = updated_content
                        masael.save(update_fields=['content'])
                        updated_count += 1
                        self.stdout.write(self.style.SUCCESS(f'Updated Masael ID {masael}'))
                    else:
                        self.stdout.write(self.style.WARNING(f'No changes needed for Masael ID {masael.id}'))
                else:   
                    self.stdout.write(self.style.WARNING(f'Masael ID {masael.id} has no content'))

        self.stdout.write(self.style.SUCCESS(f'Total updated Masaels: {updated_count}'))

                #     tree = html.fromstring(original_content)
                #     updated = False 
                #     self.stdout.write(f'Original content for Masael ID {masael.id}: {original_content}')
                    
                # for element in tree.xpath('//div[@class="q-header"]/br'):
                #     parent = element.getparent()
                #     parent.remove(element)
                #     updated = True
                #     if updated:
                #         updated_content = html.tostring(tree, encoding='unicode')

                #         self.stdout.write(self.style.SUCCESS(f'element = {updated_content}'))

        # self.stdout.write(self.style.SUCCESS(f'children1 = {category.children.all()}'))
        # for chi in category.children.all():
        # masael = Masael.objects.filters(category__in=)
            
            # if chi.ch
            # for h in chi.children.all():
            # self.stdout.write(self.style.SUCCESS(f'children2> = {chi.children}'))
            # for field in chi._meta.get_fields():
                # self.stdout.write(self.style.SUCCESS(f'{field.name}: {field}'))
            
                
                
        # for field in category._meta.get_fields():
            # self.stdout.write(self.style.SUCCESS(f'{field.name}: {field}'))

        # for i in category:
            # self.stdout.write(self.style.SUCCESS(f'catei = {i}'))
             
        # masael = Masael.objects.get(title="Methods To Know Who Is Mujtahid")
        # self.stdout.write(self.style.SUCCESS(f'cmasael = {masael.title}'))
        # self.stdout.write(self.style.SUCCESS(f'e = {masael.content}'))
        
        # with transaction.atomic():
            # original_content = masael.content

            # if original_content:
                # tree = html.fromstring(original_content)
                # updated = False

                # for element in tree.xpath('//div[@class="q-header"]/br'):
                    # parent = element.getparent()
                    # parent.remove(element)
                    # updated = True      
                    # self.stdout.write(self.style.SUCCESS(f'e = {element}'))

                # if updated:
                    # updated_content = html.tostring(tree, encoding='unicode')
                    # masael.content = updated_content
                    # self.stdout.write(self.style.SUCCESS(f'content{masael.content}'))
                    # masael.save(update_fields=['content'])
                    # updated_count += 1
                    # self.stdout.write(self.style.SUCCESS(f'Updated Masael ID {masael.id}'))
            
        # with transaction.atomic():
            # for masael in masaels:
                # original_content = masael.content
# 
                # if original_content:
                    # tree = html.fromstring(original_content)
                    # updated = False
# 
                    # for element in tree.xpath('//div[@class="q-header"]/br'):
                        # parent = element.getparent()
                        # parent.remove(element)
                        # updated = True
# 
                    # if updated:
                        # updated_content = html.tostring(tree, encoding='unicode')
                        # masael.content = updated_content
                        # masael.save(update_fields=['content'])
                        # updated_count += 1
                        # self.stdout.write(self.style.SUCCESS(f'Updated Masael ID {masael.id}'))
                    # else:
                        # self.stdout.write(f'No changes needed for Masael ID {masael.id}')
                # else:
                    # self.stdout.write(f'Masael ID {masael.id} has no content')
# 
        # self.stdout.write(self.style.SUCCESS(f'Total updated Masaels: {updated_count}'))
        # self.stdout.write(self.style.SUCCESS(f'Total updated Masaels: {masael.title}'))
                
