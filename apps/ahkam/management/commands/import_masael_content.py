import os
from django.core.management.base import BaseCommand
from bs4 import BeautifulSoup
import json
from django.db import transaction
from apps.ahkam.models import <PERSON><PERSON><PERSON>, MasaelContent


class Command(BaseCommand):
    help = 'Import and process HTML content for MasaelContent model'



    def handle(self, *args, **kwargs):
        masael_objects = Masael.objects.all()

        self.stdout.write(self.style.WARNING(f"{Masael.objects.all().count()}"))
        
        if not masael_objects.exists():
            self.stdout.write(self.style.WARNING("No Masael objects found with language code 'en'."))
            return
        
        for masael in masael_objects:
            html_content = masael.content

            soup = BeautifulSoup(html_content, 'html.parser')
            
                
            def process_q_text_elements():
            
                # Rule 1: If a q-header is inside a q-text, move it out along with the following text
                texts = [text for text in soup.find_all('p', class_='q-text') if text.get_text(strip=True)]
                for text in texts:
                    for header in text.find_all('div', class_='q-header'):
                        # Extract the header and the following text, insert after the current q-text element
                        next_text = header.next_sibling if header.next_sibling and isinstance(header.next_sibling, str) else ""
                        header.extract()
                        if next_text:
                            new_text = soup.new_tag('p', **{'class': 'q-text'})
                            new_text.string = next_text.strip()
                            text.insert_after(header)
                            header.insert_after(new_text)
                            text.string = text.get_text().replace(next_text, "").strip()
                            # 
                for element in soup.find_all():
                    # print(f'ok--> {element}')
                    if element.name not in ['p', 'div'] and element.string and element.string.strip():
                        new_tag = soup.new_tag('p', **{'class': 'q-text'})
                        new_tag.string = element.string.strip()
                        element.insert_after(new_tag)
                        element.extract()
                # Re-extract all q-text elements after modifications
                texts = [text for text in soup.find_all('p', class_='q-text') if text.get_text(strip=True)]
                return texts

            texts = process_q_text_elements()

            # for element in soup.find_all(string=True):
                # print(f'Element--> {element}')



            # Rule 2: If a text is not wrapped in any tag, wrap consecutive texts in a single q-text tag
            combined_text = []
            current_combined_tag = None
            for element in soup.find_all(string=True):
                if element.parent.name not in ['p', 'div'] and element.strip():
                    combined_text.append(element.strip())
                    if current_combined_tag is None:
                        current_combined_tag = element.parent
                else:
                    if combined_text:
                        new_tag = soup.new_tag('p', **{'class': 'q-text'})
                        new_tag.string = '\n'.join(combined_text)
                        if current_combined_tag and current_combined_tag.parent:
                            parent = current_combined_tag.parent
                            index = list(parent.children).index(current_combined_tag)
                            parent.insert(index, new_tag)
                        else:
                            # print(f'ok-> {new_tag}')
                            # If no parent, insert at the beginning of the document
                            soup.insert(0, new_tag)
                        combined_text = []
                        current_combined_tag = None

            # If there are still combined texts left at the end
            if combined_text and current_combined_tag:
                # print('ok')
                new_tag = soup.new_tag('p', **{'class': 'q-text'})
                new_tag.string = '\n'.join(combined_text)
                # new_tag.string = ' '.join(combined_text)

                if current_combined_tag.parent:
                    parent = current_combined_tag.parent
                    index = list(parent.children).index(current_combined_tag)
                    parent.insert(index, new_tag)
                else:
                    # If no parent, insert at the beginning of the document
                    soup.insert(0, new_tag)
                            
            # Replace all <br> tags with newlines within q-text elements
            for br in soup.find_all('br'):
                br.replace_with('\n')
                
            texts = [text for text in soup.find_all('p', class_='q-text') if text.get_text(strip=True)]
            

            priority_counter = 1
            rulings = []
            
            for text in texts:
                # print(f'->>>>>>>>>>>>>>>>>>>.{text}')
                text_content = text.get_text().strip()
                header_tag = text.find_previous_sibling('div', class_='q-header')
                header_text = header_tag.get_text(strip=True).replace('<br>', '').strip() if header_tag else None
                
                answer_tag = text.find_next_sibling('p', class_='q-answer')
                answer_text = answer_tag.get_text(strip=True).replace('<br>', '').strip() if answer_tag else None
            
                iframe_tag = soup.find('iframe')
                video_src = iframe_tag['src'] if iframe_tag else None
                if video_src:
                    all_text_elements = [el.strip() for el in soup.find_all(string=True) if el.strip()]
                    # text_content = ' '.join(all_text_elements)
                    text_content = '\n'.join(all_text_elements)

                ruling = {
                    'mesael_id': masael.id,
                    'header': header_text,
                    'text': text_content,
                    'answer': answer_text,
                    'video': video_src,
                    'priority': priority_counter
                }
                rulings.append(ruling)
                priority_counter += 1

            with transaction.atomic():
                for ruling in rulings:
                    # Create a new MasaelContent instance and link it to the provided Masael instance
                    MasaelContent.objects.create(
                        masael=masael,
                        header=ruling['header'],
                        text=ruling['text'],
                        answer=f"VIDEO={ruling['video']}" if ruling['video'] and ruling['video'] != "" else ruling['answer'],
                        priority=ruling['priority']
                        )

                self.stdout.write(self.style.SUCCESS(f"Created {masael.id}"))
            

            # file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'contents', f'masael_content_{masael.id}.json')            
            # with open(file_path, 'w', encoding='utf-8') as f:
            #     json.dump(rulings, f, ensure_ascii=False, indent=4)

