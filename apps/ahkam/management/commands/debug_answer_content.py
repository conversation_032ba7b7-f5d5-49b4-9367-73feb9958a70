# In this code, the word answer is debugged for questions and answers in
# English for Sistani, Azeri language for leadership, Russian language for Makarem and 
# leadership, Turkish language for leadership, German language for Sistani,
# and Swahili language for Sistani.

from django.core.management.base import BaseCommand
from django.db.models import Subquery, OuterRef, Q

from apps.ahkam.models import Masa<PERSON>, MasaelCategory
from django.db import transaction
import re
from lxml import html

class Command(BaseCommand):
    help = 'Debug Word Answer Questions For Languages (e.g, Answer -> debug -> A.)'

    def handle(self, *args, **kwargs):
        updated_count = 0
        category_id = 1992
        category_children_id = 1721
        category = MasaelCategory.objects.get(id=category_id)
        self.stdout.write(self.style.SUCCESS(f'cate = {category}'))
        for chi in category.children.all():
            # self.stdout.write(self.style.SUCCESS(f'children = {chi.name}, {chi.id}'))
            if chi.id == category_children_id:
                categories = chi.get_descendants(include_self=True)
                self.stdout.write(self.style.SUCCESS(f'childrens = {categories.count()}'))
                masaels = Masael.objects.filter(category__in=categories, title__icontains="Question")
                
                with transaction.atomic():
                    for masael in masaels:
                        self.stdout.write(self.style.SUCCESS(f'maseal = {masael.id}/{masael.title}'))
                        original_content = masael.content
                        if original_content:
                            self.stdout.write(self.style.SUCCESS(f'Masael = {masael}'))
                            self.stdout.write(f'Original content for Masael ID {masael.id}: {original_content}')
                            updated_content = re.sub(r'(<p class="q-answer">)Answer:', r'\1A:', original_content)
                            if original_content != updated_content:
                                self.stdout.write(self.style.SUCCESS(f'Updated content for Masael ID {masael.id}: {updated_content}'))
                                masael.content = updated_content
                                masael.save(update_fields=['content'])
                                updated_count += 1
                                self.stdout.write(self.style.SUCCESS(f'Updated Masael ID {masael}'))
                            else:
                                self.stdout.write(self.style.WARNING(f'No changes needed for Masael ID {masael.id}'))
                        else:
                            self.stdout.write(self.style.WARNING(f'Masael ID {masael.id} has no content'))
        self.stdout.write(self.style.SUCCESS(f'Total updated Masaels: {updated_count}'))

                
