import re

from rest_framework import serializers
from .models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from dj_category.models import BaseCategory
from dj_filer.admin import get_thumbs
from dj_language.serializer import LanguageSerializer


class MasaelSerializer(serializers.ModelSerializer):
    category = serializers.StringRelatedField(read_only=True)
    root_id = serializers.SerializerMethodField()

    def get_root_id(self, obj):
        if hasattr(obj, 'root_id'):
            return obj.root_id

        return obj.category.get_root().id

    class Meta:
        model = Masael
        fields = ('id', 'title', 'content', 'category', 'root_id')


class MasaelCategoriesSerializer(serializers.ModelSerializer):
    thumbnail = serializers.SerializerMethodField('get_thumbnail_object')
    masael_index = serializers.SerializerMethodField('get_masael_index')
    children = serializers.SerializerMethodField('get_children')
    language = LanguageSerializer()
    name = serializers.SerializerMethod<PERSON>ield()

    def get_name(self, obj):
        return re.sub("\d+", "", obj.name)

    def get_masael_index(self, obj: BaseCategory):
        # cannot access tot obj.cat_masaels bcz of mptt module limitation
        # for proxy models in mptt.managers.TreeManager line 54
        # so we directly query to Masael model
        return list(
            Masael.objects.filter(category__id__in=[obj.id]).values_list('id', flat=True)
        )
        # return list(obj.cat_masaels.values_list('id', flat=True))

    def get_thumbnail_object(self, obj: BaseCategory):
        try:
            request = self.context.get('request')  
            return get_thumbs(obj.thumbnail, request)
        except Exception:
            return {}
    
    def get_children(self, obj: BaseCategory):
        return [self.to_dict(cat) for cat in obj.get_children()]

    def to_dict(self, c):
        children = c.get_children()
        return {
            'id': c.id,
            'name': re.sub(r"\d+", "", c.name),
            'thumbnails': self.get_thumbnail_object(c),
            'children': [] if not children else [self.to_dict(i) for i in children],
            'masael_index': self.get_masael_index(c),
        }

    class Meta:
        model = BaseCategory
        fields = ('id', 'name', 'thumbnail', 'masael_index', 'language', 'children')



class MasaelContentSerializer(serializers.ModelSerializer):
    class Meta:
        model = MasaelContent
        fields = ('id', 'header', 'text', 'answer', 'priority')
        
class MasaelSerializerV2(serializers.ModelSerializer):
    contents = MasaelContentSerializer(many=True, read_only=True)
    
    class Meta:
        model = Masael
        fields = ('id', 'priority', 'category', 'title', 'contents')
        
        
        
        
        
class MarjaaGuideSerializer(serializers.ModelSerializer):
    language = LanguageSerializer()

    class Meta:
        model = MarjaaGuide
        fields = ['title', 'language', 'answer', 'priority']  
        
        
        