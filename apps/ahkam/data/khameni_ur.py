import json
import os
import requests
from bs4 import BeautifulSoup
import re

headers = {
    'User-Agent': 'Mozilla/5.0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
}


class Crawl:
    base_url = 'https://www.leader.ir/ur/book/106/%D8%A7%D8%B3%D8%AA%D9%81%D8%AA%D8%A7%D8%A2%D8%AA-%DA%A9%DB%92-%D8%AC%D9%88%D8%A7%D8%A8%D8%A7%D8%AA'
    language_id = 57
    category_name = "Ayatollah Khamenei Urdu"
    filename = "khamenei_ur.json"

    def parse(self, link: str):
        print('DOWNLOADING: ', link)
        html = requests.get(link).text
        return BeautifulSoup(html, "html.parser")

    def parse_ajax(self, _id):
        print('DOWNLODING:', _id)
        return requests.post(
            'https://www.leader.ir/ajax/book',
            data={'parent': str(_id), 'catid': '42'},
            headers=headers
        ).json()

    def parse_ahkam(self, content, category):
        data = []
        try:
            extra_data = []
            for item in content.select('.matn'):
                if atag := item.select_one('a'):
                    if "ftnref" in atag.get('href'):
                        extra_data.append(item.get_text(strip=True))
                        os.system(f"echo '{item.get_text(strip=True)}' >> ignored_atag.log")
                        continue
                if smalltag := item.select_one('small'):
                    extra_data.append(item.get_text(strip=True))
                    os.system(f"echo '{item.get_text(strip=True)}' >> ignored_smalltag.log")
                    continue

                if que := item.get_text(strip=True):
                    if ans := item.find_all_next('h5')[0].get_text(strip=True):
                        if _que := re.search(r'(س?\s*\d+\s*.|:)([\s\w\W\S]+)', que):
                            qt, qn = _que[2], _que[1]
                        else:
                            data[-1]['que'] += "<br>" + que
                            print('que')
                        if regans := re.sub(r'^ج\s*:', '', ans):
                            _ans = regans
                        else:
                            print('xx')

                        data.append({'num': qn.strip(), 'que': qt.strip(), 'ans': _ans.strip()})
                    else:
                        if _que := re.search(r'(س?\s*\d+\s*.|:)([\s\w\W\S]+)', que):
                            data.append({
                                'num': _que[1],
                                'que': _que[2],
                                'ans': '',
                            })

            if extra_data:
                data.append({'subtitle': extra_data})

            return data


        except Exception as e:
            print()

    def crawl(self):
        page = self.parse(self.base_url)
        result = {}

        for category in page.select('ul.level0 > li'):
            category_name = category.select_one('span').get_text(strip=True)
            result[category_name] = {}

            if category.select_one('i.doc'):
                result[category_name]['type'] = 'single'
                result[category_name]['contents'] = self.parse_ahkam(category, category_name)

            else:
                _id = category.select_one('a').get('href').split('=')[1]
                ajax_data = self.parse_ajax(_id)
                root, sections, tchain = ajax_data
                result[category_name]['type'] = 'many'
                result[category_name]['contents'] = []

                if root['root']:
                    result[category_name]['contents'].append({
                        'title': category_name,
                        'data': self.parse_ahkam(BeautifulSoup(root['root'], "html.parser"), category_name)
                    })

                for hokm in sections['data']:
                    result[category_name]['contents'].append({
                        'title': hokm['title'],
                        'data': self.parse_ahkam(BeautifulSoup(hokm['body'], "html.parser"), category_name)
                    })

        with open(self.filename, "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def create_category(self, name):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type, language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=MasaelCategory.objects.get(name=self.category_name)
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, data):
        content_str = ""

        for item in data:
            if que := item.get('que'):
                q = re.search(r"([\d]+)", item['num'])
                q = q.groups()
                question = f'<div class="q-header">سوال {q[0].strip()}</div><p class="q-text">{item["que"]}</p>'
                answer = f'<p class="q-answer">{item["ans"].strip()}</p>'
                content_str += question + answer

            elif item.get('subtitle'):
                content_str += "<br>".join(item['subtitle'])

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory

        with open(self.filename) as f:
            ahkam = json.load(f)

        for category, values in ahkam.items():
            if values['type'] == 'many':
                cat = self.create_category(category)
                for content in values['contents']:
                    Masael.objects.create(
                        title=content['title'],
                        category=cat,
                        content=self.compact_questions(content['data']),
                        priority=self.get_priority(),
                    )
            else:
                Masael.objects.create(
                    title=category,
                    category=MasaelCategory.objects.get(name=self.category_name),
                    content=self.compact_questions(values['contents']),
                    priority=self.get_priority(),
                )

    def get_priority(self):
        from apps.ahkam.models import Masael
        try:
            return Masael.objects.filter(category__language__id=6).last().priority + 1
        except Exception:
            return 1

    def load_django(self):
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
        from django.core.wsgi import get_wsgi_application
        application = get_wsgi_application()
        return application


Crawl().load_django()
# بعد از کرال سوالات
# سوال 1703 باید در فایل json بضورت دستی درست شود
Crawl().import_to_database()
