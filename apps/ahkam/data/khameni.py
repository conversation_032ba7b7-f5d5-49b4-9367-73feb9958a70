import json
import os
import requests
from bs4 import BeautifulSoup
from django.contrib.contenttypes.models import ContentType

headers = {
    'User-Agent': 'Mozilla/5.0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
}


class Crawl:
    def parse(self, link: str):
        print('DOWNLOADING: ', link)
        html = requests.get(link).text
        return BeautifulSoup(html, "html.parser")

    def parse_ajax(self, _id):
        print('DOWNLODING:', _id)
        return requests.post(
            'https://www.leader.ir/ajax/book',
            data={'parent': str(_id), 'catid': '170'},
            headers=headers
        ).json()

    def parse_ahkam(self, content, category):
        data = []
        try:
            extra_data = []
            for item in content.select('.matn'):
                if atag := item.select_one('a'):
                    if "ftnref" in atag.get('href'):
                        extra_data.append(item.get_text(strip=True))
                        os.system(f"echo '{item.get_text(strip=True)}' >> ignored_atag.log")
                        continue
                if smalltag := item.select_one('small'):
                    extra_data.append(item.get_text(strip=True))
                    os.system(f"echo '{item.get_text(strip=True)}' >> ignored_smalltag.log")
                    continue

                if que := item.get_text(strip=True):
                    if ans := item.find_all_next('h5')[0].get_text(strip=True):
                        data.append({'que': que, 'ans': ans})
                    else:
                        os.system(f"echo '{item.get_text(strip=True)}' >> wed.log")

            if extra_data:
                data.append({'subtitle': extra_data})

            return data


        except Exception as e:
            print()

    def crawl(self):
        page = self.parse("https://www.leader.ir/az/book/132/%C5%9E%C6%8FR%C4%B0-SUALLARA-CAVABLAR")
        result = {}

        for category in page.select('ul.level0 > li'):
            category_name = category.select_one('span').get_text(strip=True)
            result[category_name] = {}

            if category.select_one('i.doc'):
                result[category_name]['type'] = 'single'
                result[category_name]['contents'] = self.parse_ahkam(category, category_name)

            else:
                _id = category.select_one('a').get('href').split('=')[1]
                ajax_data = self.parse_ajax(_id)
                root, sections, tchain = ajax_data
                result[category_name]['type'] = 'many'
                result[category_name]['contents'] = []

                if root['root']:
                    result[category_name]['contents'].append({
                        'title': category_name,
                        'data': self.parse_ahkam(BeautifulSoup(root['root'], "html.parser"), category_name)
                    })

                for hokm in sections['data']:
                    result[category_name]['contents'].append({
                        'title': hokm['title'],
                        'data': self.parse_ahkam(BeautifulSoup(hokm['body'], "html.parser"), category_name)
                    })

        with open("ahkam.json", "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def create_category(self, name):
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type, language_id=18).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=18,
                    name=name,
                    content_type=content_type,
                    parent=MasaelCategory.objects.get(name="Ayətullah Xamenei")
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, data):
        content_str = ""

        for item in data:
            if item.get('que'):
                if len(item['que'].split(":", 1)) != 2:
                    os.system(f"echo '{item['que']}' >> errors.log")
                else:
                    qnum, qtext = item['que'].split(":", 1)
                    question = f'<div class="q-header">{qnum}</div><p class="q-text">{qtext}</p>'
                    answer = f'<p class="q-answer">{item["ans"]}</p>'
                    content_str += question + answer

            elif item.get('subtitle'):
                content_str += "<br>".join(item['subtitle'])

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory

        with open("ahkam.json") as f:
            ahkam = json.load(f)

        for category, values in ahkam.items():
            if values['type'] == 'many':
                cat = self.create_category(category)
                for content in values['contents']:
                    Masael.objects.create(
                        title=content['title'],
                        category=cat,
                        content=self.compact_questions(content['data'])
                    )
            else:
                Masael.objects.create(
                    title=category,
                    category=MasaelCategory.objects.get(name="Ayətullah Xamenei"),
                    content=self.compact_questions(values['contents'])
                )


Crawl().import_to_database()
