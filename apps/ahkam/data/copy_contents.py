import os

import json
import time
from django.db import models
from django.db.models import Q
import argparse
from utils.telegram_logger import telegram_logger

# اضافه کردن مسیر پروژه به sys.path

# تنظیم متغیر محیطی برای تنظیمات Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
import anthropic
# مطمئن شوید که مدل‌های لازم را import کرده‌اید
from apps.ahkam.models import Masael, MasaelCategory, MasaelContent
from dj_language.models import Language  # مطمئن شوید که مدل Language را ایمپورت کرده‌اید

parser = argparse.ArgumentParser(description='Translation Script for Masael Categories')
parser.add_argument('--lang_code', type=str, help='Target language code', required=True)
args = parser.parse_args()

target_language_code = args.lang_code

root_categories = [4940, 2121]

# دیکشنری برای نگهداری معادل دسته‌بندی‌های انگلیسی و ازبکی
category_translation_map = {}

telegram_logger(f"Starting the copy process for Masael categories and content to {target_language_code}...")
def copy_and_get_or_create_category(category):
    if category.id in category_translation_map:
        return category_translation_map[category.id]

    target_language_instance = Language.objects.get(code=target_language_code)  # بازیابی نمونه زبان جدید

    # بررسی وجود دسته‌بندی معادل
    copied_category, created = MasaelCategory.objects.get_or_create(
        slug=category.slug + '-' + target_language_code,
        defaults={
            'name': category.name,  # کپی نام دسته‌بندی بدون تغییر
            'language': target_language_instance,  # تنظیم زبان جدید
            'thumbnail': category.thumbnail,  # استفاده از همان تصویر بندانگشتی
            'content_type': category.content_type,  # نوع محتوا را حفظ کنید
            'is_active': category.is_active,
            'created_at': category.created_at
        }
    )

    if category.parent:
        copied_parent = copy_and_get_or_create_category(category.parent)
        copied_category.parent = copied_parent  # تنظیم والد برای دسته‌بندی
        copied_category.save()

    category_translation_map[category.id] = copied_category

    return copied_category

def copy_and_create_masael_content(masael):
    target_language_instance = Language.objects.get(code=target_language_code)

    copied_masael, created = Masael.objects.get_or_create(
        title=masael.title,
        category=copy_and_get_or_create_category(masael.category),
        priority=masael.priority,
        translate_from= masael.translate_from,
        defaults={
            'content': '',
            'created_at': masael.created_at,
            'updated_at': masael.updated_at,
        }
    )

    for content in masael.contents.all():
        MasaelContent.objects.create(
            masael=copied_masael,
            header=content.header,  # کپی هدر بدون تغییر
            text=content.text,  # کپی متن بدون تغییر
            answer=content.answer,  # کپی پاسخ بدون تغییر
            priority=content.priority,
            translate_from=content.translate_from
        )

def get_all_subcategories(root_categories):
    all_categories = []  # این را به لیست خالی تغییر دهید

    # بازیابی دسته‌بندی‌ها با استفاده از queryset
    all_category_objects = MasaelCategory.objects

    def collect_subcategories(categories):
        subcategories = []
        for category in categories:
            subcategories.append(category)
            # یافتن دسته‌بندی‌های زیرمجموعه
            child_categories = all_category_objects.filter(parent=category)
            subcategories.extend(collect_subcategories(child_categories))
        return subcategories

    # آغاز بازگشتی از ریشه‌ها
    root_categories_objects = all_category_objects.filter(id__in=root_categories)
    all_categories.extend(collect_subcategories(root_categories_objects))  # افزودن زیرمجموعه‌ها به لیست
    return all_categories

all_subcategories = get_all_subcategories(root_categories)
# کپی دسته‌بندی‌ها و محتوا و ذخیره در دیکشنری
for category in all_subcategories:
    copy_and_get_or_create_category(category)  # استفاده از تابع کپی به جای ترجمه

masaels = Masael.objects.filter(category__in=all_subcategories)
for masael in masaels:
    copy_and_create_masael_content(masael)  # استفاده از تابع کپی برای محتوا
# ذخیره نقشه دسته‌بندی‌ها در فایل JSON
with open(f'category_copy_map_{target_language_code}.json', 'w', encoding='utf-8') as f:
    json.dump({str(k): v.id for k, v in category_translation_map.items()}, f, ensure_ascii=False, indent=4)

tg_message = f"Copying process completed for Masael categories and content to {target_language_code}."
telegram_logger(tg_message)
print(tg_message)


