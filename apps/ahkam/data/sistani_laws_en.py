import json
import os
import re

import requests
from bs4 import BeautifulSoup

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()


class Crawl:
    language_id = 6
    category_name = "Ayatullah Sistani Laws"
    base_url = "https://www.sistani.org/english/book/48/"
    filename = "sistani_laws_en.json"
    last_priority = 0

    def parse(self, link: str):
        print(link)
        html = requests.get(link).text
        return BeautifulSoup(html, "html.parser")

    def crawl(self):
        page = self.parse(self.base_url)
        result = {}

        for a in page.select('.baz a'):
            sub_page = self.parse(f"https://www.sistani.org{a.get('href')}")
            result[a.text] = sub_page.select_one('.book-text').text

        with open(self.filename, "w") as f:
            json.dump(result, f, ensure_ascii=False, indent=4)

    def create_category(self, name):
        from django.contrib.contenttypes.models import ContentType
        from apps.ahkam.models import MasaelCategory

        try:
            parent = MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id)
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent,
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory

        data = json.load(open(self.filename))

        for _title, content in data.items():
            if len(_title.split('»')) == 1:
                cat = self.create_category(self.category_name)
                title = _title
            else:
                _cat, title = _title.split('»')
                cat = self.create_category(_cat.strip().title())

            if items := re.findall(r'(ruling\s*\d+\.((?!ruling\s*\d+\.\s*)[\s\S])*)', content, re.I | re.S):
                _content = ""
                for i, j in items:
                    if xx := re.search(r'(Ruling\s*\d+)\.([\w\S\s]+)', i, re.S):
                        q, t = xx.groups()
                        _content += f'<div class="q-header">{q}</div><p class="q-text">{t.strip()}</p>'

                _content = _content.replace('\n', '<br>')
                Masael.objects.create(
                    title=title.strip().title(), priority=self.get_priority(), content=_content, category=cat,
                )
            else:
                _c = content.replace("\n", "<br>")

                Masael.objects.create(
                    title=title, priority=self.get_priority(), content=f'<p class="q-text">{_c}</p>', category=cat,
                )

    def get_priority(self):
        self.last_priority += 1
        return self.last_priority


Crawl().import_to_database()
