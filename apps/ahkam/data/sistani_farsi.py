import json
import os
import re
import time

import requests
from bs4 import BeautifulSoup

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()


class Crawl:
    language_id = 69
    category_name = "توضیح المسائل جامع آیت الله سیستانی"
    base_url = "https://www.sistani.org/persian/book/26578/"
    filename = "sistani_farsi_4.json"
    last_priority = 0

    def strips(self, t):
        return re.sub(" +", ' ', t).strip() or None

    def parse(self, link: str):
        time.sleep(0.5)
        print(link)
        html = requests.get(link).text
        return BeautifulSoup(html, "html.parser")

    def crawl(self):
        page = self.parse(self.base_url)
        result = {}

        for a in page.select('.baz a'):
            sub_page = self.parse(f"https://www.sistani.org{a.get('href')}")
            result[a.text] = self.normalize_content(sub_page.select_one('.book-text').text,
                                                    sub_page.select_one('.book-footnote'))

            with open(self.filename, "w") as f:
                json.dump(result, f, ensure_ascii=False, indent=4)

    def create_category(self, name):
        from django.contrib.contenttypes.models import ContentType
        from apps.ahkam.models import MasaelCategory

        try:
            parent = MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id)
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type, parent=parent,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent,
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def normalize_content(self, content, footnote):
        _content = ""
        parts = re.split(r"(مسأله \d+[\w\S]+\.)", content, re.I | re.M)
        for i in parts:
            if "مسأله" in i:
                _content += f'<div class="q-header">{i}</div>'
            else:
                _content += f'<p class="q-text">{i}</p>'

        if footnote and footnote.text:
            _content += f"<hr>{footnote.text}"

        return _content.replace('\n', '<br>')

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory
        data = json.load(open(self.filename))
        cat = self.create_category('توضیح المسائل جامع جلد (4)', )

        for i, j in data.items():
            print(i)
            Masael.objects.create(
                title=i, priority=self.get_priority(), content=j, category=cat,
            )

    def get_priority(self):
        self.last_priority += 1
        return self.last_priority


Crawl().import_to_database()
