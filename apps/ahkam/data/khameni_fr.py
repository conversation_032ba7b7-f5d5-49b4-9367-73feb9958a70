import json

import bs4.element
from bs4 import BeautifulSoup
import re


class Crawl:
    language_id = 23
    category_name = "Ayatollah KH FR"
    base_url = "khamenei_fr.html"
    filename = "khamenei_fr.json"

    def strips(self, t):
        return re.sub(" +", ' ', t).strip()

    def parse(self, html: str):
        return BeautifulSoup(html, "html.parser")

    def parse_block(self, html):
        blocks = []
        p = self.parse(html)
        for a in p.select('b'):
            d = {
                'num': a.get_text(strip=True).replace(' :', ':'),
                'block': [],
            }

            for elem in a.next_siblings:
                if isinstance(elem, bs4.element.NavigableString) and elem.name == "b":
                    break
                if isinstance(elem, bs4.element.Tag) and (elem.find('b') or elem.name == "b"):
                    break

                d['block'].append(elem)

            blocks.append(d)

        for bl in blocks:
            for i, j in enumerate(bl['block']):
                if isinstance(j, bs4.element.Tag):
                    if j.find('i') or j.name == "i":
                        bl['que'] = " ".join([xq.get_text() for xq in bl['block'][:i]]).strip()
                        ans = ""

                        for xq in bl['block'][i:]:
                            if xq.name in ['p', 'ul', 'li', 'br']:
                                ans += ('<br> ' + xq.get_text(strip=True))
                            else:
                                ans += (' ' + xq.get_text(strip=True))

                        ans_split: list = ans.split(':', 1)
                        if len(ans_split) > 1:
                            ans = ans_split[1]
                        else:
                            print(ans_split)

                        bl['ans'] = self.strips(ans)

                        continue

        for block in blocks:
            if not block.get('que'):
                content = re.search(rf'{block["num"].replace(":", "")}\s?:?(.*?)<b>', p.encode_contents().decode(),
                                    re.DOTALL)
                if not content:
                    content = re.search(rf'{block["num"].replace(":", "")}\s?:?(.+)', p.encode_contents().decode(),
                                        re.DOTALL)

                contents = list(self.parse(content.groups()[0]))
                xxxx = {}
                if not contents:
                    print(contents)

                for i, j in enumerate(contents):
                    if isinstance(j, bs4.element.Tag):
                        if j.find('i') or j.name == "i":
                            xxxx['que'] = " ".join([xq.get_text() for xq in contents[:i]]).strip()
                            ans = ""

                            for xq in contents[i:]:
                                if xq.name in ['p', 'ul', 'li', 'br']:
                                    ans += ('<br> ' + xq.get_text(strip=True))
                                else:
                                    ans += (' ' + xq.get_text(strip=True))

                            ans_split: list = ans.split(':', 1)
                            if len(ans_split) > 1:
                                ans = ans_split[1]
                            else:
                                print(ans_split)

                            xxxx['ans'] = self.strips(ans)

                            continue

                if not xxxx.get('que'):
                    print(xxxx)
                    del block['block']

                    continue

                block['que'] = xxxx['que']
                block['ans'] = xxxx['ans']

            del block['block']

        return blocks

    def crawl(self):
        page_html = open(self.base_url, 'r').read()
        page = self.parse(page_html)
        sections = []

        for title in page.select('h1'):
            block_html = ""
            for i in title.next_siblings:
                if i.name == "h1":
                    break
                if i.get_text(strip=True):
                    x = i.encode_contents().decode()
                    block_html += "<br>" + x

            sections.append({
                'title': title.get_text(strip=True),
                'contents': self.parse_block(block_html)
            })

        with open(self.filename, "w") as f:
            json.dump(sections, f, ensure_ascii=False, indent=4)

    def compact_questions(self, data):
        content_str = ""
        for item in data:
            if not item.get('que'):
                self.log(item['num'])
                continue

            html = f'<div class="q-header">{item["num"]}</div><p class="q-text">{item["que"]}</p><p class="q-answer">{item["ans"]}</p>'
            content_str += html

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory
        # MasaelCategory.objects.get(name=self.category_name).delete()

        with open(self.filename) as f:
            ahkams = json.load(f)

        for hokm in ahkams:
            Masael.objects.create(
                category=MasaelCategory.objects.get(name=self.category_name),
                priority=self.get_priority(),
                title=hokm['title'],
                content=self.compact_questions(hokm['contents'])
            )

    def get_priority(self):
        from apps.ahkam.models import Masael, MasaelCategory
        try:
            return Masael.objects.filter(category__name=self.category_name).last().priority + 1
        except Exception as e:
            print(e)
            return 1

    def log(self, text):
        with open(self.filename + '.log', 'a+') as f:
            f.write(str(text))
            f.write('\n')


Crawl().import_to_database()
