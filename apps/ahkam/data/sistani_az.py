import json
from bs4 import BeautifulSoup
import re


class Crawl:
    language_id = 18
    category_name = '<PERSON><PERSON><PERSON><PERSON>'
    base_url = "sistani_az.html"
    filename = "ahkam_sis_az.json"

    def parse(self, html: str):
        return BeautifulSoup(html, "html.parser")

    def parse_ahkam(self, content, category_name: str):
        data = []
        if len(content.select('h3')):
            titles = [title.text for title in content.select('h3')]

            sections = {
                'type': 'many',
                'category': category_name,
                'data': []
            }
            for i, title in enumerate(titles):
                wrapper = '<h3 class="content_h3">%s</h3>'
                if i == (len(titles) - 1):  # the last iteration
                    next_tag = re.escape('<span class="chapter"></span>')
                else:
                    next_tag = wrapper % re.escape(titles[i + 1])

                reg = re.search(fr'{wrapper % re.escape(title)}([\w\W]+){next_tag}', str(content))

                pre_doc = ""
                if itag := self.parse(reg.groups()[0]).find('i'):
                    if parent_itag := itag.find_parent(attrs={'class': 'content_paragraph'}):
                        if prev_sibs := parent_itag.find_previous_siblings():
                            pre_doc = "<br>".join([x.text for x in prev_sibs[::-1]])
                            pre_doc = pre_doc.replace("\n", '')

                else:
                    print(reg)

                sections['data'].append(
                    {'pre_doc': pre_doc, 'title': title, 'contents': self.extract_masael(self.parse(reg.groups()[0]))}
                )

            data.append(sections)

        else:
            data.append({
                'name': category_name,
                'type': 'single',
                'data': self.extract_masael(content)
            })

        return data

    def extract_masael(self, content):
        d = []
        masaels = [i.text for i in content.select('i')]
        for i, masael in enumerate(masaels):
            if i == (len(masaels) - 1):  # the last iteration
                next_tag = re.escape('</')
            else:
                next_tag = f'<i>{re.escape(masaels[i + 1])}</i>'

            cur = re.escape(masael)
            reg = re.search(fr"<i>{cur}</i>([\w\W]+){next_tag}", str(content))
            if not reg:
                print(reg)

            d.append({
                "que": masael,
                "ans": self.parse(reg.groups()[0]).text.strip().replace('\n', '<br>').replace('<br><br>', '<br>')
            })

        return d

    def crawl(self):
        page_html = open("sistani_az.html", 'r').read()
        page = self.parse(page_html)
        result = []
        sections = []
        titles = [title.text for title in page.select('h2')]

        # putting each category in box
        for i, title in enumerate(titles):
            if i == (len(titles) - 1):  # the last iteration
                next_tag = re.escape("</BODY>")
            else:
                next_tag = f'<H2 class=content_h2>{re.escape(titles[i + 1])}</H2>'

            cur = re.escape(title)
            reg = re.search(fr"<H2 class=content_h2>{cur}</H2>([\w\W]+){next_tag}", page_html)

            sections.append({
                "title": title,
                "html": reg.groups()[0]
            })

        # parse each box and getting masaels
        data = []
        for section in sections:
            data.append(
                self.parse_ahkam(self.parse(section['html']), section['title'])
            )

        with open(self.filename, "w") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

    def create_category(self, name):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id)
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, data):
        content_str = ""
        for item in data:
            question = f'<div class="q-header">{item["que"]}</div><p class="q-text">{item["ans"].strip()}</p>'
            content_str += question

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory

        with open(self.filename) as f:
            ahkams = json.load(f)

        for ahkam in ahkams:
            ahkam = ahkam[0]
            if ahkam['type'] == 'many':
                cat = self.create_category(ahkam['category'])
                for doc in ahkam['data']:
                    content = ""
                    if doc["pre_doc"]:
                        content = f'<p class="q-text">{doc["pre_doc"]}</p>'

                    content += self.compact_questions(doc['contents'])

                    Masael.objects.create(
                        title=doc['title'],
                        category=cat,
                        content=content,
                        priority=self.get_priority(),
                    )
            else:
                Masael.objects.create(
                    title=ahkam["name"],
                    category=MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id),
                    content=self.compact_questions(ahkam['data']),
                    priority=self.get_priority(),
                )

    def get_priority(self):
        from apps.ahkam.models import Masael, MasaelCategory
        kids = MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id).get_descendants()
        try:
            return Masael.objects.filter(category__in=kids).last().priority + 1
        except Exception:
            return 1

    def log(self, text):
        with open(self.filename + '.log', 'a+') as f:
            f.write(str(text))
            f.write('\n')

    def check(self):
        with open(self.filename) as f:
            ahkams = json.load(f)

        for ahkam in ahkams:
            ahkam = ahkam[0]
            if ahkam['type'] == 'many':
                for doc in ahkam['data']:
                    if doc["pre_doc"] and len(doc["pre_doc"]) > 4:
                        self.log(doc["title"])


Crawl().check()
