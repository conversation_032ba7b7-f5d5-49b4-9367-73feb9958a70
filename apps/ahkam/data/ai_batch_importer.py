import os

import json
import re
import time
from django.db import models
from django.db.models import Q
import argparse
from utils.telegram_logger import telegram_logger

# اضافه کردن مسیر پروژه به sys.path

# تنظیم متغیر محیطی برای تنظیمات Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
import anthropic
# مطمئن شوید که مدل‌های لازم را import کرده‌اید
from apps.ahkam.models import Masael, MasaelCategory, Ma<PERSON><PERSON>Content
from dj_language.models import Language  # مطمئن شوید که مدل Language را ایمپورت کرده‌اید

parser = argparse.ArgumentParser(description='Translation Script for Masael Categories')
parser.add_argument('--lang_code', type=str, help='Target language code', required=True)
parser.add_argument('--lang_name', type=str, help='Target language name', required=True)
parser.add_argument('--action', type=str, help='Batch Translation Manager', required=True)
parser.add_argument('--batch_id', type=str, help='Batch Translation Manager')

args = parser.parse_args()

target_language_code = args.lang_code
target_language_name = args.lang_name
if target_language_code == "ul":
    target_language_name = "Urdu Roman"

action = args.action
batch_id = args.batch_id

# دیکشنری برای نگهداری معادل دسته‌بندی‌های انگلیسی و ازبکی
category_translation_map = {}

telegram_logger(f"Starting the translation process for Masael categories and content to {target_language_name}...")

client = anthropic.Anthropic(api_key="************************************************************************************************************")

def prepare_translation_requests(text_items):
    requests = []
    for idx, (text, context, item_id) in enumerate(text_items):
        # Context: Description
        prompt = ""
        if context == "masael_content":
            roman_urdu_instructions = "Use simple and clear vocabulary appropriate for Roman Urdu readers.\nBe fluent and natural, not just a word-for-word rendering.\nInclude brief clarifying phrases if needed to convey the full message — but avoid long commentary." if target_language_code == "ul" else ""
            prompt = f"""
Your task is to translate a Shiite fiqhi text extracted from one of the prominent religious scholars into {target_language_name}. This text contains specialized fiqhi terms. Please follow these instructions carefully:

The translation must be natural and fluent, ensuring that the reader in {target_language_name} feels as if the text was originally written in their language.
The translation must be accurate, preserving the meaning and intent of every part of the original text.
If a term has no direct equivalent in {target_language_name}, transliterate the original term using the {target_language_name} alphabet, ensuring that the transliteration matches the original fiqhi style and context.
Maintain the style, tone, and technical consistency of the original text throughout the entire translation.
For languages with specific writing systems (e.g., Arabic, Urdu), ensure the text is properly formatted and adheres to the conventions of the target language.
{roman_urdu_instructions}
Output Format: Your translation should strictly follow the JSON structure below:
{{
            "header": "",
  "masale": "",
  "answer": ""
}}
Note: Avoid adding any extra words, explanations, or commentary beyond the translation itself. Ensure all terminology and stylistic elements remain consistent.
        """

        # Context: Title
        elif context == "masael_title":
            roman_urdu_instructions = "Use simple and clear vocabulary appropriate for Roman Urdu readers.\nBe fluent and natural, not just a word-for-word rendering.\nInclude brief clarifying phrases if needed to convey the full message — but avoid long commentary." if target_language_code == "ul" else ""
            prompt = f"""
        You will receive the title of a Shiite fiqhi text . Your task is to translate the title into {target_language_name}. Please ensure that:
The translation is natural and fluent, making it sound like it was originally written in {target_language_name}.
The translation is accurate, preserving the exact meaning and intent of the original title.
If the title contains specialized fiqhi terms with no direct equivalent in {target_language_name}, transliterate the terms using the {target_language_name} alphabet.
The style and tone of the original title are retained in the translation.
{roman_urdu_instructions}
important Note: Please provide ONLY the translation without any extra text.
        """

        # Context: Category Name
        elif context == "category_name":
            roman_urdu_instructions = "Use simple and clear vocabulary appropriate for Roman Urdu readers.\nBe fluent and natural, not just a word-for-word rendering.\nInclude brief clarifying phrases if needed to convey the full message — but avoid long commentary." if target_language_code == "ul" else ""
            prompt = f"""
        You will receive the category name of a Shiite fiqhi text . Your task is to translate the category name into {target_language_name}. Please ensure that:
The translation is natural and fluent, making it sound like it was originally written in {target_language_name}.
The translation is accurate, preserving the exact meaning and intent of the original category name.
If the category name contains specialized fiqhi terms with no direct equivalent in {target_language_name}, transliterate the terms using the {target_language_name} alphabet.
The style and tone of the original category name are retained in the translation.
{roman_urdu_instructions}
important Note: Please provide ONLY the translation without any extra text.
        """

        if target_language_code == "uz":
            prompt += f"\nPlease ensure that the entire translation is rendered using Cyrillic characters."
        else:
            prompt += f'\nuse the standard alphabet associated with the {target_language_name}'

        if context == "masael_content":
            prompt += (f"\n header: {text.header}" if text.header else "")
            prompt += (f"\n masale: {text.text}" if text.text else "")
            prompt += (f"\n answer: {text.answer}" if text.answer else "")
        else:
            prompt += f"\noriginal text:\n{text}"


        requests.append({
            "custom_id": f"{context}__{item_id}",
            "params": {
                "model": "claude-3-7-sonnet-latest",
                "max_tokens": 2048,
                "messages": [{"role": "user", "content": prompt}]
            }
        })
    return requests


def send_translation_requests():
    categories = MasaelCategory.objects.filter(language__code=target_language_code)  # زبان انگلیسی با شناسه 1
    text_items = []
    for category in categories:
        if category.name:  # برای عنوان
            text_items.append((category.name, "category_name", category.id))

    masaels = Masael.objects.filter(category__in=categories).all()

    for masael in masaels:
        if masael.title:  # برای عنوان
            text_items.append((masael.title, "masael_title", masael.id))

        masael_contents = masael.contents.all()
        for content in masael_contents:
            if content:  # برای سرفصل
                text_items.append((content, "masael_content", content.id))

    requests = prepare_translation_requests(text_items)

    with open(f'masael_translation_map_{target_language_code}.json', 'w', encoding='utf-8') as f:
        json.dump(requests, f, ensure_ascii=False, indent=4)

    batch = client.beta.messages.batches.create(requests=requests)
    telegram_logger(f"Batch translation requests sent. Batch detail: {batch}")
    print(f"Batch translation requests sent. Batch ID: {batch.id}")


def extract_json_from_text(text):
    # جستجوی اولین JSON در متن
    match = re.search(r'\{.*?\}', text, re.DOTALL)
    if match:
        json_text = match.group()  # گرفتن محتوای JSON

        # تلاش برای تبدیل JSON به دیکشنری
        parsed_json = json.loads(json_text)
        return parsed_json
    else:
        print("هیچ JSON معتبری یافت نشد.")
        return None


def json_export(text):
    prompt = "I will provide you with a text containing some JSON data. Your task is to process the input and return only a standardized JSON response. Do not include any additional text, explanations, or comments—just return the JSON."

    client = anthropic.Anthropic(
        api_key="************************************************************************************************************",
    )
    message = client.messages.create(
        model="claude-3-5-sonnet-20240620",
        max_tokens=1024,
        messages=[
            {"role": "user", "content": f"{prompt} \n {text}"}
        ]
    )
    json = message.content[0].text
    json = extract_json_from_text(json)
    time.sleep(0.3)
    return json
def check_and_update_translations():
    results = client.beta.messages.batches.results(batch_id)
    for result in results:
        custom_id = result.custom_id
        translation = result.result.message.content[0].text if result.result.type == "succeeded" else None

        if not translation:
            continue

        # استخراج context و item_id
        context, item_id = custom_id.split("__")
        item_id = int(item_id)
        try:
            # جایگزینی محتوا
            if context == "masael_title":
                masael = Masael.objects.get(id=item_id)
                masael.title = translation  # جایگزینی عنوان
                masael.save()
            elif context == "category_name":
                category = MasaelCategory.objects.get(id=item_id)
                category.name = translation.strip()  # جایگزینی محتوا
                category.save()
            elif context == "masael_content":
                try:
                    translation = extract_json_from_text(translation)
                except:
                    print(f"send {item_id} to ai")
                    translation = json_export(translation)
                content = MasaelContent.objects.get(id=item_id)
                if 'header' in translation and translation['header']:
                    content.header = translation['header']
                if 'masale' in translation and translation['masale']:
                    content.text = translation['masale']
                if 'answer' in translation and translation['answer']:
                    content.answer = translation['answer']
                content.save()
        except Exception as e:
            print(f'error context {item_id}: {str(e)}')

    telegram_logger(f"Batch {batch_id} processed and database updated.")
    print(f"Batch {batch_id} processed and database updated.")



# Main function
def main():
    if action == "send":
        send_translation_requests()
    elif action == "check":
        check_and_update_translations()
    else:
        print("Invalid action. Use 'send' or 'check'.")


if __name__ == "__main__":
    main()

