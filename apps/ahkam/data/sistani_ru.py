import json
from bs4 import BeautifulSoup
import re

import os

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()


class Crawl:
    language_id = 9
    category_name = "<PERSON><PERSON><PERSON><PERSON>"
    base_url = "sistani_ru.html.html"
    filename = "sistani_ru_data.json"
    last_priority = 0

    def parse(self, html: str):
        return BeautifulSoup(html, "html.parser")

    def escape_title(self, title):
        q = r"[\s-]*".join([i for i in title.replace(' ', '')])
        for i in ['(', ')', '?']:
            q = q.replace(i, fr'\{i}')
        return q

    def find_title(self, page, title, parent):
        q = self.escape_title(title)
        t = [x for x in page.find_all(text=re.compile(f"^{q}$", re.I)) if x.name == parent or x.find_parent(parent)]
        if not t or len(t) > 1:
            print(page.find_all(text=re.compile(f"^{q}$", re.I)))
            return
        return t[0].parent

    def get_tags_between(self, elem1):
        if elem1.text == 'МОЛИТВА':
            elem2 = elem1.find_next('div')
        else:
            elem2 = elem1.find_next(['h1', 'h3', 'h4'])

        tags = ""
        for e in elem1.find_all_next(['p', 'sub']):
            if e.sourcepos >= elem2.sourcepos:
                break

            tags += str(e)

        string = []
        for i in self.parse(tags):
            if i.name == 'sub':
                string.append(f"<sub>{i.text}</sub>")
            else:
                string.append(i.text)

        return "<br>".join(string)

    def crawl(self):
        page = self.parse(open('sistani_ru.html.html').read())
        cats = json.load(open('sistani_ru.json'))
        for k, v in cats.items():
            k1 = self.find_title(page, k, 'h1')
            v['content'] = self.get_tags_between(k1)
            if v.get('sub'):
                for kk, vv in v['sub'].items():
                    k2 = self.find_title(page, kk, 'h3')
                    vv['content'] = self.get_tags_between(k2)
                    if vv.get('sub'):
                        for kkk, vvv in vv['sub'].items():
                            k3 = self.find_title(page, kkk, 'h4')
                            vvv['content'] = self.get_tags_between(k3)

        with open(self.filename, "w") as f:
            json.dump(cats, f, ensure_ascii=False, indent=4)

    def create_category(self, name, parent=None):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent or MasaelCategory.objects.get(name=self.category_name)
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, content: str):
        items = []
        if pre := re.match(r'.+?(?=Положение\s*\d+\.)', content):
            if not pre[0].startswith('Положение'):
                if pre[0].endswith('<br>'):
                    items.append([None, pre[0][:-4]])
                else:
                    items.append([None, pre[0]])

        if groups := re.findall(r'(Положение\s*\d+\s*\.((?!Положение\s*\d+\s*\.)[\s\S])*)', content):
            for res in groups:
                q, a = re.search(r'(Положение\s*\d+\s*)\.?([\W\S]+)', res[0]).groups()
                if a.endswith('<br>'):
                    a = a[:-4]

                items.append([q.strip(), a.strip()])
        else:
            items.append((None, content))

        html = ""
        for index, i in enumerate(items):
            q, t = i
            if q:
                html += f"<div class='q-header'>{q}</div><p class='q-text'>{t}</p>"
            else:
                html += f"<p class='q-text'>{t}</p>"

        return html

    def import_to_database(self):
        from apps.ahkam.models import MasaelCategory
        from apps.ahkam.models import Masael

        ahkams = json.load(open(self.filename))

        for k, v in ahkams.items():
            cat1 = self.create_category(self.category_name)
            if v['content']:
                Masael.objects.create(
                    category=cat1,
                    priority=self.get_priority(),
                    title=k,
                    content=self.compact_questions(v['content'])
                )

            if not v.get('sub'):
                continue

            for kk, vv in v['sub'].items():
                cat2 = self.create_category(k, parent=cat1)
                if vv['content']:
                    Masael.objects.create(
                        category=cat2,
                        priority=self.get_priority(),
                        title=kk,
                        content=self.compact_questions(vv['content'])
                    )

                if not vv.get('sub'):
                    continue

                for kkk, vvv in vv['sub'].items():
                    cat3 = self.create_category(kk, parent=cat2)
                    if vvv['content']:
                        Masael.objects.create(
                            category=cat3,
                            priority=self.get_priority(),
                            title=kkk,
                            content=self.compact_questions(vvv['content'])
                        )

    def get_priority(self):
        self.last_priority += 1
        return self.last_priority


Crawl().import_to_database()

# replace footnotes
# source = self.parse(open('sistani_ru.html').read().replace('\n', ' '))
# page = self.parse(open('sistani_ru.html.html.jy', 'r').read().replace('\n', ' '))
# source.select_one('div.WordSection6').replaceWith()
#
# for sup in source.find_all('a', {'name': re.compile('_bookmark\d+')})[0:]:
#     p = sup.find_parent('p')
#     if not p:
#         continue
#
#     if num := p.find(text=re.compile(r'^\d+$')):
#         pass
#     else:
#         print('num not found', p.text)
#         continue
#
#     ptext = p.get_text(strip=True)
#     if ptext == 'ПРЕДПИСАНИЯОБИДЖТИХАДЕИТАКЛИДЕ':
#         continue
#     elif 'М АХ РАМ  И Н Е М АХ РАМ 2' == p.text:
#         continue
#     elif 'М АХ РАМ 1 И Н Е М АХ РАМ 2' == p.text:
#         continue
#
#     else:
#         _ptext = self.escape_title(ptext)
#
#     if xx := page.find(text=re.compile(_ptext)):
#         p.find(text=re.compile(r'^\d+$'))
#         xxp = xx.find_parent('p')
#         if not xxp:
#             xxp = xx.parent
#         xxp.name = "sub"
#         xxp.string = p.text
#     else:
#         print()
#
# open('sistani_ru.html.html', 'w').write(str(page))
