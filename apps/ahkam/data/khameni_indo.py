import json
import os
import time

import requests
from bs4 import BeautifulSoup
import re

headers = {
    'User-Agent': 'Mozilla/5.0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
}


class Crawl:
    base_url = 'https://www.leader.ir/id/book/90/Ajwibatul-Istiftaat'
    language_id = 64
    category_name = "Ayatollah Khamenei Indo"
    filename = "khamenei-indo.json"
    last_priority = 0

    def parse(self, link: str):
        time.sleep(0.5)
        print('DOWNLOADING: ', link)
        html = requests.get(link).text
        return BeautifulSoup(html, "html.parser")

    def parse_ajax(self, _id):
        time.sleep(0.5)
        print('DOWNLODING:', _id)
        return requests.post(
            'https://www.leader.ir/ajax/book',
            data={'parent': str(_id), 'catid': '89'},
            headers=headers
        ).json()

    def parse_ahkam(self, content, category):

        text = ""
        removed = False
        if heading := content.find(text=category):
            heading.replaceWith()
            removed = True

        if heading := content.select_one('span.titr'):
            heading.replaceWith()
            removed = True

        if not removed:
            print(category, ' -> no heading found')


        if div := content.select_one('div.details'):
            for j in div:
                if j.name:
                    if 'p' in j.name or 'h' in j.name or 'div' in j.name:
                        text += "<br>" + j.text.strip()
                    else:
                        print(j.name)
                elif j.text:
                    text += " " + j.text.strip()

        else:
            for j in content:
                if j.get_text(strip=True):
                    if j.name:
                        if 'p' in j.name or 'h' in j.name or 'div' in j.name:
                            text += "<br>" + j.text.strip()
                        else:
                            print(j.name)

                    elif j.text:
                        text += " " + j.text.strip()

        parts = re.findall(r'(SOAL\s*\d+\s*:((?!SOAL\s*\d+\s*:)[\s\S])*)', text)
        parts = [j[0] for j in parts]

        pparts = []

        for i in parts:
            if qa := re.search(r'(SOAL\s*\d+\s*:[\w\s\W]+)(JAWAB\s*:[\w\s\W]+)', i):
                pparts.append(qa.groups())
            else:
                print(i)

        return pparts

    def crawl(self):
        page = self.parse(self.base_url)
        result = {}

        for category in page.select('ul.level0 > li'):
            category_name = category.select_one('span').get_text(strip=True)
            result[category_name] = {}

            if category.select_one('i.doc'):
                result[category_name]['type'] = 'single'
                result[category_name]['contents'] = self.parse_ahkam(category, category_name)

            else:
                _id = category.select_one('a').get('href').split('=')[1]
                ajax_data = self.parse_ajax(_id)
                root, sections, tchain = ajax_data
                result[category_name]['type'] = 'many'
                result[category_name]['contents'] = []

                if root['root']:
                    result[category_name]['contents'].append({
                        'title': category_name,
                        'data': self.parse_ahkam(BeautifulSoup(root['root'], "html.parser"), category_name)
                    })

                for hokm in sections['data']:
                    if hokm["body"]:
                        result[category_name]['contents'].append({
                            'title': hokm['title'],
                            'data': self.parse_ahkam(BeautifulSoup(hokm['body'], "html.parser"), hokm['title'])
                        })
                    else:
                        print('ok')
                        data = []
                        _root, _sections, _tchain = self.parse_ajax(hokm['id'])
                        for _hokm in _sections['data']:
                            if _hokm["body"]:
                                data.append({
                                    'title': _hokm['title'],
                                    'data': self.parse_ahkam(BeautifulSoup(_hokm['body'], "html.parser"),
                                                             _hokm['title'])
                                })

                        result[category_name]['contents'].append({
                            'title': hokm['title'],
                            'items': data,
                        })

        with open(self.filename, "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def create_category(self, name, parent=None):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent or MasaelCategory.objects.get(name=self.category_name)
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, data):
        content_str = ""

        for item in data:
            que = re.search(r'SOAL\s*(\d*)\s*:([\w\s\W]+)', item[0])
            if que:
                q = que.groups()
            else:
                print()

            a = re.sub(r'JAWAB\s*\d*:', '', item[1])

            if not a:
                print(a)

            content_str += f'<div class="q-header">سوال {q[0]}</div><p class="q-text">{q[1]}</p><p class="q-answer">{a}</p>'

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory

        with open(self.filename) as f:
            ahkam = json.load(f)

        for category, values in ahkam.items():
            if values['type'] == 'many':
                cat = self.create_category(category.title())
                for content in values['contents']:
                    if content.get('items'):
                        for item in content['items']:
                            Masael.objects.create(
                                title=item['title'].title(),
                                category=self.create_category(content['title'].title(), cat),
                                content=self.compact_questions(item['data']),
                                priority=self.get_priority(),
                            )
                    else:
                        Masael.objects.create(
                            title=content['title'].title(),
                            category=cat,
                            content=self.compact_questions(content['data']),
                            priority=self.get_priority(),
                        )
            else:
                Masael.objects.create(
                    title=category.title(),
                    category=MasaelCategory.objects.get(name=self.category_name),
                    content=self.compact_questions(values['contents']),
                    priority=self.get_priority(),
                )

    def get_priority(self):
        self.last_priority += 1
        return self.last_priority
        # from apps.ahkam.models import Masael
        # try:
        #     return Masael.objects.filter(category=self.create_category(self.category_name)).last().priority + 1
        # except Exception:
        #     return 1

    def load_django(self):
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
        from django.core.wsgi import get_wsgi_application
        application = get_wsgi_application()
        return application


Crawl().load_django()

Crawl().import_to_database()
