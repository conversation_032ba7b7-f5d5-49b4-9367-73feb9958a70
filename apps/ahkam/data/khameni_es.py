import json, requests

import bs4.element
from bs4 import BeautifulSoup
import re


class Crawl:
    language_id = 11
    category_name = "Ayatollah KH ES"
    base_url = "khamenei_es.html"
    filename = "khamenei_es.json"

    def strips(self, t):
        return re.sub(" +", ' ', t).strip()

    def parse(self, html: str):
        return BeautifulSoup(html, "html.parser")

    def parse_block(self, html):
        blocks = []
        p = self.parse(html)
        parts = re.findall(r'(Cuestión\s?[\W\d]+:((?!Cuestión\s?[\W\d]+:)[\s\S])*)', p.text, flags=re.I | re.M)

        for part in parts:
            text = part[0].replace('?R.', '?R:')
            search = re.search(r'(Cuestión\s?\d+\s?:)([\W\S]+(?=Respuesta|R:))[Respuesta|R.*:]([\S\W]+)', text,
                               flags=re.M | re.I)
            if not search:
                print(part)

            groups = search.groups()
            blocks.append({
                'number': f'<div class="q-header">{groups[0]}</div>',
                'question': f'<p class="q-text">{groups[1].strip()}</p>',
                'answer': f'<p class="q-answer">{groups[2].strip(":").strip()}</p>',
            })

        return blocks

    def crawl(self):

        data = []
        for url in [
            'https://www.al-islam.org/es/respuestas-las-cuestiones-sobre-las-leyes-practicas-del-islam-vol-1-sayyid-ali-khamenei/capitulo-1',
            'https://www.al-islam.org/es/respuestas-las-cuestiones-sobre-las-leyes-practicas-del-islam-vol-1-sayyid-ali-khamenei/capitulo-2',
        ]:
            page_html = requests.get(url).text
            page = self.parse(page_html)
            sections = []
            for title in page.select('.field-name-body h2'):
                block_html = ""
                for i in title.next_siblings:
                    if i.name == 'h2':
                        break
                    if i.get_text(strip=True):
                        block_html += str(i)

                sections.append({
                    'title': title.get_text(strip=True),
                    'contents': self.parse_block(block_html),
                })
            data.append(sections)

        with open(self.filename, "w") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

    def compact_questions(self, data):
        content_str = ""
        for item in data:
            if not item.get('que'):
                self.log(item['num'])
                continue

            html = f'<div class="q-header">{item["num"]}</div><p class="q-text">{item["que"]}</p><p class="q-answer">{item["ans"]}</p>'
            content_str += html

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory
        # MasaelCategory.objects.get(name=self.category_name).delete()

        with open(self.filename) as f:
            ahkams = json.load(f)

        for hokm in ahkams:
            Masael.objects.create(
                category=MasaelCategory.objects.get(name=self.category_name),
                priority=self.get_priority(),
                title=hokm['title'],
                content=self.compact_questions(hokm['contents'])
            )

    def get_priority(self):
        from apps.ahkam.models import Masael, MasaelCategory
        try:
            return Masael.objects.filter(category__name=self.category_name).last().priority + 1
        except Exception as e:
            print(e)
            return 1

    def log(self, text):
        with open(self.filename + '.log', 'a+') as f:
            f.write(str(text))
            f.write('\n')


Crawl().crawl()
