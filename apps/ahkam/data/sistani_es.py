import json
import os
import requests
from bs4 import BeautifulSoup

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')


class Crawl:
    language_id = 11
    site_catid = '89'
    category_name = 'Ayatullah Sistani ES'
    base_url = "http://spa.al-shia.org/page.php?catid=8"
    filename = "sistani_es.json"

    def parse(self, link: str):
        print('DOWNLOADING: ', link)
        html = requests.get(link).text
        return BeautifulSoup(html, "html.parser")

    def get_pages_content(self, href):
        page = self.parse(f"http://spa.al-shia.org/page.php{href}")
        page_links = page.select('table td a.down_link')
        html = ""
        if len(page_links):
            for link in page_links:
                if link.get_text(strip=True).isdigit():
                    page_p_elements = self.parse(f"http://spa.al-shia.org/page.php{link.get('href')}").select('p')
                    html += "".join([str(p) for p in page_p_elements])
        else:
            html = "".join([str(p) for p in page.select('p')])

        content = BeautifulSoup(html, "html.parser")
        sections_title = []
        for i in content.descendants:
            if hasattr(i, 'attrs') and "rosybrown" in i.attrs.get('style', ''):
                if len(i.get_text(strip=True)):
                    sections_title.append(i)

        if not len(sections_title):
            print("No sections")
            return html

        sections = []
        for i, sec in enumerate(sections_title):
            if i + 1 == len(sections_title):
                next_pos = None
            else:
                next_pos = sections_title[i + 1].sourcepos
            if not sec.get_text(strip=True):
                print(sec)

            sections.append({
                'title': sec.get_text(strip=True),
                'html': html[sec.sourcepos:next_pos].replace(str(sec), '')
            })

        return sections

    def crawl(self):
        page = self.parse(self.base_url)
        result = []

        for category in page.select('table td a'):
            category_name = category.get_text(strip=True)
            if category.get('href') != '?catid=13':
                # category has no sub-category
                content = self.get_pages_content(category.get('href'))
                result.append({
                    'name': category_name,
                    'data': content,
                    'has_sub': False,
                })

            else:
                subs = []
                # category has sub-category
                category_page = self.parse(f"http://spa.al-shia.org/page.php{category.get('href')}")
                for sub_category in category_page.select('table td a'):
                    sub_category_name = sub_category.get_text(strip=True)
                    subs.append({
                        'name': sub_category_name,
                        'data': self.get_pages_content(sub_category.get('href')),
                    })

                result.append({
                    'name': category_name,
                    'data': subs,
                    'has_sub': True,
                })

        with open(self.filename, "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def create_category(self, name):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=MasaelCategory.objects.get(name=self.category_name)
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory

        ahkam = json.load(open(self.filename))

        for item in ahkam:
            cat = self.create_category(item['name'].lower())

            if not item['has_sub']:
                if type(item['data']) is list:
                    # has multiple documents
                    for doc in item['data']:
                        Masael.objects.create(
                            title=doc['title'].lower(),
                            category=cat,
                            content=doc['html'],
                            priority=self.get_priority()
                        )
                else:
                    # has one document
                    Masael.objects.create(
                        title=item['name'].lower(),
                        category=cat,
                        content=item['data'],
                        priority=self.get_priority(),
                    )

            else:
                for doc in item['data']:
                    Masael.objects.create(
                        title=doc['name'].lower(),
                        category=cat,
                        content=doc['data'],
                        priority=self.get_priority(),
                    )

    def get_priority(self):
        from apps.ahkam.models import Masael, MasaelCategory
        kids = MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id).get_descendants()
        try:
            return Masael.objects.filter(category__in=kids).last().priority + 1
        except Exception:
            return 1


Crawl().import_to_database()
