import json
import os
import re
import sys
import time

import requests
from bs4 import BeautifulSoup

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
from apps.ahkam.models import Masael
from django.contrib.contenttypes.models import ContentType
from apps.ahkam.models import MasaelCategory

headers = {
    'User-Agent': 'Mozilla/5.0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
}

"https://www.musicdays.ir/%D8%AF%D8%A7%D9%86%D9%84%D9%88%D8%AF-%D8%A2%D9%87%D9%86%DA%AF-%D8%A8%DB%8C%D8%B3-%D8%AF%D8%A7%D8%B1/"
"https://golsarmusic.ir/%D8%A2%D9%87%D9%86%DA%AF-%D8%A8%D8%A7%D8%B4%DA%AF%D8%A7%D9%87-%D8%AC%D8%AF%DB%8C%D8%AF/"


class Crawl:
    category_name = 'وحید خراسانی'
    language_id = 6
    filename = 'vahid_ar.json'

    def parse(self, link: str):
        time.sleep(1)
        if link.startswith('http'):
            print('DOWNLOADING: ', link)
            html = requests.get(link).text
        else:
            html = link
        return BeautifulSoup(html, "html.parser")

    def parse_ahkam(self, page):
        content_str = ""
        for section in page.select('section'):
            for p in section.select('p'):
                try:
                    title, content = re.search('^(\d+)\.(.*)', p.get_text()).groups()
                    content_str += f'<div class="q-header">{title}</div>'
                    content_str += f'<p class="q-text">{content}</p>'
                except Exception as e:
                    content_str += f'<p class="q-text">{p.get_text()}</p>'

        return content_str

    def crawl_categories(self):
        page = self.parse(
            "http://wahidkhorasani.com/عربي/الفتاوى/المحتوى/1283_الرسالة-العملية/4215_الفصل-الثاني-شروط-المتعاقدين")
        categories = {}

        def convert_ul_to_json(ul_element):
            json_tree = []
            for li_element in ul_element.find_all('li', recursive=False):
                item = {}
                item['title'] = li_element.find('a').get_text(strip=True)
                item['link'] = li_element.find('a').get('href')
                sub_ul_element = li_element.find('ul')
                if sub_ul_element:
                    item['children'] = convert_ul_to_json(sub_ul_element)
                json_tree.append(item)
            return json_tree

        for cat in page.select('div.lists > ul'):
            categories[cat.find('a').get_text(strip=1)] = convert_ul_to_json(cat)

        json.dump(categories, open(self.filename, "w"), ensure_ascii=False, indent=4)

    def crawl(self):
        def import_item(item):
            for i in item:
                if i.get('children'):
                    import_item(i)
                else:
                    _id = re.search("\d+", i['link']).group()
                    page = self.parse(
                        f"http://wahidkhorasani.com/English/Fatwa/List?SourceNo=218&TocNo={_id}&SearchText=")

                    page_content = self.parse_ahkam(page)
                    self.create_obj(page_content, c['name'], c['parent'])

        categories = json.load(open(self.filename))
        for cats in categories:
            for c1 in cats:
                if c1.get('children'):
                    pass

            _id = re.search("\d+", c['link']).group()
            page = self.parse(
                f"http://wahidkhorasani.com/English/Fatwa/List?SourceNo=218&TocNo={_id}&SearchText=")

            page_content = self.parse_ahkam(page)
            self.create_obj(page_content, c['name'], c['parent'])

    def create_category(self, name, parent=None):

        if type(parent) is str:
            parent = self.create_category(parent)

        try:
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cc := MasaelCategory.objects.filter(name=name, content_type=content_type, language_id=self.language_id):
                for c in cc:
                    if c.get_root().name == self.category_name:
                        return c

            return MasaelCategory.objects.create(
                language_id=self.language_id,
                name=name,
                content_type=content_type,
                parent=parent or MasaelCategory.objects.get(name=self.category_name)
            )
        except Exception as e:
            print(e, name)

    def get_priority(self):
        from apps.ahkam.models import Masael
        try:
            return Masael.objects.filter(category__language__id=6).last().priority + 1
        except Exception:
            return 1

    def create_obj(self, content, title, cat):
        if cat == title:
            category = self.create_category(self.category_name)
        else:
            category = self.create_category(cat)

        Masael.objects.update_or_create(
            title=title,
            category=category,
            defaults={
                'content': content,
                'priority': self.get_priority(),
            }
        )


Crawl().crawl_categories()
