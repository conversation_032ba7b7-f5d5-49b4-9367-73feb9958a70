import json
import requests
from bs4 import BeautifulSoup
import re


class Crawl:
    language_id = 11
    category_name = 'Ayatullah KHOMEINI'
    base_url = "khomeini_es.html"
    filename = "khomeini_es.json"

    def parse(self, html: str):
        return BeautifulSoup(html, "html.parser")

    def crawl(self):
        page = self.parse(open(self.base_url).read().replace('\n', ' '))
        result = []
        titles = page.select('span.cls_009')
        for i, title in enumerate(titles):
            if i == len(titles) - 1:
                sourcepos = title.find_all_next('span')[-1].sourcepos
            else:
                sourcepos = titles[i + 1].sourcepos

            html = {
                'title': title.text,
                'contents': []
            }

            for span in title.find_all_next('span'):
                if span.sourcepos >= sourcepos:
                    break

                if span.attrs['class'][0] in ['cls_015']:
                    html['contents'].append({
                        'title': span.text,
                        'has_sub': False,
                        'contents': [],
                    })

                elif span.attrs['class'][0] in ['cls_014', 'cls_021']:
                    if not len(html['contents']):
                        html['contents'].append({
                            'title': span.text,
                            'has_sub': False,
                            'contents': [],
                        })
                    else:
                        html['contents'][-1]['has_sub'] = True

                        if not html['contents'][-1].get('sub'):
                            html['contents'][-1]['sub'] = []

                        html['contents'][-1]['sub'].append({'title': span.text, 'contents': []})

                elif span.attrs['class'][0] in ['cls_010', 'cls_019', 'cls_020']:
                    if html['contents'][-1]['has_sub']:
                        self.reformat_txt(html['contents'][-1]['sub'][-1]['contents'], span)
                    else:
                        self.reformat_txt(html['contents'][-1]['contents'], span)

            result.append(html)

        with open(self.filename, "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def reformat_txt(self, html, span):
        if txt := span.get_text(strip=True):
            if res := re.search(r'^\d+\s?.\s?-[\w\W]+', txt):
                html.append(span.text)
            elif len(html):
                html[-1] += span.text
            else:
                html.append(span.text)
        else:
            print(span)

    def compact_questions(self, content: list):
        html = ""
        for j in content:
            if res := re.search(r'^(\d+)\s?.\s?-?\s?\s?([\W\w]+)', j):
                res = res.groups()
                html += f"<div class='q-header'>{res[0]}</div><p class='q-text'>{res[1]}</p>"
            else:
                html += f"<p class='q-text'>{j}</p>"

        return html

    def create_category(self, name: str, parent=None):
        name = name.title()
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent or MasaelCategory.objects.get(name=self.category_name)
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def import_to_database(self):
        self.load_django()

        from apps.ahkam.models import Masael, MasaelCategory

        with open(self.filename) as f:
            data = json.load(f)

        for item in data:
            title, contents = item['title'], item['contents'][0]
            print(title)

            if contents['has_sub'] is False:
                Masael.objects.create(
                    title=contents['title'],
                    category=self.create_category(self.category_name),
                    content=self.compact_questions(contents['contents']),
                    priority=self.get_priority(),
                )

            else:
                parent_cat = self.create_category(title)
                subcat, _contents, subs = contents['title'], contents['contents'], contents['sub']
                if len(_contents):
                    Masael.objects.create(
                        title=title,
                        category=parent_cat,
                        content=self.compact_questions(_contents),
                        priority=self.get_priority(),
                    )
                for sub in subs:
                    Masael.objects.create(
                        title=sub['title'],
                        category=self.create_category(subcat, parent_cat),
                        content=self.compact_questions(sub['contents']),
                        priority=self.get_priority(),
                    )

    def get_priority(self):
        from apps.ahkam.models import Masael, MasaelCategory
        kids = MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id).get_descendants()
        try:
            return Masael.objects.filter(category__in=kids).last().priority + 1
        except Exception:
            return 1

    def log(self, text):
        with open(self.filename + '.log', 'a+') as f:
            f.write(str(text))
            f.write('\n')

    def load_django(self):
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
        from django.core.wsgi import get_wsgi_application
        application = get_wsgi_application()
        return application


Crawl().import_to_database()
# Crawl().crawl()
