import json
import os
import time

import requests
from bs4 import BeautifulSoup
import re

headers = {
    'User-Agent': 'Mozilla/5.0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
}


class Crawl:
    base_url = 'https://www.leader.ir/ar/book/14/%D8%A7%D8%AC%D9%88%D8%A8%D8%A9-%D8%A7%D9%84%D8%A5%D8%B3%D8%AA%D9%81%D8%AA%D8%A7%D8%A1%D8%A7%D8%AA'
    language_id = 2
    category_name = "Ayatollah Khamenei Esteftaat"
    filename = "khamenei_estef_ar.json"
    last_priority = 0

    def parse(self, link: str):
        time.sleep(0.5)
        print('DOWNLOADING: ', link)
        html = requests.get(link).text
        return BeautifulSoup(html, "html.parser")

    def parse_ajax(self, _id):
        time.sleep(0.5)
        print('DOWNLODING:', _id)
        return requests.post(
            'https://www.leader.ir/ajax/book',
            data={'parent': str(_id), 'catid': '12'},
            headers=headers
        ).json()

    def parse_ahkam(self, content, category):

        text = ""
        removed = False
        if heading := content.find(text=category):
            heading.replaceWith()
            removed = True

        if heading := content.select_one('.headline'):
            heading.replaceWith()
            removed = True

        if heading := content.find('h5', attrs={'class': 'subTitle', 'style': 'text-align:center'}):
            heading.replaceWith()
            removed = True

        if not removed:
            print(category, ' -> no heading found')

        pre = None
        if _pre := re.match(r'.+?(?=س\s?\d+)', text):
            if _pre[0]:
                pre = _pre[0]

        if div := content.select_one('div.details'):
            for j in div:
                if j.name:
                    if 'p' in j.name or 'h' in j.name or 'div' in j.name:
                        text += "<br>" + j.text.strip()
                    else:
                        print(j.name)
                elif j.text:
                    text += " " + j.text.strip()

        else:
            for j in content:
                if j.get_text(strip=True):
                    if j.name:
                        if 'p' in j.name or 'h' in j.name or 'div' in j.name:
                            text += "<br>" + j.text.strip()
                        else:
                            print(j.name)

                    elif j.text:
                        text += " " + j.text.strip()

        parts = re.findall(r'(س\s*\d+\s*:((?!س\s*\d+\s*:)[\s\S])*)', text)
        parts = [j[0] for j in parts]
        if not parts and not pre:
            parts.insert(0, text)
        elif pre:
            parts.insert(0, pre)

        pparts = []

        if category == 'المقدمة':
            return parts

        for i in parts:
            if qa := re.search(r'(س\s*\d+\s*:[\w\s\W]+)(ج\s*:[\w\s\W]+)', i):
                pparts.append(qa.groups())
            else:
                print(i)

        return pparts

    def crawl(self):
        page = self.parse(self.base_url)
        result = {}

        for category in page.select('ul.level0 > li'):
            category_name = category.select_one('span').get_text(strip=True)
            result[category_name] = {}

            if category.select_one('i.doc'):
                result[category_name]['type'] = 'single'
                result[category_name]['contents'] = self.parse_ahkam(category, category_name)

            else:
                _id = category.select_one('a').get('href').split('=')[1]
                ajax_data = self.parse_ajax(_id)
                root, sections, tchain = ajax_data
                result[category_name]['type'] = 'many'
                result[category_name]['contents'] = []

                if root['root']:
                    result[category_name]['contents'].append({
                        'title': category_name,
                        'data': self.parse_ahkam(BeautifulSoup(root['root'], "html.parser"), category_name)
                    })

                for hokm in sections['data']:
                    if hokm["body"]:
                        result[category_name]['contents'].append({
                            'title': hokm['title'],
                            'data': self.parse_ahkam(BeautifulSoup(hokm['body'], "html.parser"), hokm['title'])
                        })
                    else:
                        print('ok')
                        data = []
                        _root, _sections, _tchain = self.parse_ajax(hokm['id'])
                        for _hokm in _sections['data']:
                            if _hokm["body"]:
                                data.append({
                                    'title': _hokm['title'],
                                    'data': self.parse_ahkam(BeautifulSoup(_hokm['body'], "html.parser"),
                                                             _hokm['title'])
                                })

                        result[category_name]['contents'].append({
                            'title': hokm['title'],
                            'items': data,
                        })

        with open(self.filename, "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def create_category(self, name, parent=None):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent or MasaelCategory.objects.get(name=self.category_name)
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, data):
        content_str = ""

        for item in data:
            if "المقدمة" in item:
                return f"<p class='q-text'>{item}</p>"

            que = re.search(r'س\s*(\d*)\s*:([\w\s\W]+)', item[0])
            if que:
                q = que.groups()
            else:
                print()

            a = re.sub(r'ج\s*\d*:', '', item[1])

            if not a:
                print(a)

            content_str += f'<div class="q-header">مسألة {q[0]}</div><p class="q-text">{q[1]}</p><p class="q-answer">{a}</p>'

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory

        with open(self.filename) as f:
            ahkam = json.load(f)

        for category, values in ahkam.items():
            if values['type'] == 'many':
                cat = self.create_category(category)
                for content in values['contents']:
                    if content.get('items'):
                        for item in content['items']:
                            Masael.objects.create(
                                title=item['title'],
                                category=self.create_category(content['title'], cat),
                                content=self.compact_questions(item['data']),
                                priority=self.get_priority(),
                            )
                    else:
                        Masael.objects.create(
                            title=content['title'],
                            category=cat,
                            content=self.compact_questions(content['data']),
                            priority=self.get_priority(),
                        )
            else:
                Masael.objects.create(
                    title=category,
                    category=MasaelCategory.objects.get(name=self.category_name),
                    content=self.compact_questions(values['contents']),
                    priority=self.get_priority(),
                )

    def get_priority(self):
        self.last_priority += 1
        return self.last_priority
        # from apps.ahkam.models import Masael
        # try:
        #     return Masael.objects.filter(category=self.create_category(self.category_name)).last().priority + 1
        # except Exception:
        #     return 1

    def load_django(self):
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
        from django.core.wsgi import get_wsgi_application
        application = get_wsgi_application()
        return application


Crawl().load_django()
# بخش الحوالة زیرمجموعه الوکاله باید دستی وارد شود در  json

Crawl().import_to_database()
