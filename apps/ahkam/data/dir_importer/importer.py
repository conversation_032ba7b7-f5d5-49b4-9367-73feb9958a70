import os
import re

import pandas

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
from django.contrib.contenttypes.models import ContentType

from apps.ahkam.models import <PERSON><PERSON><PERSON>, Ma<PERSON>el<PERSON>ategory
from dj_language.models import Language
from mptt.exceptions import InvalidMove


def import_excel(file, cat):
    if "lock" in file:
        return
    df = pandas.read_excel(file)

    try:
        content = ""
        for i, row in df.iterrows():
            row = list(row)
            if i == 0:
                continue
            if pandas.notna(row[0]) and row[0]:
                content += f'<div class="q-header">{str(row[0]).strip()}</div>'

            if pandas.notna(row[1]) and row[1]:
                content += f'<p class="q-text">{str(row[1]).strip()}</p>'
            if len(row) > 2:
                if pandas.notna(row[2]) and row[2]:
                    content += f'<p class="q-answer">{str(row[2]).strip()}</p>'

        title = os.path.basename(file.replace('.xlsx', '').title())
        title = re.sub(r'\d+-?', '', title).strip()

        dua, created = Masael.objects.get_or_create(
            title=title,
            defaults={
                'category': cat,
                'content': content,
            }
        )
    except Exception as e:
        raise e

    if not created:
        dua.content = content
        dua.save()


def import_directory(base_path, cat=None, lang=None):
    for entry_name in os.listdir(base_path):
        entry_path = os.path.join(base_path, entry_name)
        entry_name = re.sub(r'\d+-?', '', entry_name).strip()

        if os.path.isdir(entry_path):
            # there is a cat, lets iterate over it
            try:
                _cat = MasaelCategory.objects.create(
                    language_id=lang.id,
                    name=entry_name,
                    content_type=ContentType.objects.get(model=MasaelCategory._meta.model_name)
                )
                if cat:
                    _cat.move_to(cat, 'last-child')
            except InvalidMove as e:
                _cat = MasaelCategory.objects.create(
                    language_id=lang.id,
                    name=entry_name + ' -1',
                    content_type=ContentType.objects.get(model=MasaelCategory._meta.model_name)
                )
                if cat:
                    _cat.move_to(cat, 'last-child', )

            import_directory(entry_path, cat=_cat, lang=lang)

        else:
            # It's a file, and a doc of the cat
            print(entry_name, cat)
            try:
                import_excel(entry_path, cat=cat)
            except Exception as e:
                print(e)


print("started...")
l = Language.objects.filter(code='ar').first()


def delete_all_categories():
    # delete all categories
    MasaelCategory.objects.filter(
        content_type=ContentType.objects.get(model=MasaelCategory._meta.model_name),
    ).delete()


# delete_all_categories()

c = MasaelCategory.objects.create(
    name='najafi',
    content_type=ContentType.objects.get(model=MasaelCategory._meta.model_name),
    language=l,
)
import_directory(
    base_path='najafi', cat=c, lang=l
)

print("finished")
