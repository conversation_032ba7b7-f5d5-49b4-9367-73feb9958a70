import json
from bs4 import BeautifulSoup
import re


class Crawl:
    language_id = 23
    category_name = "<PERSON><PERSON><PERSON><PERSON>"
    base_url = "sistani_fr.html"
    filename = "sistani_fr.json"

    def strips(self, t):
        return re.sub(" +", ' ', t).strip() or None

    def parse(self, html: str):
        return BeautifulSoup(html, "html.parser")

    def crawl(self):
        page_html = open(self.base_url, 'r').read()
        page = self.parse(page_html)
        sections = {}
        titles = page.select('h1')
        # putting each category in box
        for title in titles:
            category, _title = title.get_text(strip=True).split("»")
            if not sections.get(category):
                sections[category] = []

            part = title.find_next_sibling('p')

            if len(part.select('b')) == 0:
                self.log(f"NO AHKAM: {_title}")
                sections[category].append({
                    'title': _title.strip(),
                    "multiple": False,
                    'content': {
                        "que": "",
                        "ans": part.encode_contents().decode('utf-8'),
                    },
                })

            elif len(part.select('b')) == 1:
                self.log(f"JUST ONE AHKAM: {_title}")
                b = part.find('b').replace_with()
                sections[category].append({
                    "multiple": False,
                    'title': _title.strip(),
                    'content': {
                        "que": b.get_text(strip=True),
                        "ans": part.encode_contents().decode('utf-8'),
                    },
                })
            else:
                pre_doc = None
                if pre_doc := re.search(r'^(.*?)Article', part.text):
                    if pre_doc.groups():
                        pre_doc = pre_doc.groups()[0] or None

                xhtml = ""
                for tag in part.contents:
                    if tag.find('b'):
                        xhtml += tag.text
                    else:
                        xhtml += tag.encode_contents().decode('utf-8')

                normalized_text = re.sub('(Article\s\d+:)', r'\n\1', xhtml)
                groups = re.findall(r'(?=(article\s\d+:(.)+))', normalized_text, flags=re.I)
                articles = []
                for article in groups:
                    que, ans = article[0].split(':', 1)
                    articles.append({
                        'que': que.strip(),
                        'ans': ans.strip(),
                    })

                sections[category].append({
                    'multiple': True,
                    'pre_doc': pre_doc,
                    'title': _title.strip(),
                    'contents': articles,
                })

        with open(self.filename, "w") as f:
            json.dump(sections, f, ensure_ascii=False, indent=4)

    def create_category(self, name):
        from django.contrib.contenttypes.models import ContentType
        from apps.ahkam.models import MasaelCategory

        try:
            parent = MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id)
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type, parent=parent,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent,
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, data):
        content_str = ""
        for item in data:
            question = f'<div class="q-header">{item["que"]}</div><p class="q-text">{item["ans"].strip()}</p>'
            content_str += question

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael

        with open(self.filename) as f:
            ahkams = json.load(f)

        for category, items in ahkams.items():
            category = category.replace('\n', '')
            cat = self.create_category(self.strips(category))
            for item in items:
                if item['multiple'] is False:
                    item['title'] = item['title'].replace('\n', '')
                    item['title'] = self.strips(item['title'])

                    content = ""
                    if item['content']['que']:
                        content += f'<div class="q-header">{item["content"]["que"]}</div>'

                    content += f'<p class="q-text">{item["content"]["ans"].strip()}</p>'

                    Masael.objects.create(
                        title=item["title"],
                        category=cat,
                        content=content,
                        priority=self.get_priority(),
                    )

                else:
                    content = ""
                    if item["pre_doc"]:
                        content += f'<p class="q-text">{item["pre_doc"]}</p>'

                    content += self.compact_questions(item['contents'])
                    item['title'] = item['title'].replace('\n', '')
                    item['title'] = self.strips(item['title'])
                    Masael.objects.create(
                        title=item['title'],
                        category=cat,
                        content=content,
                        priority=self.get_priority(),
                    )

    def get_priority(self):
        from apps.ahkam.models import Masael, MasaelCategory
        kids = MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id).get_descendants()
        try:
            return Masael.objects.filter(category__in=kids).last().priority + 1
        except Exception:
            return 1

    def log(self, text):
        with open(self.filename + '.log', 'a+') as f:
            f.write(str(text))
            f.write('\n')


Crawl().import_to_database()
