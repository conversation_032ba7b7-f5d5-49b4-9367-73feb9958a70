import json
from bs4 import BeautifulSoup
import re


class Crawl:
    language_id = 57
    category_name = "<PERSON><PERSON><PERSON><PERSON>"
    base_url = "sistani_ur.htm"
    filename = "sistani_ur.json"

    def parse(self, html: str):
        return BeautifulSoup(html, "html.parser")

    def crawl(self):
        page = self.parse(open(self.base_url, 'r').read())
        result = []

        ptags = page.find('a', attrs={'name': '_Toc453586716'}).find_all_next(['p', 'h3', 'h2', 'h1', 'h4', 'h5', 'h6'])

        for p in ptags:
            classes = p.attrs.get('class', [])
            txt = p.get_text(strip=True)
            if not txt:
                continue

            if "Heading2Center" in classes:
                result.append({
                    'title': txt,
                    'type': 'flat',
                    'items': []
                })

            elif p.name == 'h3':
                if not result[-1].get('subs'):
                    result[-1]['subs'] = []
                    result[-1]['type'] = 'multi'

                result[-1]['subs'].append({
                    'title': txt,
                    'items': []
                })

            elif "libArabic" in classes or 'libNormal' in classes:
                if result[-1]['type'] == 'flat':
                    if re.search(r'^\d+\s?۔', txt) or not len(result[-1]['items']):
                        result[-1]['items'].append(txt)
                    else:
                        result[-1]['items'][-1] += " <br> " + txt

                else:
                    if re.search(r'^\d+\s?۔', txt) or not len(result[-1]['subs'][-1]['items']):
                        result[-1]['subs'][-1]['items'].append(txt)
                    else:
                        result[-1]['subs'][-1]['items'][-1] += " <br> " + txt

        with open(self.filename, "w") as f:
            json.dump(result, f, ensure_ascii=False, indent=4)

    def create_category(self, name):
        from django.contrib.contenttypes.models import ContentType
        from apps.ahkam.models import MasaelCategory

        try:
            parent = MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id)
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type, parent=parent,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent,
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, content: list):
        items = []
        for j in content:
            if res := re.search(r'(^\d+\s?۔)([\W\w\s\S]*)', j):
                res = res.groups()
                q = res[0].replace('۔', '').strip()
                if q.isnumeric():
                    q = int(q)
                else:
                    print(q)

                items.append([q, res[1]])
            else:
                items.append((None, j))
        html = ""
        for index, i in enumerate(items):
            q, t = i
            if index >= 1 and items[0][0] and q < items[0][0]:
                t = f"{q} - {t}"
                q = None

            if q:
                html += f"<div class='q-header'>مسألة {q}</div><p class='q-text'>{t}</p>"
            else:
                html += f"<p class='q-text'>{t}</p>"

        return html

    def import_to_database(self):
        from apps.ahkam.models import MasaelCategory

        from apps.ahkam.models import Masael

        with open(self.filename) as f:
            ahkams = json.load(f)

        for i in ahkams:
            if i['type'] == 'flat':
                Masael.objects.create(
                    title=i["title"],
                    category=MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id),
                    content=self.compact_questions(i['items']),
                    priority=self.get_priority(),
                )
            else:
                cat = self.create_category(i['title'])
                if len(i['items']):
                    Masael.objects.create(
                        title=i["title"],
                        category=cat,
                        content=self.compact_questions(i['items']),
                        priority=self.get_priority(),
                    )

                for j in i['subs']:
                    Masael.objects.create(
                        title=j["title"],
                        category=cat,
                        content=self.compact_questions(j['items']),
                        priority=self.get_priority(),
                    )

    def get_priority(self):
        from apps.ahkam.models import Masael, MasaelCategory
        kids = MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id).get_descendants()
        try:
            return Masael.objects.filter(category__in=kids).last().priority + 1
        except Exception:
            return 1

    def load_django(self):
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
        from django.core.wsgi import get_wsgi_application
        application = get_wsgi_application()
        return application


Crawl().load_django()
Crawl().import_to_database()
