import json
import requests
from bs4 import BeautifulSoup
import re

headers = {
    'User-Agent': 'Mozilla/5.0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
}


class Crawl:
    language_id = 44
    site_catid = '105'
    category_name = '<PERSON><PERSON><PERSON><PERSON>'
    base_url = "https://www.leader.ir/tr/book/41/Sorular-ve-Fetvalar"
    filename = "ahkam_tr.json"

    def parse(self, link: str):
        print('DOWNLOADING: ', link)
        html = requests.get(link).text
        return BeautifulSoup(html, "html.parser")

    def parse_ajax(self, _id):
        print('DOWNLODING:', _id)
        return requests.post(
            'https://www.leader.ir/ajax/book',
            data={'parent': str(_id), 'catid': self.site_catid},
            headers=headers
        ).json()

    def parse_ahkam(self, content, category):
        data = []
        if "ALIŞ VERİŞ HÜKÜMLERİ" == category:
            print(category)
        try:
            extra_data = []
            for item in content.select('.matn'):
                if "Cevap" in item.get_text() and "Soru" not in item.get_text():
                    continue

                if atag := item.select_one('a'):
                    if "ftnref" in atag.get('href'):
                        extra_data.append(item.get_text(strip=True))
                        self.log(f"ERROR [line 44]: {item.get_text(strip=True)}")
                        continue
                if smalltag := item.select_one('small'):
                    extra_data.append(item.get_text(strip=True))
                    self.log(f"ERROR [line 48]: {item.get_text(strip=True)}")
                    continue

                if que := item.get_text(strip=True):
                    if ans := item.find_next('strong'):
                        data.append({'que': que, 'ans': ans.get_text(strip=True)})
                    else:
                        self.log(f"ERROR [line 55]: {item.get_text(strip=True)}")

            if extra_data:
                data.append({'subtitle': extra_data})

            return data


        except Exception as e:
            print(e)

    def crawl(self):
        page = self.parse(self.base_url)
        result = {}

        for category in page.select('ul.level0 > li'):
            category_name = category.select_one('span').get_text(strip=True)
            result[category_name] = {}

            if category.select_one('i.doc'):
                result[category_name]['type'] = 'single'
                result[category_name]['contents'] = self.parse_ahkam(category, category_name)

            else:
                _id = category.select_one('a').get('href').split('=')[1]
                ajax_data = self.parse_ajax(_id)
                root, sections, tchain = ajax_data
                result[category_name]['type'] = 'many'
                result[category_name]['contents'] = []

                if root['root']:
                    result[category_name]['contents'].append({
                        'title': category_name,
                        'data': self.parse_ahkam(BeautifulSoup(root['root'], "html.parser"), category_name)
                    })

                for hokm in sections['data']:
                    result[category_name]['contents'].append({
                        'title': hokm['title'],
                        'data': self.parse_ahkam(BeautifulSoup(hokm['body'], "html.parser"), category_name)
                    })

        with open(self.filename, "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def create_category(self, name):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id)
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, data):
        content_str = ""
        for item in data:
            if que := item.get('que'):
                q = re.search(r"(Soru[^:.]*)[:.]([\s\S]*)", que)
                if not q:
                    print(item)
                    raise Exception(q)
                q = q.groups()
                if len(q) != 2:
                    self.log(f"ERROR [line 129]: {que}")
                    raise Exception
                else:
                    qnum, qtext = q
                    question = f'<div class="q-header">{qnum.strip()}</div><p class="q-text">{qtext.strip()}</p>'
                    answer = f'<p class="q-answer">{item["ans"].strip()}</p>'
                    content_str += question + answer

            elif item.get('subtitle'):
                content_str += "<br>".join(item['subtitle'])

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory

        with open(self.filename) as f:
            ahkam = json.load(f)

        for category, values in ahkam.items():
            if values['type'] == 'many':
                cat = self.create_category(category)
                for content in values['contents']:
                    if content['data'] is None:
                        raise Exception(content)
                    Masael.objects.create(
                        title=content['title'],
                        category=cat,
                        content=self.compact_questions(content['data']),
                        priority=self.get_priority(),
                    )
            else:
                Masael.objects.create(
                    title=category,
                    category=MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id),
                    content=self.compact_questions(values['contents']),
                    priority=self.get_priority(),
                )

    def get_priority(self):
        from apps.ahkam.models import Masael, MasaelCategory
        kids = MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id).get_descendants()
        try:
            return Masael.objects.filter(category__in=kids).last().priority + 1
        except Exception:
            return 1

    def log(self, text):
        with open(self.filename + '.log', 'a+') as f:
            f.write(str(text))
            f.write('\n')


Crawl().import_to_database()
