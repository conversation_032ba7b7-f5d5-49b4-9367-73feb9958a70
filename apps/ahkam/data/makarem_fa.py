import json
import os
import re, sys

import requests
from bs4 import BeautifulSoup

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()


class Crawl:
    language_id = 69
    category_name = "آیت الله مکارم"
    base_url = "https://makarem.ir/main.aspx?typeinfo=30"
    filename = "makarem_fa.json"
    last_priority = 0

    def parse(self, cat: str):
        data = requests.post(
            "https://makarem.ir/WebServiceClient/Category.asmx/Get?typeinfo=30",
            json={'CategoryDBID': cat},
            headers={
                'cookie': '_ga_QM6KTESFNJ=GS1.1.1690874460.4.0.1690874507.0.0.0; _ga=GA1.1.1115615104.1690717028; ASP.NET_SessionId=3wvpj54w4sl0prwtk2jcpaxu; Token=389df6f7-116f-4dc8-961b-5a5429323d54',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0'
            }
        ).json()
        return data['d']

    def crawl(self):
        cats = json.load(open("makarem_fa_data.json"))

        for cat in cats:
            if cat['ParentID'] == 0 and cat['CountChild'] == 0:
                self.create_obj(cat)

            elif cat['ParentID'] != 0:
                self.create_obj(cat)

    def create_category(self, name, parent=None):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(
                    name=name, content_type=content_type, language_id=self.language_id
            ).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent or MasaelCategory.objects.get(name=self.category_name)
                )
        except Exception as e:
            print(e, name)

    def import_categories(self):
        cats = json.load(open("makarem_fa_data.json"))

        for c in cats:
            if c['CountChild'] > 0:
                print(c['Title'])
                self.create_category(c['Title'])

    def create_obj(self, cat):
        from apps.ahkam.models import Masael
        data = self.parse(cat['CategoryID'])
        if cat['CountChild'] > 0:
            return

        cate = self.get_cat(cat['ParentID'])
        category = self.create_category(cate or self.category_name)

        if not data['Comment']:
            return

        print(cat['Title'])
        content = self.compact_questions(data['Comment'], footnote=data['UniqueTitle'])
        if content:
            Masael.objects.update_or_create(
                title=cat['Title'],
                category=category,
                defaults={
                    'content': content,
                    'priority': self.get_priority(),
                }
            )

    def get_cat(self, _id):
        cats = json.load(open("makarem_fa_data.json"))
        for c in cats:
            if c['CategoryID'] == _id:
                return c['Title']

    def compact_questions(self, data, footnote=''):
        try:
            content_str = ""
            contents = re.split(r'(مسأله \d+ـ)', data)
            for item in contents:
                item = item.strip()
                if not item:
                    continue

                if item.startswith('مسأله'):
                    content_str += f'<div class="q-header">{item}</div>'
                else:
                    content_str += f'<p class="q-answer">{item}</p>'

            if ft := footnote.strip():
                content_str += f"<hr>{ft}"

            return content_str

        except Exception as e:
            print(e)

    def get_priority(self):
        self.last_priority += 1
        return self.last_priority


Crawl().import_categories()
