import os

import json
import time
from django.db import models
from django.db.models import Q
import argparse
from utils.telegram_logger import telegram_logger

# اضافه کردن مسیر پروژه به sys.path

# تنظیم متغیر محیطی برای تنظیمات Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
import anthropic
# مطمئن شوید که مدل‌های لازم را import کرده‌اید
from apps.ahkam.models import Masael, MasaelCategory, MasaelContent
from dj_language.models import Language  # مطمئن شوید که مدل Language را ایمپورت کرده‌اید

parser = argparse.ArgumentParser(description='Translation Script for Masael Categories')
parser.add_argument('--lang_code', type=str, help='Target language code', required=True)
parser.add_argument('--lang_name', type=str, help='Target language name', required=True)
args = parser.parse_args()

target_language_code = args.lang_code
target_language_name = args.lang_name
root_categories = [2250, 2330]

# دیکشنری برای نگهداری معادل دسته‌بندی‌های انگلیسی و ازبکی
category_translation_map = {}

telegram_logger(f"Starting the translation process for Masael categories and content to {target_language_name}...")

# تابع برای ترجمه به زبان مقصد با استفاده از Anthropic Claude API
def translate_text(text, target_lang, translation_type):
    prompt = f"Translate this Masael {translation_type} into {target_language_name}. The translation should be accurate and culturally appropriate, considering the Islamic context. The goal is for the translation to be fluent and easily understandable by native {target_language_name} speakers. important: Please provide ONLY the translation without any extra text."

    client = anthropic.Anthropic(
        api_key="************************************************************************************************************",
    )
    message = client.messages.create(
        model="claude-3-5-sonnet-20240620",
        max_tokens=1024,
        messages=[
            {"role": "user", "content": f"{prompt} {text}"}
        ]
    )
    translated_text = message.content[0].text
    return translated_text

def translate_and_get_or_create_category(category):
    if category.id in category_translation_map:
        return category_translation_map[category.id]

    translated_name = translate_text(category.name, target_language_code, "category name")
    target_language_instance = Language.objects.get(code=target_language_code)  # بازیابی نمونه‌ی زبان از مدل Language

    # بررسی وجود دسته‌بندی معادل ازبکی
    translated_category, created = MasaelCategory.objects.get_or_create(
        slug=category.slug + '-' + target_language_code,
        defaults={
            'name': translated_name,
            'language': target_language_instance,  # استفاده از نمونه‌ی زبان
            'thumbnail': category.thumbnail,  # استفاده از همان تصویر بندانگشتی
            'content_type': category.content_type,  # نوع محتوا را حفظ کنید
            'is_active': category.is_active,
            'created_at': category.created_at
        }
    )

    if category.parent:
        translated_parent = translate_and_get_or_create_category(category.parent)
        translated_category.parent = translated_parent  # تنظیم والد برای دسته‌بندی
        translated_category.save()

    category_translation_map[category.id] = translated_category

    telegram_logger(f"Category '{category.name}' translated to '{target_language_name}' and saved.")
    return translated_category

def translate_and_create_masael_content(masael):
    translated_title = translate_text(masael.title, target_language_code, "title")
    target_language_instance = Language.objects.get(code=target_language_code)

    translated_masael, created = Masael.objects.get_or_create(
        title=translated_title,
        defaults={
            'priority': masael.priority,
            'content': None,
            'category': translate_and_get_or_create_category(masael.category),
            'created_at': masael.created_at,
            'updated_at': masael.updated_at
        }
    )

    if created:
        telegram_logger(f"Masael '{masael.title}' translated to '{target_language_name}' and saved with new content.")
    else:
        telegram_logger(f"Masael '{masael.title}' already exists in '{target_language_name}', skipping creation.")

    for content in masael.contents.all():
        translated_header = translate_text(content.header, target_language_code, "header") if content.header else None
        translated_text = translate_text(content.text, target_language_code, "text")
        translated_answer = translate_text(content.answer, target_language_code, "answer") if content.answer else None

        MasaelContent.objects.create(
            masael=translated_masael,
            header=content.header,
            text=content.text,
            answer=translated_answer,
            priority=content.answer
        )
        telegram_logger(f"Translated and saved new Masael content for '{masael.title}' to '{target_language_name}'.")

# استخراج دسته‌بندی‌ها و محتواهای مرتبط به زبان انگلیسی
def get_all_subcategories(root_categories):
    all_categories = []
    # گرفتن دسته‌بندی‌های زبان مورد نظر
    all_categories = MasaelCategory.objects

    def collect_subcategories(categories):
        subcategories = []
        for category in categories:
            subcategories.append(category)
            # یافتن دسته‌بندی‌های زیرمجموعه
            child_categories = all_categories.filter(parent=category)
            subcategories.extend(collect_subcategories(child_categories))
        return subcategories

    # آغاز بازگشتی از ریشه‌ها
    root_categories_objects = all_categories.filter(id__in=root_categories)
    all_categories.extend(collect_subcategories(root_categories_objects))

    return all_categories

all_subcategories = get_all_subcategories(root_categories)
# ترجمه دسته‌بندی‌ها و محتوا و ذخیره در دیکشنری
for category in all_subcategories:
    translate_and_get_or_create_category(category)

masaels = Masael.objects.filter(category__in=all_subcategories)
for masael in masaels:
    translate_and_create_masael_content(masael)


with open(f'category_translation_map_{target_language_code}.json', 'w', encoding='utf-8') as f:
    json.dump({str(k): v.id for k, v in category_translation_map.items()}, f, ensure_ascii=False, indent=4)

tg_message = f"Translation process completed for Masael categories and content to {target_language_name}."
telegram_logger(tg_message)
print(tg_message)
