import json
import os
import re
import sys
import time

import requests
from bs4 import BeautifulSoup

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

headers = {
    'User-Agent': 'Mozilla/5.0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
}


class Crawl:
    category_name = 'آیت الله خمینی'
    language_id = 69

    def parse(self, link: str):
        if link.startswith('http'):
            print('DOWNLOADING: ', link)
            html = requests.get(link).text
        else:
            html = link
        return BeautifulSoup(html, "html.parser")

    def parse_ajax(self, _id):
        time.sleep(1)
        print('DOWNLODING:', _id)
        return requests.post(
            'https://www.leader.ir/ajax/book',
            data={'parent': str(_id), 'catid': '2'},
            headers=headers
        ).json()

    def parse_ahkam(self, content):
        html = self.parse(content)
        if ss := html.findAll(['h5', 'p']):
            ssc = ""
            if len(ss) > 2:
                for i in ss[2:]:
                    ssc += i.get_text() + '\n'
            else:
                for i in ss[1:]:
                    ssc += i.get_text() + "\n"

            if sss := re.split(r'(\d+\s)', ssc):
                return self.compact_questions(sss)

            elif xsd := re.split(r"(مسألة\s?\d+)", ssc.strip()):
                return self.compact_questions(xsd)
            else:
                return ssc
        return ''

    def parse_row(self, row):
        if row['nodetype'] == 'F':
            data = {
                'title': row['title'],
                'body': row['body'],
                'data': []
            }
            for j in self.parse_ajax(row['id'])[1]['data']:
                data['data'].append(self.parse_row(j))
            return data
        else:
            return {
                'title': row['title'],
                'body': row['body']
            }

    def crawl(self):
        page = self.parse("https://www.leader.ir/fa/book/2")
        result = {}

        for category in page.select('ul.level0 > ul > li'):
            category_name = category.select_one('span').get_text(strip=True)
            cat_id = category.get('id')
            data = self.parse_ajax(cat_id)
            if not result.get(category_name):
                result[category_name] = []
            for row in data[1]['data']:
                x = self.parse_row(row)
                result[category_name].append(x)

                with open("khomeini_fa.json", "w") as f:
                    json.dump(result, f, indent=4, ensure_ascii=False)

    def create_category(self, name, parent=None):
        from django.contrib.contenttypes.models import ContentType
        if type(parent) is str:
            parent = self.create_category(parent)

        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(
                    name=name, content_type=content_type, language_id=self.language_id
            ).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent or MasaelCategory.objects.get(name=self.category_name)
                )
        except Exception as e:
            print(e, name)

    def compact_questions(self, data):
        content_str = ""
        for we in data:
            if we := we.strip():
                if len(we) > 10:
                    content_str += f'<p class="q-answer">{we.strip()}</p>'
                else:
                    content_str += f'<div class="q-header">{we.strip()}</div>'
        return content_str

    def import_to_database(self):
        def insert_row(row, cat=None):
            if len(row.get('data', [])) > 1:
                cat = self.create_category(row['title'], cat)
                for r in row['data']:
                    insert_row(r, cat)
            else:
                self.create_obj(row, cat)

        data = json.load(open("khomeini_fa.json"))
        for title, rows in data.items():
            print(title)
            for row in rows:
                insert_row(row, title)

    def get_priority(self):
        from apps.ahkam.models import Masael
        try:
            return Masael.objects.filter(category__language__id=6).last().priority + 1
        except Exception:
            return 1

    def create_obj(self, row, cat):
        from apps.ahkam.models import Masael
        category = self.create_category(cat or self.category_name)
        content = self.compact_questions(row['body'])
        Masael.objects.update_or_create(
            title=row['title'],
            category=category,
            defaults={
                'content': content,
                'priority': self.get_priority(),
            }
        )


Crawl().import_to_database()
