import json
import os
import requests
from bs4 import BeautifulSoup
import re

headers = {
    'User-Agent': 'Mozilla/5.0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
}


class Crawl:
    base_url = 'https://www.leader.ir/ar/book/15/%D8%AA%D8%AD%D8%B1%D9%8A%D8%B1-%D8%A7%D9%84%D9%88%D8%B3%D9%8A%D9%84%D8%A9'
    language_id = 2
    category_name = "Ayatollah Khamenei Tahrir Arabic"
    filename = "khamenei_tahrir_ar.json"

    def parse(self, link: str):
        print('DOWNLOADING: ', link)
        html = requests.get(link).text
        return BeautifulSoup(html, "html.parser")

    def parse_ajax(self, _id):
        print('DOWNLODING:', _id)
        return requests.post(
            'https://www.leader.ir/ajax/book',
            data={'parent': str(_id), 'catid': '13'},
            headers=headers
        ).json()

    def parse_ahkam(self, content, category):
        try:
            text = ""
            if heading := content.select_one('.headline'):
                print('removing ->', heading)
                heading.replaceWith()

            if div := content.select_one('div.details'):
                text = div.get_text(strip=True)
            else:
                for j in content:
                    text += j.get_text(strip=True)
            # for p in content.find_all(['p', 'h5', 'div']):
            #     if txt := p.get_text(strip=True):
            #         text += txt

            if pre := re.match(r'.+?(?=مسألة[\d\s]+)', text):
                pre = pre[0]

            parts = re.findall(r'(مسألة\s*\d+((?!مسألة\s*\d+)[\s\S])*)', text)
            parts = [j[0] for j in parts]
            if not parts and not pre:
                parts.insert(0, text)
            else:
                parts.insert(0, pre)
            return parts

        except Exception as e:
            print(e)

    def crawl(self):
        page = self.parse(self.base_url)
        result = {}

        for category in page.select('ul.level0 > li'):
            category_name = category.select_one('span').get_text(strip=True)
            result[category_name] = {}

            if category.select_one('i.doc'):
                result[category_name]['type'] = 'single'
                result[category_name]['contents'] = self.parse_ahkam(category, category_name)

            else:
                _id = category.select_one('a').get('href').split('=')[1]
                root, sections, tchain = self.parse_ajax(_id)
                result[category_name]['type'] = 'many'
                result[category_name]['contents'] = []

                if root['root']:
                    result[category_name]['contents'].append({
                        'title': category_name,
                        'data': self.parse_ahkam(BeautifulSoup(root['root'], "html.parser"), category_name)
                    })

                for hokm in sections['data']:
                    if hokm["body"]:
                        result[category_name]['contents'].append({
                            'title': hokm['title'],
                            'data': self.parse_ahkam(BeautifulSoup(hokm['body'], "html.parser"), category_name)
                        })
                    else:
                        print('ok')
                        data = []
                        _root, _sections, _tchain = self.parse_ajax(hokm['id'])
                        for _hokm in _sections['data']:
                            if _hokm["body"]:
                                data.append({
                                    'title': _hokm['title'],
                                    'data': self.parse_ahkam(BeautifulSoup(_hokm['body'], "html.parser"), category_name)
                                })

                        result[category_name]['contents'].append({
                            'title': hokm['title'],
                            'items': data,
                        })

        with open(self.filename, "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def create_category(self, name, parent=None):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent or MasaelCategory.objects.get(name=self.category_name)
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, data):
        content_str = ""

        for item in data:
            if q := re.search(r'(^مسألة\s*\d*-)([\w\s\W\S]*)', item):
                q = q.groups()
                if len(q) == 2:
                    content_str += f'<div class="q-header">{q[0].replace("-","").strip()}</div><p class="q-text">{q[1]}</p>'
                else:
                    print('shit')
            else:
                content_str += f"<p class='q-text'>{item}</p>"

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory

        with open(self.filename) as f:
            ahkam = json.load(f)

        for category, values in ahkam.items():
            if values['type'] == 'many':
                cat = self.create_category(category)
                for content in values['contents']:
                    if content.get('items'):
                        for item in content['items']:
                            Masael.objects.create(
                                title=item['title'],
                                category=self.create_category(content['title'], cat),
                                content=self.compact_questions(item['data']),
                                priority=self.get_priority(),
                            )
                    else:
                        Masael.objects.create(
                            title=content['title'],
                            category=cat,
                            content=self.compact_questions(content['data']),
                            priority=self.get_priority(),
                        )
            else:
                Masael.objects.create(
                    title=category,
                    category=MasaelCategory.objects.get(name=self.category_name),
                    content=self.compact_questions(values['contents']),
                    priority=self.get_priority(),
                )

    def get_priority(self):
        from apps.ahkam.models import Masael
        try:
            return Masael.objects.filter(category__language__id=6).last().priority + 1
        except Exception:
            return 1

    def load_django(self):
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
        from django.core.wsgi import get_wsgi_application
        application = get_wsgi_application()
        return application


Crawl().load_django()
Crawl().import_to_database()
