import json
import os
import re

from bs4 import BeautifulSoup

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()


class Crawl:
    language_id = 9
    category_name = "Ayatollah Khameni Esteftaat Russian"
    base_url = "khameni_estef_ru.html"
    filename = "khameni_estef_ru.json"
    last_priority = 0

    def parse(self, html: str):
        return BeautifulSoup(html, "html.parser")

    def escape_title(self, title):
        q = r"[\s-]*".join([i for i in title.replace(' ', '')])
        for i in ['(', ')', '?']:
            q = q.replace(i, fr'\{i}')
        return q

    def find_title(self, page, title, parent):
        q = self.escape_title(title)
        t = [x for x in page.find_all(text=re.compile(f"^{q}$", re.I)) if x.name == parent or x.find_parent(parent)]
        if not t or len(t) > 1:
            print(page.find_all(text=re.compile(f"^{q}$", re.I)))
            return
        return t[0].parent

    def get_tags_between(self, elem1):
        if elem1.text == 'МОЛИТВА':
            elem2 = elem1.find_next('div')
        else:
            elem2 = elem1.find_next(['h1', 'h3', 'h4'])

        tags = ""
        for e in elem1.find_all_next(['p', 'sub']):
            if e.sourcepos >= elem2.sourcepos:
                break

            tags += str(e)

        string = []
        for i in self.parse(tags):
            if i.name == 'sub':
                string.append(f"<sub>{i.text}</sub>")
            else:
                string.append(i.text)

        return "<br>".join(string)

    def extract_parts(self, content):
        j = re.findall(r'(Вопрос\s*[\W\d]+:((?!Вопрос\s?[\W\d]+:)[\s\S])*)', content, re.I | re.M)
        parts = []
        for i in j:
            grp = re.search(r'Вопрос\s*№?\s*(\d+):\s*([\w\s\S]+)(Ответ[\S\W]+)', i[0], re.I)
            if not grp or len(grp.groups()) < 3:
                print('dc')

            parts.append(grp.groups())

        return parts

    def crawl(self):
        page = self.parse(open(self.base_url).read().replace('\n', ' '))
        result = {}
        for title in page.select('p.a3'):
            t = title.get_text(strip=True)
            result[t] = []
            for el in title.find_all_next(['h1', 'p']):
                if not el.get_text(strip=True):
                    continue

                if 'a3' in el.get('class', ''):
                    break
                if el.name == 'h1':
                    et = el.get_text(strip=True)
                    result[t].append({
                        'title': et,
                        'contents': ""
                    })
                    continue
                if not len(result[t]):
                    result[t].append({
                        'title': t,
                        'contents': el.get_text()
                    })

                else:
                    result[t][-1]['contents'] += el.get_text(strip=True)

        for i, v in result.items():
            for j in v:
                j['contents'] = self.extract_parts(j['contents'])

        with open(self.filename, "w") as f:
            json.dump(result, f, ensure_ascii=False, indent=4)

    def create_category(self, name, parent=None):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent or MasaelCategory.objects.get(name=self.category_name)
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, data: list):
        content_str = ""
        for item in data:
            html = f'<div class="q-header">Вопрос {item[0]}</div><p class="q-text">{item[1]}</p><p class="q-answer">{item[2]}</p>'
            content_str += html

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael

        ahkams = json.load(open(self.filename))

        for k, v in ahkams.items():
            cat = self.create_category(k)
            for i in v:
                Masael.objects.create(
                    category=cat,
                    priority=self.get_priority(),
                    title=i['title'],
                    content=self.compact_questions(i['contents'])
                )

    def get_priority(self):
        self.last_priority += 1
        return self.last_priority


Crawl().import_to_database()
