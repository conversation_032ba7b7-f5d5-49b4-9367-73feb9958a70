import json
import os
import re

import requests
from bs4 import BeautifulSoup

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()


class Crawl:
    language_id = 9
    category_name = "Ayatu<PERSON>"
    base_url = "https://www.makarem.ir/main.aspx?lid=5&typeinfo=30&catid=4286"
    filename = "makarem_fa.json"
    last_priority = 0

    def parse(self, link: str):
        print(link)
        html = requests.get(link).text
        return BeautifulSoup(html, "html.parser")

    def grab(self, catid):
        # https://www.makarem.ir/main.aspx?lid=5&typeinfo=21&catid=4281
        # test for multi page

        data = []
        link = f"https://www.makarem.ir/main.aspx?lid=5&typeinfo=21&catid={catid}"
        page = self.parse(link)
        sections = page.select('#Content_JS_ContentIC .NewsOneColumnBox')
        for sec in sections:
            data.append({
                'title': sec.select_one('a.TitleNOCB').get_text(strip=True),
                'que': sec.select_one('article.SummaryNOCB').get_text(strip=True),
                'ans': sec.select_one('article.AnswerNOCB').get_text(strip=True),
            })

        page_links = page.find('section', attrs={'class': 'pagination'}).find_all('li', attrs={'class': ''})
        for _link in [p.get('href') for p in page_links]:
            if not _link:
                continue

            _link = 'https://www.makarem.ir' + _link
            page = self.parse(_link)
            sections = page.select('#Content_JS_ContentIC .NewsOneColumnBox')
            for sec in sections:
                data.append({
                    'title': sec.select_one('a.TitleNOCB').get_text(strip=True),
                    'que': sec.select_one('article.SummaryNOCB').get_text(strip=True),
                    'ans': sec.select_one('article.AnswerNOCB').get_text(strip=True),
                })

        return data

    def crawl(self):
        result = {}
        treedata: list = json.load(open('makarem_ru_data.json'))

        for ii, i in enumerate(treedata):
            if i['pid'] == 0:
                # first level
                result[i['id']] = {'title': i['t'], 'sub': {}}
                continue

            def xter(x, i):
                if i['pid'] in x:
                    x[i['pid']]['sub'][i['id']] = {'title': i['t'], 'sub': {}}
                else:
                    if x.get('sub'):
                        xter(x['sub'], i)
                    else:
                        for k in x:
                            xter(x[k]['sub'], i)

            # iterate over kids and build tree structure
            xter(result, i)

        for x in treedata:
            # check if all ids are in result tree by regex
            s = str(result)
            if not re.search(rf"{x['id']}", s, re.M):
                raise Exception(f"{x['id']} not found in result")

        def grab_just_last_items_iterator(o, oc):
            if o.get('sub'):
                for c, cc in o.get('sub').items():
                    grab_just_last_items_iterator(cc, c)
            else:
                o['data'] = self.grab(oc)

        for iv, v in result.items():
            # iterate over items and grab content just the last kid of the tree
            grab_just_last_items_iterator(v, iv)

        with open(self.filename, "w") as f:
            json.dump(result, f, ensure_ascii=False, indent=4)

    def create_category(self, name, parent=None):
        from django.contrib.contenttypes.models import ContentType
        from apps.ahkam.models import MasaelCategory

        try:

            if parent:
                parent = self.create_category(parent)

            _parent = MasaelCategory.objects.get(name=self.category_name, language_id=self.language_id)
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent or _parent,
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def import_to_database(self):
        data = json.load(open(self.filename))

        # first level
        for k, v in data.items():
            if v.get('sub'):
                cat1 = self.create_category(v['title'])
                # second level
                for k, v2 in v['sub'].items():
                    if v2.get('sub'):
                        cat2 = self.create_category(v2['title'], parent=cat1)
                        # third level
                        for k, v3 in v2['sub'].items():
                            if v3.get('sub'):
                                cat3 = self.create_category(v3['title'], parent=cat2)
                                # fourth level
                                for k, v4 in v3['sub'].items():
                                    if v4.get('sub'):
                                        cat4 = self.create_category(v4['title'], parent=cat3)
                                        # fifth level
                                        for k, v5 in v4['sub'].items():
                                            if v5.get('sub'):
                                                cat5 = self.create_category(v5['title'], parent=cat4)
                                                # sixth level: last level
                                                for k, v6 in v5['sub'].items():
                                                    self.create_obj(v6, cat5)
                                            else:
                                                self.create_obj(v5, cat4)

                                    else:
                                        self.create_obj(v4, cat3)

                            else:
                                self.create_obj(v3, cat2)

                    else:
                        self.create_obj(v2, cat1)
            else:
                self.create_obj(v, self.create_category(self.category_name))

    def create_obj(self, d, cat):
        from apps.ahkam.models import Masael

        Masael.objects.create(
            title=d['title'],
            content=self.compact_questions(d['data']),
            category=cat,
            priority=self.get_priority(),
        )

    def compact_questions(self, data):
        content_str = ""

        for item in data:
            question = f'<div class="q-header">{item["title"]}</div><p class="q-text">{item["que"]}</p>'
            answer = f'<p class="q-answer">{item["ans"]}</p>'
            content_str += question + answer

        return content_str

    def get_priority(self):
        self.last_priority += 1
        return self.last_priority


Crawl().import_to_database()
