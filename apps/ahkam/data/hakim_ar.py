import json
import os
import re, sys
import time

import requests
from bs4 import BeautifulSoup

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()


class Crawl:
    language_id = 2
    category_name = "آیت الله حکیم"
    base_url = "https://www.alhakeem.com/ar/questions"
    filename = "hakim_ar.json"
    last_priority = 0

    def parse(self, url: str):
        print(url)
        time.sleep(0.5)
        html = requests.get(url).text
        return BeautifulSoup(html, "html.parser")

    def crawl(self):
        cats = self.import_categories()
        for p, items in cats.items():
            for title, link in items.items():
                boxes = []
                for pn in range(1, 300):
                    page = self.parse(link + f"/{pn}")

                    if res := page.select('div.pull-left div.box-rounded.box-shadow'):
                        boxes += res
                    else:
                        break

                self.create_obj(title, p, boxes)

    def create_category(self, name, parent=None):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(
                    name=name, content_type=content_type, language_id=self.language_id
            ).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent or MasaelCategory.objects.get(name=self.category_name)
                )
        except Exception as e:
            print(e, name)

    def import_categories(self):
        page = self.parse("https://www.alhakeem.com/ar/questions/1")
        cats = {}
        for cat in page.select('.easy-tree > ul > li'):
            i = cat.find('a').get_text()
            cats[i] = {}
            for li in cat.select('li a'):
                cats[i][li.get_text(strip=True)] = li.attrs['href']

        for parent, items in cats.items():
            self.create_category(parent)

        return cats

    def create_obj(self, title, category, content):
        from apps.ahkam.models import Masael

        category = self.create_category(category)
        content = self.compact_questions(content)

        print(title)

        Masael.objects.update_or_create(
            title=title,
            category=category,
            defaults={
                'content': content,
                'priority': self.get_priority(),
            }
        )

    def compact_questions(self, data):
        content_str = ""
        for content in data:
            question = content.select_one('div.question_content').get_text(strip=True)
            answer = content.select_one('div.question_answer').get_text(strip=True)
            content_str += f"<div class='q-header'>{question}</div>"
            content_str += f"<p class='q-answer'>{answer}</p>"

        return content_str

    def get_priority(self):
        self.last_priority += 1
        return self.last_priority


Crawl().crawl()
