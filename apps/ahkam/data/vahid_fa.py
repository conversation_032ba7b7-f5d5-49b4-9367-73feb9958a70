import json
import os
import re
import sys
import time

import requests
from bs4 import BeautifulSoup

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
from apps.ahkam.models import Masael
from django.contrib.contenttypes.models import ContentType
from apps.ahkam.models import MasaelCategory

headers = {
    'User-Agent': 'Mozilla/5.0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
}

"https://www.musicdays.ir/%D8%AF%D8%A7%D9%86%D9%84%D9%88%D8%AF-%D8%A2%D9%87%D9%86%DA%AF-%D8%A8%DB%8C%D8%B3-%D8%AF%D8%A7%D8%B1/"
"https://golsarmusic.ir/%D8%A2%D9%87%D9%86%DA%AF-%D8%A8%D8%A7%D8%B4%DA%AF%D8%A7%D9%87-%D8%AC%D8%AF%DB%8C%D8%AF/"


class Crawl:
    category_name = 'آیت الله وحید خراسانی'
    language_id = 69

    def parse(self, link: str):
        time.sleep(1)
        if link.startswith('http'):
            print('DOWNLOADING: ', link)
            html = requests.get(link).text
        else:
            html = link
        return BeautifulSoup(html, "html.parser")

    def parse_ahkam(self, page):
        content_str = ""
        for section in page.select('section'):
            ps = section.select('p')
            if len(ps) > 2:
                print(ps)

            title = ps[0].get_text(strip=True)
            content = ps[1].get_text()

            content_str += f'<div class="q-header">{title}</div>'
            content_str += f'<p class="q-text">{content}</p>'

        return content_str

    def crawl_categories(self):
        page = self.parse(
            "http://wahidkhorasani.com/%D9%81%D8%A7%D8%B1%D8%B3%DB%8C/%D9%85%D8%B3%D8%A7%D8%A6%D9%84-%D8%B4%D8%B1%D8%B9%DB%8C/%D9%85%D8%AD%D8%AA%D9%88%D8%A7/218_%D8%AA%D9%88%D8%B6%DB%8C%D8%AD-%D8%A7%D9%84%D9%85%D8%B3%D8%A7%D8%A6%D9%84/376_%D9%86%D9%85%D8%A7%D8%B2%D9%87%D8%A7%D9%89-%D9%88%D8%A7%D8%AC%D8%A8")
        categories = []

        def iter_cat(item, parent):
            cats = []
            for li in item.select('li'):
                if li.find_all('ul', recursive=False):
                    a = li.select_one('a')
                    cats.append({
                        'name': a.get_text(strip=True),
                        'link': a.get('href'),
                        'parent': parent.select_one('a').get_text(strip=True),
                        'is_parent': True,
                    })
                    cats += iter_cat(li, item)
                else:
                    a = li.select_one('a')
                    cats.append(
                        {
                            'name': a.get_text(strip=True),
                            'link': a.get('href'),
                            'parent': parent.select_one('a').get_text(strip=True),
                        }
                    )
            return cats

        for ul in page.select('div.lists ul'):
            print(ul.select_one('a').get_text(strip=True))
            categories += iter_cat(ul, ul)

        json.dump(categories, open("vahid_fa.json", "w"), ensure_ascii=False, indent=4)

    def import_categories(self):
        # MasaelCategory.objects.filter(name=self.category_name).delete()

        categories = json.load(open("vahid_fa.json"))
        for c in categories:
            if c.get('is_parent'):
                if c['name'] == c['parent']:
                    self.create_category(c['name'], c['parent'])
                else:
                    self.create_category(c['name'], c['parent'])

    def crawl(self):

        categories = json.load(open("vahid_fa.json"))
        for c in categories:
            if c.get('is_parent'):
                continue
            _id = re.search("\d+", c['link']).group()
            page = self.parse(
                f"http://wahidkhorasani.com/%D9%81%D8%A7%D8%B1%D8%B3%DB%8C/Fatwa/List?SourceNo=218&TocNo={_id}&SearchText=")

            page_content = self.parse_ahkam(page)
            self.create_obj(page_content, c['name'], c['parent'])

    def create_category(self, name, parent=None):
        if type(parent) is str:
            parent = self.create_category(parent)

        try:
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cc := MasaelCategory.objects.filter(name=name, content_type=content_type, language_id=self.language_id):
                for c in cc:
                    if c.get_root().name == self.category_name:
                        return c

            return MasaelCategory.objects.create(
                language_id=self.language_id,
                name=name,
                content_type=content_type,
                parent=parent or MasaelCategory.objects.get(name=self.category_name)
            )
        except Exception as e:
            print(e, name)

    def compact_questions(self, data):
        content_str = ""
        for we in data:
            if we := we.strip():
                if len(we) > 10:
                    content_str += f'<p class="q-answer">{we.strip()}</p>'
                else:
                    content_str += f'<div class="q-header">{we.strip()}</div>'
        return content_str

    def import_to_database(self):
        def insert_row(row, cat=None):
            if len(row.get('data', [])) > 1:
                cat = self.create_category(row['title'], cat)
                for r in row['data']:
                    insert_row(r, cat)
            else:
                self.create_obj(row, cat)

        data = json.load(open("vahid_fa_.json"))
        for title, rows in data.items():
            for row in rows:
                insert_row(row, title)

    def get_priority(self):
        from apps.ahkam.models import Masael
        try:
            return Masael.objects.filter(category__language__id=6).last().priority + 1
        except Exception:
            return 1

    def create_obj(self, content, title, cat):
        if cat == title:
            category = self.create_category(self.category_name)
        else:
            category = self.create_category(cat)

        Masael.objects.update_or_create(
            title=title,
            category=category,
            defaults={
                'content': content,
                'priority': self.get_priority(),
            }
        )


Crawl().crawl()
