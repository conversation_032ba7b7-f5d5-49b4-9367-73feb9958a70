import json
import os
import requests
from bs4 import BeautifulSoup
import re


class Crawl:
    def parse(self, link: str):
        print('DOWNLOADING: ', link)
        html = requests.get(link).text
        return BeautifulSoup(html, "html.parser")

    def strips(self, t):
        return re.sub(" +", ' ', t).strip()

    def extract_part_answers(self, part):
        ans_text = ""
        for ans_elem in part.next_siblings:
            if ans_elem.name == "b":
                break
            ans_text += ans_elem.text

        return self.strips(ans_text)

    def extract_sections_parts(self, title):
        html = ""
        data = {
            'title': title.get_text(strip=True),
            'contents': []
        }

        for elem in title.next_siblings:
            if elem.name == 'h3':
                break
            html += str(elem)

        for j in BeautifulSoup(html, "html.parser").select('b'):
            data['contents'].append({'que': self.strips(j.text.replace('\n', '')), 'ans': self.extract_part_answers(j)})

        return data

    def crawl(self):
        result = []
        for i in range(2, 100):
            page_num = "0" * (3 - len(str(i))) + str(i)
            page = self.parse(f"http://lankarani.com/azr/res/res1/{page_num}.php")
            titles = page.select('h3')
            if len(titles) == 1:
                result.append({
                    'type': 'single',
                    'title': titles[0].get_text(strip=True),
                    'contents': [
                        {
                            'que': self.strips(part_title.text.replace('\n', '')),
                            'ans': self.extract_part_answers(part_title),
                        }
                        for part_title in page.select('b')
                    ],
                })
            else:
                data = {
                    'type': 'many',
                    'category': page.find('h3').get_text(strip=True),
                    'contents': []
                }
                for title in titles:
                    data['contents'].append(self.extract_sections_parts(title))

                result.append(data)

        with open("ahkam_lankarani_en.json", "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def create_category(self, name):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type, language_id=18).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=18,
                    name=name,
                    content_type=content_type,
                    parent=MasaelCategory.objects.get(name="Ayətullah lənkərani")
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, data):
        content_str = ""
        for item in data:
            content_str += f'<div class="q-header">{item["que"]}</div><p class="q-text">{item["ans"]}</p>'

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory

        with open("ahkam_lankarani_en.json") as f:
            ahkams = json.load(f)

        for ahkam in ahkams:
            if ahkam['type'] == 'many':
                cat = self.create_category(ahkam['category'])
                for content in ahkam['contents']:
                    Masael.objects.create(
                        title=content['title'],
                        category=cat,
                        content=self.compact_questions(content['contents']),
                        priority=self.get_priority(),
                    )
            else:
                Masael.objects.create(
                    title=ahkam['title'],
                    category=MasaelCategory.objects.get(name="Ayətullah lənkərani"),
                    content=self.compact_questions(ahkam['contents']),
                    priority=self.get_priority(),
                )

    def get_priority(self):
        from apps.ahkam.models import Masael, MasaelCategory
        kids = MasaelCategory.objects.get(name="Ayətullah lənkərani").get_descendants()
        try:
            return Masael.objects.filter(category__in=kids).last().priority + 1
        except Exception:
            return 1

    def check(self):
        with open("ahkam_lankarani_en.json") as f:
            ahkams = json.load(f)

        f = open("may_contains_lots_of_new_lines.txt", "w")

        for ahkam in ahkams:
            if ahkam['type'] == 'many':
                for content in ahkam['contents']:
                    for item in content['contents']:
                        if item['ans'].count('<br>') > 4:
                            f.write(content['title'].strip())
                            f.write('\n')

            else:
                for item in ahkam['contents']:
                    if item['ans'].count('<br>') > 4:
                        f.write(ahkam['title'].strip())
                        f.write('\n')


Crawl().check()
