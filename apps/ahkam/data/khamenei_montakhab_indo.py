import json
import os
import requests
from bs4 import BeautifulSoup
import re

headers = {
    'User-Agent': 'Mozilla/5.0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
}


class Crawl:
    base_url = 'khamenei_montakhab_indo.html'
    language_id = 64
    category_name = "Ayatollah Khamenei Montakhab Indo"
    filename = "khamenei_montakhab_indo.json"
    last_priority = 0

    def parse(self, html: str):
        return BeautifulSoup(html, "html.parser")

    def parse_ahkam(self, content, category):
        try:
            text = ""
            if heading := content.select_one('.headline'):
                print('removing ->', heading)
                heading.replaceWith()

            if div := content.select_one('div.details'):
                text = div.get_text(strip=True)
            else:
                for j in content:
                    text += j.get_text(strip=True)
            # for p in content.find_all(['p', 'h5', 'div']):
            #     if txt := p.get_text(strip=True):
            #         text += txt

            if pre := re.match(r'.+?(?=مسألة[\d\s]+)', text):
                pre = pre[0]

            parts = re.findall(r'(مسألة\s*\d+((?!مسألة\s*\d+)[\s\S])*)', text)
            parts = [j[0] for j in parts]
            if not parts and not pre:
                parts.insert(0, text)
            else:
                parts.insert(0, pre)
            return parts

        except Exception as e:
            print(e)

    def crawl(self):
        page = self.parse(open(self.base_url).read().replace('\n', ''))
        result = {}
        for ptag in page.select_one('body'):

            footnote = ""
            if x := ptag.select_one('.Footnote_20_anchor'):
                footnote = x.get('title')
                x.replaceWith()

            if 'P233' in ptag.get('class', []):
                t = ptag.get_text(strip=True)
                result[t] = {'content': [], 'footnote': footnote}
                continue

            if footnote and result[list(result)[-1]]['footnote']:
                result[list(result)[-1]]['footnote'] += "<br>" + footnote
            elif footnote:
                result[list(result)[-1]]['footnote'] += x.get('title')

            if tt := ptag.get_text(strip=True):
                if len(result[list(result)[-1]]['content']) and not tt.startswith('Masalah'):
                    result[list(result)[-1]]['content'][-1] += f"\n{tt}"
                else:
                    result[list(result)[-1]]['content'].append(tt)

        with open(self.filename, "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def create_category(self, name, parent=None):
        from django.contrib.contenttypes.models import ContentType
        try:
            from apps.ahkam.models import MasaelCategory
            content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
            if cat := MasaelCategory.objects.filter(name=name, content_type=content_type,
                                                    language_id=self.language_id).first():
                return cat
            else:
                return MasaelCategory.objects.create(
                    language_id=self.language_id,
                    name=name,
                    content_type=content_type,
                    parent=parent or MasaelCategory.objects.get(name=self.category_name)
                )
        except Exception as e:
            print(e, name)
            raise Exception

    def compact_questions(self, data):
        content_str = ""

        for item in data['content']:
            if q := re.search(r'(Masalah\s*\d+)[):]([\w\s\S]+)', item):
                q = q.groups()
                if len(q) == 2:
                    content_str += f'<div class="q-header">{q[0]}</div><p class="q-text">{q[1]}</p>'
                else:
                    print('shit')
            else:
                content_str += f"<p class='q-text'>{item}</p>"

        if data['footnote']:
            content_str += f"<hr><p class='q-text'>{data['footnote']}</p>"

        return content_str

    def import_to_database(self):
        from apps.ahkam.models import Masael, MasaelCategory
        ahkam: dict = json.load(open(self.filename))
        for k, v in ahkam.items():
            if v['content']:
                Masael.objects.create(
                    title=k.title(),
                    content=self.compact_questions(v),
                    category=self.create_category(self.category_name),
                    priority=self.get_priority(),
                )

    def get_priority(self):
        self.last_priority += 1
        return self.last_priority

    def load_django(self):
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
        from django.core.wsgi import get_wsgi_application
        application = get_wsgi_application()
        return application


Crawl().load_django()
Crawl().import_to_database()
