from dj_category.field import Category<PERSON>ode<PERSON><PERSON>ield
from dj_category.models import BaseCategory
from dj_language.field import LanguageField
from django.db import models
from django.utils.translation import gettext_lazy as _
from limitless_dashboard.fields.summernote import SummernoteField
from nwh_seo.fields import SeoGenericRelation


class Masael(models.Model):
    title = models.CharField(verbose_name=_('title'), max_length=255)
    priority = models.PositiveIntegerField(null=True, blank=True, verbose_name=_('priority'))
    content = SummernoteField(verbose_name=_('content'))
    category = CategoryModelField(
        'MasaelCategory', verbose_name=_('category'), related_name='masaels', on_delete=models.CASCADE, null=True,
    )
    translate_from = models.PositiveIntegerField(null=True, blank=True, verbose_name=_('translate_from'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('updated at'))
    seo_fields = SeoGenericRelation(verbose_name=_('seo fields'))

    def __str__(self):
        return self.title

    class Meta:
        ordering = ('priority', 'id',)


class MasaelCategory(BaseCategory):
    class Meta:
        proxy = True
        verbose_name_plural = _('Masael Categories')



class MasaelContent(models.Model):
    masael = models.ForeignKey('Masael', on_delete=models.CASCADE, related_name='contents')

    header = models.TextField(null=True, blank=True)
    text = models.TextField()
    answer = models.TextField(null=True, blank=True)
    translate_from = models.PositiveIntegerField(null=True, blank=True, verbose_name=_('translate_from'))

    priority = models.PositiveIntegerField(null=True, blank=True, verbose_name=_('priority'))


    def __str__(self):
        header_str = self.header[:10] if self.header else " "
        text_str = ' '.join(self.text.split()[:3])
        answer_str = ' '.join(self.answer.split()[:2]) if self.answer else "No Answer"
      
        return f"{header_str} | {text_str} | {answer_str}"


    class Meta:
        verbose_name = _('Masael Content')
        verbose_name_plural = _('Masael Contents')
        
        
class MarjaaGuide(models.Model):
    title = models.TextField(null=True, blank=True)              
    language = LanguageField(verbose_name=_('language'), null=True, blank=True)
    answer = models.TextField(null=True, blank=True)  
    priority = models.IntegerField(verbose_name=_('Priority'), default=0)


    def __str__(self):
        return self.title
    
    class Meta:
        verbose_name = _('Marjaa Guide')
        verbose_name_plural = _('Marjaah Guides')
        ordering = ['priority', '-id'] 
        
