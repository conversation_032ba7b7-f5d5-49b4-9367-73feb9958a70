# Generated by Django 3.2.25 on 2024-10-19 13:13

import dj_language.field
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
        ('ahkam', '0006_alter_masaelcontent_header'),
    ]

    operations = [
        migrations.CreateModel(
            name='MarjaaGuide',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.TextField(blank=True, null=True)),
                ('answer', models.TextField(blank=True, null=True)),
                ('priority', models.IntegerField(default=0, verbose_name='Priority')),
                ('language', dj_language.field.LanguageField(blank=True, default=69, limit_choices_to={'status': True}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language')),
            ],
            options={
                'verbose_name': 'Marja Guide',
                'verbose_name_plural': 'Marja Guides',
                'ordering': ['priority', '-id'],
            },
        ),
    ]
