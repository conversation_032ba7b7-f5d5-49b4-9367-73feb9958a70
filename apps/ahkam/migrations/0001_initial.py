# Generated by Django 3.2.5 on 2021-10-17 00:28

import dj_category.field
from django.db import migrations, models
import limitless_dashboard.fields.summernote


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('dj_category', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MasaelCategory',
            fields=[
            ],
            options={
                'verbose_name_plural': 'Masael Categories',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('dj_category.basecategory',),
        ),
        migrations.CreateModel(
            name='Masa<PERSON>',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='title')),
                ('content', limitless_dashboard.fields.summernote.SummernoteField(verbose_name='content')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('categories', dj_category.field.CategoryM2mModelField(limit_choices_to={'is_active': True}, related_name='masaels', to='ahkam.MasaelCategory', verbose_name='categories')),
            ],
        ),
    ]
