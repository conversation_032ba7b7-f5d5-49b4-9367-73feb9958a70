{% extends "admin/change_form.html" %}
{% load static %}
{% block scripts %}

    <script src="{% static "admin/panel/global_assets/js/plugins/editors/summernote/summernote.min.js" %}"></script>
    <script>


        var HelloButton = function (context) {
            var ui = $.summernote.ui;

            // create button
            var button = ui.button({
                contents: '<i class="fa fa-child"/> clear format',
                tooltip: 'clear format',
                click: function (e) {
                    var html = $('.note-editable').html();
                    $('.note-editable').html(html.replaceAll(/(<(?!(br|p|\/p|\/br)\s*\/?)[^>]+>)/g, ''))
                    $('.note-editable').find('*').removeAttr('style');
                    $('.note-editable').find('*').removeClass('MsoNormal');
                    $('.note-editable').css('font-family', 'arial');


                }
            });

            return button.render();   // return button as jquery object
        }

        $('.summernote').summernote({
            maxHeight: $(window).height() - 120,
            styleTags: [
                {
                    tag: 'div',
                    title: 'Part Title',
                    style: 'font-size:12px',
                    className: 'q-header',
                    value: 'div'
                }, {
                    tag: 'p',
                    title: 'Question',
                    style: 'font-size:12px',
                    className: 'q-text',
                    value: 'p'
                }, {
                    tag: 'p',
                    title: 'Answer',
                    style: 'font-size:12px',
                    className: 'q-answer',
                    value: 'p'
                }
            ],
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']],
                ['height', ['height']],
                ['mybutton', ['hello']]
            ],
            buttons: {
                hello: HelloButton
            },
            callbacks: {
                onPaste: function (e) {

                }
            }
        })


    </script>
    <style>
        div.q-header {
            width: 100%;
            background: #2991e8;
            border-radius: 15px;
            color: #fff;
            padding: 8px;
            font-size: 12px;
            text-align: center;
            margin: 8px 0;
        }

        p.q-text {
            color: #3e3e3e;
            font-size: 12px;
            text-align: left;
            justify-content: left;
            margin: 8px 0;
        }

        p.q-answer {
            color: #2991e8;
            font-size: 12px;
            text-align: left;
            justify-content: left;
            margin: 8px 0;
        }
    </style>
    {{ block.super }}
{% endblock %}
