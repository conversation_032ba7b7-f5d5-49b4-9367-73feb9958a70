from django.contrib.contenttypes.models import ContentType
from django.db.models import Subquery, OuterRef, Q
from django.core.cache import cache
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from rest_framework.generics import List<PERSON><PERSON>View
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .models import MasaelCategory
from .serializer import *
from ..mafatih.views.v1 import put_key_render


class MasaelView(ListAPIView):
    serializer_class = MasaelSerializer
    pagination_class = None
    permission_classes = (IsAuthenticated,)

    key = 'id'

    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        data = put_key_render(serializer.data, self.key)
        return Response(data)

    def get_queryset(self):
        """
            include english ahkam if language is german
        """
        if self.request.LANGUAGE_CODE == 'de':
            lang_filter = Q(category__language__code__in=[self.request.LANGUAGE_CODE, 'en'])
        elif self.request.LANGUAGE_CODE == 'sw':
            lang_filter = Q(category__language__code__in=[self.request.LANGUAGE_CODE, 'en'])
        else:
            lang_filter = Q(category__language__code=self.request.LANGUAGE_CODE)

        sub = MasaelCategory.objects.filter(parent=None, tree_id=OuterRef('category__tree_id')).values('id')[:1]

        return Masael.objects.filter(lang_filter).annotate(
            root_id=Subquery(sub)
        ).prefetch_related('category', )

class MasaelCategoriesView(ListAPIView):
    serializer_class = MasaelCategoriesSerializer
    pagination_class = None
    permission_classes = (IsAuthenticated,)

    @method_decorator(cache_page(60 * 60))  # Cache for 1 Hour
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)

    def get_queryset(self):
        """
            include english ahkam if language is german, swahili
        """
        if self.request.LANGUAGE_CODE == 'de':
            lang_filter = Q(language__code__in=['de', 'en'])
        elif self.request.LANGUAGE_CODE == 'sw':
            lang_filter = Q(language__code__in=['sw', 'en'])
        else:
            lang_filter = Q(language__code=self.request.LANGUAGE_CODE)

        return MasaelCategory.objects.filter(
            lang_filter,
            content_type=ContentType.objects.get_for_model(MasaelCategory, for_concrete_model=False),
            is_active=True,
            level=0,
        ).order_by('-order')


class MasaelViewV2(ListAPIView):
    serializer_class = MasaelSerializerV2
    pagination_class = None
    permission_classes = (IsAuthenticated,)

    key = 'id'

    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        cache_key = f"masael_view_{self.request.LANGUAGE_CODE}"
        cached_data = cache.get(cache_key)
        if cached_data:
            data = cached_data
        else:
            # If cache miss, get the data from the database
            serializer = self.get_serializer(queryset, many=True)
            data = put_key_render(serializer.data, self.key)

            # Cache the data with a timeout of 1 hour (3600 seconds)
            cache.set(cache_key, data, timeout=3600)
        return Response(data)
    
    def get_queryset(self):
        """
            include english ahkam if language is german
            and include contents
        """
        if self.request.LANGUAGE_CODE == 'de':
            lang_filter = Q(category__language__code__in=[self.request.LANGUAGE_CODE, 'en'])
        elif self.request.LANGUAGE_CODE == 'sw':
            lang_filter = Q(category__language__code__in=[self.request.LANGUAGE_CODE, 'en'])
        else:
            lang_filter = Q(category__language__code=self.request.LANGUAGE_CODE)

        sub = MasaelCategory.objects.filter(parent=None, tree_id=OuterRef('category__tree_id')).values('id')[:1]

        return Masael.objects.filter(lang_filter, category__is_active=True).annotate(
            root_id=Subquery(sub)
        ).only('id', 'priority', 'category', 'title').select_related('category').prefetch_related('contents', 'category')  
        
        
        
class MarjaaGuideListAPIView(ListAPIView):
    serializer_class = MarjaaGuideSerializer
    pagination_class = None 
    
    def get_queryset(self):
        language_code = self.request.LANGUAGE_CODE  
        return MarjaaGuide.objects.filter(language__code=language_code)