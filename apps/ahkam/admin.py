from ajaxdatatable.admin import AjaxDatatable
from dj_category.admin import BaseCategoryAdmin
from django.contrib import admin
from django.contrib.contenttypes.models import ContentType
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from nwh_seo.admin import SeoAdminInline
from dj_language.models import Language

from apps.mafatih.admin import CategoryLanguageFilter
from .models import <PERSON><PERSON><PERSON>, MasaelCategory, Ma<PERSON>elContent, MarjaaGuide



@admin.register(MarjaaGuide)
class MarjaaGuideAdmin(AjaxDatatable):
    list_display = ('title', 'language', 'priority')
    list_editable = ('priority',)
    search_fields = ('title', 'language', 'answer')
    list_filter = ('language',)  
    
    

class CategoryFilter(admin.SimpleListFilter):
    title = 'Category'
    parameter_name = 'category'

    def lookups(self, request, model_admin):
        res = MasaelCategory.objects.filter(
            is_active=True, content_type=ContentType.objects.get(model=MasaelCategory._meta.model_name)
        ).all()
        return [
            (r.id, r.name) for r in res
        ]

    def queryset(self, request, queryset):
        if category_id := request.GET.get(self.parameter_name):
            return queryset.filter(category_id=category_id)
        return queryset.all()


class AhkamCategoryLanguageFilter(admin.SimpleListFilter):
    title = 'Language'
    parameter_name = 'language'

    def lookups(self, request, model_admin):
        res = Language.objects.filter(status=True).all()
        return [
            (r.id, r.name) for r in res
        ]

    def queryset(self, request, queryset):
        if language_id := request.GET.get(self.parameter_name):
            return queryset.filter(category__language__id=language_id)
        return queryset.all()

class MasaelContentInline(admin.StackedInline):
    model = MasaelContent
    extra = 1  


@admin.register(Masael)
class AhkamAdmin(AjaxDatatable):
    change_form_template = 'admin/ahkam_change_form.html'
    list_display = ('title', '_category', '_language', 'updated_at', 'priority')
    list_filter = (AhkamCategoryLanguageFilter, CategoryFilter,)
    inlines = [MasaelContentInline, SeoAdminInline]
    search_fields = ('title', 'category__name')
    ordering = ('-id', 'priority')
    latest_by = 'created_at'
    readonly_fields = ('category',)

    @admin.display(description=_('Language'), ordering='category__language')
    def _language(self, obj):
        language = obj.category.language.name if obj.category else '-'
        html = "<small>%s</small><br>" % language
        return mark_safe(html)

    @admin.display(description=_('Category'), ordering='category')
    def _category(self, obj):
        if obj.category:
            cats = list(obj.category.get_ancestors(include_self=True).values_list('name', flat=True))
            # html = "<small>%s</small><br>"
            return mark_safe(", ".join(cats))
        return '-'

    def get_queryset(self, request):
        return self.model.objects.prefetch_related('category', 'category__language')


@admin.register(MasaelCategory)
class AhkamCategoryAdmin(BaseCategoryAdmin):
    pass
