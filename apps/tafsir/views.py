from rest_framework.generics import ListAP<PERSON>View, RetrieveAPIView
from rest_framework.permissions import IsAuthenticated

from .serializer import *

language_map = {
    'de': ['de', 'en'],
}


class TafsirBooksView(ListAPIView):
    serializer_class = TafsirBookSerializer
    pagination_class = None
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        lang = self.request.LANGUAGE_CODE
        qs = TafsirBook.objects.filter(status=True).order_by('-default_show').all()

        if langs := language_map.get(lang):
            return qs.filter(language__code__in=langs)

        return qs.filter(language__code=lang)


class TafsirVersesListView(ListAPIView):
    serializer_class = TafsirVersesSerializer
    pagination_class = None

    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        book_id = self.kwargs['book']
        surah = self.kwargs['surah']
        return VersesTafsir.objects.filter(book_id=book_id, surah__index=surah)


class TafsirVerseView(RetrieveAPIView):
    serializer_class = TafsirVersesSerializer
    pagination_class = None

    permission_classes = (IsAuthenticated,)

    def get_object(self):
        book_id = int(self.kwargs['book'])
        verse_id = int(self.kwargs['verse'])
        surah = int(self.kwargs['surah'])
        return VersesTafsir.objects.filter(
            book_id=book_id, surah__index=surah, from_verse__lte=verse_id, to_verse__gte=verse_id
        ).first()
