# Generated by Django 3.2.12 on 2022-06-12 10:41

import dj_language.field
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import filer.fields.image
import limitless_dashboard.fields.summernote


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
        ('quran', '0002_quranversetranslation_updated_at'),
        migrations.swappable_dependency(settings.FILER_IMAGE_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='VersesTafsir',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_verse', models.PositiveIntegerField(verbose_name='from verse')),
                ('to_verse', models.PositiveIntegerField(verbose_name='to verse')),
                ('text', limitless_dashboard.fields.summernote.SummernoteField(verbose_name='text')),
                ('surah', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='quran.quransura', verbose_name='surah')),
            ],
        ),
        migrations.CreateModel(
            name='TafsirBook',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=112, verbose_name='title')),
                ('image', filer.fields.image.FilerImageField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.FILER_IMAGE_MODEL, verbose_name='image')),
                ('language', dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language')),
            ],
        ),
    ]
