import json
import os
import re
import sys

import requests
from bs4 import BeautifulSoup

sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

nemoone = [
    "https://www.aldhiaa.com/urdu/show_book.php?book_id=3779",
    "https://www.aldhiaa.com/urdu/show_book.php?book_id=3885",
    "https://www.aldhiaa.com/urdu/show_book.php?book_id=3887",
    "https://www.aldhiaa.com/urdu/show_book.php?book_id=3888",
    "https://www.aldhiaa.com/urdu/show_book.php?book_id=3890",
    "https://www.aldhiaa.com/urdu/show_book.php?book_id=3892",
    "https://www.aldhiaa.com/urdu/show_book.php?book_id=3946",
    "https://www.aldhiaa.com/urdu/show_book.php?book_id=3904",
    "https://www.aldhiaa.com/urdu/show_book.php?book_id=3893",
]


class Crawl:
    filename = "urdu_nemone.json"

    def req(self, link):
        html = requests.get(link, allow_redirects=0)
        if html.status_code != 200:
            print(html)
            return False
        if html.apparent_encoding == 'utf-8':
            html.encoding = 'utf-8'
        else:
            html.encoding = 'utf-8'
        return html.text

    def parse(self, txt):
        return BeautifulSoup(txt, "html.parser")

    def download_pages(self):
        for i in range(1, 9):
            print('vol ', i)
            for _page in range(1, 900):
                link = "https://www.aldhiaa.com/urdu/book/book/holy_quran_library/quran_interpretation/tafseer_e_namona_{i}/{:0>3d}.html".format(
                    _page, i=i,
                )
                page = self.req(link)
                if not page:
                    break
                os.makedirs('nemone/' + str(i), exist_ok=True)
                with open(f"nemone/{i}/{_page}.html", 'w', encoding='utf-8') as f:
                    f.write(page)

    def crawl(self):
        result = {}
        for file in sorted(os.listdir('nemone'), key=lambda f: int(f.split('.')[0])):
            page = self.parse(open(f'nemone/{file}').read())
            sura = ""
            for el in page.select_one('#bookContent').find_all(['p', 'a', 'h2']):
                if "Heading2Center" in el.get('class', [''])[0]:
                    if re.fullmatch("\d+\.(سوره|سورہ|سورئہ)[\w\W\s]+", el.get_text(strip=1)):
                        sura = el.get_text(strip=1)
                        if result.get(sura) is None:
                            result[sura] = {
                                'file': file,
                                'content': []
                            }
                        continue

                if not sura or not el.get_text(strip=1):
                    continue

                if "Heading" in el.get('class', [''])[0]:
                    result[list(result.keys())[-1]]['content'].append({
                        "title": el.get_text(strip=1),
                        "content": []
                    })
                else:
                    txt = [p.strip() for p in el.get_text(strip=1).split('\n')]
                    if result[list(result.keys())[-1]]['content']:
                        result[list(result.keys())[-1]]['content'][-1]['content'].append("<br>".join(txt))
                    else:
                        print(el.get_text(strip=1))

        with open(self.filename, "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def import_to_database(self):
        from apps.tafsir.models import VersesTafsir, TafsirBook

        book, _ = TafsirBook.objects.get_or_create(
            title="تفسير نمونه",
            language_id=57,
        )

        data = json.load(open(self.filename))
        for sura, ayas in data.items():
            print(sura)
            for ayah in ayas:
                text = ""
                for content in ayah['content']:
                    text += f"""<div class="t-header">{content["title"]}</div><p class="t-text">{"<br>".join(content["content"])}</p>"""

                VersesTafsir.objects.create(
                    surah_id=int(sura.split('.')[0]),
                    from_verse=ayah['aya'][0],
                    to_verse=ayah['aya'][1],
                    text=text,
                    book=book,
                )

    def check(self):
        x = {}
        data = json.load(open(self.filename))
        for i, v in data.items():
            for ii, j in enumerate(v['content']):
                if re.match(r"(آیات|ـآیات|آیت)[\s\d+,]+", j['title']):
                    if x.get(i) is None:
                        x[i] = []
                    r = re.findall(r'(\d+)', j['title'])
                    x[i] += [int(n) for n in r]

        for k, v in x.items():
            for ii, vv in enumerate(v):
                try:
                    if vv + 1 != v[ii + 1]:
                        print(k, vv + 1, sep=' -> ')
                except:
                    pass

        with open('asx.json', "w") as f:
            json.dump(x, f, indent=4, ensure_ascii=False)

    def sort_by_aya(self):
        x = {}
        data = json.load(open(self.filename))
        for i, v in data.items():
            for ii, j in enumerate(v['content']):
                if x.get(i) is None:
                    x[i] = [
                        {
                            'aya': (1, 0),
                            'content': []
                        }
                    ]

                if re.match(r"(آیات|ـآیات|آیت)[\s\d+,]+", j['title']):
                    r = re.findall(r'(\d+)', j['title'])
                    if int(r[0]) == 1:
                        x[i][-1]['aya'] = (int(r[0]), int(r[-1]))
                        x[i][-1]['content'].append(j)
                    else:
                        x[i].append({
                            'aya': (int(r[0]), int(r[-1])),
                            'content': [j]
                        })
                else:
                    x[i][-1]['content'].append(j)

        with open(self.filename, "w") as f:
            json.dump(x, f, indent=4, ensure_ascii=False)

    def remove_arabic_ayahs(self):
        data = json.load(open(self.filename))

        for sura, ayas in data.items():
            for ayah in ayas:
                for ic, content in enumerate(ayah['content']):
                    if content['title'].startswith("آی"):
                        ayah['content'].pop(ic)

        json.dump(data, open('urdu_nemone_removed_arabic.json', 'w'), ensure_ascii=False, indent=4)


# Crawl().crawl()
# Crawl().check()
# Crawl().sort_by_aya()
Crawl().import_to_database()
