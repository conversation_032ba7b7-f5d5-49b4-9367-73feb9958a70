import json
import os
import re
import sys

import requests
from bs4 import BeautifulSoup

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

from functools import wraps
from time import time


def measure(func):
    @wraps(func)
    def _time_it(*args, **kwargs):
        start = int(round(time() * 1000))
        try:
            return func(*args, **kwargs)
        finally:
            end_ = int(round(time() * 1000)) - start
            print(f"{func.__name__} -> Total execution time: {end_ if end_ > 0 else 0} ms")

    return _time_it


class Crawl:
    filename = "indo.json"

    @measure
    def parse(self, text):
        return BeautifulSoup(text, "lxml")

    def crawl(self):
        result = {}

        page1 = self.parse(requests.get('http://alhassanain.org/indonesian/?com=book&id=68').text)
        p = requests.post(data={'m': 'getPacket', 'num': '2', 'id': '68', 'com': 'book', 'j': '1', 'no_out': '1'},
                          url='http://alhassanain.org/indonesian/?=&PHPSESSID=8uqo8ct7uh6vsg62dd47jrm6u7&JsHttpRequest=0-xml&m=getPacket&num=2&id=68&com=book&j=1&no_out=1',
                          ).json()['js']['result']

        for el in page1.select('.page')[2:]:
            self.get_page_content(el, result)

        for pp in p:
            self.get_page_content(self.parse(pp['text']), result)

        with open(self.filename, "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def get_page_content(self, el, result):
        for i in el.select_one('.pageContent'):
            if txt := i.get_text(strip=1):
                if sura := re.search('^SURAH\s*([^\s]+)', txt):
                    sura = sura.groups()[0].strip().lower()
                    if not result.get(sura):
                        result[sura] = {1: []}
                else:
                    if x := re.search(r'[ا-ي]+', txt):
                        continue
                    if x := re.search('^(\d+)\.', txt):
                        if result[list(result.keys())[-1]].get(int(x.groups()[0])) is None:
                            result[list(result.keys())[-1]][int(x.groups()[0])] = []

                    result[list(result.keys())[-1]][list(result[list(result.keys())[-1]].keys())[-1]].append(txt)

    def import_to_database(self):
        from apps.tafsir.models import VersesTafsir, TafsirBook

        book, _ = TafsirBook.objects.get_or_create(
            title="TAFSIR JUZ 'AMMA",
            language_id=64,
        )

        data = json.load(open(self.filename))
        for sura, ayahs in data.items():
            print(sura)
            for i, ayah in ayahs.items():
                text = "<br>".join(ayah)

                VersesTafsir.objects.create(
                    surah_id=int(sura),
                    from_verse=i,
                    to_verse=i,
                    text=text,
                    book=book,
                )

    def check(self):
        data = json.load(open(self.filename))
        for i, s in data.items():
            # print(i, int(list(s.keys())[-1]))
            # x = QuranSura.objects.get(index=int(i)).verse_count
            #
            # if len(s) != x:
            #     print(i)
            res = [ele for ele in range(1, int(list(s.keys())[-1]) + 1) if str(ele) not in list(s.keys())]
            if res:
                print(res)


Crawl().import_to_database()
