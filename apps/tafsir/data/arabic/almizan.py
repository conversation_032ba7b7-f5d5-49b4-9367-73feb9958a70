import json
import os
import random
import re
import sys
from urllib.parse import urlparse, parse_qs
import requests
from bs4 import BeautifulSoup
from time import sleep
base_url = "https://www.holyquran.net/cgi-bin/"


sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
from apps.tafsir.models import VersesTafsir, TafsirBook



class Crawl:
    data = {}

    def parse(self, link: str):
        u = [
            'Opera/9.80 (X11; Linux i686; U; ru) Presto/2.8.131 Version/11.11',
            'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; GTB7.4; InfoPath.2; SV1; .NET CLR 3.3.69573; WOW64; en-US)',
            'Mozilla/5.0 (iPad; CPU OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5355d Safari/8536.25',
        ]

        html = requests.get(link, headers={'User-Agent': random.choice(u)})
        html.encoding = 'windows-1256'
        return BeautifulSoup(html.text, "lxml")

    _content = ''
    result = []
    def crawl(self, href):
        sleep(3)
        page = self.parse(href)
        aya = [i.text for i in page.select("tr:nth-child(2) td p font")][0]
        aya_numbers = re.findall('\d+', aya)
        query_params = parse_qs(urlparse(href).query)

        sura_number = query_params['ch'][0]
        start_aya = min(map(int,aya_numbers))
        end_aya = max(map(int,aya_numbers))
        content  = str(page.select("tr")[2])
        print(sura_number)
        print(aya_numbers)
        for i in page.select("tr td p font a"):
            if i.text == 'التالي':
                next_url = i.get('href')
                next_url = base_url+next_url
                query_params = parse_qs(urlparse(next_url).query)
                ch = query_params['ch'][0]
                vr = query_params['vr'][0]
                self._content += content
                print(next_url)
                if (int(ch.rstrip('/')) == int(sura_number) and int(vr.rstrip('/')) == int(start_aya)):
                    self.crawl(href=next_url)
                else:
                    self.result = {
                        'sura_number': sura_number,
                        'start_aya': start_aya,
                        'end_aya': end_aya,
                        'content': self._content
                    }
                    with open("almizan.json", "a", encoding="utf-8") as f:
                        json.dump(self.result, f, indent=4, ensure_ascii=False)
                    self._content = ''
                    self.crawl(href=next_url)
        if 'النهاية' in [i.text for i in page.select("tr td p font")] :
            self._content += content
            self.result = {
                'sura_number': sura_number,
                'start_aya': start_aya,
                'end_aya': end_aya,
                'content': self._content
            }
            with open("almizan.json", "a", encoding="utf-8") as f:
                json.dump(self.result, f, indent=4, ensure_ascii=False)
            self._content = ''
            url = f'https://www.holyquran.net/cgi-bin/almizan.pl?ch={int(sura_number)+1}&vr=1/'
            self.crawl(href= url)


    def import_to_database(self):


        book, _ = TafsirBook.objects.get_or_create(
            title="الميزان في تفسير القرآن",
            language_id=15,
        )

        data = json.load(open("almizan.json"))
        for s in data:
                print(s)
                q = VersesTafsir.objects.filter(surah_id=int(s['sura_number']),from_verse=int(s['start_aya']),to_verse=int(s['end_aya']),book=book,)
                if q.exists():
                    q.update(
                    surah_id=int(s['sura_number']),
                    from_verse=int(s['start_aya']),
                    to_verse=int(s['end_aya']),
                    text=s['content'],
                    book=book,
                    )
                else:
                    VersesTafsir.objects.create(
                        surah_id=int(s['sura_number']),
                        from_verse=int(s['start_aya']),
                        to_verse=int(s['end_aya']),
                        text=s['content'],
                        book=book,
                    )








# Crawl().crawl(href="https://www.holyquran.net/cgi-bin/almizan.pl?ch=28&vr=15&sp=0&sv=0")
# Crawl().import_persian_categories()
Crawl().import_to_database()
