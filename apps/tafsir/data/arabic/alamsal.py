import json
import os
import random
import re
import sys
from time import sleep

import lxml.html
import requests
from bs4 import BeautifulSoup
from lxml.html.defs import tags
from lxml import etree

base_url ="https://www.ghbook.ir"
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))



class Crawl:
    data = {}

    def parse(self, link: str):
        u = [
            'Opera/9.80 (X11; Linux i686; U; ru) Presto/2.8.131 Version/11.11',
            'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; GTB7.4; InfoPath.2; SV1; .NET CLR 3.3.69573; WOW64; en-US)',
            'Mozilla/5.0 (iPad; CPU OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5355d Safari/8536.25',
        ]

        html = requests.get(link, headers={'User-Agent': random.choice(u)}).text
        return BeautifulSoup(html, "lxml")

    def crawl(self):
        result = []
        _pages = list(range(1, 50))
        all_content = ""

        for p in _pages:
            page = self.parse(f"https://lib.eshia.ir/27526/1/{p}/")
            content = [i for i in page.select("td.book-page-show")][0]
            all_content += str(content)

        with open("all_content.html", "w", encoding="utf-8") as f:
            f.write(all_content)


Crawl().crawl()
# Crawl().import_persian_categories()