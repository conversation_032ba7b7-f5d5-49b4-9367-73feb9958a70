import json
import os
import re, sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

from bs4 import BeautifulSoup

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

s_map = {
    'HƏMD': 'Fatihə',
    'ALİ-İMRAN': 'Ali-İmran',
    'NİSA': 'Nisa',
    'MAİDƏ': 'Maidə',
    'ӘRАF': 'Əraf',
    'ӘNFАL': "Ənfal",
    'YUSUF': "Yusi<PERSON>",
    'İBRAHİM': "İbrahim",
    'HİCR': "Hicr",
    'ƏNBİYA': "Ənbiya",
    'MUMİNUN': "<PERSON><PERSON><PERSON>",
    'FATİR': "<PERSON>ir",
    'YASİN': "<PERSON><PERSON>",
    'CAFFAT': "Saffat",
    'ƏR-RƏHMAN': "Rəhman",
    'VAQEƏ': "Vaqiə",
    'HƏDİD': "Hədid",
    'MÜCADƏLƏ': "Mücadilə",
    'MUNAFİQUN': "Munafiqun",
    'TƏHRİM': "Təhrim",
    'MULK': "Mülk",
    'QƏLƏM': "Əl-Qələm",
    'MƏARİC': "Məaric",
    'CİNN': "Cinn",
    'MUZƏMMİL': "Muzzəmmil",
    'MUDDƏSSİR': "Muddəssir",
    'QİYAMƏT': "Qiyamə",
    'NAZİAT': "Naziat",
    'TƏKVİR': "Təkvir",
    'İNFİTAR': "İnfitar",
    'MUTƏFFİFİN': "Mutəffifin",
    'İNŞİQAQ': "İnşiqaq",
    'BÜRUC': "Buruc",
    'TARİQ': "Tariq",
    'ĞAŞİƏ': "Ğaşiyə",
    'LƏYL': "Leyl",
    'ŞƏRH': "İnşirah",
    'TİN': "Tin",
    'BƏYYİNƏ': "Bəyyinə",
    'ZƏLZƏLƏ': "Zilzal",
    'ADİYAT': "Adiyat",
    'QARİƏ': "Qariə",
    'FİL': "Fil",
    'QÜREYŞ': "Qureyşsurəsi",
    'KƏVSƏR': "Kövsər",
    'KAFİRUN': "Kafirun",
    'MƏSƏD': "Əbu Ləhəb",
}

AR = "ءﺎﻐﺘﺑاو ﺔﻨـﺘﻔْﻟا ءﺎﻐﺘﺑا ﻪﻨﻣ ﻪﺑﺎﺸﺗ ﺎﻣ نﻮﻌﺒﺘﻴـﻓ ﻎﻳز ﻢﻬﺑﻮﻠـﻗ ﻲﻓ ﻦﻳﺬﻟا ﺎﻣﺄﻓ تﺎﻬﺑﺎﺸﺘﻣ".replace(' ', '')


class Crawl:
    filename = "azeri.json"

    def parse(self, text):
        return BeautifulSoup(text, "lxml")

    def crawl(self):
        arx = open('asx.txt', 'w')
        arx2 = open('asx2.txt', 'w')
        arx3 = open('asx3.txt', 'w')
        result = []
        for vol in range(1, 13):
            print('vol -> ', vol)
            page_html = open(f'html/{vol}.html').read()
            page_html = re.sub(r'\s{6,60}', '[subtitle]', page_html)
            pages = self.parse(page_html).find_all(
                'div', attrs={'data-page-no': re.compile(r'[\d\w]+')}
            )
            aya_starts = False
            aya_number = []
            for n, page in enumerate(pages):
                # if len(result):
                # result[-1]['content'].append(f'[PAGE: {n}]')

                is_in_subtitle_loop = False
                to_append_before = None
                for _line in page.select('div.ws0'):
                    line = _line.text.strip()
                    if not line:
                        continue

                    line = line.replace('●', '').replace('◊', '').strip()

                    if to_append_before:
                        line = to_append_before + line
                        to_append_before = None

                    if re.search(r'^\d+\.$', line):
                        to_append_before = line + ' '
                        continue

                    if "﴾" in line or "﴿" in line:
                        is_in_subtitle_loop = False

                    if "◘" in line:
                        aya_starts = False
                        aya_number = []

                    if "Pеyğәmbәrlәr әqidә mәktәbinin öncüllәridir" in line:
                        print()

                    if re.match(r'“[^”]+” SUR\wS\w\s*$', line):
                        result.append({
                            'surah': line,
                            'content': []
                        })
                        continue

                    if len(result) and len(result[-1]['content']) and ("[subtitle]" in line or is_in_subtitle_loop):
                        is_in_subtitle_loop = True

                        if re.match(r'^\d+$', result[-1]['content'][-1]):
                            result[-1]['content'][-1] = result[-1]['content'][-1] + ' ' + line
                            continue

                        result[-1]['content'].append(line)
                        continue

                    if len(result):
                        if not len(line) > 30 and any(
                                [i for i in ['NÖQTӘLӘR', "Nöqtələr", "Bildirişlər", "BİLDİRİŞLӘR"] if
                                 i in line]):
                            aya_starts = False
                            aya_number = []
                            is_in_subtitle_loop = False
                            result[-1]['content'].append('[HEADER] ' + line + '.')
                            continue

                        if "" in line \
                                or "" in line \
                                or "﴿" in line \
                                or "﴿" in line \
                                or "﴾" in line \
                                or "" in line \
                                or re.findall(r'^\s*\(?АYӘ:[\d\s,-]+', line, re.I) \
                                or re.findall(r'^\s*Ayə\s*[\d,-]+:', line, re.I) \
                                or re.findall(r'^\s*Ayə:?\s*[\d,-]+', line, re.I) \
                                or re.match(r'^[\d\-]+:$', line, re.I):
                            if Ayn := re.findall(r'\d+', line):
                                aya_starts = True
                                aya_number = list(map(int, Ayn))
                            continue

                        if aya_starts:
                            AYAH = f"[AYAH {aya_number}]"
                            if not line.isprintable():
                                # arx.write(line + '\n')
                                if result[-1]['content'][-1] != f'[AYAH {aya_number}]':
                                    arx.write('1 -> ' + line + '\n')
                                    # result[-1]['content'].append(AYAH)
                                continue

                            if [i for i in ["ﱠ ﱢﱢ", " ﱢ", "ﱠ ﱠ", " ﱠ", " ﱠ", 'ﱢ ﱠ', " ﱢ"] if i in line]:
                                # arx2.write(line + '\n')
                                if result[-1]['content'][-1] != AYAH:
                                    arx.write('2 -> ' + line + '\n')
                                    # result[-1]['content'].append(AYAH)
                                continue

                            if re.findall('[\u0600-\u06FF]+', line):
                                # arx2.write(line + '\n')
                                if result[-1]['content'][-1] != AYAH:
                                    arx.write('3 -> ' + line + '\n')
                                    # result[-1]['content'].append(AYAH)
                                continue

                            if len(line.strip()) <= 4:
                                # arx3.write(line + '\n')
                                continue

                            if re.match(r"^\d+\.?$", line):
                                if result[-1]['content'][-1] != AYAH:
                                    arx.write('4 -> ' + line + '\n')
                                    # result[-1]['content'].append(AYAH)
                                continue

                            if len(result[-1]['content']) and result[-1]['content'][-1].startswith(AYAH):
                                if not [i for i in AR if i in line]:
                                    result[-1]['content'][-1] = result[-1]['content'][-1] + ' ' + line
                            else:
                                if not [i for i in AR if i in line]:
                                    result[-1]['content'].append(AYAH + line)
                            continue

                        if len(result[-1]['content']) \
                                and (
                                len(result[-1]['content'][-1]) and not result[-1]['content'][-1][-1] in ['.', '!', '?',
                                                                                                         ';', ']']) \
                                and not result[-1]['content'][-1].startswith('[AYAH'):
                            if "hb" in _line['class']:
                                result[-1]['content'][-1] = result[-1]['content'][-1] + "<sup>%s</sup>" % line
                                continue
                            else:
                                result[-1]['content'][-1] = result[-1]['content'][-1] + ' ' + line
                                continue

                        if "hb" in _line['class']:
                            result[-1]['content'][-1] = result[-1]['content'][-1] + "<sup>%s</sup>" % line
                            continue

                        if line.strip().isnumeric():
                            continue

                        if re.match(r"^\d+\.?$", line):
                            # print(line)
                            continue

                        result[-1]['content'].append(line)
                    else:
                        pass
                        # print('No Surah: ', line)

        with open(f"book.json", "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def clean_book(self):
        from apps.quran.models import QuranSuraTranslation
        result = {}
        data = json.load(open("book.json"))
        for d in data:
            s = re.search('“(.*)”', d['surah']).groups()[0]
            s = s_map.get(s) or s
            sur = QuranSuraTranslation.objects.filter(name__istartswith=s, language_code='az').first()
            if not result.get(s, None):
                result[s] = []

            in_aya_loop, in_subtitle_loop, in_header = False, False, False

            for i, line in enumerate(d['content']):
                if len(line) <= 4 and not line == "ev;":
                    print(line)
                    continue

                if not len(result[s]) and not line.startswith('[AYAH'):
                    result[s].append({
                        'content': [f"<p class='t-text'>{line}</p>"]
                    })
                    continue

                if line.startswith('[AYAH'):
                    if aya_nums := re.search(r"\[([\d,\s]+)]", line):
                        aya_nums = list(map(int, aya_nums.groups()[0].split(',')))
                        txt = re.sub(r".*[^]]]+", "", line)
                        result[s].append({
                            'ayahs': aya_nums,
                            'content': [txt]
                        })
                        in_aya_loop, in_subtitle_loop, in_header = 1, False, False

                        continue

                if line.startswith('[subtitle'):
                    result[s][-1]['content'].append('<hr>')
                    in_aya_loop, in_subtitle_loop, in_header = False, 1, False

                    continue

                if line.startswith('[HEADER'):
                    header = re.search(r'\[HEADER\]\s?([^.]+)', line).groups()[0]
                    result[s][-1]['content'].append(f'<div class="t-header">{header}</div>')
                    in_aya_loop, in_subtitle_loop, in_header = False, False, 1
                    continue

                if in_aya_loop:
                    result[s][-1]['content'].append(f"<p class='t-text'>{line}</p>")
                    continue

                if in_subtitle_loop:
                    if len(line) > 60:
                        result[s][-1]['content'].append("<br>" + line)
                    else:
                        result[s][-1]['content'][-1] = result[s][-1]['content'][-1] + "<br>" + line
                    continue

                if in_header:
                    result[s][-1]['content'].append(f"<p class='t-text'>{line}</p>")

            # if len(set(result[s])) != sur.sura.verse_count:
            #     print()

        # a = list(set(result[s]))
        # for j, jj in enumerate(a):
        #     if len(a) > j + 1 and jj + 1 != a[j + 1]:
        #         print(d['surah'], '->', jj + 1)

        with open(f"book_cleaned.json", "w") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)

    def import_to_database(self):
        from apps.tafsir.models import TafsirBook, VersesTafsir
        from apps.quran.models import QuranSuraTranslation

        book, _ = TafsirBook.objects.get_or_create(
            title="noor azeri",
            language_id=18,
        )

        data = json.load(open("book_cleaned.json"))
        for s, parts in data.items():
            sura = QuranSuraTranslation.objects.get(name__istartswith=s, language_code='az').sura
            for part in parts:
                from_verse, to_verse = part.get('ayahs', [1])[0], part.get('ayahs', [1])[-1]
                text = "".join(part['content'])
                VersesTafsir.objects.create(
                    surah=sura,
                    from_verse=int(from_verse),
                    to_verse=to_verse,
                    text=text,
                    book=book,
                )


# Crawl().crawl()
# Crawl().clean_book()
Crawl().import_to_database()
