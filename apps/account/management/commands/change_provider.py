# فایل: management/commands/change_provider.py

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from apps.account.models import ProviderRequest
from apps.meet.models import MeetProviderProfile
from django.db import transaction


User = get_user_model()

class Command(BaseCommand):
    help = 'Change provider for a specific service to a new user'

    def add_arguments(self, parser):
        parser.add_argument('user_id', type=int, help='ID of the user to become the new provider')
        parser.add_argument('service', type=str, help='Service name (e.g., hussainiya, meet, etc.)')
        parser.add_argument('user_provider_id', type=int, help='ID of the current provider')

    def handle(self, *args, **kwargs):
        user_id = kwargs['user_id']
        service = kwargs['service']
        user_provider_id = kwargs['user_provider_id']

        try:
            user = User.objects.get(id=user_id)
            user_provider = User.objects.get(id=user_provider_id)
        except User.DoesNotExist:
            raise CommandError('User or provider does not exist')

        # عملیات اول: بررسی اینکه کاربر قبلاً برای این سرویس پرووایدر نباشد
        if service in user.publisher_in():
            raise CommandError(f'User {user.username} is already a provider for service {service}')

        user_provider_request = ProviderRequest.objects.filter(
            user=user,
            service=service,
        ).first()
        if user_provider_request:
            raise CommandError(f"ProviderRequest found for user with ID {user_id} and service '{service}'.")
            

        # Find the ProviderRequest for the current provider
        provider_request = ProviderRequest.objects.filter(
            user=user_provider,
            service=service,
            status=ProviderRequest.Status.accepted
        ).first()

        if not provider_request:
            raise CommandError(f"No accepted ProviderRequest found for provider with ID {user_provider_id} and service '{service}'.")


        old_profile_info = ProviderRequest.get_provider_profile_info(provider_request, user_provider)
        if not old_profile_info:
            raise CommandError(f"No provider profile found for provider with ID {provider_id} and service '{service}'.")

        # Transfer the ProviderRequest to the new user
        with transaction.atomic():
            provider_request.is_custom = False
            provider_request.user = user
            provider_request.save()

            self.stdout.write(self.style.SUCCESS(f"ProviderRequest transferred to user with ID {user_id}."))
            # Update the provider profile's user to the new user
            if provider_request.service == 'meet':
                from apps.meet.models.provider import MeetProviderProfile
                profile_model = MeetProviderProfile
            elif provider_request.service == 'hussainiya':
                from apps.hussainiya.provider.models import ProviderProfile
                profile_model = ProviderProfile
            elif provider_request.service == 'talk':
                from apps.q_and_a.models import Consultants
                profile_model = Consultants
            else:
                raise CommandError(f"Unsupported service: {provider_request.service}")
            if profile_model == Consultants:
                profile = profile_model.objects.filter(user_id=user_provider.id).first()
                if profile:
                    profile.user_id = user_id
                    profile.token = user.auth_token.key
                    profile.save()
            else:
                profile = profile_model.objects.filter(user=user_provider).first()
                if profile:
                    profile.user = user
                    profile.save()
                    self.stdout.write(self.style.SUCCESS(f"{profile_model.__name__} transferred to user with ID {user_id}."))
                else:
                    raise CommandError(f"No {profile_model.__name__} found for provider with ID {provider_id} and service '{service}'.")


        self.stdout.write(self.style.SUCCESS(f'Successfully changed provider for service {service} to user {user.username}'))