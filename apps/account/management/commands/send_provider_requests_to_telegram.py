import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.account.models.provider_request import ProviderRequest
from apps.account.tasks.provider_request_telegram import send_provider_request_to_telegram

logger = logging.getLogger(__name__)
import time

class Command(BaseCommand):
    help = 'ارسال درخواست‌های پرووایدری به کانال تلگرام'

    def add_arguments(self, parser):
        parser.add_argument(
            '--ids',
            type=str,
            help='ارسال درخواست‌ها با شناسه‌های مشخص (جدا شده با کاما، مثال: 1,2,3)'
        )
        parser.add_argument(
            '--range',
            type=str,
            help='ارسال درخواست‌ها در یک بازه از شناسه‌ها (مثال: 426-500 یا فقط 426 برای شروع از 426 تا آخر)'
        )

    def handle(self, *args, **options):
        ids = options.get('ids')
        id_range = options.get('range')

        if id_range:
            try:
                # پردازش بازه شناسه‌ها
                range_parts = id_range.split('-')

                if not range_parts[0].strip().isdigit():
                    raise ValueError("شناسه شروع باید یک عدد باشد")

                start_id = int(range_parts[0].strip())

                if len(range_parts) > 1 and range_parts[1].strip():
                    if not range_parts[1].strip().isdigit():
                        raise ValueError("شناسه پایان باید یک عدد باشد")

                    # اگر پایان بازه مشخص شده باشد
                    end_id = int(range_parts[1].strip())

                    if end_id < start_id:
                        raise ValueError("شناسه پایان باید بزرگتر یا مساوی شناسه شروع باشد")

                    queryset = ProviderRequest.objects.filter(id__gte=start_id, id__lte=end_id).order_by('id')
                else:
                    # اگر فقط شروع بازه مشخص شده باشد، تا آخرین شناسه ادامه می‌دهیم
                    queryset = ProviderRequest.objects.filter(id__gte=start_id).order_by('id')
            except ValueError as e:
                self.stdout.write(self.style.ERROR(f"خطا در پردازش بازه شناسه‌ها: {str(e)}"))
                return
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"خطای غیرمنتظره در پردازش بازه شناسه‌ها: {str(e)}"))
                return
        elif ids:
            # اگر شناسه‌ها مشخص شده باشند، فقط آن‌ها را پردازش می‌کنیم
            id_list = [int(id.strip()) for id in ids.split(',') if id.strip().isdigit()]
            queryset = ProviderRequest.objects.filter(id__in=id_list)
        else:
            queryset = ProviderRequest.objects.all().order_by('created_at')

        # نمایش اطلاعات درخواست‌های انتخاب شده
        if not queryset.exists():
            self.stdout.write(self.style.WARNING("هیچ درخواستی با شرایط مشخص شده یافت نشد"))
            return

        first_id = queryset.first().id
        last_id = queryset.last().id
        self.stdout.write(f"درخواست برای ارسال {len(queryset)} درخواست پرووایدری از شناسه {first_id} تا {last_id}")
        
        # ارسال هر درخواست به تلگرام
        success_count = 0
        error_count = 0
        
        for request in queryset:
            try:
                # ارسال همزمان (به جای delay)
                send_provider_request_to_telegram(request.id)
                time.sleep(20)     
                success_count += 1
                self.stdout.write(self.style.SUCCESS(f"درخواست با شناسه {request.id} با موفقیت ارسال شد"))
            except Exception as e:
                error_count += 1
                self.stdout.write(self.style.ERROR(f"خطا در ارسال درخواست با شناسه {request.id}: {str(e)}"))
                logger.error(f"خطا در ارسال درخواست با شناسه {request.id} به تلگرام: {str(e)}")
        
        self.stdout.write(
            self.style.SUCCESS(
                f"عملیات با {success_count} موفقیت و {error_count} خطا به پایان رسید"
            )
        ) 