from django.core.management.base import BaseCommand
from django.db import transaction
from apps.account.models.geoNames import GeoNamesCity
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Import GeoNames data from allCountries.txt into database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            default='staticfiles/allCountries.txt',
            help='Path to allCountries.txt file'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=10000,
            help='Batch size for bulk create'
        )

    def handle(self, *args, **options):
        file_path = Path(options['file'])
        batch_size = options['batch_size']
        
        if not file_path.exists():
            self.stderr.write(f"File not found: {file_path}")
            return

        # Clear existing data
        self.stdout.write("Clearing existing GeoNames data...")
        GeoNamesCity.objects.all().delete()

        self.stdout.write("Starting GeoNames import...")
        cities = []
        processed = 0

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    fields = line.strip().split('\t')
                    if len(fields) >= 8:
                        try:
                            # Only import cities (feature class = P)
                            if fields[6] == 'P':
                                city = GeoNamesCity(
                                    name=fields[1],
                                    country_code=fields[8],
                                    latitude=float(fields[4]),
                                    longitude=float(fields[5]),
                                    feature_class=fields[6],
                                    population=int(fields[14]) if fields[14].isdigit() else None
                                )
                                cities.append(city)
                                processed += 1

                                # Bulk create when batch size is reached
                                if len(cities) >= batch_size:
                                    with transaction.atomic():
                                        GeoNamesCity.objects.bulk_create(cities)
                                    self.stdout.write(f"Processed {processed} cities...")
                                    cities = []

                        except (ValueError, IndexError) as e:
                            logger.error(f"Error processing line: {e}")
                            continue

                # Create remaining cities
                if cities:
                    with transaction.atomic():
                        GeoNamesCity.objects.bulk_create(cities)

        except Exception as e:
            self.stderr.write(f"Error importing GeoNames data: {e}")
            return

        self.stdout.write(f"Successfully imported {processed} cities") 