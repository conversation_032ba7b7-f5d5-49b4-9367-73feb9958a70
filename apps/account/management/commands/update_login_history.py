import time
import logging
import random
import math
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.db import transaction
from apps.account.models import LoginHistory
from utils.geo import get_country_city_from_point

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Update LoginHistory records with country and city based on latitude and longitude'

    def add_arguments(self, parser):
        parser.add_argument(
            '--batch-size',
            type=int,
            default=50,
            help='Number of records to process in each batch'
        )
        parser.add_argument(
            '--max-records',
            type=int,
            default=None,
            help='Maximum number of records to process (default: all)'
        )

    def handle(self, *args, **kwargs):
        batch_size = kwargs['batch_size']
        max_records = kwargs['max_records']

        # Advanced rate limiting configuration
        self.rate_config = {
            'initial_delay': 2.0,       # Start with a conservative delay (seconds)
            'min_delay': 1.0,           # Minimum delay between requests
            'max_delay': 30.0,          # Maximum delay between requests
            'retry_count': 3,           # Number of retries for failed requests
            'timeout_multiplier': 2.0,  # Extra delay multiplier for timeout errors
            'success_threshold': 5,     # Number of consecutive successes before reducing delay
            'failure_threshold': 2,     # Number of consecutive failures before increasing delay
            'success_factor': 0.9,      # Factor to reduce delay after consecutive successes
            'failure_factor': 1.5,      # Factor to increase delay after consecutive failures
            'high_failure_rate': 0.25,  # Threshold for considering failure rate high
            'jitter_factor': 0.2,       # Jitter factor to add randomness (±20%)
            'batch_cooldown_max': 15.0, # Maximum cooldown between batches (seconds)
            'window_size': 20,          # Size of sliding window for rate calculations
        }

        # State tracking
        self.current_delay = self.rate_config['initial_delay']
        self.consecutive_failures = 0
        self.consecutive_successes = 0
        self.last_request_time = None
        self.total_requests = 0
        self.failed_requests = 0
        self.request_history = []  # For sliding window calculations
        self.error_types = {}      # Track different error types

        # Define time-based factor function
        def get_time_based_factor():
            """Return a multiplier based on time of day (slower during peak hours)."""
            now = datetime.now()
            hour = now.hour

            # Assume API might be busier during working hours (8am-6pm)
            if 8 <= hour < 18:
                return 1.2  # Add 20% more delay during business hours
            elif 22 <= hour or hour < 5:
                return 0.9  # Reduce delay by 10% during night hours
            return 1.0

        # Adaptive rate limiting based on time of day
        self.time_based_factor = get_time_based_factor()

        def get_delay_with_jitter():
            """Calculate delay with jitter to avoid synchronized requests."""
            base_delay = self.current_delay * self.time_based_factor
            jitter = base_delay * self.rate_config['jitter_factor']
            return base_delay + random.uniform(-jitter, jitter)

        def update_sliding_window(success):
            """Update sliding window of request results."""
            # Add current result to history
            self.request_history.append(success)

            # Keep only the most recent requests based on window size
            if len(self.request_history) > self.rate_config['window_size']:
                self.request_history.pop(0)

        def get_window_failure_rate():
            """Calculate failure rate in the current sliding window."""
            if not self.request_history:
                return 0

            failures = sum(1 for success in self.request_history if not success)
            return failures / len(self.request_history)

        def adjust_delay(success, error_type=None):
            """Dynamically adjust delay based on success/failure patterns."""
            # Track error types
            if not success and error_type:
                self.error_types[error_type] = self.error_types.get(error_type, 0) + 1

            # Update sliding window
            update_sliding_window(success)

            if success:
                self.consecutive_failures = 0
                self.consecutive_successes += 1

                # Gradually decrease delay after multiple consecutive successes
                if self.consecutive_successes >= self.rate_config['success_threshold']:
                    old_delay = self.current_delay
                    self.current_delay = max(
                        self.rate_config['min_delay'],
                        self.current_delay * self.rate_config['success_factor']
                    )
                    self.consecutive_successes = 0

                    if old_delay != self.current_delay:
                        logger.info(f"Decreasing delay to {self.current_delay:.2f}s after {self.rate_config['success_threshold']} consecutive successes")
            else:
                self.consecutive_successes = 0
                self.consecutive_failures += 1

                # Exponentially increase delay after failures
                if self.consecutive_failures >= self.rate_config['failure_threshold']:
                    old_delay = self.current_delay
                    self.current_delay = min(
                        self.rate_config['max_delay'],
                        self.current_delay * self.rate_config['failure_factor']
                    )

                    if old_delay != self.current_delay:
                        logger.info(f"Increasing delay to {self.current_delay:.2f}s after {self.consecutive_failures} consecutive failures")

            # Adjust based on sliding window failure rate
            window_failure_rate = get_window_failure_rate()
            if window_failure_rate > self.rate_config['high_failure_rate'] and self.total_requests > 10:
                old_delay = self.current_delay
                self.current_delay = min(
                    self.rate_config['max_delay'],
                    self.current_delay * (1 + (window_failure_rate / 2))
                )

                if old_delay != self.current_delay and abs(old_delay - self.current_delay) > 0.1:
                    logger.info(f"Adjusting delay to {self.current_delay:.2f}s due to high failure rate ({window_failure_rate:.2f})")

            # Special handling for specific error types
            if not success and error_type == 'timeout' and self.current_delay < self.rate_config['max_delay'] / 2:
                self.current_delay = min(
                    self.rate_config['max_delay'],
                    self.current_delay * self.rate_config['timeout_multiplier']
                )
                logger.info(f"Timeout detected, increasing delay to {self.current_delay:.2f}s")

            return self.current_delay

        def enforce_rate_limit():
            """Ensure minimum time between requests with adaptive rate limiting."""
            if self.last_request_time:
                elapsed = (datetime.now() - self.last_request_time).total_seconds()
                delay_needed = get_delay_with_jitter()

                if elapsed < delay_needed:
                    sleep_time = delay_needed - elapsed
                    if sleep_time > 0.1:  # Only sleep for meaningful durations
                        self.stdout.write(self.style.NOTICE(f"Rate limiting: waiting {sleep_time:.2f}s"))
                        time.sleep(sleep_time)

            self.last_request_time = datetime.now()
            self.total_requests += 1

            # Periodically update time-based factor
            if self.total_requests % 50 == 0:
                self.time_based_factor = get_time_based_factor()

        def categorize_error(error_str):
            """Categorize error type for specialized handling."""
            error_str = error_str.lower()
            if 'timeout' in error_str:
                return 'timeout'
            elif 'connection' in error_str:
                return 'connection'
            elif 'rate' in error_str and ('limit' in error_str or 'exceed' in error_str):
                return 'rate_limit'
            elif '429' in error_str:
                return 'rate_limit'
            elif '5' in error_str and len(error_str) == 3:  # 5xx errors
                return 'server_error'
            elif '4' in error_str and len(error_str) == 3:  # 4xx errors
                return 'client_error'
            return 'other'

        def process_login(login, retry_attempt=0):
            """Process a single LoginHistory record with smart retry logic."""
            if login.lat is None or login.lon is None:
                return False

            # Enforce rate limiting before making the API request
            enforce_rate_limit()

            try:
                # Use our improved geocoding function
                country, city = get_country_city_from_point(login.lat, login.lon)

                if country and city:
                    login.country = country
                    login.city = city
                    login.save(update_fields=['country', 'city', 'location_method'])
                    self.stdout.write(self.style.SUCCESS(
                        f"Successfully updated: {login.id}/user:{login.user.id}/lat={login.lat}, lon={login.lon}, country={country}, city={city}"
                    ))
                    adjust_delay(True)
                    return True
                else:
                    self.stdout.write(self.style.WARNING(
                        f"Could not get location for login ID {login.id} (lat: {login.lat}, lon: {login.lon})"
                    ))
                    self.failed_requests += 1
                    adjust_delay(False, 'no_result')
                    return False
            except Exception as e:
                error_str = str(e)
                error_type = categorize_error(error_str)
                error_msg = f"Error processing login ID {login.id}: {error_str}"
                self.stdout.write(self.style.ERROR(error_msg))
                logger.warning(error_msg)
                self.failed_requests += 1

                # Implement retry with exponential backoff
                if retry_attempt < self.rate_config['retry_count']:
                    # Calculate retry delay with exponential backoff
                    retry_delay = min(
                        self.rate_config['max_delay'],
                        self.rate_config['initial_delay'] * (2 ** retry_attempt)
                    )

                    # Add jitter to avoid thundering herd problem
                    retry_delay_with_jitter = retry_delay + random.uniform(0, retry_delay * 0.5)

                    # Apply error-specific multipliers
                    if error_type == 'timeout':
                        retry_delay_with_jitter *= self.rate_config['timeout_multiplier']
                    elif error_type == 'rate_limit':
                        retry_delay_with_jitter *= 2.5  # Even longer delay for rate limits
                        # Increase global delay as well
                        self.current_delay = min(
                            self.rate_config['max_delay'],
                            self.current_delay * 2
                        )
                        logger.info(f"Rate limit detected, increasing global delay to {self.current_delay:.2f}s")

                    self.stdout.write(self.style.WARNING(
                        f"Retrying in {retry_delay_with_jitter:.2f}s (attempt {retry_attempt + 1}/{self.rate_config['retry_count']})"
                    ))
                    time.sleep(retry_delay_with_jitter)
                    return process_login(login, retry_attempt + 1)

                adjust_delay(False, error_type)
                return False

        def calculate_batch_cooldown(batch_success_rate):
            """Calculate cooldown period between batches based on success rate."""
            if batch_success_rate >= 0.9:  # Very successful batch
                return 0

            # Exponential increase in cooldown as success rate decreases
            failure_rate = 1 - batch_success_rate
            cooldown = self.rate_config['batch_cooldown_max'] * (1 - math.exp(-5 * failure_rate))
            return cooldown

        def update_login_history():
            # Get login histories with empty country and city, ordered by newest first
            login_histories = LoginHistory.objects.filter(
                country__isnull=True,
                city__isnull=True
            ).order_by('-last_login_at')

            # Limit the number of records if specified
            if max_records:
                login_histories = login_histories[:max_records]

            total_logins = login_histories.count()
            self.stdout.write(self.style.SUCCESS(f"Total records to process: {total_logins}"))
            self.stdout.write(self.style.SUCCESS(f"Starting with delay: {self.current_delay:.2f}s"))

            success_count = 0
            failed_logins = []

            # Initialize timing variables
            start_time = datetime.now()
            last_progress_report = start_time

            # Process in batches
            for start in range(0, total_logins, batch_size):
                end = min(start + batch_size, total_logins)
                batch = login_histories[start:end]
                self.stdout.write(self.style.SUCCESS(
                    f"Processing batch {start} to {end} (current delay: {self.current_delay:.2f}s)"
                ))

                batch_start_time = datetime.now()
                batch_success = 0

                for login in batch:
                    if process_login(login):
                        success_count += 1
                        batch_success += 1
                    else:
                        failed_logins.append(login)

                # Report batch statistics
                batch_duration = (datetime.now() - batch_start_time).total_seconds()
                batch_success_rate = batch_success / len(batch) if batch else 0

                self.stdout.write(self.style.SUCCESS(
                    f"Batch completed: {batch_success}/{len(batch)} successful ({batch_success_rate:.1%}) in {batch_duration:.2f}s"
                ))

                # Report overall progress every 10 minutes
                if (datetime.now() - last_progress_report).total_seconds() > 600:
                    elapsed = (datetime.now() - start_time).total_seconds()
                    progress = (start + len(batch)) / total_logins * 100
                    overall_success_rate = success_count / (start + len(batch)) if (start + len(batch)) > 0 else 0

                    self.stdout.write(self.style.SUCCESS(
                        f"Progress: {progress:.1f}% complete ({success_count} successful, {overall_success_rate:.1%}) after {elapsed/60:.1f} minutes"
                    ))

                    # Report error distribution
                    if self.error_types:
                        error_report = ", ".join([f"{k}: {v}" for k, v in self.error_types.items()])
                        self.stdout.write(self.style.NOTICE(f"Error distribution: {error_report}"))

                    last_progress_report = datetime.now()

                # Adaptive batch pause - calculate cooldown based on success rate
                if end < total_logins:  # Don't pause after the last batch
                    cooldown = calculate_batch_cooldown(batch_success_rate)
                    if cooldown > 0.5:
                        self.stdout.write(self.style.NOTICE(
                            f"Taking a {cooldown:.1f}s cooldown between batches (success rate: {batch_success_rate:.1%})"
                        ))
                        time.sleep(cooldown)

            # Retry remaining failed logins with increased delays
            if failed_logins:
                # Wait a bit longer before starting retries to let API recover
                recovery_wait = min(60, max(10, self.current_delay * 3))
                self.stdout.write(self.style.WARNING(
                    f"Waiting {recovery_wait:.1f}s before retrying {len(failed_logins)} failed records..."
                ))
                time.sleep(recovery_wait)

                self.stdout.write(self.style.WARNING(
                    f"Retrying {len(failed_logins)} failed records with increased delays..."
                ))
                retry_success = 0

                # Increase base delay for the retry phase
                self.current_delay = min(
                    self.rate_config['max_delay'],
                    self.current_delay * 1.5
                )

                # Group failed logins by error type for prioritized processing
                prioritized_logins = []
                normal_logins = []

                for login in failed_logins:
                    # This is a simplification - in reality we'd need to store error types with the failed logins
                    # For now, just randomize to simulate prioritization
                    if random.random() < 0.3:  # 30% chance to be "high priority"
                        prioritized_logins.append(login)
                    else:
                        normal_logins.append(login)

                # Process prioritized logins first
                all_logins = prioritized_logins + normal_logins

                for i, login in enumerate(all_logins):
                    self.stdout.write(self.style.NOTICE(
                        f"Retry {i+1}/{len(all_logins)} (current delay: {self.current_delay:.2f}s)"
                    ))

                    if process_login(login):
                        retry_success += 1
                        success_count += 1

                    # Take extra breaks during retry phase
                    if (i + 1) % 5 == 0 and i < len(all_logins) - 1:
                        self.stdout.write(self.style.NOTICE(f"Taking a short break after {i+1} retries"))
                        time.sleep(self.current_delay / 2)

                if retry_success < len(all_logins):
                    self.stdout.write(self.style.ERROR(
                        f"Failed to update {len(all_logins) - retry_success} LoginHistory records after retry."
                    ))

            # Final statistics
            elapsed = (datetime.now() - start_time).total_seconds()
            self.stdout.write(self.style.SUCCESS(
                f"Successfully updated {success_count} out of {total_logins} records in {elapsed/60:.1f} minutes."
            ))
            if success_count > 0:
                self.stdout.write(self.style.SUCCESS(
                    f"Average processing time: {elapsed/success_count:.2f} seconds per successful record."
                ))

            # Report API efficiency statistics
            if self.total_requests > 0:
                failure_rate = self.failed_requests / self.total_requests * 100
                self.stdout.write(self.style.SUCCESS(
                    f"API request statistics: {self.total_requests} total requests, {failure_rate:.1f}% failure rate"
                ))

                # Report error distribution
                if self.error_types:
                    self.stdout.write(self.style.SUCCESS("Error type distribution:"))
                    for error_type, count in self.error_types.items():
                        percentage = count / self.failed_requests * 100 if self.failed_requests > 0 else 0
                        self.stdout.write(self.style.SUCCESS(f"  - {error_type}: {count} ({percentage:.1f}%)"))

        # Use transaction.atomic only for smaller batches to avoid long-running transactions
        if max_records and max_records <= 100:
            with transaction.atomic():
                update_login_history()
        else:
            update_login_history()