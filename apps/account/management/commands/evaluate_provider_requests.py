from django.core.management.base import BaseCommand
from apps.account.models.provider_request import ProviderRequest
import anthropic
import json
import time
from utils.telegram_logger import telegram_logger
import re
PROMPT_TEMPLATE = """
You are an expert evaluator for an online Islamic (Shia-oriented) educational multilangual platform called Habib Meet. Users submit their profiles to apply as potential instructors who will deliver online educational sessions. Based on the provided user data, do the following:

1. Rate the user's suitability to be an instructor on a scale from 1 (not suitable) to 10 (highly suitable).
2. Determine if the user is suitable to teach at all.
3. Suggest up to 3 appropriate teaching categories for the user from the list below.
4. Write the comment and reasoning in **Persian (Farsi)**.

Allowed categories (only select from this list):

- Islamic Lectures (General)
- Shia Theology
- Quran Recitation (Tajwid)
- Islamic Ethics / Akhlaq
- Family and Parenting
- Youth and Teen Guidance
- Marriage and Relationships
- Religious Storytelling
- Eulogies (Maddahi)
- Educational Skills (e.g. Arabic, Farsi, English)
- Women's Programs
- Convert Support / Interfaith
- Not ready to teach (Learner)

Please return your response strictly in the following JSON format:

{{
  "score": [1-10],
  "suitable_for_teaching": [true/false],
  "suggested_categories": [list of up to 3 categories],
  "comment": "توضیح کوتاه به زبان فارسی"
}}

User data:
{user_data}
"""
def build_user_data(request):
    user = request.user
    return {
        "public_name": request.public_name,
        "description": request.description,
        "service": request.service,
        "username": user.username,
        "email": user.email,
    }

def evaluate_provider(request):
    prompt = PROMPT_TEMPLATE.format(user_data=json.dumps(build_user_data(request), ensure_ascii=False, indent=2))
    client = anthropic.Anthropic(
        api_key="************************************************************************************************************",
    )
    message = client.messages.create(
        model="claude-3-7-sonnet-latest",
        max_tokens=1024,
        messages=[
            {"role": "user", "content": prompt}
        ]
    )
    return message.content[0].text.strip()

class Command(BaseCommand):
    help = "ارزیابی درخواست‌های پرووایدری با Claude و ذخیره نتیجه"

    def add_arguments(self, parser):
        parser.add_argument('--ids', type=str, help='Comma separated ProviderRequest IDs (e.g. 1,2,3)', required=False)

    def handle(self, *args, **options):
        ids = options.get('ids')
        if ids:
            ids = [int(i) for i in ids.split(',') if i.strip().isdigit()]
            queryset = ProviderRequest.objects.filter(id__in=ids)
        else:
            queryset = ProviderRequest.objects.all()

        total = queryset.count()
        telegram_logger(f"شروع ارزیابی AI برای {total} درخواست پرووایدری...")

        success = 0
        for req in queryset:
            try:
                # اگر قبلاً ارزیابی شده، رد شو
                if req.ai_score is not None:
                    telegram_logger(f"ProviderRequest {req.id} قبلاً ارزیابی شده و رد شد.")
                    continue

                ai_response = evaluate_provider(req)
                telegram_logger(f"AI raw response for ProviderRequest {req.id}: {ai_response}")

                if not ai_response:
                    raise ValueError("AI response is empty or None.")

                # پاک‌سازی پیشرفته پاسخ
                match = re.search(r'\{[\s\S]*\}', ai_response)
                if match:
                    ai_response_clean = match.group(0)
                else:
                    ai_response_clean = ai_response.strip()

                result = json.loads(ai_response_clean)
                req.ai_score = result.get("score")
                req.ai_suitable_for_teaching = result.get("suitable_for_teaching")
                req.ai_suggested_categories = result.get("suggested_categories")
                req.ai_comment = result.get("comment")
                req.save()
                success += 1
                telegram_logger(f"ProviderRequest {req.id} ارزیابی و ذخیره شد.\nنتیجه: {result}")
            except Exception as e:
                print(e)
                telegram_logger(f"خطا در ارزیابی ProviderRequest {req.id}: {e}\nAI response: {ai_response if 'ai_response' in locals() else None}")
            time.sleep(0.5)

        telegram_logger(f"ارزیابی AI تکمیل شد: {success} از {total} درخواست.")