from django.core.management.base import BaseCommand
from django.db import transaction
from apps.account.models.geoip import GeoIPCity
from pathlib import Path
import geoip2.database
import ipaddress
import logging
import mmdb_reader

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Import GeoIP2 City data into database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            default='utils/country_city_db/GeoLite2-City.mmdb',
            help='Path to GeoLite2-City.mmdb file'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=1000,
            help='Batch size for bulk create'
        )

    def handle(self, *args, **options):
        file_path = Path(options['file'])
        batch_size = options['batch_size']
        
        if not file_path.exists():
            self.stderr.write(f"File not found: {file_path}")
            return

        # Clear existing data
        self.stdout.write("Clearing existing GeoIP data...")
        GeoIPCity.objects.all().delete()

        self.stdout.write("Starting GeoIP import...")
        records = []
        processed = 0

        try:
            # Open both the MMDB reader and the GeoIP2 reader
            with mmdb_reader.MMDBReader(str(file_path)) as mmdb, geoip2.database.Reader(file_path) as reader:
                # Iterate through all networks in the MMDB file
                for network, _ in mmdb.records_iter():
                    try:
                        # Get the first IP in the network
                        ip_network = ipaddress.ip_network(network)
                        first_ip = str(ip_network[0])
                        
                        # Get city data for this IP
                        response = reader.city(first_ip)

                        record = GeoIPCity(
                            network=str(network),
                            geoname_id=response.city.geoname_id if response.city else None,
                            registered_country_geoname_id=response.registered_country.geoname_id if response.registered_country else None,
                            represented_country_geoname_id=response.represented_country.geoname_id if response.represented_country else None,
                            is_anonymous_proxy=response.traits.is_anonymous_proxy,
                            is_satellite_provider=response.traits.is_satellite_provider,
                            postal_code=response.postal.code if response.postal else None,
                            latitude=response.location.latitude if response.location else None,
                            longitude=response.location.longitude if response.location else None,
                            accuracy_radius=response.location.accuracy_radius if response.location else None,
                            city_name=response.city.name if response.city else None,
                            country_code=response.country.iso_code if response.country else None,
                            network_start_ip=str(ip_network[0]),
                            network_end_ip=str(ip_network[-1])
                        )
                        records.append(record)
                        processed += 1

                        if len(records) >= batch_size:
                            with transaction.atomic():
                                GeoIPCity.objects.bulk_create(records)
                            self.stdout.write(f"Processed {processed} networks...")
                            records = []

                    except Exception as e:
                        logger.error(f"Error processing network {network}: {e}")
                        continue

                # Create remaining records
                if records:
                    with transaction.atomic():
                        GeoIPCity.objects.bulk_create(records)

        except Exception as e:
            self.stderr.write(f"Error importing GeoIP data: {e}")
            return

        self.stdout.write(f"Successfully imported {processed} networks") 