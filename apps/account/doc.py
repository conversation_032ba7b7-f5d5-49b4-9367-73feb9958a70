def doc_gust_auth():
    return """
    ### Guest Authentication API

    This API is invoked during the app's intro phase to authenticate guest users. It collects device information
    and performs authentication based on the provided data.    

    | **Field**           | **Type**   | **Required** | **Description**                        |
    |---------------------|------------|--------------|----------------------------------------|
    | `auth_method`              | String     | No           |  phone or third_party                 |
    | `country_code`              | int     | No           |       phone country code          |

    #### Response:
    - **200 OK**: Returns `token`, `auth_method`, and `country_code`.
    - **400 Bad Request**: Input validation failed.
    - **500 Internal Server Error**: Unexpected error.
    
    #### Example Response:

    ```json
    {
      "token": "1e126d575f3e37d5f8f0fd5a893fae90a7511c25",
      "auth_method": "phone",
      "country_code": 964
    },
    {
      "token": "1e126d575f3e37d5f8f0fd5a893fae90a7511c25",
      "auth_method": "third_party",
      "country_code": 964
    }
    ```
    """


def doc_phone_auth():
    return """
    ### Phone Authentication API

    This API allows users to authenticate by providing a phone number. A one-time password (OTP) is sent to the
    user via the specified method, and the response indicates whether the OTP was successfully sent.

    #### Response:
    - **200 OK**: Returns user ID, phone number, verification method, OTP send status, and time limit for resending the OTP.
    - **400 Bad Request**: Input validation failed.
    - **500 Internal Server Error**: Unexpected error occurred.

    #### Output Fields:

    | **Field**                 | **Type**   | **Description**                                            |
    |---------------------------|------------|----------------------------------------------------------|
    | `user_id`                 | String     | Unique identifier of the authenticated user.             |
    | `phone_number`            | String     | The phone number used for authentication.                |
    | `verification_method`     | String     | Method used to send the OTP (e.g., "SMS" or "WHATSAPP").|
    | `verification_time_limit` | Integer    | Time (in minutes) until the OTP can be resent.           |
    | `is_send`                 | Boolean    | Indicates whether the OTP was successfully sent.         |

    #### Example Response:

    ```json
    {
        "user_id": "1a2b3c4d",
        "phone_number": "+1234567890",
        "verification_method": "WHATSAPP",
        "verification_time_limit": 5,
        "is_send": true
    }
    ```
    """


def doc_phone_verify_auth():
    return """
    ### Phone Verification Logic

    The phone verification process ensures user authenticity through a one-time verification code. Below is the streamlined flow:

    1. **Input Validation **:
       - Accepts `user_id`, `phone_number`, and a 5-digit `code`.
       - Validates input and raises errors for missing or invalid data.

    2. **User Retrieval**:
       - Fetch the user using `user_id`.
       - Raise an error if the user does not exist.

    3. **Verification Data Fetch**:
       - Retrieve the verification data from Redis using `phone_number`.
       - Raise an error if data is missing or expired.

    4. **Code Comparison**:
       - Compare the provided `code` with the one in Redis.
       - Raise an error for mismatched codes.

    5. **User Update**:
       - On success, update the user's `phone_number` and mark them as verified.

    6. **Response Generation**:
       - Return an authentication token along with user profile details (e.g., name, email, location, settings).

    #### Failure Scenarios:
    - Errors are raised for invalid input, missing verification data, or incorrect codes.

    This process ensures secure and efficient phone verification.
    """


def doc_logout():
    return """
    ### User Logout API

    This API endpoint allows authenticated users to securely log out from the application. The logout process
    clears the user's FCM (Firebase Cloud Messaging) token to ensure they no longer receive push notifications
    on the logged-out device.

    #### Key Features:
    - **Authentication Required**: Only authenticated users can access this endpoint
    - **FCM Token Clearing**: Automatically clears the user's FCM token to stop push notifications
    - **Secure Logout**: Provides a clean logout process following REST API best practices
    - **Error Handling**: Comprehensive error handling with appropriate HTTP status codes

    #### Business Logic:
    1. **Authentication Check**: Verifies that the user is properly authenticated via token
    2. **FCM Token Cleanup**: Sets the user's FCM token field to null/empty
    3. **Database Update**: Saves the user record with the cleared FCM token
    4. **Response Generation**: Returns appropriate success or error response

    #### Authentication:
    - **Required**: Yes
    - **Method**: Token-based authentication
    - **Header**: `Authorization: Token <your-auth-token>`

    #### HTTP Method:
    - **POST**: Used for logout action (following REST conventions for state-changing operations)

    #### Request Headers:
    | **Header**        | **Type**   | **Required** | **Description**                    |
    |-------------------|------------|--------------|-----------------------------------|
    | `Authorization`   | String     | Yes          | Token <your-authentication-token> |
    | `Content-Type`    | String     | Yes          | application/json                  |

    #### Response Codes:
    - **200 OK**: Logout successful, FCM token cleared
    - **401 Unauthorized**: Authentication credentials not provided or invalid
    - **500 Internal Server Error**: Unexpected server error during logout

    #### Success Response Example:
    ```json
    {
        "status": "success",
        "message": "Logged out successfully"
    }
    ```

    #### Error Response Examples:

    **401 Unauthorized:**
    ```json
    {
        "detail": "Authentication credentials were not provided."
    }
    ```

    **500 Internal Server Error:**
    ```json
    {
        "status": "error",
        "message": "An error occurred during logout"
    }
    ```

    #### Usage Example:
    ```bash
    curl -X POST https://api.example.com/auth/logout/ \\
         -H "Authorization: Token 516e059d2a6b31d74b6a9e4c8f98fe4e8413efbc" \\
         -H "Content-Type: application/json"
    ```

    #### Security Considerations:
    - The FCM token is immediately cleared to prevent unauthorized push notifications
    - The authentication token remains valid until explicitly deleted or expired
    - Users should clear local storage/cache on the client side after successful logout
    - Consider implementing token blacklisting for enhanced security if needed

    #### Integration Notes:
    - After successful logout, clients should redirect users to the login screen
    - Clear any locally stored user data and authentication tokens
    - Stop any background services that depend on user authentication
    - Update UI state to reflect the logged-out status
    """
