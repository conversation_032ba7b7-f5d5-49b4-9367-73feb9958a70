from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from django.utils.translation import gettext_lazy as _
from apps.account.models import User


class CustomUserCreationForm(UserCreationForm):
    """
    Custom form for creating a new user in the admin panel.
    This form checks if the entered email already exists in the system.
    """
    email = forms.EmailField(
        label=_("Email"),
        required=True,
        help_text=_("Required. Enter a valid email address."),
    )
    
    class Meta:
        model = User
        fields = ('email', 'password1', 'password2', 'name', 'birthdate', 'country')
        
    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email and User.objects.filter(email=email).exists():
            raise forms.ValidationError(_('A user with this email already exists in the system.'))
        return email


class CustomUserChangeForm(UserChangeForm):
    """
    Custom form for editing a user in the admin panel.
    This form makes first_name field optional.
    """
    first_name = forms.Char<PERSON>ield(
        label=_("First name"),
        max_length=254,
        required=False,
    )
    
    class Meta:
        model = User
        fields = '__all__'


