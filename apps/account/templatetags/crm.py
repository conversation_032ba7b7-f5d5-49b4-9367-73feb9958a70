from os import path

from django import template

register = template.Library()


@register.filter
def base_name(v):
    return path.basename(v)


@register.simple_tag(takes_context=True)
def get_pagination(context, pagination):
    page_num = context['cl'].page_num
    return pagination.get_page(page_num)


@register.simple_tag(takes_context=True)
def get_elided_page_range(context, pagination):
    page_num = context['cl'].page_num
    return pagination.get_elided_page_range(page_num)
