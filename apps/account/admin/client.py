from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.forms import AdminPasswordChangeForm
from django.core.paginator import Paginator
from django.db.models import Q, F, Value
from django.db.models.functions import Coalesce
from django.forms import model_to_dict
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from django_countries.data import COUNTRIES
from import_export.admin import ImportExportModelAdmin
from import_export.fields import Field
from import_export.resources import ModelResource

from apps.account.models import User, LoginHistory, AllUser, LocationHistory, UserSettings
from apps.account.models.activity import Activity
from apps.account.forms import CustomUserCreationForm, CustomUserChangeForm

continents = {
    'Africa': ['DZ', 'AO', 'BJ', 'BW', 'BF', 'BI', 'CM', 'CV', 'CF', 'TD', 'KM', 'CG', 'CD', 'CI', 'DJ', 'EG', 'GQ', 'ER', 'ET', 'GA', 'GM', 'GH', 'GN', 'GW', 'KE', 'LS', 'LR', 'LY', 'MG', 'MW', 'ML', 'MR', 'MU', 'MA', 'MZ', 'NA', 'NE', 'NG', 'RW', 'ST', 'SN', 'SC', 'SL', 'SO', 'ZA', 'SS', 'SD', 'SZ', 'TZ', 'TG', 'TN', 'UG', 'EH', 'ZM', 'ZW'],
    'Asia': ['AF', 'AM', 'BH', 'BD', 'BT', 'BN', 'KH', 'CN', 'CY', 'GE', 'IN', 'ID', 'IR', 'IQ', 'IL', 'JP', 'JO', 'KZ', 'KW', 'KG', 'LA', 'LB', 'MY', 'MV', 'MN', 'MM', 'NP', 'KP', 'OM', 'PK', 'PS', 'PH', 'QA', 'SA', 'SG', 'KR', 'LK', 'SY', 'TW', 'TJ', 'TH', 'TL', 'TR', 'TM', 'AE', 'UZ', 'VN', 'YE'],
    'Europe': ['AL', 'AD', 'AT', 'BY', 'BE', 'BA', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR', 'DE', 'GR', 'HU', 'IS', 'IE', 'IT', 'LV', 'LI', 'LT', 'LU', 'MT', 'MD', 'MC', 'ME', 'NL', 'MK', 'NO', 'PL', 'PT', 'RO',  'SM', 'RS', 'SK', 'SI', 'ES', 'SE', 'CH', 'UA', 'GB', 'VA'],
    'North America': ['AG', 'BS', 'BB', 'BZ', 'CA', 'CR', 'CU', 'DM', 'DO', 'SV', 'GD', 'GT', 'HT', 'HN', 'JM', 'MX', 'NI', 'PA', 'KN', 'LC', 'VC', 'TT', 'US'],
    'Oceania': ['AU', 'FJ', 'KI', 'MH', 'FM', 'NR', 'NZ', 'PW', 'PG', 'WS', 'SB', 'TO', 'TV', 'VU'],
    'South America': ['AR', 'BO', 'BR', 'CL', 'CO', 'EC', 'GY', 'PY', 'PE', 'SR', 'UY', 'VE']
}

class GeoFilter(admin.SimpleListFilter):
    title = 'geo'
    parameter_name = 'geo'

    def lookups(self, request, model_admin):
        return []

    def queryset(self, request, queryset):
        return queryset


from geopy.distance import geodesic
from django.contrib import admin
from django.db.models import Q


class GeoFilter(admin.SimpleListFilter):
    title = _('Geographic Filter')
    parameter_name = 'geo'  # این باید مطابق با پارامترهای URL باشد که ممکن است به شکل lat|lon|radius ارسال شود

    def lookups(self, request, model_admin):
        # گزینه‌های خاصی برای نمایش داده نمی‌شود، مقادیر از URL گرفته می‌شود
        return []

    def queryset(self, request, queryset):
        geo_params = request.GET.get(self.parameter_name)
        if geo_params:
            try:
                lat, lon, radius = map(float, geo_params.split('|'))
                central_point = (lat, lon)
                radius = float(radius)  # فرض می‌کنیم که radius به کیلومتر است

                # لیستی از کاربران در محدوده مورد نظر
                user_ids_in_range = []
                for user in queryset:
                    for login_history in user.login_history.all():
                        if geodesic((login_history.lat, login_history.lon), central_point).km <= radius:
                            user_ids_in_range.append(user.id)
                            break

                queryset = queryset.filter(id__in=user_ids_in_range)
            except ValueError:
                # در صورتی که پارامترهای نامعتبر باشد، فیلتر اعمال نمی‌شود
                pass

        return queryset




from django.db.models import Exists, OuterRef, Q

class ExcludeCountriesFilter(admin.SimpleListFilter):
    title = _('Exclude Countries')
    parameter_name = 'exclude_countries'

    def lookups(self, request, model_admin):
        # نمایش لیستی از کشورها برای انتخاب
        return [(c, n) for c, n in COUNTRIES.items()]

    def queryset(self, request, queryset):
        if exclude_countries := request.GET.getlist('exclude_countries'):
            # استفاده از Subquery برای استثنا کردن کاربران با حداقل یک رکورد login history در کشورهای مستثنی
            excluded_login_history = LoginHistory.objects.filter(
                Q(country__iexact=OuterRef('country')) |
                Q(country__iexact__in=exclude_countries),
                user_id=OuterRef('pk')
            )

            # حذف کاربران بر اساس شرط وجودی رکوردهای مرتبط
            return queryset.exclude(Exists(excluded_login_history)).distinct()
        return queryset

class GoogleUsersFilter(admin.SimpleListFilter):
    parameter_name = 'is_google'
    title = "Has Google Account"

    def lookups(self, request, model_admin):
        return [[True, 1, ], [False, 0]]

    def queryset(self, request, queryset):
        if is_google := request.GET.get('is_google', None):
            if int(is_google):
                return queryset.filter(social_auth_data__oauth='google')
        return queryset


class CountryFilter(admin.SimpleListFilter):
    parameter_name = 'country'
    title = "Country"

    def lookups(self, request, model_admin):
        return [(c, n) for c, n in COUNTRIES.items()]

    def queryset(self, request, queryset):
        if country := request.GET.get('country', None):
            return queryset.filter(Q(country=country) | Q(login_history__country=country.lower()))
        return queryset



from django.db.models import Count
from urllib.parse import unquote  # برای decode کردن مقادیر URL-encoded

class MultipleCountriesFilter(admin.SimpleListFilter):
    title = _('Number of distinct countries')
    parameter_name = 'distinct_countries'

    def lookups(self, request, model_admin):
        return [
            ('2+', _('More than 2 countries')),
            ('3+', _('More than 3 countries')),
            ('4+', _('More than 4 countries')),
        ]

    def queryset(self, request, queryset):
        if self.value():
            # Decode کردن مقدار دریافتی و تبدیل آن به عدد
            value = unquote(self.value())
            threshold = int(value.strip('+'))
            return queryset.annotate(
                num_countries=Count('login_history__country', distinct=True)
            ).filter(num_countries__gt=threshold)
        return queryset


class MaritalStatusFilter(admin.SimpleListFilter):
    parameter_name = 'marital'
    title = "Marital Status"

    def lookups(self, request, model_admin):
        return (
            ('1', 'Married'),
            ('0', 'Single'),
        )

    def queryset(self, request, queryset):
        if marital := request.GET.get('marital', None):
            return queryset.filter(
                interest__isnull=False,
                interest__marital_status=True if marital == '1' else False,
            )
        return queryset

class LocationHistoryAdmin(admin.TabularInline):
    model = LocationHistory
    extra = 0
    readonly_fields = ('at_time',)
    ordering = ('-id',)

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj):
        return False

    def has_change_permission(self, request, obj=None):
        return False


class LoginHistoryAdmin(admin.TabularInline):
    model = LoginHistory
    extra = 0
    readonly_fields = ('last_login_at',)
    ordering = ('-id',)

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj):
        return False

    def has_change_permission(self, request, obj=None):
        return False


class ActionHistoryAdmin(admin.TabularInline):
    model = Activity
    extra = 0
    readonly_fields = ()
    ordering = ('-id',)

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj):
        return False

    def has_change_permission(self, request, obj=None):
        return False

EXCLUDED_COORDINATES = [
    (32.616565, 44.03462),
    (51.5287718, -0.2416802)
]


class ContinentFilter(admin.SimpleListFilter):
    title = _('Continent')
    parameter_name = 'continent'

    def lookups(self, request, model_admin):
        return [(continent, continent) for continent in continents.keys()]

    def queryset(self, request, queryset):
        if continent := self.value():
            countries = continents.get(continent, [])
            exclusion_conditions = Q()
            for lat, lon in EXCLUDED_COORDINATES:
                exclusion_conditions |= Q(login_history__lat=lat, login_history__lon=lon)

            return queryset.filter(
                Q(country__in=countries) | Q(login_history__country__in=countries)
            ).exclude(exclusion_conditions).distinct()
        return queryset



class UserSettingsInline(admin.StackedInline):  
    model = UserSettings
    can_delete = False
    verbose_name_plural = _('User Settings')
    fk_name = 'user'
    fields = ('settings', 'service_versions', 'is_tester', 'is_reminder', 'api_version','bucket_services','notification_settings')
    


@admin.register(User)
class UsersAdmin(UserAdmin, AjaxDatatable):
    add_form = CustomUserCreationForm
    form = CustomUserChangeForm
    change_password_form = AdminPasswordChangeForm
    
    inlines = [
        LocationHistoryAdmin, LoginHistoryAdmin, ActionHistoryAdmin, UserSettingsInline
    ]
    change_list_template = 'admin/changelist_users.html'
    
    search_fields = (
        'email', 'name', 'mobile_device_id',  'device_brand'
    )
    list_display = (
        'mobile_device_id', 'date_joined', 'country', 'city', 'language', 'last_login', 'email', '_age', '_interests',
        '_talk_rooms', 'is_tester_display')

    list_filter = (
        'is_active', 'os_platform', 'language', 'interest__interests', GoogleUsersFilter, CountryFilter, GeoFilter,
        MaritalStatusFilter, MultipleCountriesFilter, ContinentFilter)

    ordering = ('-id',)

    readonly_fields = ('country', 'city', 'date_joined', 'last_login', 'mobile_device_id', 'social_auth_data', 'get_auth_token', 'deleted_data')
    exclude = ('password', 'user_permissions')
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2', 'name'),
        }),
    )
    latest_by = 'date_joined'
    fieldsets = (
        (_('Personal info'),
         {'fields': (
             'password', 'name', 'first_name', 'phone_number', 'email', 'language', 'get_auth_token', 'country', 'city', 'avatar')}),
        (_('Device Info'),
         {'fields': (
             'cpu_type', 'model_name', 'os_platform', 'screen_size', 'device_brand', 'mobile_device_id', 'fcm')}),
        (_('Important dates'), {'fields': ('last_login', 'date_joined', 'social_auth_data', 'deleted_data')}),
        (_('Permissions'), {'fields': ('groups', 'is_staff')}),
    )
    
    def get_inline_instances(self, request, obj=None):
        if not obj:  
            return []
        return super().get_inline_instances(request, obj)
    
    def save_model(self, request, obj, form, change):
        # Save the User model without affecting the inlines
        super().save_model(request, obj, form, change)
        
    @admin.display(description="Is Tester", ordering="settings__is_tester", boolean=True)
    def is_tester_display(self, obj):
        try:
            return obj.settings.is_tester
        except UserSettings.DoesNotExist:
            return False

    def get_auth_token(self, obj):
        if hasattr(obj, 'auth_token'):
            return obj.auth_token.key
        return None
    
    get_auth_token.short_description = _('Auth Token')  

    @admin.action(description='Set selected users as Tester')
    def make_tester(self, request, queryset):
        for user in queryset:
            user_settings, created = UserSettings.objects.update_or_create(
                user=user, 
                defaults={'is_tester': True}  
            )            
    @admin.action(description='Soft delete selected accounts')
    def delete_account(self, request, queryset):
        for user in queryset:
            user.soft_delete()
        self.message_user(request, f"{queryset.count()} accounts have been soft deleted successfully.")

    actions = [make_tester, delete_account]
        

    @admin.display(description="Interest", ordering='interest_id')
    def _interests(self, obj):
        if obj.interest_id:
            return mark_safe(
                f"<a href='/admin/account/userinterest/{obj.interest_id}/change/'>Interests</a>"
            )

        return '-'

    @admin.display(description="Age", ordering='age')
    def _age(self, obj):
        return {
            0: '-10',
            1: '11-15',
            2: '16-25',
            3: '26-40',
            4: '+40',
        }.get(obj.age, '-')

    @admin.display(description="TalkRooms", )
    def _talk_rooms(self, obj):
        return mark_safe(
            f"<a href='/admin/q_and_a/rooms/?user__username={obj.username}'>Talk Rooms</a>"
        )

    def get_point_radius(self, lat: float, lon: float, radius: int):
        import geopy
        from geopy.distance import geodesic
        radius = int(radius)
        point = geopy.Point(lat, lon)
        max1, max2, _ = tuple(geodesic(kilometers=radius).destination(point, radius))
        _, min1, _ = tuple(geodesic(kilometers=radius).destination(point, -radius))
        min2, _, _ = tuple(geodesic(kilometers=-radius).destination(point, radius))
        return max1, max2, min1, min2

    def get_queryset(self, request):
        qs = self.model.objects.prefetch_related('interest').annotate(
            interest_id=F('interest__id'),
            age=Coalesce('interest__age', Value(-1))
        )

        if geo := request.GET.get('geo'):
            if geo.count('|') == 2:
                lat, lon, radius = geo.split('|')
                x1, x2, y1, y2 = self.get_point_radius(lat, lon, radius)
                qs = qs.filter(login_history__lat__range=(y2, x1), login_history__lon__range=(y1, x2))

        return qs.distinct()


class UsersExportResource(ModelResource):
    login_history = Field(column_name='login_history')

    def dehydrate_login_history(self, obj):
        return [
            model_to_dict(i, fields=['lat', 'lon', 'country', 'city', 'ip', 'timezone', 'last_login_at']) for i in
            obj.login_history.all()
        ]

    class Meta:
        model = User
        exclude = ('password', 'groups', 'user_permissions')


# @admin.register(GuestUser)
class GuestUserAdmin(UsersAdmin):
    list_display = ('mobile_device_id', 'date_joined', 'country', 'city', 'last_login')

    def get_queryset(self, request):
        return self.model.objects.guest_users()


def paginate(obj, page, request):
    paginator = Paginator(object_list=obj, per_page=page)
    page_number = request.GET.get('p')

    return paginator.get_page(page_number)


# @admin.register(AllUser)
# class AllUserAdmin(ImportExportModelAdmin, UsersAdmin):
#     list_filter = ('country', 'city', 'language', 'device_brand', GeoFilter)
#     search_fields = ('mobile_device_id', 'city', 'country')
#     list_per_page = 24
#     change_list_template = "admin/list_user.html"
#     resource_class = UsersExportResource
#     export_template_name = 'admin/export_account.html'
#     ordering = ('-date_joined',)
#
#     inlines = [
#         LoginHistoryAdmin,
#     ]
#
#     def has_add_permission(self, request, obj=None):
#         return False
#
#     def get_queryset(self, request):
#         qs = super(AllUserAdmin, self).get_queryset(request)
#
#         if geo := request.GET.get('geo'):
#             lat, lon, radius = geo.split('|')
#             x1, x2, y1, y2 = self.get_point_radius(lat, lon, radius)
#             qs = qs.filter(login_history__lat__range=(y2, x1), login_history__lon__range=(y1, x2))
#         return qs.exclude(mobile_device_id__isnull=True, mobile_device_id='')
#
#     def get_point_radius(self, lat: float, lon: float, radius: int):
#         import geopy
#         from geopy.distance import geodesic
#         radius = int(radius)
#         point = geopy.Point(lat, lon)
#         max1, max2, _ = tuple(geodesic(kilometers=radius).destination(point, radius))
#         _, min1, _ = tuple(geodesic(kilometers=radius).destination(point, -radius))
#         min2, _, _ = tuple(geodesic(kilometers=-radius).destination(point, radius))
#         return max1, max2, min1, min2
