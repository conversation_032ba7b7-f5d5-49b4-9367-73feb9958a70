from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from ..models.activity import Activity


@admin.register(Activity)
class ActivityAdmin(AjaxDatatable):
    list_display = ('user', 'service', 'usage_duration', 'entered_at')
    ordering = ('-id',)
    search_fields = (
        'service', 'user__email'
    )
    list_filter = ('service',)
    latest_by = 'entered_at'
    autocomplete_fields = ('user',)
