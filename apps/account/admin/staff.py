from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.forms import AdminPasswordChangeForm
from django.utils.translation import gettext_lazy as _

from apps.account.models import AdminUser
from apps.account.forms import CustomUserCreationForm, CustomUserChangeForm


@admin.register(AdminUser)
class AdminsAdmin(UserAdmin, AjaxDatatable):
    add_form = CustomUserCreationForm
    form = CustomUserChangeForm
    change_password_form = AdminPasswordChangeForm
    
    list_display = ('email', 'first_name', 'name', 'is_staff', 'last_login')
    ordering = 'email',
    readonly_fields = ('date_joined', 'last_login',)
    exclude = ('password', 'user_permissions')
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2', 'name', 'avatar'),
        }),
    )
    search_fields = (
        'email', 'first_name', 'name', 'country', 'city', 'phone_number', 'device_brand', 'mobile_device_id'
    )
    fieldsets = (
        (_('Personal info'), {'fields': ('username', 'name', 'email', 'avatar')}),
        (_('Permissions'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'password'),
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined', 'fcm')}),
    )

    def save_model(self, request, obj, form, change) -> None:
        obj.is_staff = 1
        super(AdminsAdmin, self).save_model(request, obj, form, change)

    def get_queryset(self, request):
        return super(AdminsAdmin, self).get_queryset(request).filter(is_staff=1)
