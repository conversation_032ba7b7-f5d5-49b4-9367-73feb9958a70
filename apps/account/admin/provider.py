import json
from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.safestring import mark_safe
from django.utils.html import format_html
from django import forms
from filer.models import Image

from apps.account.models import ProviderRequest
from apps.meet.models import MeetProviderProfile
from apps.hussainiya.provider.models import ProviderProfile
from django.contrib import messages

class ProviderRequestForm(forms.ModelForm):
    class Meta:
        model = ProviderRequest
        exclude = ['public_name']  

    def save(self, commit=True):
        if self.cleaned_data.get('service') == ProviderRequest.Service.talk:
            self.instance.public_name = 'talk-provider'
        elif self.cleaned_data.get('service') == ProviderRequest.Service.meet:
            self.instance.public_name = 'meet-provider'
        
        # self.instance.is_custom = False
        
        return super().save(commit=commit)


@admin.register(ProviderRequest)
class ProviderRequestAdmin(AjaxDatatable):
    form =  ProviderRequestForm
    list_display = ('user', '_service', '_status', 'public_name','created_at','ai_score','ai_suggested_categories')
    readonly_fields = ('request_info', 'created_at', 'public_name')
    search_fields = (
      'service', 'user__username',
    )
    list_filter = ('service', 'status','ai_suitable_for_teaching')
    autocomplete_fields = ('user',)

    latest_by = ('created_at',)
    ordering = '-created_at',

    fieldsets = (
       (None, {
            'fields': ('user', 'service', 'status', 'is_custom', 'description','ai_score','ai_suitable_for_teaching','ai_suggested_categories','ai_comment')

       }),
       ('Request Information', {
           'fields': ('request_info',),
       }),
    )

    def request_info(self, obj):
        profile, is_url = self.get_profile(obj)
        if not profile:
            return 'No profile information available.'

        avatar_html = self.get_avatar_html(profile['avatar'], is_url)
        return mark_safe(f'''
            <div style="border: 1px solid #ccc; border-radius: 5px; padding: 10px; margin-bottom: 15px; background-color: #f9f9f9;">
                {avatar_html}
                {self.get_info_rows(profile)}
            </div>
        ''')
        
        
    def get_profile(self, obj):
        if obj.service == 'talk':
            provider = json.loads(obj.description.split('Data: ')[-1])
            return provider, True
        profile_model = ProviderProfile if obj.service == 'hussainiya' else MeetProviderProfile
        profile = profile_model.objects.filter(request=obj).first()
        if profile:
            return {
                'full_name': profile.full_name, 'email': profile.email, 'bio': profile.bio,
                'birthdate': profile.birthdate, 'institution_name': profile.institution_name,
                'social_medias': profile.social_medias, 'user_id': profile.user.id,
                'languages': list(profile.languages.values_list('name', flat=True)),
                'wa_number': profile.wa_number, 'avatar': profile.avatar
            }, False
        return None, False

    def get_avatar_html(self, avatar, is_url):
        if avatar:
            avatar = avatar if is_url else avatar.url 
            return format_html(
                '<div style="text-align: center; margin-bottom: 10px;">'
                '<img src="{}" style="border-radius: 50%; width: 150px; height: 150px; object-fit: cover;" alt="Avatar">'
                '</div>', avatar
            )
        return ''

    def get_info_rows(self, profile):
        fields = ['full_name', 'user_id', 'email', 'institution_name', 'bio', 'birthdate', 'languages', 'wa_number', 'social_medias']
        return ''.join([f"<p><strong>{field.replace('_', ' ').title()}:</strong> {profile.get(field, 'N/A')}</p>" for field in fields])

    request_info.short_description = "Request Information"
    


    @admin.display(description='Status', ordering='status')
    def _status(self, obj):
        if obj.status == "pending":
            m = "<span class='badge badge-secondary'>Pending</span>"
        elif obj.status == 'accepted':
            m = "<span class='badge badge-success'>Accepted</span>"
        elif obj.status == 'rejected':
            m = "<span class='badge badge-danger'>Rejected</span>"
        else:
            m = '-'
        return mark_safe(m)


    @admin.display(description='Service', ordering='service')
    def _service(self, obj):
        if obj.service == "talk":
            m = "<span class='badge badge-yellow'>Talk</span>"
        elif obj.service == 'meet':
            m = "<span class='badge badge-primary'>Meet</span>"
        elif obj.service == 'hussainiya':
            m = "<span class='badge badge-burgundy'>Hussainiya</span>"
        else:
            m = '-'
        return mark_safe(
            ''' 
            <style>
              .badge-burgundy {
                background-color: #800020;
                color: white;
               }
              .badge-yellow {
                background-color: #FFC107; 
                color: white;
              }
            </style>
            '''+ m
        )
