from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.contrib.admin.views.main import ChangeList
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.contrib.admin import SimpleListFilter
from django.db.models import Q
from django.utils.html import mark_safe

from apps.account.fcm_notification import send_notification
from apps.account.models import AllUser, PushMessage
from apps.eightmag.models import Eightmag
from apps.hadis.models import Hadis
from apps.q_and_a.models import PushNotification


class PushMessageDataModelFilter(SimpleListFilter):
    title = 'Model Type'  
    parameter_name = 'model'  

    def lookups(self, request, model_admin):
        return (
            ('Hadis', 'Hadis'),
            ('Eightmag', 'Eightmag'),
            ('Ahkam', 'Ahkam'),
            ('Donate', 'Donate'),
            ('Meet', 'Meet'),
            ('<PERSON><PERSON>', '<PERSON><PERSON>'),
        )
    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(data__model=self.value())
        return queryset

@admin.register(PushMessage)
class PushMessageAdmin(AjaxDatatable):
    list_display = ('title', '_success', '_users', 'created_at', 'updated_at')
    search_fields = ('title', 'message',)
    autocomplete_fields = ('users',)
    readonly_fields = ('result',)
    change_form_template = 'admin/pushmessage_change_form.html'
    ordering = ('-id',)
    latest_by = 'created_at'
    list_filter = (PushMessageDataModelFilter,)


    @admin.display(description=_('Users'))
    def _users(self, obj):
        users = obj.users.all()
        if not users:
            return '-'
        user_names = []
        for user in users:
            name = user.email
            if name:
                user_names.append(name)
        return mark_safe(' | '.join(user_names))
    

    def get_search_results(self, request, queryset, search_term):
        # اجرای جستجوی پیش‌فرض برای title و message
        queryset, use_distinct = super().get_search_results(request, queryset, search_term)

        if search_term:
            user_ids = AllUser.objects.filter(Q(email__icontains=search_term)).values_list('id', flat=True)
            queryset |= self.model.objects.filter(users__in=user_ids)

        return queryset, use_distinct
    
    @admin.display(ordering='result', description=_('Success'))
    def _success(self, obj):
        try:
            if obj.result[0]["success"]:
                return 'Yes'
            else:
                return 'No'
        except:
            return '-'

    def get_changeform_initial_data(self, request):
        initial = super().get_changeform_initial_data(request)

        if request.GET.get('users') or not request.GET:
            return initial

        admin_instance: ChangeList = admin.site._registry[AllUser].get_changelist_instance(request)
        if user_filter_query := admin_instance.get_queryset(request).exclude(fcm='', fcm__isnull=True):
            initial['users'] = user_filter_query

        return initial

    def save_related(self, request, form, formsets, change):
        super(PushMessageAdmin, self).save_related(request, form, formsets, change)
        self.send_fcm_notif(form.instance)

    @staticmethod
    def send_fcm_notif(obj):
        ids = list(obj.users.values_list('fcm', flat=True))
        model, _id = obj.data['model'], obj.data['value']
        data = {'navigation': '', 'data': ''}

        if model == 'Ahkam':
            data['navigation'] = '/Masael'
            data['data'] = _id

        elif model == 'Mafatih':
            data['navigation'] = '/doas'
            data['data'] = _id

        elif model == 'Eightmag':
            data['navigation'] = '/eightDetailPage'
            slug = ''
            if eightmag := Eightmag.objects.filter(id=_id).first():
                slug = eightmag.slug

            data['data'] = reverse('eightmag-detail', kwargs={'slug': slug})

        elif model == 'Hadis':
            data = {
                "model": "hadis",
                "data": None,
                "navigation": "/HadisDailyPage"
            }
            # print(f'---> {data}')
            
            # obj.data.pop('value')
            # data = obj.data
            # data['navigation'] = '/HadithMainPage'
            # data['data'] = '1'
            # hadis = Hadis.objects.get(id=_id)
            # data['data'] = hadis.number


        elif model == 'Donate':
            data['navigation'] = '/donate'
            data['data'] = 'Donate'

        response = send_notification(ids, title=obj.title, body=obj.message, data=data)
        obj.result = response
        obj.save()
        return response

    # def get_model_perms(self, request):
        # return {}
