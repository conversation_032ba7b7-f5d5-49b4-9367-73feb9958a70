from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin

from ..models import LocationHistory


@admin.register(LocationHistory)
class LocationHistoryAdmin(AjaxDatatable):
    list_display = ('user', 'country', 'city', 'at_time')
    ordering = ('-id',)
    search_fields = (
        'user', 'city'
    )
    list_filter = ('country',)
    latest_by = 'at_time'
    autocomplete_fields = ('user',)
