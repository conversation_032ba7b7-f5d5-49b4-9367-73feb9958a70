import json

from ajaxdatatable.admin import AjaxDatatable
from dj_language.models import Language
from django import forms
from django.contrib import admin
from django.db.models import Count
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from apps.account.models.interest import Interest, UserInterest
from utils.json_editor_field import JsonEditorWidget


def get_translation_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Translation')),
            'properties': {
                'title': {'type': 'string', 'title': str(_('Title'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }


class InterestForm(forms.ModelForm):
    class Meta:
        model = Interest
        exclude = ()
        widgets = {
            'translations': JsonEditorWidget(attrs={'schema': get_translation_schema}),
        }


@admin.register(Interest)
class InterestAdmin(AjaxDatatable):
    list_display = ('title', '_translation', 'priority', '_tags', '_hadis')
    form = InterestForm
    autocomplete_fields = ('tags',)
    search_fields = ('title',)

    @admin.display(description=_('Tags'), ordering='tags_count')
    def _tags(self, obj):
        return obj.tags_count

    @admin.display(description=_('Related hadis'), ordering='hadis_count')
    def _hadis(self, obj):
        link = reverse('admin:hadis_hadis_changelist') + f'?interest={obj.id}'
        return mark_safe(
            "<a href='%s' target='_blank' >%s حدیث</a>" % (link, obj.hadis_count)
        )

    @admin.display(description=_('Translation'), ordering='translations')
    def _translation(self, obj):
        return mark_safe(" | ".join([i['language_code'] for i in obj.translations]))

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.annotate(
            tags_count=Count('tags', distinct=True),
            hadis_count=Count('tags__hadis', distinct=True)
        )


@admin.register(UserInterest)
class UserInterestAdmin(AjaxDatatable):
    list_display = ('user', 'gender', 'age', 'marital_status', 'has_children', '_interests', 'created_at',)
    readonly_fields = ('user',)
    autocomplete_fields = ('interests',)

    @admin.display(description=_('Interests'), ordering='interests_count')
    def _interests(self, obj):
        return obj.interests_count

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.annotate(
            interests_count=Count('interests'),
        )
