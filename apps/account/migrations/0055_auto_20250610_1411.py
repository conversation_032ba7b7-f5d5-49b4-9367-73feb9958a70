# Generated by Django 3.2.25 on 2025-06-10 14:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0054_auto_20250531_1728'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='geonamescity',
            index=models.Index(fields=['latitude', 'longitude'], name='geonames_ci_latitud_443791_idx'),
        ),
        migrations.AddIndex(
            model_name='geonamescity',
            index=models.Index(fields=['country_code'], name='geonames_ci_country_9c873a_idx'),
        ),
        migrations.AddIndex(
            model_name='geonamescity',
            index=models.Index(fields=['feature_class'], name='geonames_ci_feature_85b0fe_idx'),
        ),
        migrations.AddIndex(
            model_name='geonamescity',
            index=models.Index(fields=['feature_class', 'latitude', 'longitude'], name='idx_geonames_feature_lat_lon'),
        ),
        migrations.AddIndex(
            model_name='geonamescity',
            index=models.Index(condition=models.Q(('feature_class', 'P')), fields=['latitude'], name='idx_geonames_lat_populated'),
        ),
        migrations.AddIndex(
            model_name='geonamescity',
            index=models.Index(condition=models.Q(('feature_class', 'P')), fields=['longitude'], name='idx_geonames_lon_populated'),
        ),
    ]
