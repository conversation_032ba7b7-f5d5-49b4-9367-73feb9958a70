# Generated by Django 3.2.13 on 2022-11-24 16:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('tag', '0003_tagginglog'),
        ('account', '0020_alter_user_mobile_device_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='Interest',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='title (en)')),
                ('priority', models.PositiveSmallIntegerField(blank=True, default=0)),
                ('translations', models.JSONField(default=dict, verbose_name='translations')),
                ('tags', models.ManyToManyField(to='tag.Tag')),
            ],
            options={
                'verbose_name': 'interest',
                'verbose_name_plural': 'interests',
                'ordering': ('-priority', '-id'),
            },
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True, unique=True, verbose_name='email'),
        ),
        migrations.CreateModel(
            name='UserInterest',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female')], max_length=255, verbose_name='gender')),
                ('age', models.PositiveSmallIntegerField(verbose_name='age')),
                ('marital_status', models.BooleanField(verbose_name='marital status')),
                ('has_children', models.BooleanField(verbose_name='has children')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('interests', models.ManyToManyField(to='account.Interest', verbose_name='interests')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='interest', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'user interests',
                'verbose_name_plural': 'users interests',
                'ordering': ('-id',),
            },
        ),
    ]
