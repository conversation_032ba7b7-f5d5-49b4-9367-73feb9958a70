# Generated by Django 3.2.12 on 2022-05-21 12:43

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0011_alter_user_mobile_device_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='AllUser',
            fields=[
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'all users',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('account.user',),
        ),
        migrations.CreateModel(
            name='PushMessage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='title')),
                ('message', models.CharField(max_length=512, verbose_name='message')),
                ('url', models.URLField(verbose_name='url')),
                ('users', models.ManyToManyField(to=settings.AUTH_USER_MODEL, verbose_name='users')),
            ],
        ),
    ]
