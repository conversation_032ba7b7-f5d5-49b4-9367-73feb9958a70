# Generated by Django 3.2.7 on 2023-04-16 14:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0026_userinterest_tend_to_marry'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='userinterest',
            name='has_children',
            field=models.BooleanField(blank=True, default=None, null=True, verbose_name='has children'),
        ),
        migrations.AlterField(
            model_name='userinterest',
            name='marital_status',
            field=models.BooleanField(blank=True, default=None, null=True, verbose_name='marital status'),
        ),
        migrations.AlterField(
            model_name='userinterest',
            name='tend_to_marry',
            field=models.<PERSON>oleanField(blank=True, default=None, null=True, verbose_name='tend to marry'),
        ),
    ]
