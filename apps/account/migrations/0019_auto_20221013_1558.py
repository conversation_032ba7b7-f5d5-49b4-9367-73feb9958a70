# Generated by Django 3.2.13 on 2022-10-13 15:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0018_remove_pushmessage_url'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='loginhistory',
            options={'ordering': ('-id',), 'verbose_name': 'login history', 'verbose_name_plural': 'user login histories'},
        ),
        migrations.RemoveField(
            model_name='user',
            name='last_name',
        ),
        migrations.AddField(
            model_name='loginhistory',
            name='location_method',
            field=models.CharField(blank=True, choices=[('DEFAULT', 'DEFAULT'), ('MANUAL', 'MANUAL'), ('AUTO', 'AUTO')], max_length=19, null=True),
        ),
        migrations.AddField(
            model_name='loginhistory',
            name='mobile_device_id',
            field=models.Char<PERSON>ield(blank=True, max_length=254, null=True, verbose_name='mobile device id'),
        ),
        migrations.AddField(
            model_name='user',
            name='name',
            field=models.CharField(max_length=254, null=True, verbose_name='name'),
        ),
    ]
