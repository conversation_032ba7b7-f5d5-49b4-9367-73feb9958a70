# Generated by Django 3.2.23 on 2024-01-09 15:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0035_user_username'),
    ]

    operations = [
        migrations.CreateModel(
            name='LocationHistory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lat', models.FloatField(verbose_name='lat')),
                ('lon', models.FloatField(verbose_name='lon')),
                ('country', models.CharField(blank=True, max_length=255, null=True, verbose_name='country')),
                ('city', models.CharField(blank=True, max_length=255, null=True, verbose_name='city')),
                ('at_time', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='location_history', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
