# Generated by Django 3.2.25 on 2025-04-29 19:50

from django.db import migrations
import multiselectfield.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0050_usersettings_is_reminder'),
    ]

    operations = [
        migrations.AddField(
            model_name='usersettings',
            name='bucket_services',
            field=multiselectfield.db.fields.MultiSelectField(blank=True, choices=[('hussainiyah', 'Hussainiyah'), ('quran', 'Quran'), ('mafatih', 'Ma<PERSON>tih'), ('qiblah', 'Qiblah Finder'), ('ahkam', 'Ahkam'), ('calendar', 'Calendar'), ('talk', 'Talk'), ('meet', 'Meet'), ('library', 'Library'), ('hadis', 'Hadis'), ('habit', 'Habit')], default=[], max_length=77, null=True, verbose_name='Bucket Services'),
        ),
        migrations.AddField(
            model_name='usersettings',
            name='notification_settings',
            field=multiselectfield.db.fields.MultiSelectField(blank=True, choices=[('hussainiyah', '<PERSON>iyah'), ('meet', 'Meetings'), ('talk', 'Talks'), ('library', 'Library')], default=['hussainiyah', 'meet', 'talk', 'library'], max_length=29, null=True, verbose_name='Notification setting'),
        ),
    ]
