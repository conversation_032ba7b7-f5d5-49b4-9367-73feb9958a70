# Generated by Django 3.2.25 on 2024-12-21 01:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import filer.fields.image


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.FILER_IMAGE_MODEL),
        ('account', '0043_auto_20241108_1731'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='pushpanel',
            options={'verbose_name': 'Push Panel', 'verbose_name_plural': 'Push Panel'},
        ),
        migrations.AddField(
            model_name='pushpaneldata',
            name='image',
            field=filer.fields.image.FilerImageField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.FILER_IMAGE_MODEL, verbose_name='تصویر در نوتفیکیشن'),
        ),
        migrations.AddField(
            model_name='pushpaneluser',
            name='is_read',
            field=models.BooleanField(default=False, verbose_name='Is Read'),
        ),
        migrations.AlterField(
            model_name='pushpanel',
            name='service',
            field=models.CharField(choices=[('hussainiya', 'Hussainiya'), ('library', 'Library'), ('talk', 'Talk'), ('meet', 'Meet'), ('mafatih', 'Mafatih'), ('hadis', 'Hadis'), ('khatm', 'Khatm'), ('quran', 'Quran'), ('tafsir', 'Tafsir'), ('ahkam', 'Ahkan'), ('coin', 'Coin'), ('global', 'Global'), ('null', 'خالی')], help_text='نام سرویس نوتیفیکیشن(اگر سرویسی نمیخواهید خالی را انتخاب کنید)', max_length=100),
        ),
        migrations.AlterField(
            model_name='pushpaneldata',
            name='data',
            field=models.CharField(blank=True, help_text=' آیدی آبجکت بر اساس سرویس انتخاب شده را برای منتقل شدن کاربر هنگام کلیک بر روی نوتفیکیشن وارد کنید', max_length=255, null=True, verbose_name='رکورد مورد نظر'),
        ),
    ]
