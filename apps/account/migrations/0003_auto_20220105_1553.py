# Generated by Django 3.2.5 on 2022-01-05 15:53

import dj_language.field
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dj_language', '0001_initial'),
        ('account', '0002_user_avatar'),
    ]

    operations = [
        migrations.AddField(
            model_name='loginhistory',
            name='api_v',
            field=models.CharField(max_length=64, null=True, verbose_name='API Version'),
        ),
        migrations.AddField(
            model_name='user',
            name='language',
            field=dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language'),
        ),
    ]
