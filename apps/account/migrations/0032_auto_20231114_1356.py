# Generated by Django 3.2.22 on 2023-11-14 13:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0031_user_is_provider'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='is_provider',
        ),
        migrations.CreateModel(
            name='ProviderRequest',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('rejected', 'Rejected'), ('accepted', 'Accepted')], default='pending', max_length=16, verbose_name='status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('service', models.CharField(choices=[('hussain<PERSON>', 'Hussain<PERSON>'), ('net', 'Net'), ('library', 'Library'), ('talk', 'Talk'), ('shop', 'Shop'), ('travel', 'Travel')], max_length=16, verbose_name='service')),
                ('public_name', models.CharField(max_length=255, verbose_name='public name')),
                ('description', models.TextField(verbose_name='description')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'provider request',
                'verbose_name_plural': 'provider requests',
                'ordering': ('-id',),
            },
        ),
    ]
