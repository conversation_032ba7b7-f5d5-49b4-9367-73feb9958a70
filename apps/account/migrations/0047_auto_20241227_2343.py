# Generated by Django 3.2.25 on 2024-12-27 23:43

import apps.account.validators
from django.db import migrations, models
import phonenumber_field.modelfields


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0046_auto_20241222_1325'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='is_verified',
            field=models.BooleanField(default=False, verbose_name='Is the phone number verified'),
        ),
        migrations.AddField(
            model_name='user',
            name='possible_phone_number',
            field=phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=254, null=True, region=None, validators=[apps.account.validators.validate_possible_number], verbose_name='possible phone number'),
        ),
    ]
