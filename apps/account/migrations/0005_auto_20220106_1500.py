# Generated by Django 3.2.5 on 2022-01-06 15:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0004_user_cpu_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='device_brand',
            field=models.CharField(blank=True, max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='model_name',
            field=models.CharField(blank=True, max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='os_platform',
            field=models.Char<PERSON>ield(blank=True, max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='screen_size',
            field=models.CharField(blank=True, max_length=120, null=True),
        ),
    ]
