# Generated by Django 3.2.25 on 2025-05-18 11:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0051_auto_20250429_1950'),
    ]

    operations = [
        migrations.CreateModel(
            name='GeoIPCity',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('network', models.CharField(db_index=True, max_length=50)),
                ('geoname_id', models.IntegerField(null=True)),
                ('registered_country_geoname_id', models.IntegerField(null=True)),
                ('represented_country_geoname_id', models.IntegerField(null=True)),
                ('is_anonymous_proxy', models.BooleanField(default=False)),
                ('is_satellite_provider', models.BooleanField(default=False)),
                ('postal_code', models.<PERSON>r<PERSON>ield(max_length=20, null=True)),
                ('latitude', models.<PERSON><PERSON><PERSON><PERSON>(null=True)),
                ('longitude', models.FloatField(null=True)),
                ('accuracy_radius', models.IntegerField(null=True)),
                ('city_name', models.CharField(max_length=200, null=True)),
                ('country_code', models.CharField(db_index=True, max_length=2, null=True)),
                ('network_start_ip', models.GenericIPAddressField(db_index=True)),
                ('network_end_ip', models.GenericIPAddressField()),
            ],
            options={
                'db_table': 'geoip_city',
            },
        ),
        migrations.CreateModel(
            name='GeoNamesCity',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('country_code', models.CharField(max_length=2)),
                ('latitude', models.FloatField(db_index=True)),
                ('longitude', models.FloatField(db_index=True)),
                ('feature_class', models.CharField(db_index=True, max_length=1)),
                ('population', models.BigIntegerField(null=True)),
            ],
            options={
                'db_table': 'geonames_city',
            },
        ),
        migrations.AddIndex(
            model_name='geoipcity',
            index=models.Index(fields=['network_start_ip', 'network_end_ip'], name='geoip_city_network_1c73b1_idx'),
        ),
    ]
