# Generated by Django 3.2.23 on 2023-12-14 17:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0033_user_ios_email'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='device_os',
            field=models.CharField(choices=[('android', 'android'), ('apple', 'apple')], max_length=16, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True, verbose_name='email'),
        ),
        migrations.AlterUniqueTogether(
            name='user',
            unique_together={('email', 'device_os')},
        ),
        migrations.RemoveField(
            model_name='user',
            name='ios_email',
        ),
    ]
