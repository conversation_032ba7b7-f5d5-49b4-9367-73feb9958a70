# Generated by Django 3.2.5 on 2021-10-17 00:28

import apps.account.validators
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_countries.fields
import phonenumber_field.modelfields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('email', models.EmailField(max_length=254, null=True, unique=True, verbose_name='email')),
                ('first_name', models.CharField(max_length=254, null=True, verbose_name='first name')),
                ('last_name', models.CharField(max_length=254, null=True, verbose_name='last name')),
                ('country', django_countries.fields.CountryField(max_length=254, null=True, verbose_name='country')),
                ('city', models.CharField(max_length=254, null=True, verbose_name='city')),
                ('phone_number', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=254, null=True, region=None, validators=[apps.account.validators.validate_possible_number], verbose_name='phone number')),
                ('is_staff', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('date_joined', models.DateTimeField(auto_now_add=True)),
                ('social_auth_data', models.JSONField(blank=True, editable=False, null=True)),
                ('mobile_device_id', models.CharField(blank=True, max_length=254, null=True, verbose_name='mobile device id')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'customer user',
                'verbose_name_plural': 'customer users',
                'ordering': ('email',),
            },
        ),
        migrations.CreateModel(
            name='LoginHistory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lat', models.CharField(max_length=255, verbose_name='lat')),
                ('lon', models.CharField(max_length=255, verbose_name='lon')),
                ('country', models.CharField(max_length=255, null=True, verbose_name='country')),
                ('city', models.CharField(max_length=255, null=True, verbose_name='city')),
                ('ip', models.CharField(max_length=255, verbose_name='IP')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='login_history', to=settings.AUTH_USER_MODEL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'login history',
                'verbose_name_plural': 'user login histories',
            },
        ),
        migrations.CreateModel(
            name='AdminUser',
            fields=[
            ],
            options={
                'verbose_name': 'admin',
                'verbose_name_plural': 'admins',
                'ordering': ('email',),
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('account.user',),
        ),
        migrations.CreateModel(
            name='GuestUser',
            fields=[
            ],
            options={
                'verbose_name': 'guest user',
                'verbose_name_plural': 'guest users',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('account.user',),
        ),
    ]
