# Generated by Django 3.2.25 on 2024-05-06 11:55

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0038_auto_20240218_1157'),
    ]

    operations = [
        migrations.CreateModel(
            name='Activity',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service', models.CharField(blank=True, max_length=100, null=True, verbose_name='service')),
                ('usage_duration', models.DurationField()),
                ('entered_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('additional_info', models.JSONField(default=dict, verbose_name='Additional info')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'activity',
                'verbose_name_plural': 'activities',
                'ordering': ('-id',),
            },
        ),
    ]
