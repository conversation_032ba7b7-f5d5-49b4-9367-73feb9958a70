# Generated by Django 3.2.25 on 2024-09-30 01:50

import dj_language.field
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
        ('account', '0041_usersettings'),
    ]

    operations = [
        migrations.CreateModel(
            name='PushPanel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(help_text='.موضوعی را برای نوتفیکیشن وارد نمایید', max_length=255, verbose_name='موضوع')),
                ('service', models.CharField(choices=[('hussainiya', 'Hussainiya'), ('library', 'Library'), ('talk', 'Talk'), ('meet', 'Meet'), ('ma<PERSON>tih', '<PERSON><PERSON><PERSON><PERSON>'), ('hadis', 'Hadis'), ('khatm', 'Khatm'), ('quran', 'Quran'), ('tafsir', 'Tafsir'), ('ahkam', 'Ahkan'), ('null', 'خالی')], help_text='نام سرویس نوتیفیکیشن(اگر سرویسی نمیخواهید خالی را انتخاب کنید)', max_length=100)),
                ('user_group', models.CharField(choices=[('active_10_days', 'کاربران فعال 10 روز گذشته'), ('active_20_days', 'کاربران فعال 20 روز گذشته'), ('active_30_days', 'کاربران فعال 30 روز گذشته'), ('consultant', 'مشاورین تاک'), ('new_users', 'کاربران جدید'), ('all_users', 'تمام کاربران'), ('developer', 'تست توسعه دهنده')], default='new_users', help_text='گروه کاربرانی که این نوتیفیکیشن برای آن\u200cها ارسال می\u200cشود', max_length=30)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_send', models.BooleanField(default=False, help_text='نشان\u200cدهنده این است که نوتیفیکیشن ارسال شده است یا خیر')),
                ('sent_at', models.DateTimeField(blank=True, help_text='زمانی که نوتیفیکیشن ارسال شده است', null=True)),
                ('user_count', models.PositiveIntegerField(default=0, verbose_name='تعداد کاربران')),
                ('retry_count', models.PositiveIntegerField(default=0, verbose_name='تعداد دفعات تکرار ارسال')),
                ('admin_user', models.ForeignKey(blank=True, help_text='مدیری که این نوتیفیکیشن را مدیریت می\u200cکند', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='push_notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'PushPanel',
                'verbose_name_plural': 'PushPanel',
            },
        ),
        migrations.CreateModel(
            name='PushPanelData',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='عنوان')),
                ('message', models.TextField(max_length=512, verbose_name=' پیام')),
                ('data', models.CharField(blank=True, help_text='آیدی آبجکت مورد نظر برای منتقل شدن کاربر هنگام کلیک بر روی نوتفیکیشن (اگر نوتفیکیشن دارای سرویس نیست، می\u200cتوانید این فیلد را خالی رها کنید)', max_length=255, null=True, verbose_name='رکورد مورد نظر')),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('total_users', models.PositiveIntegerField(default=0)),
                ('successful_sends', models.PositiveIntegerField(default=0, verbose_name='تعداد ارسال موفق')),
                ('failed_sends', models.PositiveIntegerField(default=0, verbose_name='تعداد ارسال ناموفق')),
                ('language', dj_language.field.LanguageField(blank=True, default=69, limit_choices_to={'status': True}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='زبان')),
                ('push_panel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='histories', to='account.pushpanel')),
            ],
            options={
                'verbose_name': 'Push Panel Data',
                'verbose_name_plural': 'Push Panel Data Records',
            },
        ),
        migrations.CreateModel(
            name='PushPanelUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('result', models.JSONField(blank=True, default=dict, null=True, verbose_name='Result')),
                ('is_successful', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('push_panel_data', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='push_panel_user', to='account.pushpaneldata')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Push Panel User',
                'verbose_name_plural': 'Push Panel Users',
            },
        ),
        migrations.AddField(
            model_name='pushpaneldata',
            name='users',
            field=models.ManyToManyField(through='account.PushPanelUser', to=settings.AUTH_USER_MODEL, verbose_name='users'),
        ),
    ]
