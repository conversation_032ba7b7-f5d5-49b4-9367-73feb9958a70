# Generated by Django 3.2.25 on 2025-06-27 13:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0055_auto_20250610_1411'),
    ]

    operations = [
        migrations.AddField(
            model_name='providerrequest',
            name='ai_comment',
            field=models.TextField(blank=True, null=True, verbose_name='AI comment'),
        ),
        migrations.AddField(
            model_name='providerrequest',
            name='ai_score',
            field=models.IntegerField(blank=True, null=True, verbose_name='AI score'),
        ),
        migrations.AddField(
            model_name='providerrequest',
            name='ai_suggested_categories',
            field=models.JSONField(blank=True, null=True, verbose_name='AI suggested categories'),
        ),
        migrations.AddField(
            model_name='providerrequest',
            name='ai_suitable_for_teaching',
            field=models.BooleanField(blank=True, null=True, verbose_name='AI suitable for teaching'),
        ),
    ]
