import logging
import datetime

from dj_language.models import Language
from django.utils import timezone
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework.authtoken.models import Token
from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.generics import CreateAPIView, GenericAPIView
from rest_framework.generics import UpdateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.exceptions import ValidationError
from django.db.models import Q

from utils.redis_manager import RedisManager
from utils.ios_auth_decode import decode_apple_token
from config.settings import base as settings
from city_detection_ip import get_location_by_coordinates, get_location_by_ip, SPECIAL_COORDINATES

from apps.account.models import UserSettings, User, ProviderRequest
from apps.account.serializer import UserSerializer, GuestUserSerializer, GoogleAuthSerializer, AppleAuthSerializer, \
    PhoneAuthSerializer, PhoneVerifySerializer
from apps.account.tasks.new_user_task import new_user_alarm_to_telegram, update_country_city
from apps.account.tasks.send_otp_code import send_otp_code_whatsapp, send_otp_code_sms, generate_otp_code
from apps.account.doc import *

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SignupView(CreateAPIView):
    serializer_class = UserSerializer
    queryset = User.objects.none()

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = self.perform_create(serializer)

        return Response({
            'token': self.generate_login_token(user)
        }, status=200)

    def perform_create(self, serializer):
        lat, lon = serializer.validated_data.pop('lat'), serializer.validated_data.pop('lon')
        avatar_url = serializer.validated_data.pop('avatar', None)
        avatar = self.save_avatar(avatar_url)
        
        # Check if coordinates are special to determine the location method
        is_special = (lat, lon) in SPECIAL_COORDINATES
        location_method = 'IP_DETECTION' if is_special else 'COORDINATES'
        
        country, city = self.get_country_city_from_point(lat, lon)
        # create user
        user_obj: User = serializer.save(
            avatar=avatar,
            country=country,
            city=city,
        )
        
        # log user login
        user_obj.login_history.create(
            lat=lat,
            lon=lon,
            country=country,
            city=city,
            ip=self.get_client_ip(),
            timezone=serializer.validated_data.pop('timezone', None),
            mobile_device_id=serializer.validated_data.get('mobile_device_id'),
            location_method=location_method
        )

        return user_obj

    def get_country_city_from_point(self, lat, lon) -> tuple:
        try:
            # Check if coordinates are in SPECIAL_COORDINATES using exact matching with float conversion
            is_special = (float(lat), float(lon)) in SPECIAL_COORDINATES
            
            if is_special:
                # Use IP detection for special coordinates
                ip = self.get_client_ip()
                location_data = get_location_by_ip(ip)
                if location_data and location_data['status'] == 'success':
                    return location_data['countryCode'], location_data['city']
            else:
                # Use normal coordinates detection
                location_data = get_location_by_coordinates(lat, lon)
                if location_data and location_data['status'] == 'success':
                    return location_data['countryCode'], location_data['city']
            
            return '', ''

        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"account/views.py - Error getting location: {e} lat: {lat} | lon: {lon}")
            return '', ''

    @staticmethod
    def save_avatar(url: str = None):
        if not url:
            return None

        from django.core.files import File as DjangoFile
        from filer.models.imagemodels import Image
        from filer.models.foldermodels import Folder
        import requests
        from secrets import token_urlsafe
        try:
            name = token_urlsafe(5) + ".png"

            with open(f"/tmp/{name}", "wb") as f:
                f.write(requests.get(url).content)

            file_obj = DjangoFile(open(f"/tmp/{name}", 'rb'), name=name)
            folder, res = Folder.objects.get_or_create(name="users_avatar")

            obj, created = Image.objects.get_or_create(
                original_filename=file_obj.name,
                file=file_obj,
                folder=folder,
                is_public=True
            )

        except Exception as e:
            # log error
            return None

        return obj

    def get_client_ip(self):
        request = self.request
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    @staticmethod
    def generate_login_token(user):
        token, created = Token.objects.update_or_create(user=user)
        return token.key

    def send_new_user_alarm_to_telegram(self, obj):
        try:
            new_user_alarm_to_telegram.apply_async((obj.id,), countdown=10)
            logger.info(f"----------------> new_user_alarm_to_telegram")

        except Exception as e:
            logger.error(f"Failed to send new user alarm to Telegram: {e}")

    def update_user_fields(self, user, serializer):
        for i in ['cpu_type', 'model_name', 'os_platform', 'screen_size', 'device_brand', 'timezone',
                  'api_version', 'location_method', 'fcm']:
            if val := serializer.validated_data.get(i, None):
                setattr(user, i, val)


class AccountAuthToken(ObtainAuthToken):
    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data,
                                           context={'request': request})
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']
        token, created = Token.objects.get_or_create(user=user)
        return Response({
            'token': token.key,
            'user_id': user.pk,
            'email': user.email
        })