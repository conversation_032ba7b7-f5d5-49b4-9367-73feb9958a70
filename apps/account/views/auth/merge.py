from .base import *

from apps.account.serializer import MergeAccountSerializer, MergeVerifySerializer
from config.settings import base as settings

from rest_framework import status

from django.db import transaction
from django.apps import apps
from django.db.models import ForeignKey, OneToOneField, ManyToManyField
from apps.q_and_a.models import Users
import logging

logger = logging.getLogger(__name__)




class UserMerger:
    general_merge_models = [
        "downloadbook"
        "locationhistory", "loginhistory","activity", 
        "playlist", "like", "recent", 
        "userrate", "bookmark", "purchasebook",
        "usersession", "meetapproval", "meet", "userdialogue", "userpurchases",  
        "report", "remindermeet", "donateuser", "report", "reportcomment", 
        "individualkhatm", "groupkhatm", "userkhatm", "usercoinusage", "userpackage", 
        "support", "bookmark", "commandlog", "note", "notefeedback", "pushpanel", "pushpaneluser", 
    ]
    
    special_merge_models = [
        "calls", "rooms", "rates",
    ]
    def __init__(self, old_user, new_user, method):
        self.old_user = old_user
        self.new_user = new_user
        self.models_to_merge = self.get_models(self.general_merge_models)
        self.special_models_to_merge = self.get_models(self.special_merge_models)
        self.merge_method = method
        
    def get_models(self, merge_models):
        all_models = apps.get_models()
        models_to_merge = [model for model in all_models if model.__name__.lower() in merge_models]
        return models_to_merge

    @transaction.atomic
    def merge(self):
        try:
            self._merge_foreign_keys()
            # self._handle_special_models()
            self.delete_old_user()
            return True
        except Exception as exp:
            return False

    def _merge_foreign_keys(self):
        for model in self.models_to_merge:
            model_name = model.__name__
            for field in model._meta.fields:
                if isinstance(field, ForeignKey) and field.remote_field.model == User:
                    try:                        
                        model.objects.filter(**{field.name: self.old_user}).update(**{field.name: self.new_user})
                    except Exception as e:
                        logger.error(f"Error merging ForeignKey in {model_name}: {e}")
                        
    # def _handle_special_models(self):
    #     old_user = get_user_special_models(self.old_user)
    #     if not old_user:
    #         logger.info(f"--INFO _handle_special_models--> not special models found for user {self.old_user}")
    #         return None
    #     for model in self.special_models_to_merge:
    #         model_name = model.__name__
    #         for field in model._meta.fields:
    #             if isinstance(field, ForeignKey) and field.remote_field.model == Users:
    #                 try:
    #                     model.objects.filter(**{field.name: old_user}).update(**{field.name: self.new_user})
    #                 except Exception as e:
    #                     logger.error(f"Error merging ForeignKey in {model_name}: {e}")
                
                
                
    def get_user_special_models(self, user):
        if self.merge_method == "phone":
            old_user = Users.objects.filter(username__contains=user.phone_number).first()
        elif self.merge_method == "email":
            old_user = Users.objects.filter(email__contains=user.email).first()
        else:
            old_user = None
            
        
    def delete_old_user(self):
        try:
            self.old_user.soft_delete()
        except Exception as e:
            logger.error(f"Error deleting old user: {e}")        
        
        

class MergeAccountView(CreateAPIView):
    serializer_class = MergeAccountSerializer
    permission_classes = [IsAuthenticated] 
        
    @swagger_auto_schema(
        request_body=MergeAccountSerializer,
        
    )    
    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = self.request.user
        data = serializer.validated_data
        if data['method'] == "phone":
            old_user = User.objects.filter(phone_number=data['value'], email__isnull=False).first()
            if old_user:
                return Response({
                    'message': 'The user is already registered and cannot be reused.',
                }, status=status.HTTP_409_CONFLICT)  # Conflict (409) is appropriate for indicating a conflict.
                
            if settings.VERIFICATION_METHOD == "WHATSAPP":
                code = generate_otp_code()
                user = User.objects.filter(phone_number=str(data['value'])).first()
                try:
                    phone_number = RedisManager().add_to_redis(code, phone_number=str(data['value']), user=str(user.id), password='')
                    if phone_number:
                        logger.info(f'PhoneAuthView: {phone_number}/{code}')
                        is_send_otp = send_otp_code_whatsapp(data['value'], code, str(user))
                except Exception as exp:
                    logger.error(f'PhoneAuthView: add-to-redis: {exp}')
            else: 
                # TODO: send sms otp code
                pass
            return Response({
                'phone_number': data['value'],
                "verification_method": settings.VERIFICATION_METHOD,
                "verification_time_limit": settings.VERIFICATION_TIME_LIMIT or 5,
                'message': 'send code phone_number'
            }, status=200)
           

        else:
            old_user = User.objects.filter(email=data['value'], is_verified=True).first()
            if old_user:
                return Response({
                    'message': 'The user is already registered and cannot be reused.',
                }, status=status.HTTP_409_CONFLICT)
            result = UserMerger(old_user, user, "email").merge()
            if not result:
                return Response({
                    'message': 'Error merging users.'
                }, status=400)
            user.email = data['value']
            user.save()    
                        
        return Response({
            'message': 'User merged successfully.'
        }, status=200)
                       

class MergePhoneNumber(CreateAPIView):
    serializer_class = MergeVerifySerializer
    permission_classes = [IsAuthenticated] 

    @swagger_auto_schema(
        request_body=MergeVerifySerializer,
    )
    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        user = self.request.user

        verify_data = self.get_verification_data(data['phone_number'])
        old_user = User.objects.filter(phone_number=str(data['phone_number'])).first()
        del verify_data['code']
        if not old_user:
            return Response({
                'message': 'User not found.'
            }, status=404)     
        
               
        result = UserMerger(old_user, user, "phone").merge()
        if not result:
            return Response({
                'message': 'Error merging users.'
            }, status=400)
        user.phone_number = data['phone_number']
        user.is_verified = True
        user.save()    

        
        return Response({
            'message': 'User merged successfully.'
        }, status=200)       


    def get_verification_data(self, phone_number):
        """
        Retrieves verification data from Redis, raises ValidationError if not found.
        """
        try:
            verify_data = RedisManager().get_by_redis(phone_number)
            if not verify_data:
                logger.info(f"Verification data for phone number {phone_number} not found or expired.")
                raise ValidationError({"code": "Verification data not found or expired."})
            return verify_data
        except Exception as exp:
            logger.error(f"Error retrieving verification data for {phone_number}: {exp}")
            raise ValidationError({"code": "Verification data not found or expired."})

        
    def valied_code(self, current_code, save_code):
        if (current_code and save_code) and ( current_code != save_code):
            raise ValidationError({"code": "The verification code is incorrect."})

        return current_code    

