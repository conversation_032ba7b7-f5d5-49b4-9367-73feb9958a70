from .base import *


class GoogleAuth(SignupView):
    serializer_class = GoogleAuthSerializer

    def post(self, request, *args, **kwargs):
        logger.info(f'googleAuth--> {request.data}')
        return super().post(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = self.perform_create(serializer)

        return Response({
            "user_id": user.id,
            'token': self.generate_login_token(user),
            "name": user.name,
            "email": user.email,
            "avatar": user.avatar.url if user.avatar else '',
            "country": user.country.name if user.country else '',
            "city": user.city,
            'phone_number': str(user.phone_number),
            'date_joined': user.date_joined,
            'publisher_in': user.publisher_in(),
            'settings': user.get_user_settings,
        }, status=200)

    @staticmethod
    def generate_login_token(user):
        # Token.objects.filter(user=user).delete()
        token, created = Token.objects.get_or_create(user=user)
        return token.key

    def perform_create(self, serializer):
        avatar_url = serializer.validated_data.pop('avatar', None)
        mobile_id = serializer.validated_data.pop('mobile_device_id', )
        id_token = serializer.validated_data.pop('id_token', None)
        server_auth_token = serializer.validated_data.pop('server_auth_token', None)
        name = serializer.validated_data.pop('name')
        email = serializer.validated_data.pop('email')

        if email == '<EMAIL>':
            email = None

        is_apple = False
        try:
            is_apple = User.objects.filter(mobile_device_id=mobile_id).first().device_brand.lower() == 'apple'
        except Exception:
            is_apple = False


        auth_data = {
            'id_token': id_token,
            'server_auth_token': server_auth_token,
            'oauth': 'google',
        }

        if is_apple:
            print(f'--> Apple User')
            # Apple user
            ios_data = decode_apple_token(id_token)
            auth_data = {
                'email': ios_data['email'],
                'user_id': ios_data['sub'],
                'server_auth_token': server_auth_token,
                'oauth': 'apple',
            }

            user = User.objects.filter(social_auth_data__user_id=ios_data['sub']).first()

            if not user:
                user = User.objects.filter(mobile_device_id=mobile_id).first()

            if not user:
                user = User.objects.create(
                    email=ios_data['email'],
                    device_os=User.DeviceOs.apple,
                    mobile_device_id=mobile_id,
                    social_auth_data=auth_data,
                )

        else:
            # Google User
            user = User.objects.filter(
                email=email
            ).first()

            if not user:
                user = User.objects.filter(
                    email=email, device_os=User.DeviceOs.android,
                ).first()

            if not user: 
                user = User.objects.filter(mobile_device_id=mobile_id, email__isnull=True).first()

            if user is None:
                user = User.objects.create(
                    email=email,
                    device_os=User.DeviceOs.android,
                    mobile_device_id=mobile_id,
                    social_auth_data=auth_data,
                )
                self.send_new_user_alarm_to_telegram(user)

        # Rest of the Process
        if mobile_id:
            user.mobile_device_id = mobile_id

        if email:
            user.email = email

        if user.avatar is None:
            avatar = self.save_avatar(avatar_url)
            user.avatar = avatar

        if name:
            user.name = name

        self.update_user_fields(user, serializer)

        user.language = Language.objects.filter(code=self.request.LANGUAGE_CODE).first()
        user.social_auth_data = auth_data
        user.last_login = timezone.now()
        user.save()

        # Handle build number from header and save to UserSettings
        build_number = self.request.headers.get('build-number')
        if build_number:
            user_settings, _ = UserSettings.objects.get_or_create(user=user)
            user_settings.api_version = build_number
            user_settings.save()

        return user
