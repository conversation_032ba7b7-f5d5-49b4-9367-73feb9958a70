from .base import *


class AppleAuth(SignupView):
    serializer_class = AppleAuthSerializer


    def post(self, request, *args, **kwargs):
        logger.info(f'AppleAuth--> {request.data}')
        return super().post(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = self.perform_create(serializer)

        return Response({
            "user_id": user.id,
            'token': self.generate_login_token(user),
            "name": user.name,
            "email": user.email,
            "avatar": user.avatar.url if user.avatar else '',
            "country": user.country.name if user.country else '',
            "city": user.city,
            'phone_number': str(user.phone_number),
            'date_joined': user.date_joined,
            'publisher_in': user.publisher_in(),
            'settings': user.get_user_settings,
        }, status=200)


    def perform_create(self, serializer):
        validated_data = serializer.validated_data
        apple_user_id = validated_data['social_auth_data']['user_id']
        email = validated_data['social_auth_data']['email']
        user = User.objects.filter(social_auth_data__user_id=apple_user_id).first()

        if not user:
            user = User.objects.filter(mobile_device_id=validated_data['mobile_device_id'], email__isnull=True, phone_number__isnull=True).first()

        if not user:
            user = User.objects.create(
                email=email,
                device_os=User.DeviceOs.apple,
                mobile_device_id=validated_data['mobile_device_id'],
                social_auth_data=validated_data['social_auth_data'],
            )
            self.send_new_user_alarm_to_telegram(user)

        user.mobile_device_id = validated_data['mobile_device_id']
        if email:
            user.email = email

        self.update_user_fields(user, serializer)

        user.language = Language.objects.filter(code=self.request.LANGUAGE_CODE).first()

        user.last_login = timezone.now()

        user.save()

        return user

    @staticmethod
    def generate_login_token(user):
        token, created = Token.objects.get_or_create(user=user)
        return token.key
