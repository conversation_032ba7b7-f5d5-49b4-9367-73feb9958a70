from .base import *
import phonenumbers

from utils.geo import get_country_city


class PhoneAuthInfo(GenericAPIView):
    # @swagger_auto_schema(
    # )    
    def get(self, request):
        client_ip = self.get_client_ip()
        country, city = get_country_city(client_ip)
        country_code = phonenumbers.country_code_for_region(country.upper()) if country else None

        response_data = {
            "auth_method": settings.VERIFICATION_METHOD,  
            "verification_time_limit": settings.VERIFICATION_TIME_LIMIT or 5,
            "country_code": country_code,  
        }
        return Response(response_data)


    def get_client_ip(self):
        request = self.request
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip



class PhoneAuthView(SignupView):
    serializer_class = PhoneAuthSerializer
        
    @swagger_auto_schema(
        operation_description=doc_phone_auth(),
        request_body=PhoneAuthSerializer,
    )    
    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        logger.info(f'PhoneAuthView-->: {data}')
                
        user_accounts = User.objects.filter(mobile_device_id=data['mobile_device_id'])

        user_accounts.update(possible_phone_number=data['phone_number'])
         
        user = User.objects.filter(phone_number=data['phone_number'], is_verified=True).first()
        if not user:                
            user = User.objects.filter(mobile_device_id=data['mobile_device_id'], email__isnull=True, phone_number__isnull=True).first()
        
        if not user:
            user = User.objects.create(possible_phone_number=data['phone_number'], mobile_device_id=data['mobile_device_id'], is_verified=False)
            
    
        self.update_user_fields(user, serializer)
        user.save()
        is_send_otp = False
        
        try:
            code = generate_otp_code()
            phone_number = RedisManager().add_to_redis(code, phone_number=str(data['phone_number']), user=user.id, password='')
            if phone_number:
                user = "guest" if not user else user
                is_send_otp = send_otp_code_whatsapp(data['phone_number'], code, user)
                
        except Exception as exp:
            logger.error(f'PhoneAuthView: add-to-redis: {exp}')
            

        # Define the method used to send the code (either via SMS or WhatsApp)
        verification_method = "WHATSAPP"  # This can be dynamic: "SMS" or "WHATSAPP"
        
        
        return Response({
            "user_id": str(user.id),
            "phone_number": data['phone_number'],
            "verification_time_limit": settings.VERIFICATION_TIME_LIMIT or 5,
            "verification_method": verification_method,
            "is_send": is_send_otp,
        }, status=200)
        
        
class PhoneVerifyView(SignupView):
    serializer_class = PhoneVerifySerializer
    
    @swagger_auto_schema(
        request_body=PhoneVerifySerializer,
        operation_description=doc_phone_verify_auth(),
    )
    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        logger.info(f'PhoneVerifyView-->: {data}')
        
        # Retrieve user and verification data using helper methods
        verify_data = self.get_verification_data(data['phone_number'])
        user = self.get_user(data['user_id'])            
        code = self.valied_code(data['code'], verify_data['code'])
        del verify_data['code']
        
        user.phone_number = data['phone_number']
        user.is_verified = True
        user.save()

        return Response({
            "user_id": user.id,
            'token': self.generate_login_token(user),
            "name": user.name,
            "email": user.email,
            "avatar": str(user.avatar.url) if user.avatar else '',
            "country": user.country.name if user.country else '',
            "city": user.city,
            'phone_number': str(user.phone_number) if user.phone_number else None,
            'date_joined': user.date_joined,
            'publisher_in': user.publisher_in(),
            'settings': user.get_user_settings,
        }, status=200)




    def valied_code(self, current_code, save_code):
        if (current_code and save_code) and ( current_code != save_code):
            raise ValidationError({"code": "The verification code is incorrect."})

        return current_code    
        
    def get_user(self, user_id):
        """
        Retrieve user by ID, raises ValidationError if not found.
        """
        try:
            return User.objects.get(id=user_id)
        except ObjectDoesNotExist:
            logger.error(f"User with ID {user_id} not found.")
            raise ValidationError({"user_id": "User not found."})

        
    def get_verification_data(self, phone_number):
        """
        Retrieves verification data from Redis, raises ValidationError if not found.
        """
        try:
            verify_data = RedisManager().get_by_redis(phone_number)
            if not verify_data:
                logger.info(f"Verification data for phone number {phone_number} not found or expired.")
                raise ValidationError({"code": "Verification data not found or expired."})
            return verify_data
        except Exception as exp:
            logger.error(f"Error retrieving verification data for {phone_number}: {exp}")
            raise ValidationError({"code": "Verification data not found or expired."})

        
                

            
        
        
    
