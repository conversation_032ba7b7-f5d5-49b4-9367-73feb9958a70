from .base import *
from django.db.models import Q
from django.db import connection, transaction
from utils.geo import get_country_city
import phonenumbers
from django.db import transaction
import time
from city_detection_ip import get_location_by_coordinates, get_location_by_ip, SPECIAL_COORDINATES
import logging

logger = logging.getLogger(__name__)

class GuestAuthView(SignupView):
    serializer_class = GuestUserSerializer

    @swagger_auto_schema(
        operation_description=doc_gust_auth(),
        request_body=GuestUserSerializer,
    )
    def post(self, request, *args, **kwargs):
        logger.info(f'GuestAuthView--> {request.data}')
        return super().post(request, *args, **kwargs)

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user, country_code = self.perform_create(serializer)
        logger.info(f'Guest-(user)->:{user.mobile_device_id}')

        return Response({
            'token': self.generate_login_token(user),
            'auth_method': settings.AUTH_METHOD,
            "country_code": country_code,
        }, status=200)

    def perform_create(self, serializer):
        device_id = serializer.validated_data.get('mobile_device_id')
        lat, lon = serializer.validated_data.pop('lat'), serializer.validated_data.pop('lon')
        api_version = serializer.validated_data.pop('api_version')
        user_timezone = serializer.validated_data.pop('timezone', None)
        serializer_data = dict(serializer.validated_data)
        email = serializer_data.pop('email', None)

        # Check if coordinates are special to determine the location method
        is_special = (float(lat), float(lon)) in SPECIAL_COORDINATES
        location_method = 'IP_DETECTION' if is_special else 'COORDINATES'

        with transaction.atomic():
            with connection.cursor() as cursor:
                cursor.execute("LOCK TABLE account_user IN ACCESS EXCLUSIVE MODE")

            obj = User.objects.select_for_update().filter(Q(mobile_device_id=device_id)).first()
            if email:
                logger.info(f'Guest-has-email-> {obj}')

            if not obj:
                obj, created = User.objects.select_for_update().get_or_create(
                    mobile_device_id=device_id,
                    defaults=serializer_data
                )
                if created:
                    logger.info(f'Guest-(created)->: {obj.mobile_device_id}')
                    self.send_new_user_alarm_to_telegram(obj)
                else:
                    self.update_user_fields(obj, serializer)

            if lat and lon:
                obj.lat = lat
                obj.lon = lon

            obj.last_login = timezone.now()
            obj.language = serializer.validated_data.get('language', None)
            obj.save()

            # Get country and city using the same method as base.py
            if is_special:
                # Use IP detection for special coordinates
                ip = self.get_client_ip()
                location_data = get_location_by_ip(ip)
                country, city = (location_data['countryCode'], location_data['city']) if location_data and location_data['status'] == 'success' else ('', '')
            else:
                # Use normal coordinates detection
                location_data = get_location_by_coordinates(lat, lon)
                country, city = (location_data['countryCode'], location_data['city']) if location_data and location_data['status'] == 'success' else ('', '')

            # log user login
            login_history_obj = obj.login_history.create(
                lat=lat,
                lon=lon,
                country=country,
                city=city,
                ip=self.get_client_ip(),
                timezone=user_timezone,
                api_v=api_version,
                location_method=location_method
            )
            obj.country = country
            obj.city = city
            obj.save()

            # Handle build number from header and save to UserSettings
            build_number = self.request.headers.get('build-number')
            if build_number:
                user_settings, _ = UserSettings.objects.get_or_create(user=obj)
                user_settings.api_version = build_number
                user_settings.save()

            # No need for update_country_city task since we're handling it here
            return obj, phonenumbers.country_code_for_region(country.upper()) if country else 1