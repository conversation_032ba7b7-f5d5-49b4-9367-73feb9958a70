from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.utils import timezone
from apps.account.models import User, UserSettings

from ..serializer.activity import ActivitySerializer, UserSettingsSerializer

class ActivityCreateAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=ActivitySerializer)
    def post(self, request, format=None):
        serializer = ActivitySerializer(data=request.data, many=True, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UpdateUserSettingsView(generics.UpdateAPIView):
    permission_classes = [IsAuthenticated]  
    serializer_class = UserSettingsSerializer

    @swagger_auto_schema(
        operation_description="Update user settings",
        request_body=UserSettingsSerializer,
        responses={
            200: UserSettingsSerializer,
            401: 'Authentication credentials were not provided.',
            404: 'User not found.'
        }
    )
    def update(self, request, *args, **kwargs):
        user = request.user

        settings_data = request.data.get('settings')
        is_reminder = request.data.get('is_reminder', False)
        
        defaults = {
            'settings': settings_data, 
            'is_reminder': is_reminder
        }
        
        # Only update fields if they're provided in the request
        for field in ['notification_settings', ]:
            if field in request.data:
                defaults[field] = request.data.get(field, [])
                            
        user_settings, created = UserSettings.objects.update_or_create(
            user=user, 
            defaults=defaults
        )

        return Response(UserSettingsSerializer(user_settings).data, status=status.HTTP_200_OK)


class BucketUpdateAPIView(generics.GenericAPIView):
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="Remove a service from user's bucket services",
        manual_parameters=[
            openapi.Parameter(
                'service', 
                openapi.IN_QUERY, 
                description="Service name to remove from bucket services", 
                type=openapi.TYPE_STRING,
                required=False
            )
        ],
        responses={
            200: openapi.Response(
                description="Service removed successfully from bucket services",
                examples={
                    "application/json": {
                        "message": "Successfully updated"
                    }
                }
            ),
            400: "Bad request",
            401: "Authentication credentials were not provided"
        }
    )
    def get(self, request, *args, **kwargs):
        # Get the service name from query parameters
        service_name = request.query_params.get('service')
        
        if service_name:
            # Get user settings
            user = request.user
            user_settings, created = UserSettings.objects.get_or_create(user=user)
            
            # Check if the service exists in bucket_services
            if user_settings.bucket_services and service_name in user_settings.bucket_services:
                # Convert the MultiSelectField to a list
                bucket_services_list = user_settings.bucket_services
                
                # Remove the service from the list
                if service_name in bucket_services_list:
                    bucket_services_list.remove(service_name)
                
                # Convert back to string format with comma separator
                user_settings.bucket_services = bucket_services_list
                user_settings.save()
        
        # Return success response in both cases
        return Response({"message": "Successfully updated"}, status=status.HTTP_200_OK)

