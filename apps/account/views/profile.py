import datetime
import logging
from dj_language.models import Language
from django.utils import timezone
from django.db.models import Q, <PERSON>, Sum, Avg, Count
from rest_framework.authtoken.models import Token
from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.generics import CreateAPIView, GenericAPIView
from rest_framework.generics import UpdateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import DetailView
from django.utils.translation import gettext as _
from django.db.models import Prefetch, Q
from django.db.models.functions import Coalesce

from utils.ios_auth_decode import decode_apple_token
from config.settings import base as settings
from ..models import User, ProviderRequest, UserSettings, LoginHistory, LocationHistory, UserInterest
from ..serializer import User<PERSON>erializer, GuestUserSerializer, GoogleAuthSerializer, UserProfileSerializer
from ..tasks.new_user_task import new_user_alarm_to_telegram, update_country_city
from ..doc import doc_logout
from apps.q_and_a.models import Users as UserTalk
from apps.khatm.models import UserKhatm
from apps.notespace.models import Note
from apps.bookmark.models import Bookmark
from apps.q_and_a.models import Rooms, Messages
from apps.library.models import UserRate
from apps.donate.models import DonateUser
from apps.habit.models import Challenge, ChallengeParticipant, UserChecklistItem, ChecklistEntry
from city_detection_ip import get_location_by_coordinates, get_location_by_ip, SPECIAL_COORDINATES
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HelloUser(APIView):
    permission_classes = (IsAuthenticated,)

    @swagger_auto_schema(
        operation_description="این متد اطلاعات کاربر را برمی‌گرداند",
        responses={200: openapi.Response(
            description="اطلاعات کاربر",
            examples={
                'application/json': {
                    "user_id": "ایدی کاربر",
                    "name": "نام کاربر",
                    "email": "ایمیل کاربر",
                    "avatar": "آدرس آواتار",
                    "country": "کشور",
                    "city": "شهر",
                    'phone_number': "شماره تلفن",
                    'possible_phone_number': 'شماره تماس احتمالی کاربر',
                    'is_phone_number_verified': 'شماره تماس تایید شده باشد',
                    'date_joined': "تاریخ عضویت",
                    'publisher_in': "اطلاعات ناشر",
                    'settings': "تنظیمات کاربر",
                    'provider': "اطلاعات ارائه‌دهنده",
                    'coin_balance': "موجودی کوین ها",
                    'balance_in_dollars': "مقدار دلار کاربر (اگر پرووایدر نباشد خالی است)",
                    'notification_count': "تعداد نوتیفیکیشن‌ها",
                    'bucket_services': "لیست سرویس باکت های کاربر",
                }
            }
        )}
    )
    def get(self, request):
        user = self.request.user
        unverified = None

        possible_auth_account = self.get_possible_auth_account(user)
        # print(f'--> {possible_auth_account}')
        # print(possible_auth_account.phone_number)
        if user.email and not user.phone_number:
            possible_auth_account = possible_auth_account.filter(Q(phone_number__isnull=False, is_verified=True, email__isnull=True)).first()
        elif not user.email and (user.phone_number and user.is_verified):
            possible_auth_account = possible_auth_account.filter(Q(email__isnull=False, phone_number__isnull=True)).first()
        else:
            possible_auth_account = None

        if possible_auth_account:
            if possible_auth_account.email:
                unverified = self.create_unverified_dict("email", possible_auth_account.email)

            elif possible_auth_account.phone_number and possible_auth_account.is_verified:
                unverified = self.create_unverified_dict(
                    "phone", str(possible_auth_account.phone_number),
                    settings.IS_SEND_OTP, settings.VERIFICATION_TIME_LIMIT or 5, settings.VERIFICATION_METHOD
                )

        user_data = {
            "user_id": user.id,
            "name": user.name,
            "email": user.email,
            'phone_number': str(user.phone_number) if user.phone_number else None,
            "unverified": unverified,
            "avatar": request.build_absolute_uri(user.avatar.url) if user.avatar else '',
            "country": user.country.name if user.country else '',
            "city": user.city,
            'is_phone_number_verified': user.is_verified,
            'date_joined': user.date_joined,
            'publisher_in': user.publisher_in(),
            'settings': user.get_user_settings,
            'provider': ProviderRequest.get_provider_info(user),
            'coin_balance': user.coin_balance,
            'balance_in_dollars': user.get_balance_in_dollars(),
            'notifications': user.get_notification_count(),
            'bucket_service': self.get_bucket_services(user),
            'notification_settings': self.get_notification_settings(user)
        }
        return Response(user_data)

    def get_bucket_services(self, user):
        try:
            return user.settings.bucket_services
        except UserSettings.DoesNotExist:
            return []
    def get_notification_settings(self, user):
        try:
            return user.settings.notification_settings
        except UserSettings.DoesNotExist:
            return []

    def get_possible_auth_account(self, user):
        possible_users = User.objects.filter(
            mobile_device_id=user.mobile_device_id,
        ).exclude(id=user.id)
        possible_users = possible_users.annotate(
            last_login_at=Max('login_history__last_login_at')
        ).order_by('last_login_at')

        return possible_users


    def create_unverified_dict(self, method, value, is_send=None, verification_time_limit=None, verification_method=None):
        if method == "email":
            verification_method = "EMAIL"

        elif is_send == False and method == "phone":
            verification_method = "WHATSAPP_MESSAGE"

        elif is_send == True and method == "phone" and verification_method == "WHATSAPP":
            verification_method = "OTP_WHATSAPP"

        elif is_send == False and method == "phone" and verification_method == "SMS":
            verification_method = "OTP_SMS"

        return {
            'method': method,
            'value': str(value),
            "verification_time_limit": verification_time_limit,
            "verification_method": verification_method,
        }


class ProfileUpdateView(UpdateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = UserProfileSerializer

    def get_object(self):
        return self.request.user

    def update(self, request, *args, **kwargs):
        logger.info(f"-log-ProfileUpdateView-->: {request.data}")
        return super().update(request, *args, **kwargs)

class UserUpdateView(UpdateAPIView):
    serializer_class = GuestUserSerializer

    lookup_field = 'device_id'

    def perform_update(self, serializer):
        device_id = serializer.validated_data.get('mobile_device_id')
        lat, lon = serializer.validated_data.pop('lat'), serializer.validated_data.pop('lon')

        api_version = serializer.validated_data.pop('api_version')
        user_timezone = serializer.validated_data.pop('timezone', None)
        serializer_data = dict(serializer.validated_data)

        obj = self.get_object()
        if not obj:
            return

        if lat:
            obj.lat = lat
        if lon:
            obj.lon = lon

        obj.last_login = timezone.now()

        for i in ['cpu_type', 'model_name', 'os_platform', 'screen_size', 'device_brand', 'timezone',
                  'api_version', 'location_method', 'fcm']:
            if val := serializer.validated_data.get(i, None):
                setattr(obj, i, val)

        obj.language = serializer.validated_data.get('language', None)

        # Debug logging for special coordinates check
        logger.info(f"Checking coordinates: lat={lat}, lon={lon}")
        logger.info(f"Special coordinates list: {SPECIAL_COORDINATES}")
        logger.info(f"Coordinates tuple: {(float(lat), float(lon))}")
        
        # Convert lat, lon to float and check if in SPECIAL_COORDINATES
        is_special = (float(lat), float(lon)) in SPECIAL_COORDINATES
        logger.info(f"Is special coordinate: {is_special}")
        
        location_method = 'IP_DETECTION' if is_special else 'COORDINATES'
        logger.info(f"Location method selected: {location_method}")

        # Get location data based on whether coordinates are special or not
        if lat and lon:
            if is_special:
                # Use IP detection for special coordinates
                ip = self.get_client_ip()
                logger.info(f"Using IP detection for special coordinates. IP: {ip}")
                location_data = get_location_by_ip(ip)
            else:
                # Use normal coordinates detection
                logger.info("Using coordinates detection")
                location_data = get_location_by_coordinates(lat, lon)

            if location_data and location_data['status'] == 'success':
                obj.city = location_data['city']
                obj.country = location_data['countryCode']
                logger.info(f"Location detected: city={obj.city}, country={obj.country}")
            else:
                logger.warning("Location detection failed")

        obj.save()

        # Create login history with the detected location
        login_history_obj = obj.login_history.create(
            lat=lat,
            lon=lon,
            ip=self.get_client_ip(),
            timezone=user_timezone,
            api_v=api_version,
            location_method=location_method,
            city=obj.city,
            country=obj.country
        )

        UserTalk.set_language_code(obj.id, obj.language.code)

        return obj

    def get_object(self):
        return User.objects.filter(mobile_device_id=self.kwargs[self.lookup_field]).order_by('-last_login').first()

    def get_client_ip(self):
        request = self.request
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class DeleteAccountView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
        user = request.user
        User.objects.filter(
            id=user.id
        ).update(
            deleted_data=f"{now_time},{user.email},{user.username},{user.mobile_device_id}",
            username=None,
            email=None,
            mobile_device_id=None,
        )

        Token.objects.filter(user=user).delete()
        return Response({
            'status': 'ok',
            'deleted_at': now_time,
        })


class UserProfileView(LoginRequiredMixin, DetailView):
    model = User
    template_name = 'account/profile/detail.html'
    context_object_name = 'user_profile'

    def get_queryset(self):
        return User.objects.prefetch_related(
            'login_history',
            'location_history',
            'interest',
            'interest__interests',
            'settings',
            'group_khatms',
            'bookmark_bookmarks',
        ).filter(pk=self.kwargs.get('pk'))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.get_object()

        # Basic Information
        context['basic_info'] = {
            'full_name': user.get_full_name(),
            'email': user.email,
            'phone_number': user.phone_number,
            'birthdate': user.birthdate,
            'bio': user.bio,
            'language': user.language.name if user.language else None,
        }

        # Location Information
        context['location_info'] = {
            'country': user.country,
            'country_name': user.country.name if user.country else None,
            'city': user.city,
            'recent_locations': user.location_history.order_by('-at_time').all(),
        }

        # Device Information
        context['device_info'] = {
            'device_os': user.device_os,
            'mobile_device_id': user.mobile_device_id,
            'cpu_type': user.cpu_type,
            'model_name': user.model_name,
            'os_platform': user.os_platform,
            'screen_size': user.screen_size,
            'device_brand': user.device_brand,
            'fcm': user.fcm,
        }

        # User Interests
        try:
            user_interest = user.interest
            context['interests'] = {
                'gender': user_interest.get_gender_display() if user_interest else None,
                'age': user_interest.age if user_interest else None,
                'tend_to_marry': user_interest.tend_to_marry if user_interest else None,
                'marital_status': user_interest.marital_status if user_interest else None,
                'has_children': user_interest.has_children if user_interest else None,
                'interests': user_interest.interests.all() if user_interest else [],
            }
        except UserInterest.DoesNotExist:
            context['interests'] = None

        # User Settings
        try:
            user_settings = user.settings
            context['user_settings'] = {
                'settings': user_settings.settings,
                'notification_settings': user_settings.notification_settings,
                'is_reminder': user_settings.is_reminder,
            }
        except UserSettings.DoesNotExist:
            context['user_settings'] = None

        # Activity History
        recent_logins = user.login_history.order_by('-last_login_at')[:5]
        khatms = user.group_khatms.all()[:5]

        # Get user activities
        activities = user.activity_set.order_by('-entered_at')[:10]

        # Calculate khatm progress
        khatms_with_progress = []
        for khatm in khatms:
            progress = (khatm.last_page_read / khatm.pages_count * 100) if khatm.pages_count else 0
            khatms_with_progress.append({
                'created_at': khatm.created_at,
                'from_page': khatm.from_page,
                'to_page': khatm.to_page,
                'progress': round(progress, 0)
            })

        context['activity'] = {
            'date_joined': user.date_joined,
            'last_login': user.last_login,
            'recent_logins': recent_logins,
            'khatms': khatms_with_progress,
            'service_usage': activities
        }

        # Notes
        context['notes'] = {
            'quran_notes': Note.objects.filter(user=user, service=Note.Service.quran).order_by('-created_at')[:5],
            'mafatih_notes': Note.objects.filter(user=user, service=Note.Service.mafatih).order_by('-created_at')[:5],
            'hadis_notes': Note.objects.filter(user=user, service=Note.Service.hadis).order_by('-created_at')[:5],
            'habit_notes': Note.objects.filter(user=user, service=Note.Service.habit).order_by('-created_at')[:5],
            'total_notes': Note.objects.filter(user=user).count(),
        }

        # Bookmarks
        bookmarks = user.bookmark_bookmarks.filter(status=True)
        context['bookmarks'] = {
            'quran_bookmarks': bookmarks.filter(service='quran').order_by('-created_at')[:5],
            'mafatih_bookmarks': bookmarks.filter(service='mafatih').order_by('-created_at')[:5],
            'ahkam_bookmarks': bookmarks.filter(service='ahkam').order_by('-created_at')[:5],
            'hadith_bookmarks': bookmarks.filter(service='hadith').order_by('-created_at')[:5],
            'book_bookmarks': bookmarks.filter(service='book').order_by('-created_at')[:5],
            'total_bookmarks': bookmarks.count(),
        }

        # Q&A (Talk) Activity with Messages
        rooms = Rooms.objects.filter(
            client__username__endswith=f":{user.id}"
        ).order_by('-created_at')[:5]
        
        rooms_with_messages = []
        for room in rooms:
            messages = Messages.objects.filter(room=room).order_by('-at_time')[:10]
            rooms_with_messages.append({
                'room': room,
                'messages': messages
            })

        context['talk_activity'] = {
            'total_rooms': Rooms.objects.filter(client__username__endswith=f":{user.id}").count(),
            'rooms_with_messages': rooms_with_messages,
            'total_messages': Messages.objects.filter(
                room__client__username__endswith=f":{user.id}"
            ).count(),
        }

        # Library Activity
        context['library_activity'] = {
            'total_rated_books': UserRate.objects.filter(user=user).count(),
            'recent_rated_books': UserRate.objects.filter(user=user).order_by('-date')[:5],
        }

        # Donation Information
        user_donations = DonateUser.objects.filter(user=user)
        total_donations = user_donations.count()
        total_amount = user_donations.aggregate(total=Sum('amount'))['total'] or 0

        # Get the last donation
        last_donation = user_donations.order_by('-created_at').first()
        
        # Calculate average donation amount
        avg_amount = user_donations.aggregate(avg=Avg('amount'))['avg'] or 0

        # Get donation type statistics
        donation_types = user_donations.values('donate_type').annotate(
            count=Count('id')
        ).order_by('-count')
        most_used_type = donation_types[0]['donate_type'] if donation_types else None

        # Get donation history
        donation_history = user_donations.order_by('-created_at')

        context['donations'] = {
            'total_donations': total_donations,
            'total_amount': total_amount,
            'last_donation_date': last_donation.created_at if last_donation else None,
            'last_donation_amount': last_donation.amount if last_donation else None,
            'average_amount': round(avg_amount, 2) if avg_amount else 0,
            'most_common_type': most_used_type,
            'history': donation_history,
        }

        # Habit & Challenge Information
        today = timezone.now().date()
        
        # Get all completed checklist entries
        completed_entries = ChecklistEntry.objects.filter(
            user=user,
            completed=True
        ).select_related(
            'user_checklist_item',
            'user_checklist_item__template_item'
        ).order_by('-date')

        # Group completed entries by checklist item
        completed_by_checklist = {}
        for entry in completed_entries:
            checklist = entry.user_checklist_item
            if checklist.id not in completed_by_checklist:
                completed_by_checklist[checklist.id] = {
                    'checklist': checklist,
                    'entries': [],
                    'total_completed': 0
                }
            completed_by_checklist[checklist.id]['entries'].append(entry)
            completed_by_checklist[checklist.id]['total_completed'] += 1

        # Calculate overall completion statistics
        total_entries = ChecklistEntry.objects.filter(user=user).count()
        total_completed = completed_entries.count()
        overall_completion_rate = (total_completed / total_entries * 100) if total_entries > 0 else 0

        context['habits'] = {
            'completed_checklists': [{
                'title': data['checklist'].custom_title or data['checklist'].template_item.get_title(lang='en') if data['checklist'].template_item else data['checklist'].custom_title,
                'total_completed': data['total_completed'],
                'is_active': data['checklist'].is_active,
                'custom_days': data['checklist'].custom_days,
                'entries': [{
                    'date': entry.date,
                    'completed': entry.completed
                } for entry in sorted(data['entries'], key=lambda x: x.date, reverse=True)[:30]]  # Last 30 completed entries
            } for data in completed_by_checklist.values()],
            'completion_stats': {
                'total_entries': total_entries,
                'total_completed': total_completed,
                'completion_rate': round(overall_completion_rate, 1)
            }
        }

        return context


class LogoutView(GenericAPIView):
    """
    API endpoint for user logout functionality.
    Clears the user's FCM token and provides appropriate response.
    """
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description=doc_logout(),
        responses={
            200: openapi.Response(
                description="Logout successful",
                examples={
                    'application/json': {
                        'status': 'success',
                        'message': 'Logged out successfully'
                    }
                }
            ),
            401: openapi.Response(
                description="Authentication required",
                examples={
                    'application/json': {
                        'detail': 'Authentication credentials were not provided.'
                    }
                }
            ),
            500: openapi.Response(
                description="Internal server error",
                examples={
                    'application/json': {
                        'status': 'error',
                        'message': 'An error occurred during logout'
                    }
                }
            )
        }
    )
    def post(self, request):
        """
        Handle POST request for user logout.

        This endpoint:
        1. Checks if the user is authenticated
        2. Clears the user's FCM token (sets to None)
        3. Returns success response

        Args:
            request: HTTP request object with authentication

        Returns:
            Response: JSON response with logout status
        """
        try:
            user = request.user

            # Clear the FCM token
            user.fcm = None
            user.save(update_fields=['fcm'])

            logger.info(f"User {user.id} logged out successfully, FCM token cleared")

            return Response({
                'status': 'success',
                'message': 'Logged out successfully'
            }, status=200)

        except Exception as e:
            logger.error(f"Logout error for user {request.user.id}: {str(e)}")
            return Response({
                'status': 'error',
                'message': 'An error occurred during logout'
            }, status=500)


