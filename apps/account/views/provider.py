from rest_framework import status
from rest_framework.generics import ListCreateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from apps.account.models import User, ProviderRequest
from apps.account.serializer.provider import ProviderSerializer


class RegisterProviderView(ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ProviderSerializer

    # def post(self, request, *args, **kwargs):
    #     serializer = self.get_serializer(data=request.data)
    #     if serializer.is_valid():
    #         serializer.save()
    #         return Response(
    #             serializer.data, status=status.HTTP_200_OK,
    #         )
    #
    #     return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_queryset(self):
        return ProviderRequest.objects.filter(user=self.request.user)
