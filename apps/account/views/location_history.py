from rest_framework.mixins import CreateModelMixin
from rest_framework.permissions import IsAuthenticated
from rest_framework.generics import GenericAPIView
from rest_framework.response import Response
from rest_framework import status
import logging
import re

from apps.account.models import LocationHistory
from apps.account.serializer.location_history import LocationHistorySerializer
from city_detection_ip import get_location_by_coordinates, get_location_by_ip, SPECIAL_COORDINATES

logger = logging.getLogger(__name__)


def detect_browser_from_user_agent(user_agent):
    """
    Detect browser name from User-Agent string.

    Args:
        user_agent (str): The User-Agent header value

    Returns:
        str or None: Browser name if detected, None if not a browser or detection fails
    """
    if not user_agent:
        return None

    try:
        user_agent = user_agent.lower()

        # Check for Flutter/Dart app (return None for non-browser requests)
        if any(keyword in user_agent for keyword in ['dart:io', 'flutter', 'dart/']):
            return None

        # Check for mobile apps that might not be browsers
        if any(keyword in user_agent for keyword in ['habibapp', 'mobile app']):
            return None

        # Browser detection patterns (order matters - more specific first)
        browser_patterns = [
            (r'edg/', 'Edge'),  # Microsoft Edge (Chromium-based)
            (r'edge/', 'Edge'),  # Microsoft Edge (Legacy)
            (r'opr/', 'Opera'),  # Opera
            (r'opera/', 'Opera'),  # Opera
            (r'chrome/', 'Chrome'),  # Google Chrome
            (r'chromium/', 'Chromium'),  # Chromium
            (r'firefox/', 'Firefox'),  # Mozilla Firefox
            (r'fxios/', 'Firefox'),  # Firefox for iOS
            (r'safari/', 'Safari'),  # Safari (check after Chrome/Edge as they also contain Safari)
            (r'version/.*safari', 'Safari'),  # Safari with version
        ]

        # Check each pattern
        for pattern, browser_name in browser_patterns:
            if re.search(pattern, user_agent):
                # Additional check for Safari to avoid false positives
                if browser_name == 'Safari':
                    # Make sure it's not Chrome, Edge, or other browsers that include Safari in UA
                    if not any(other in user_agent for other in ['chrome', 'edg', 'opr', 'opera']):
                        return browser_name
                else:
                    return browser_name

        # If no specific browser detected but contains Mozilla, it might be an unknown browser
        if 'mozilla' in user_agent and any(keyword in user_agent for keyword in ['gecko', 'webkit']):
            return 'Unknown Browser'

        return None

    except Exception as e:
        # Log the error but don't let it break the API
        logger.warning(f"Error detecting browser from user agent: {e}")
        return None


class LocationHistoryView(GenericAPIView, CreateModelMixin):
    permission_classes = [IsAuthenticated]
    serializer_class = LocationHistorySerializer

    def post(self, request, *args, **kwargs):
        ip = self.get_client_ip()
        data = request.data.copy()
        data['ip'] = ip

        # Get coordinates from request data
        lat = data.get('lat')
        lon = data.get('lon')

        try:
            # Check if coordinates are special to determine the detection method
            is_special = (lat, lon) in SPECIAL_COORDINATES

            # Get location data based on coordinates type
            if is_special:
                # Use IP detection for special coordinates
                location_data = get_location_by_ip(ip)
                if location_data and location_data['status'] == 'success':
                    data['country'] = location_data['countryCode']
                    data['city'] = location_data['city']
            else:
                # Use normal coordinates detection
                location_data = get_location_by_coordinates(lat, lon)
                if location_data and location_data['status'] == 'success':
                    data['country'] = location_data['countryCode']
                    data['city'] = location_data['city']

        except Exception as e:
            logger.error(f"Error getting location: {e} lat: {lat} | lon: {lon}")
            # Continue with empty country/city if location detection fails
            data['country'] = ''
            data['city'] = ''

        serializer = self.get_serializer(data=data)
        if serializer.is_valid():
            serializer.save(user=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_queryset(self):
        return LocationHistory.objects.filter(user=self.request.user)

    def get_client_ip(self):
        # Retrieve the client's IP address from the request headers
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip
    
class RegionInfoView(GenericAPIView):
    def get(self, request, *args, **kwargs):
        # Get browser information safely
        browser = None
        try:
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            browser = detect_browser_from_user_agent(user_agent)
        except Exception as e:
            # Log the error but continue with the API response
            logger.warning(f"Error detecting browser in RegionInfoView: {e}")
            browser = None

        region_info = {
            'ip': request.META.get('HTTP_CF_CONNECTING_IP'),
            'country': request.META.get('HTTP_CF_IPCOUNTRY'),
            'region': request.META.get('HTTP_CF_REGION'),
            'region_code': request.META.get('HTTP_CF_REGION_CODE'),
            'city': request.META.get('HTTP_CF_CITY'),
            'timezone': request.META.get('HTTP_CF_TIMEZONE'),
            'browser': browser,
        }
        return Response(region_info)