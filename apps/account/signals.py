from django.conf import settings
from django.db.models.signals import post_save
from django.dispatch import receiver
from rest_framework.authtoken.models import Token
import logging

from apps.account.models.provider_request import ProviderRequest
from apps.account.tasks.provider_request_telegram import send_provider_request_to_telegram

logger = logging.getLogger(__name__)


@receiver(post_save, sender=settings.AUTH_USER_MODEL)
def create_auth_token(sender, instance=None, created=False, **kwargs):
    if created:
        Token.objects.create(user=instance)


@receiver(post_save, sender=ProviderRequest)
def send_provider_request_notification(sender, instance, created, **kwargs):
    """
    Send a notification to Telegram 10 minutes after a new provider request is created.
    """
    if created:
        # 10 minutes = 600 seconds
        countdown_seconds = 600

        logger.info(f"New provider request created (ID: {instance.id}). Will send to Telegram after {countdown_seconds} seconds...")
        # Schedule the task to run after 10 minutes
        send_provider_request_to_telegram.apply_async(
            args=[instance.id],
            countdown=countdown_seconds
        )
