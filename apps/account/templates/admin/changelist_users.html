{% extends 'admin/change_list_ajax.html' %}
{% load i18n %}

{% block layout-buttons %}
    <div class="navbar-search float-left align-items-center py-2 py-lg-0">
        <form action="">
            <div class="form-group-feedback form-group-feedback-left flex-grow-1">
                <input name="q" value="{{ request.GET.q }}" type="search" class="form-control text-white"
                       placeholder="Search">
                <div class="form-control-feedback mx-1">
                    <i class="icon-search4 text-white opacity-50"></i>
                </div>
            </div>
        </form>
    </div>
    {% include 'admin/list_navigate_tools.html' %}


    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal">
        {% trans 'filter by geo point' %}
    </button>


    <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <form action="" method="get" id="geoform">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"
                            id="exampleModalLabel">{% trans 'Filter By Latitude And Longitude' %}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body row">
                        <input id="geo" type="hidden" name="geo" value="{{ request.GET.q }}">
                        <label class="col-6">
                            {% trans 'Latitude' %}:
                            <input step="any" class="form-control" type="number" id="lat"
                                   placeholder="eg. 36.295463045916605">
                        </label>
                        <label class="col-6">
                            {% trans 'Longitude' %}:
                            <input step="any" class="form-control" type="number" id="lon"
                                   placeholder="eg. 59.60481235325402">
                        </label>
                        <label class="col-12 mt-1">
                            {% trans 'Distance Range' %}:
                            <input class="form-control w-100" type="number" id="radius"
                                   placeholder="radius in kilometer eg. 10">
                        </label>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                                data-dismiss="modal">{% trans 'Close' %}</button>
                        <button type="button" id="apply-filter"
                                class="btn btn-primary">{% trans 'Apply Filter' %}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

{% endblock %}



{% block scripts %}
    {{ block.super }}

    <script>
        $('#apply-filter').click(function () {
            let lat = $('#lat').val()
            let lon = $('#lon').val()
            let radius = $('#radius').val()
            $('#geo').val(`${lat}|${lon}|${radius}`)
            $('form#geoform').submit()
        })
    </script>
{% endblock %}