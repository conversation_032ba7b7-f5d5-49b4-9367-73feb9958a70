{% extends "admin/import_export/base.html" %}
{% load i18n %}
{% load admin_urls %}
{% load import_export_tags %}

{% block breadcrumbs_last %}
    {% trans "Export Accounts" %}
{% endblock %}

{% block content %}
    <div class="bg-white shadow-1  mx-3 p-3">
        <form action="" method="POST">
            {% csrf_token %}

            <fieldset class="module aligned">
                {% for field in form %}
                    <div class="form-row">
                        {{ field.errors }}

                        {{ field.label_tag }}

                        {{ field }}

                        {% if field.field.help_text %}
                            <p class="help">{{ field.field.help_text|safe }}</p>
                        {% endif %}
                    </div>
                {% endfor %}
            </fieldset>
            <div class="submit-row">
                <input type="submit" class="default btn btn-primary mr-auto mt-4" value="{% trans "Submit" %}">
            </div>
        </form>
    </div>
{% endblock %}
