{% extends "admin/base_site.html" %}
{% load admin_urls %}
{% load countries %}
{% load crm %}
{% load admin_list %}
{% load i18n static %}

{% block layout-buttons %}
    <div class="navbar-search float-left align-items-center py-2 py-lg-0">
        <form action="">
            <div class="form-group-feedback form-group-feedback-left flex-grow-1">
                <input name="q" value="{{ request.GET.q }}" type="search" class="form-control text-white"
                       placeholder="Search">
                <div class="form-control-feedback mx-1">
                    <i class="icon-search4 text-white opacity-50"></i>
                </div>
            </div>
        </form>
    </div>
    {% include 'admin/list_navigate_tools.html' %}
    {% spaceless %}
        <a href="




                {% url 'admin:account_pushmessage_add' %}{% if request.GET.urlencode %}{{ cl.get_query_string }}{% endif %}"
           class="btn bg-info-400 legitRipple">
            <i class="icon-mail5"></i>
            {% trans "ارسال اعلان به کاربران فیلتر شده" %}
        </a>
    {% endspaceless %}

    <a href="{% url opts|admin_urlname:'export' %}{{ cl.get_query_string }}"
       class="btn bg-info legitRipple mx-3">
        {% trans "Export" %}
        <i class="icon-database-export"></i>
    </a>

    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal">
        {% trans 'filter by geo point' %}
    </button>
{% endblock %}


{% block content %}
    <!-- Modal -->
    <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <form action="" method="get" id="geoform">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"
                            id="exampleModalLabel">{% trans 'Filter By Latitude And Longitude' %}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body row">
                        <input id="geo" type="hidden" name="geo" value="{{ request.GET.q }}">
                        <label class="col-6">
                            {% trans 'Latitude' %}:
                            <input step="any" class="form-control" type="number" id="lat"
                                   placeholder="eg. 36.295463045916605">
                        </label>
                        <label class="col-6">
                            {% trans 'Longitude' %}:
                            <input step="any" class="form-control" type="number" id="lon"
                                   placeholder="eg. 59.60481235325402">
                        </label>
                        <label class="col-12 mt-1">
                            {% trans 'Distance Range' %}:
                            <input class="form-control w-100" type="number" id="radius"
                                   placeholder="radius in kilometer eg. 10">
                        </label>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                                data-dismiss="modal">{% trans 'Close' %}</button>
                        <button type="button" id="apply-filter"
                                class="btn btn-primary">{% trans 'Apply Filter' %}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="py-3 px-3">
        {% for object in cl.formset.forms %}
            {{ object.field }}
        {% endfor %}
        <form id="changelist-form"
                {% if cl.formset and cl.formset.is_multipart %}  enctype="multipart/form-data" {% endif %}
              novalidate xmlns="http://www.w3.org/1999/html">
            {% csrf_token %}
            <div class="row" dir="ltr">
                {% if cl.result_list %}
                    {% for user in cl.result_list %}
                        <div class="col-xl-3 col-lg-6">
                            <div class="card card-body">
                                <div class="media">
                                    <div class="mr-3">
                                        <a href="{% url 'admin:account_alluser_change' user.pk %}">
                                            <img src="{{ user.country.flag }}" class="rounded-circle"
                                                 alt="" width="22" height="22">
                                        </a>
                                    </div>

                                    <div class="media-body">
                                        <a href="{% url 'admin:account_alluser_change' user.pk %}">
                                            <h6 class="text-body mb-0">{{ user }}</h6>
                                        </a>
                                        <span class="text-muted">{{ user.get_country_display }} / {{ user.city }}</span>
                                        <br>
                                        <span class="text-muted">Joined at: {{ user.date_joined }}</span>
                                    </div>

                                    <div class="ml-3 align-self-center">
                                        <div class="list-icons">
                                            <div class="dropdown position-static">
                                                <a href="#" class="list-icons-item" data-toggle="dropdown"
                                                   aria-expanded="false"><i class="icon-menu7"></i></a>
                                                <div class="dropdown-menu dropdown-menu-right" style="">
                                                    <a href="{% url "admin:account_pushmessage_add" %}?users={{ user.id }}"
                                                       class="dropdown-item">
                                                        <i class="icon-mail5"></i> ارسال اعلان</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <img src="{% static "admin/img/no-result.svg" %}" style="max-height: 600px" alt="">
                {% endif %}
                <div class="pagination pageination-users-list w-100 mt-5">
                    {% get_pagination cl.paginator as page_obj %}
                    {% get_elided_page_range cl.paginator as elided_page_range %}
                    {% include "admin/pagination.html" with page_obj=page_obj elided_page_range=elided_page_range %}
                </div>
            </div>
        </form>
    </div>

{% endblock %}


{% block styles %}
    {{ super.block }}
    <style>
        .card {
            border-radius: 5px !important;
        }
    </style>
{% endblock %}

{% block scripts %}
    {{ super.block }}
ما
    <script>
        $('#apply-filter').click(function () {
            let lat = $('#lat').val()
            let lon = $('#lon').val()
            let radius = $('#radius').val()
            {#let q_val = $('#geo').val()#}
            {#$('#geo').val(`${q_val}&geo=[${lat}|${lon}|${radius}]`)#}
            $('#geo').val(`${lat}|${lon}|${radius}`)
            $('form#geoform').submit()
        })
    </script>
{% endblock %}




