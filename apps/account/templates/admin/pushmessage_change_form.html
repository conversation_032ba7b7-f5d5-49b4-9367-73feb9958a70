{% extends "admin/change_form.html" %}
{% load i18n %}
{% load static %}

{% block scripts %}
    {{ block.super }}

    <style>
        #date-view-editor .row .row .form-group {
            margin: 10px;
        }

        #date-view-editor .row {
            width: 30% !important;
            float: left;
            margin: 1px 10px;
        }

    </style>
    <script>

        function init_editor_json_editor() {
            let editor = document.getElementById('id_data')
            let schema_str = JSON.parse(editor.value)
            $(editor).addClass("hidden")
            let json_viewer_div = $(`<div class="json-view-editor" id='date-view-editor'></div>`)
            $(editor).parent().append(json_viewer_div)

            let properties_with_year = {
                model: {
                    type: "select",
                    enum: ['Eightmag', 'Ahkam', 'Mafatih', 'Hadis', 'Donate', "Meet", "Hussainiya"],
                    default: 'Eightmag',
                    title: "{% trans 'Model' %}"
                },
                value: {type: 'string', format: 'text', title: "{% trans 'ID' %}",},
            }

            function init(properties, start_value = []) {
                if (window.jsoneditor) {
                    window.jsoneditor.destroy()
                }
                window.jsoneditor = new JSONEditor(
                    json_viewer_div[0], {
                        theme: 'bootstrap4',
                        schema: {
                            type: "object",
                            title: ' ',
                            format: 'table',
                            properties: properties_with_year,
                        },
                        required_by_default: true,
                        disable_edit_json: true,
                        disable_array_add: true,
                        disable_properties: true,
                        disable_array_delete_all_rows: true,
                        disable_array_delete_last_row: true,
                        disable_array_reorder: true,
                        grid_columns: 3,
                        prompt_before_delete: false,
                        disable_collapse: true,
                        startval: start_value
                    })
                window.jsoneditor.on('change', () => {
                    $(editor).val(JSON.stringify(jsoneditor.getValue()))
                })
            }

            init(properties_with_year, schema_str || [])
        }

        init_editor_json_editor()


    </script>
{% endblock %}
