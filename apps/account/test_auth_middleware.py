from django.core.exceptions import PermissionDenied
from rest_framework.authtoken.models import Token


def simple_middleware(get_response):
    """
        give access to swagger and api if admin is logged in
    """

    def middleware(request):
        if "/admin/" not in request.path:
            if request.user.is_authenticated and request.user.is_staff:
                if request.META.get('HTTP_AUTHORIZATION') is None:
                    token, _ = Token.objects.get_or_create(user=request.user)
                    request.META['HTTP_AUTHORIZATION'] = "Token " + token.key

        if "/swagger" in request.path or "/redoc" in request.path:
            print(request.user)

        return get_response(request)

    return middleware
