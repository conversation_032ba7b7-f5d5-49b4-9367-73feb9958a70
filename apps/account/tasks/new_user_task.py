from celery import shared_task

from ..fcm_notification import send_notification
from ..models import PushMessage


@shared_task(ignore_result=True)
def send_push_notifications(obj_id):
    obj = PushMessage.objects.get(id=obj_id)
    ids = obj.users.values_list('fcm', flat=True)
    ids = list(ids)
    response = send_notification(ids, title=obj.title, body=obj.message, url=obj.url)
    PushMessage.objects.filter(id=obj_id).update(
        result=response
    )


@shared_task(ignore_result=True)
def new_user_alarm_to_telegram(user_id):
    import requests
    from apps.account.models import User

    user = User.objects.filter(id=user_id).first()
    if not user:
        print(user_id, ' user not found')
        return

    bot_token = '**********:AAEMm91SWns5wUkpJ8YJzAU4k82AqZqf1o0'
    chat_id = '-*************'
    message = f"""#new_user
id: <a href="https://habibapp.com/en/admin/account/user/{user.id}/change/">{user.id}</a>
joined_at: {user.date_joined.strftime('%F %H:%M')}
device_id: {user.mobile_device_id}
device: {user.device_brand}
country: {user.country.name if user.country else ''}
city: {user.city}
language: {user.language.name if user.language else ''}
"""

    url = f'https://api.telegram.org/bot{bot_token}/sendMessage'
    params = {'chat_id': chat_id, 'text': message, 'parse_mode': 'HTML'}

    response = requests.post(url, data=params)

    if response.ok:
        print('Message sent successfully!')
    else:
        print('Failed to send message.')



import time
import random
import warnings
import logging
from datetime import datetime
from utils.geo import get_country_city_from_point, get_country_city
from apps.account.models import LoginHistory, User
from celery import shared_task


def _setup_logging():
    """Set up logging configuration to suppress warnings and logs."""
    warnings.filterwarnings('ignore')
    logging.getLogger('urllib3').setLevel(logging.CRITICAL)
    logging.getLogger('requests').setLevel(logging.CRITICAL)
    
    # Disable urllib3 warnings
    import urllib3
    urllib3.disable_warnings()


def _get_delay_with_jitter(retry_count):
    """Calculate delay with jitter based on retry count."""
    # Rate limiting configuration
    rate_config = {
        'initial_delay': 2.0,       # Start with a conservative delay (seconds)
        'min_delay': 1.0,           # Minimum delay between requests
        'max_delay': 30.0,          # Maximum delay between requests
        'timeout_multiplier': 2.0,  # Extra delay multiplier for timeout errors
        'jitter_factor': 0.2,       # Jitter factor to add randomness (±20%)
    }
    
    # Calculate current delay based on retry count
    if retry_count == 0:
        current_delay = rate_config['initial_delay']
    else:
        # Use exponential backoff based on retry count
        current_delay = min(
            rate_config['max_delay'],
            rate_config['initial_delay'] * (2 ** (retry_count - 1))
        )
    
    # Add jitter to avoid synchronized requests
    jitter = current_delay * rate_config['jitter_factor']
    return current_delay + random.uniform(-jitter, jitter), rate_config


def _update_records(country, city, user_history_id, user_id, location_method=None):
    """Update country and city for user and/or login history records."""
    updated_records = 0
    
    if user_history_id:
        update_data = {
            'country': country,
            'city': city
        }
        
        # اگر location_method مشخص شده باشد، آن را هم به‌روزرسانی می‌کنیم
        if location_method:
            update_data['location_method'] = location_method
            
        updated = LoginHistory.objects.filter(id=user_history_id).update(**update_data)
        updated_records += updated
    
    if user_id:
        updated = User.objects.filter(id=user_id).update(
            country=country,
            city=city
        )
        updated_records += updated
        
    return updated_records


def _handle_retry(task, error, retry_count, rate_config):
    """Handle retry logic with exponential backoff."""
    # Categorize error for specialized handling
    error_str = str(error)
    
    if 'timeout' in error_str.lower():
        retry_delay_multiplier = rate_config['timeout_multiplier']
    elif 'rate' in error_str.lower() and ('limit' in error_str.lower() or 'exceed' in error_str.lower()):
        retry_delay_multiplier = 2.5
    elif 'connection' in error_str.lower():
        retry_delay_multiplier = 1.5
    else:
        retry_delay_multiplier = 1.5
    
    # Calculate retry delay with exponential backoff and error-specific multiplier
    retry_delay = min(
        rate_config['max_delay'],
        rate_config['initial_delay'] * (2 ** retry_count) * retry_delay_multiplier
    )
    
    # Add jitter to avoid thundering herd problem
    retry_delay_with_jitter = retry_delay + random.uniform(0, retry_delay * 0.5)
    
    # Retry with silent exception
    return task.retry(
        exc=None,  # Don't pass the exception to avoid logging
        countdown=retry_delay_with_jitter,
        max_retries=5
    )


def _handle_max_retries(user_history_id, user_id, location_info="", location_method=None):
    """Handle the case when maximum retries are reached."""
    country = ''
    city = ''
    updated_records = _update_records(country, city, user_history_id, user_id, location_method)
    
    if updated_records > 0 and location_info:
        print(f"Used empty values after max retries: {location_info}")
    
    return {
        'success': True,
        'country': country,
        'city': city,
        'updated_records': updated_records,
        'max_retries_reached': True
    }


@shared_task(ignore_result=True, bind=True, max_retries=5)
def update_country_city(self, lat, lon, user_history_id=None, user_id=None):
    """
    Update country and city for a user or login history based on coordinates.
    Implements advanced rate limiting and retry logic.
    """
    _setup_logging()
    
    # Get retry count and calculate delay
    retry_count = self.request.retries
    delay_with_jitter, rate_config = _get_delay_with_jitter(retry_count)
    
    # Apply delay if this is a retry
    if retry_count > 0:
        time.sleep(delay_with_jitter)
    
    try:
        # Make the API request
        country, city = get_country_city_from_point(lat, lon)
        
        # Update records
        updated_records = _update_records(country, city, user_history_id, user_id)
        
        # Only log successful updates
        if updated_records > 0:
            print(f"Successfully updated: lat={lat}, lon={lon}, country={country}, city={city}")
        
        return {
            'success': True,
            'country': country,
            'city': city,
            'updated_records': updated_records
        }
    
    except Exception as e:
        # Check if we've reached the maximum number of retries
        if retry_count >= 5:
            return _handle_max_retries(user_history_id, user_id, f"lat={lat}, lon={lon}")
        
        # Handle retry
        return _handle_retry(self, e, retry_count, rate_config)


@shared_task(ignore_result=True, bind=True, max_retries=5)
def update_country_city_by_ip(self, ip, user_history_id=None, user_id=None):
    """
    Update country and city for a user or login history based on IP address.
    Implements advanced rate limiting and retry logic.
    """
    _setup_logging()
    
    # Get retry count and calculate delay
    retry_count = self.request.retries
    delay_with_jitter, rate_config = _get_delay_with_jitter(retry_count)
    
    # Apply delay if this is a retry
    if retry_count > 0:
        time.sleep(delay_with_jitter)
    
    try:
        # Make the API request
        country, city = get_country_city(ip)
        
        # Update records with IP_DETECTION location method
        from apps.account.models import LoginHistory
        updated_records = _update_records(
            country, 
            city, 
            user_history_id, 
            user_id, 
            location_method=LoginHistory.LocalizationModels.ip_detection
        )
        
        # Only log successful updates
        if updated_records > 0:
            print(f"Successfully updated by IP: ip={ip}, country={country}, city={city}")
        
        return {
            'success': True,
            'country': country,
            'city': city,
            'updated_records': updated_records
        }
    
    except Exception as e:
        # Check if we've reached the maximum number of retries
        if retry_count >= 5:
            from apps.account.models import LoginHistory
            return _handle_max_retries(
                user_history_id, 
                user_id, 
                f"ip={ip}", 
                location_method=LoginHistory.LocalizationModels.ip_detection
            )
        
        # Handle retry
        return _handle_retry(self, e, retry_count, rate_config)