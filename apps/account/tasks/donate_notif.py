import datetime
import os
import sys

sys.path.append('/home/<USER>/Documents/porjects/najm/')
sys.path.append('/home/<USER>/web')

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')

import django

django.setup()

from celery import shared_task
from celery.utils.log import get_task_logger
from django.db.models import Q, F, Subquery, OuterRef
from django.utils import timezone

from apps.account.admin import PushMessageAdmin

logger = get_task_logger(__name__)

from apps.account.models import User, PushMessage


class PushNotifTask:
    def get_users(self):
        three_days_ago = timezone.now() - timezone.timedelta(days=3)
        month_days_ago = timezone.now() - timezone.timedelta(days=30)

        users = User.objects.annotate(
            last_sent_notif_date=Subquery(
                PushMessage.objects.filter(data__model='Donate', users=OuterRef('pk')).values('created_at')[
                :1]
            )

        ).filter(
            Q(last_sent_notif_date__lt=month_days_ago) | Q(last_sent_notif_date__isnull=True),
            date_joined__date__lt=three_days_ago.date(),
        ).select_related('language')

        grouped_users = {}
        for u in users:
            lang_code = 'en' if not u.language else u.language.code

            if lang_code not in grouped_users:
                grouped_users[lang_code] = []

            grouped_users[lang_code].append(u)

        return grouped_users

    def get_message(self, language_code):
        messages_map = {
            'en': """Habib is free, Support Habib's growth with your donation.
تَعاوَنُوا عَلَى الْبِرّ
Help one another in acts of piety and righteousness.
            """,
            "ar": """حبيب مجاني، و تطوير حبيب يحتاج إلى دعمكم المالي
قال الله تعالی: "تَعاوَنُوا عَلَى الْبِرّ"
            """,
            "fa": """حبیب رایگان است و توسعه حبیب نیازمند کمک مالی شماست
تَعاوَنُوا عَلَى الْبِرّ
یکدیگر را بر انجام کارهای خیر یاری نمایید
            """,
            "ur": """حبیب مفت ہے، اور حبیب کی ترقی آپ کے مالی تعاون کی محتاج ہے۔
تَعاوَنُوا عَلَى الْبِرّ
نیکی اور تقوی کے کاموں میں ایک دوسرے کی مدد کرو۔
            """,
            "de": """Habib ist kostenlos, und die Entwicklung von Habib benötigt Ihre finanzielle Unterstützung.
تَعاوَنُوا عَلَى الْبِرّ
Helft einander bei Taten der Frömmigkeit und Gerechtigkeit.""",
            "tr": """Habib ücretsizdir ve Habib'in gelişimi sizin mali desteğinize ihtiyaç duyar.
تَعاوَنُوا عَلَى الْبِرّ
Birbirinize iyilik ve takva işlerinde yardım edin.""",
            "az": """Habib pulsuzdur və Habibin inkişafı sizin mali dəstəyinizə ehtiyac duyur.
تَعاوَنُوا عَلَى الْبِرّ
Yaxşılıq və takvalıq işlərində bir-birinizə kömək edin.""",
            "fr": """Habib est gratuit, et le développement de Habib nécessite votre soutien financier.
تَعاوَنُوا عَلَى الْبِرّ
Aidez-vous mutuellement dans les actes de piété et de droiture.""",
            "id": """Habib itu gratis, dan pengembangan Habib membutuhkan dukungan finansial Anda.
تَعاوَنُوا عَلَى الْبِرّ
Bantulah satu sama lain dalam perbuatan kebajikan dan ketakwaan.""",
            "sp": """Habib es gratuito, y el desarrollo de Habib requiere de tu apoyo financiero.
تَعاوَنُوا عَلَى الْبِرّ
Ayudaos unos a otros en actos de piedad y rectitud."""
        }

        return {
            'title': 'Donate Habib',
            'message': messages_map.get(language_code) or messages_map['en'],
        }

    def send(self):
        print("started: ", datetime.datetime.now())
        grouped_users = self.get_users()
        for lang_code, user_ids in grouped_users.items():
            print('sending for :', {lang_code})
            message_obj = self.get_message(lang_code or 'en')

            obj = PushMessage.objects.create(
                **message_obj,
                data={
                    'model': 'Donate',
                    'value': '',
                },
            )

            obj.users.add(
                *user_ids
            )

            try:
                res = PushMessageAdmin.send_fcm_notif(obj)
                print('res')
            except Exception as e:
                print(e)

        print("finished: ", datetime.datetime.now())


@shared_task
def send_donate_link_notif():
    PushNotifTask().send()


PushNotifTask().send()
