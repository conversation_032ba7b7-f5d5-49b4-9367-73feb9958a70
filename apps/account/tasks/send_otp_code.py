import time
import random
from config.settings import base as settings

from celery import shared_task
import requests
import json

@shared_task
def send_otp_code_sms(phone_number, code):
    BASE_URL_SERVICE = "https://console.melipayamak.com/api/send/simple/"
    OTP_SERIVCE_KEY = "33213d78f1234e99b81f94eefda77e45"

    phone_number = str(phone_number)
    code = str(code)
    print(code)
    data = {'from': '50004001410202', 'to': phone_number, 'text': code}
    response = requests.post(f'{BASE_URL_SERVICE}{OTP_SERIVCE_KEY}',
                             json=data)
    
    print(response.json())


class ManageWhatsappApi(object):

    def __init__(self, chat_id, text):
        self.chat_id: str = chat_id
        self.text: str = text
        self.reply_to: str =  False
        self.session: str = 'default'
        
        
    def get_api_sendtext(self):
        return f"http://*************:3005/api/sendText"
    

    def sendtext(self):
        data = {
            "chatId": self.chat_id,
            "reply_to": self.reply_to,
            "text": self.text,
            "session": self.session
        }
        
        # تنظیم هدرهای درخواست
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        response = requests.post(self.get_api_sendtext(), json=data, headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": response.status_code, "message": response.text}


# @shared_task
def send_otp_code_whatsapp(phone_number, code, fullname: str = ''):
    try:
        phone = phone_number
    
        if phone.startswith('0') or phone.startswith('+'):
            phone = phone[1:]
        if not fullname:
            fullname = ''
    
        payload = {
            "chatId": f"{phone}@c.us", 
            "message": f"Hello {fullname}! Welcome to the Habib App. Your verification code is: {code}."
    
        }
        headers = {
            'Content-Type': 'application/json'
        }
    
        whatsapp_api = ManageWhatsappApi(chat_id=payload['chatId'], text=payload['message'])
        response = whatsapp_api.sendtext()
        if 'error' in response and response['error'] == 500:
            return False
        return True
    
    except Exception as e:
        return False


def generate_otp_code() -> int:
    random_code = random.randint(10000, 99999)
    return random_code

if __name__ == "__main__":
    send_otp_code_whatsapp('9012045375', 'ss222', 'ali')
    # print(response)
