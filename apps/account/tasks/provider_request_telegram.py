import json
import requests
import logging
import traceback
import os
from datetime import datetime, timedelta

from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.core.files import File
from celery import shared_task
from apps.account.models.provider_request import ProviderRequest
from apps.meet.models.provider import MeetProviderProfile

logger = logging.getLogger(__name__)

# تنظیمات بات تلگرام از فایل تنظیمات پروژه
BOT_TOKEN = "**********:AAFeymryw7S44D86LYfWqYK-tSNeV3TOwBs"
TELEGRAM_CHANNEL_ID = "-*************"


def get_provider_profile(obj):
    """
    Get provider profile information based on the service type.
    Similar to get_profile method in ProviderRequestAdmin.
    """
    if obj.service == 'talk':
        try:
            provider = json.loads(obj.description.split('Data: ')[-1])
            return provider, True, None
        except Exception as e:
            logger.error(f"Error parsing talk provider data: {e}")
            return None, False, None
    else:
        profile = MeetProviderProfile.objects.filter(request=obj).first()
    
    if profile:
        return {
            'full_name': profile.full_name, 
            'email': profile.email, 
            'bio': profile.bio,
            'birthdate': profile.birthdate, 
            'institution_name': profile.institution_name,
            'social_medias': profile.social_medias, 
            'user_id': profile.user.id,
            'languages': list(profile.languages.values_list('name', flat=True)) if hasattr(profile, 'languages') else [],
            'wa_number': profile.wa_number, 
            'avatar': profile.avatar
        }, False, profile
    return None, False, None


def get_info_text(profile):
    """
    Convert profile information to text format for Telegram message.
    Enhanced with better formatting and organization for a more professional look.
    """
    # Group related fields for better organization
    personal_fields = [
        # ('full_name', '👤 Full Name'),
        # ('user_id', '🆔 User ID'),
        # ('email', '📧 Email'),
        ('birthdate', '🎂 Birthdate'),
        # ('wa_number', '📱 WhatsApp')
    ]
    
    professional_fields = [
        ('institution_name', '🏢 Institution'),
        ('languages', '🌐 Languages'),
        ('bio', '📝 Bio')
    ]
    
    social_media_field = ('social_medias', '🔗 Social Media')
    
    # Format personal information
    personal_info = ""
    for field_key, field_label in personal_fields:
        field_value = profile.get(field_key, 'N/A')
        
        # Format birthdate
        if field_key == 'birthdate' and field_value:
            try:
                if hasattr(field_value, 'strftime'):
                    field_value = field_value.strftime('%Y-%m-%d')
            except Exception:
                pass
                
        personal_info += f"{field_label}: `{field_value}`\n"
    
    # Format professional information
    professional_info = ""
    for field_key, field_label in professional_fields:
        field_value = profile.get(field_key, 'N/A')
        
        # Format languages list
        if field_key == 'languages' and isinstance(field_value, list):
            field_value = ", ".join(field_value) if field_value else 'N/A'
            
        # For bio, add it with special formatting if it exists
        if field_key == 'bio' and field_value and field_value != 'N/A':
            professional_info += f"{field_label}:\n`{field_value}`\n"
        else:
            professional_info += f"{field_label}: `{field_value}`\n"
    
    # Format social media links with icons
    social_media_info = ""
    field_key, field_label = social_media_field
    field_value = profile.get(field_key, {})
    
    if isinstance(field_value, dict) and any(field_value.values()):
        social_media_info += f"{field_label}:\n"
        social_icons = {
            'instagram': '📸',
            'twitter': '🐦',
            'linkedin': '💼',
            'facebook': '👍',
            'telegram': '📢',
            'website': '🌐',
            'github': '💻',
            'youtube': '▶️',
            'tiktok': '🎵'
        }
        
        for platform, url in field_value.items():
            if url:
                icon = social_icons.get(platform.lower(), '🔗')
                social_media_info += f"   {icon} {platform.title()}: `{url}`\n"
    else:
        social_media_info += f"{field_label}: `N/A`\n"
    
    # Combine all sections with section headers
    info_text = professional_info + "\n" + personal_info  +  "\n" + social_media_info
    return info_text


def check_and_fix_avatar(provider_request, profile_data, profile_obj):
    """
    Check if avatar is valid, if not, use user's avatar as fallback
    and save it to the provider profile.
    
    Returns:
        tuple: (avatar_url, is_valid)
    """
    if provider_request.service == 'talk':
        avatar = profile_data.get('avatar')
        if avatar:
            return avatar, True
        return None, False
    if not profile_data.get('avatar'):
        return None, False
    
    try:
        avatar = profile_data['avatar']
        
        # اگر آواتار URL باشد
        if isinstance(avatar, str):
            return avatar, True
            
        # اگر آواتار فایل باشد
        if hasattr(avatar, 'path') and os.path.exists(avatar.path):
            return avatar.url, True
            
        # اگر به این نقطه برسیم یعنی آواتار فعلی معتبر نیست
        raise ValueError("Avatar path is invalid")
        
    except Exception as e:
        logger.warning(f"Invalid avatar for provider request {provider_request.id}: {str(e)}")
        
        # استفاده از آواتار کاربر به عنوان جایگزین
        user = provider_request.user
        if user and hasattr(user, 'avatar') and user.avatar:
            try:
                user_avatar = user.avatar
                
                # جایگزینی آواتار در پروفایل پرووایدر
                if profile_obj:
                    try:
                        # اگر آواتار کاربر از نوع FilerImageField باشد
                        if hasattr(user_avatar, 'url'):
                            profile_obj.avatar = user_avatar
                            # profile_obj.save(update_fields=['avatar'])
                            logger.info(f"Replaced provider profile avatar with user avatar for request {provider_request.id}/ {type(user_avatar)}/{user_avatar.url}")
                            return f'https://habibapp.com/{user_avatar.url}', True
                    except Exception as save_error:
                        logger.error(f"Error saving replacement avatar: {str(save_error)}")
                
                # برگرداندن URL آواتار کاربر
                if hasattr(user_avatar, 'url'):
                    return user_avatar.url, True
                    
            except Exception as user_avatar_error:
                logger.error(f"Error using user avatar as fallback: {str(user_avatar_error)}")
                
        return None, False


def normalize_hashtag(text):
    """
    Convert text to a normalized hashtag format, similar to format_telegram_message function.
    """
    if not text:
        return "unknown"
        
    # Replace spaces and special characters
    normalized = text.replace(" ", "_") \
                    .replace(".", "_") \
                    .replace("(", "") \
                    .replace(")", "") \
                    .replace("?", "") \
                    .replace("-", "_")
                    
    # Use regex to further clean the string
    import re
    normalized = re.sub(r'[^\wآ-یa-zA-Z0-9]', '_', normalized)
    normalized = re.sub(r'_+', '_', normalized).strip('_')
    
    return normalized
@shared_task(ignore_result=True)
def send_provider_request_to_telegram(request_id):
    """
    Send provider request information to Telegram channel.
    Enhanced with better visual formatting for a more professional look.
    """
    try:
        provider_request = ProviderRequest.objects.filter(id=request_id).first()
        if not provider_request:
            logger.error(f"Provider request with ID {request_id} not found.")
            return
        
        profile, is_url, profile_obj = get_provider_profile(provider_request)
        if not profile:
            logger.error(f"No profile information available for provider request {request_id}.")
            return

        # تلاش برای بررسی و تصحیح آواتار در صورت نیاز
        avatar_url, is_valid_avatar = check_and_fix_avatar(provider_request, profile, profile_obj)

        # ایجاد پیام با طراحی زیبا و حرفه‌ای
        service_emoji = get_service_emoji(provider_request.service)
        status_emoji = get_status_emoji(provider_request.status)
        
        # Get creation time and format it
        created_date = provider_request.created_at.strftime('%Y-%m-%d')
        created_time = provider_request.created_at.strftime('%H:%M')

        # Don't escape the admin link here, let escape_markdown handle it
        admin_link = f"https://habibapp.com/en/admin/account/providerrequest/{provider_request.id}/change/"
     
        # Calculate fancy header with service icon
        service_display = provider_request.get_service_display()
        header = f"✨ *NEW {service_display.upper()} PROVIDER* ✨"
        
        # Create a visually appealing divider
        divider = "━━━━━━━━━━━━━━━━━━━━━━"
        
        caption = f"""
{header}
{divider}

{service_emoji} *Service Details:*
📦 Service Type: {service_display}
{status_emoji} Status: {provider_request.get_status_display()}
📅 Date: {created_date}
⏰ Time: {created_time}

👤 *Provider Profile:*
{get_info_text(profile)}

🔗 [View Details](<{admin_link}>)
"""

        # ارسال تصویر با توضیحات اگر آواتار معتبر وجود داشته باشد، در غیر این صورت ارسال متن
        if avatar_url and is_valid_avatar:
            send_photo_to_telegram(avatar_url, caption)
        else:
            send_text_to_telegram(caption)
            
    except Exception as e:
        logger.error(f"Error sending provider request {request_id} to Telegram: {str(e)}")
        logger.error(traceback.format_exc())


def get_service_emoji(service):
    """
    Return appropriate emoji for service type.
    """
    service_emojis = {
        'hussainiya': '🕌',
        'net': '🌐',
        'library': '📚',
        'talk': '💬',
        'shop': '🛒',
        'travel': '✈️',
        'meet': '👥'
    }
    return service_emojis.get(service, '📋')


def get_status_emoji(status):
    """
    Return appropriate emoji for status.
    """
    status_emojis = {
        'pending': '⏳',
        'accepted': '✅',
        'rejected': '❌'
    }
    return status_emojis.get(status, '📊')


def send_photo_to_telegram(photo_url, caption):
    """
    Send photo to Telegram channel with caption.
    """
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendPhoto"
    
    # Escape markdown characters in caption
    escaped_caption = escape_markdown(caption)
    
    payload = {
        'chat_id': TELEGRAM_CHANNEL_ID,
        'photo': photo_url,
        'caption': escaped_caption,
        'parse_mode': 'MarkdownV2'
    }
    
    try:
        response = requests.post(url, data=payload)
        response.raise_for_status()
        logger.info(f"Successfully sent provider request photo to Telegram. Response: {response.json()}")
        return True
    except requests.exceptions.RequestException as e:
        logger.error(f"Error sending provider request photo to Telegram: {str(e)}")
        if hasattr(e, 'response'):
            logger.error(f"Response content: {e.response.text}")
        # Fall back to text message if photo sending fails
        send_text_to_telegram(caption)
        return False


def send_text_to_telegram(text):
    """
    Send text message to Telegram channel.
    """
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    
    # Escape markdown characters in text
    escaped_text = escape_markdown(text)
    
    payload = {
        'chat_id': TELEGRAM_CHANNEL_ID,
        'text': escaped_text,
        'parse_mode': 'MarkdownV2',
        'disable_web_page_preview': True

    }
    
    try:
        response = requests.post(url, data=payload)
        response.raise_for_status()
        logger.info(f"Successfully sent provider request text to Telegram. Response: {response.json()}")
        return True
    except requests.exceptions.RequestException as e:
        logger.error(f"Error sending provider request text to Telegram: {str(e)}")
        if hasattr(e, 'response'):
            logger.error(f"Response content: {e.response.text}")
        return False


def escape_markdown(text):
    """
    Escape special characters for Markdown V2.
    """
    if text is None:
        return ""
        
    # Convert text to string if it's not already
    text = str(text)
    
    # List of special characters in MarkdownV2 that need to be escaped
    special_chars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
    
    # Create a new string with escaped characters
    escaped_text = ""
    for char in text:
        if char in special_chars:
            escaped_text += f"\\{char}"
        else:
            escaped_text += char
            
    return escaped_text

