from django.contrib.auth.backends import ModelBackend
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from rest_framework import exceptions
from rest_framework.authentication import TokenAuthentication

from apps.account.models import User


class ModelBackendWithUsernameAndEmail(ModelBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        try:
            # First try to get by exact username match
            try:
                user = User.objects.get(username=username)
                if user.check_password(password) and self.user_can_authenticate(user):
                    return user
            except User.DoesNotExist:
                # If no user with that username, try by email
                try:
                    user = User.objects.get(email=username)
                    if user.check_password(password) and self.user_can_authenticate(user):
                        return user
                except User.DoesNotExist:
                    pass
                except User.MultipleObjectsReturned:
                    # If multiple users with same email, get the most recently active one
                    users = User.objects.filter(email=username).order_by('-last_login')
                    for user in users:
                        if user.check_password(password) and self.user_can_authenticate(user):
                            return user
            except User.MultipleObjectsReturned:
                # If multiple users with same username, get the most recently active one
                users = User.objects.filter(username=username).order_by('-last_login')
                for user in users:
                    if user.check_password(password) and self.user_can_authenticate(user):
                        return user
        except Exception:
            # Run the default password hasher once to reduce the timing
            # difference between an existing and a nonexistent user (#20760).
            pass

        return None
