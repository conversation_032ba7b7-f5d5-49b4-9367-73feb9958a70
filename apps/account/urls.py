from django.urls import path

from apps.account.views import(
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Hello<PERSON>ser, GoogleAuth, UserUpdateView, DeleteAccountView,
    AppleAuth, PhoneAuthView, PhoneVerifyView, ProfileUpdateView,
    MergeAccountView, MergePhoneNumber, PhoneAuthInfo
)
from apps.account.views.profile import LogoutView

from apps.account.views.activity import ActivityCreateAPIView, UpdateUserSettingsView, BucketUpdateAPIView

# from apps.account.views.apple import AppleAuth
from apps.account.views.location_history import LocationHistoryView, RegionInfoView
from apps.account.views.provider import RegisterProviderView

urlpatterns = [
    path('auth/google/', GoogleAuth.as_view()),
    path('auth/apple/', AppleAuth.as_view()),
    path('auth/guest/', GuestAuthView.as_view()),
    path('auth/phone/', PhoneAuthView.as_view()),
    path('auth/verify/', PhoneVerifyView.as_view()),
    path('auth/logout/', LogoutView.as_view(), name='logout'),

    path('auth/user/region/', RegionInfoView.as_view(), name='region-info'),
    path('auth/user/merge/', MergeAccountView.as_view()),
    path('auth/user/merge/phone/verify/', MergePhoneNumber.as_view()),
    path('auth/user/merge/phone/info/', PhoneAuthInfo.as_view()),
    
    path('auth/user/me/', HelloUser.as_view()),
    path('auth/user/profile/update/', ProfileUpdateView.as_view()),
    
    path('auth/<str:device_id>/update/', UserUpdateView.as_view()),
    path('provider/requests/', RegisterProviderView.as_view()),
    path('location-update/', LocationHistoryView.as_view()),
    path('delete-account/', DeleteAccountView.as_view()),
    path('activity/create/', ActivityCreateAPIView.as_view(), name='activity-create'),
    path('settings/', UpdateUserSettingsView.as_view(), name='user-settings'),
    path('bucket/update/', BucketUpdateAPIView.as_view(), name='bucket-update'),
    
]
