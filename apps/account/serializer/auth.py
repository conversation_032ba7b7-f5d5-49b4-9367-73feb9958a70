from dj_language.models import Language
from rest_framework import serializers
from io import BytesIO

from dj_language.models import Language
from django.core.files import File
from django.utils import timezone
from rest_framework.authtoken.models import Token

from utils.ios_auth_decode import decode_apple_token
from ..tasks.new_user_task import new_user_alarm_to_telegram
from ..models import User, GuestUser


class UserSerializer(serializers.ModelSerializer):
    lat = serializers.CharField(max_length=255)
    lon = serializers.CharField(max_length=255)
    avatar = serializers.CharField(required=False, label="~ Avatar")
    phone_number = serializers.CharField(required=False, label="~ Phone number")
    social_auth_data = serializers.JSONField(required=False, default={}, read_only=True)

    class Meta:
        model = User
        fields = (
            'email', 'first_name', 'last_name', 'mobile_device_id',
            'lat', 'lon', 'phone_number', 'avatar', 'social_auth_data',
        )


class GuestUserSerializer(serializers.ModelSerializer):
    lat = serializers.CharField(max_length=255, allow_null=True, allow_blank=True, required=False)
    lon = serializers.CharField(max_length=255, allow_null=True, allow_blank=True, required=False)
    language = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    mobile_device_id = serializers.CharField(allow_null=True, allow_blank=True)
    timezone = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    api_version = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    location_method = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    class Meta:
        model = GuestUser
        fields = (
            'lat', 'lon', 'mobile_device_id', 'language', 'cpu_type',
            'model_name', 'os_platform', 'screen_size', 'device_brand', 'timezone', 'api_version', 'fcm',
            'location_method',
        )

    def validate(self, attrs):
        language_code = attrs['language']   
        lang = Language.objects.filter(code=language_code, status=True).first()
        if not lang:
            raise serializers.ValidationError({'language': 'this Language is not available'})
        attrs['language'] = lang
        return attrs


class GoogleAuthSerializer(serializers.ModelSerializer):
    id_token = serializers.CharField(max_length=9000)
    server_auth_token = serializers.CharField(max_length=9000)
    avatar = serializers.CharField(required=False, label="~ Avatar", allow_null=True, allow_blank=True)
    mobile_device_id = serializers.CharField(max_length=500, allow_blank=True, allow_null=True)
    email = serializers.CharField(allow_null=True, allow_blank=True)
    name = serializers.CharField(max_length=255, allow_blank=True, allow_null=True)
    fcm = serializers.CharField(allow_null=True, required=False, allow_blank=True)
    
    class Meta:
        model = User
        fields = (
            'email', 'name', 'mobile_device_id', 'avatar', 'server_auth_token', 'id_token',
            'fcm', 'publisher_in'
        )




class AppleAuthSerializer(serializers.ModelSerializer):
    token = serializers.CharField(read_only=True)
    id_token = serializers.CharField(max_length=9000)
    server_auth_token = serializers.CharField(max_length=9000)
    avatar = serializers.CharField(required=False, label="~ Avatar", allow_null=True, allow_blank=True)
    mobile_device_id = serializers.CharField(max_length=255, required=True, allow_null=False)
    name = serializers.CharField(max_length=255, allow_blank=True, allow_null=True, required=False)
    fcm = serializers.CharField(allow_null=True, required=False, allow_blank=True)

    def validate(self, data):
        user_data = data.get('id_token')
        server_auth = data.get('server_auth_token')

        data['social_auth_data'] = {
            **user_data,
            'server_auth': server_auth,
        }

        return data

    def validate_id_token(self, value):
        if data := decode_apple_token(value):
            return {
                'email': data.get('email'),
                'user_id': data['sub'],
                'oauth': 'apple',
            }

        raise serializers.ValidationError('Invalid identityToken')

    def validate_avatar(self, value):
        if not value:
            return

        try:
            import requests
            file_content = requests.get(value).content
            return File(
                BytesIO(file_content)
            )

        except Exception as e:
            print(e)


    class Meta:
        model = User
        fields = (
            'id_token', 'server_auth_token', 'mobile_device_id',
            'name', 'avatar', 'fcm', 'publisher_in', 'token',
        )



class PhoneAuthSerializer(serializers.ModelSerializer):    
    lat = serializers.CharField(max_length=255, allow_null=True, allow_blank=True, required=False)
    lon = serializers.CharField(max_length=255, allow_null=True, allow_blank=True, required=False)
    mobile_device_id = serializers.CharField(allow_null=True, allow_blank=True)
    timezone = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    api_version = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    location_method = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    mobile_device_id = serializers.CharField(max_length=500, allow_blank=True, allow_null=True)
    fcm = serializers.CharField(allow_null=True, required=False, allow_blank=True)

    class Meta:
        model = User
        fields = (
            'phone_number',
            'lat', 'lon', 'mobile_device_id', 'cpu_type',
            'model_name', 'os_platform', 'screen_size', 'device_brand', 'timezone', 'api_version', 'fcm',
            'location_method',
        )
        extra_kwargs = {
            'phone_number': {'required': True,},
            "mobile_device_id": {'required': True,}
        }



                                              
            
class PhoneVerifySerializer(serializers.ModelSerializer):
    user_id = serializers.CharField(max_length=10, required=True)
    code = serializers.CharField(max_length=5, required=True)


    def validate_code(self, value):
        if len(value) != 5 or not value.isdigit():
            raise serializers.ValidationError({'code': "The code must be exactly 5 digits long and contain only numbers."})
        return value

    class Meta:
        model = User
        fields = (
            'phone_number', 'user_id', 'code'
        )
        extra_kwargs = {
            'phone_number': {'required': True,},
        }
