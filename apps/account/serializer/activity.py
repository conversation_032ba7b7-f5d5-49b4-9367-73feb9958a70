import os
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from urllib.parse import urlparse

from ..models.activity import Activity
from apps.account.models import User, UserSettings
from utils.tmp_media import FileFieldSerializer
from dj_filer.admin import get_thumbs
from config.settings import base as settings
from django.conf import settings
from utils.filer_import import import_file

class ActivitySerializer(serializers.ModelSerializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())

    class Meta:
        model = Activity
        fields = ['user', 'service', 'usage_duration', 'entered_at', 'additional_info']





class UserSettingsSerializer(serializers.ModelSerializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    notification_settings = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text='List of notification services: hussainiyah, meet, talk, library',
        default=['hussainiyah', 'meet', 'talk', 'library']
    )
    
    class Meta:
        model = UserSettings
        fields = ['settings', 'user', 'is_reminder',  'notification_settings']
        
    def to_representation(self, instance):
        ret = super().to_representation(instance)
        if ret.get('notification_settings') is None:
            ret['notification_settings'] = []
        return ret
        
        
class UserProfileSerializer(serializers.Serializer):
    name = serializers.CharField(required=False)
    avatar = serializers.CharField(required=False, allow_null=True, allow_blank=True)  
    email = serializers.EmailField(read_only=True)
    phone_number = serializers.CharField(read_only=True)
        
    class Meta:
        model = User
        fields = [
            'id', 'name', 'avatar', 'email', 'phone_number',
        ]
        read_only_fields = ['phone_number', 'email']  


    def to_representation(self, instance):
        request = self.context.get('request')
        representation = super().to_representation(instance)
        if 'avatar' in representation and representation['avatar'] and request:
            representation['avatar'] = request.build_absolute_uri(representation['avatar'])

        return representation

    def validate_avatar(self, value):
        if not value:
            return
        file_path = self.extract_file_path(value)
        file_path = f"{file_path}"
        if not os.path.exists(file_path):
            raise serializers.ValidationError(f"file does not exist, upload again")
        return file_path
    

    def validate(self, attrs):
        avatar_data = attrs.pop('avatar', None)
        if avatar_data:  
            try:

                avatar = import_file(avatar_data, 'users_avatar')
                attrs['avatar'] = avatar
            except Exception as e:
                raise serializers.ValidationError({
                    'avatar': f"Error processing the image file: {str(e)}"
                })
        return attrs



    def extract_file_path(self, file_path):
        parsed_url = urlparse(file_path)
        file_path = parsed_url.path
        file_path = file_path.replace('/static', '')
        static_path = "static" if settings.DEBUG else "staticfiles"
        return static_path + file_path
    
    
    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            if value is not None:
                setattr(instance, attr, value)
        instance.save()
        return instance



        
