from rest_framework import serializers

from apps.account.models import ProviderRequest


class ProviderSerializer(serializers.ModelSerializer):
    social_medias = serializers.JSONField(
        source='user.social_medias', default=[],
        help_text='[{"type": "instagram", "link": "https://www.instagram.com"}]'
    )
    phone_number = serializers.CharField(source='user.phone_number')
    wa_number = serializers.CharField(source='user.wa_number')
    birthdate = serializers.DateField(source='user.birthdate')

    class Meta:
        model = ProviderRequest
        fields = (
            'public_name', 'description', 'service',
            'birthdate', 'phone_number', 'wa_number', 'social_medias',
            'status',
        )
        read_only_fields = ('status',)

    def save(self, **kwargs):
        user = self.context['request'].user
        user_data = self.validated_data['user']

        user.social_medias = user_data.pop('social_medias', [])
        user.phone_number = user_data.pop('phone_number', None)
        user.wa_number = user_data.pop('wa_number', None)
        user.birthdate = user_data.pop('birthdate', None)
        user.save()

        super().save(user=user)
