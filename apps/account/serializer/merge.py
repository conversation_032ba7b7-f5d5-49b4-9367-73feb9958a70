import os
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from apps.account.models import User



class MergeAccountSerializer(serializers.ModelSerializer):
    value = serializers.CharField()
    method = serializers.ChoiceField(choices=["phone", "email"], required=True)
    
    class Meta:
        model = User
        fields = ['method', 'value']
        
class MergeVerifySerializer(serializers.Serializer):
    phone_number = serializers.CharField(required=True)
    code = serializers.CharField(required=True)
    
    class Meta:
        model = User
        fields = ['code']
        
