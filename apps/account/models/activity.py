from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone


class Activity(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, verbose_name=_('user'), null=True)
    service = models.CharField(null=True, blank=True, max_length=100, verbose_name=_('service'))
    usage_duration = models.DurationField()
    entered_at = models.DateTimeField(default=timezone.now)
    additional_info = models.JSONField(verbose_name=_('Additional info'), default=dict)

    def __str__(self):
        return f'{self.user} - {self.service} - {self.entered_at}'

    class Meta:
        verbose_name = _('activity')
        verbose_name_plural = _('activities')
        ordering = ('-id',)
