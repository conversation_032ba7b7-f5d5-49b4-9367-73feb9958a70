import random
from dj_language.field import <PERSON><PERSON><PERSON>
from django.contrib.auth.base_user import Base<PERSON>serManager
from django.contrib.auth.models import _user_has_perm, AbstractUser  # type: ignore
from django.db import models
from django.db.models import Sum, Q
from django.utils import timezone
from django.utils.translation import gettext as _
from django_countries.fields import Country<PERSON>ield
from filer.fields.image import Filer<PERSON><PERSON><PERSON><PERSON>
from phonenumber_field.modelfields import Phone<PERSON><PERSON>ber<PERSON>ield
from rest_framework.authtoken.models import Token
from multiselectfield import MultiSelectField
from apps.account.validators import validate_possible_number
from apps.q_and_a.models import Consultants
from apps.report.models import Service


class UserManager(BaseUserManager):
    def create_user(
            self, email, password=None, is_staff=False, is_active=True, **extra_fields
    ):
        """Create a user instance with the given email and password."""
        email = UserManager.normalize_email(email)
        # Google OAuth2 backend send unnecessary username field
        extra_fields.pop("username", None)

        user = self.model(
            email=email, is_active=is_active, is_staff=is_staff, **extra_fields
        )
        if password:
            user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        return self.create_user(
            email, password, is_staff=True, is_superuser=True, **extra_fields
        )

    def staff(self):
        return self.get_queryset().filter(is_staff=True)

    def guest_users(self):
        return self.get_queryset().filter(is_staff=False)

    def registered_users(self):
        return self.get_queryset().filter(email__isnull=False, is_staff=False)


class User(AbstractUser):
    class DeviceOs(models.TextChoices):
        android = 'android', 'android'
        apple = 'apple', 'apple'

    email = models.EmailField(verbose_name=_('email'), null=True, blank=True)
    device_os = models.CharField(choices=DeviceOs.choices, null=True, max_length=16)
    username = models.CharField(unique=True, null=True, blank=True, max_length=150)
    first_name = models.CharField(verbose_name=_('first name'), max_length=254, null=True)
    bio = models.CharField(max_length=512, verbose_name=_('bio'), null=True, blank=True)
    birthdate = models.DateField(null=True, blank=True, verbose_name=_('birthdate'))

    last_name = None
    name = models.CharField(verbose_name=_('name'), max_length=254, null=True)
    country = CountryField(verbose_name=_('country'), null=True, max_length=254)
    city = models.CharField(verbose_name=_('city'), null=True, max_length=254)
    phone_number = PhoneNumberField(
        verbose_name=_('phone number'), null=True, blank=True, max_length=254, validators=[validate_possible_number]
    )
    possible_phone_number = PhoneNumberField(
        verbose_name=_('possible phone number'), null=True, blank=True, max_length=254, validators=[validate_possible_number]
    )
    is_verified = models.BooleanField(default=False, verbose_name=_('Is the phone number verified'))

    wa_number = models.CharField(verbose_name=_('whatsapp number'), max_length=255, null=True, blank=True)
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    date_joined = models.DateTimeField(auto_now_add=True)
    avatar = FilerImageField(verbose_name=_('avatar'), null=True, blank=True, on_delete=models.CASCADE)
    social_auth_data = models.JSONField(null=True, blank=True, editable=False)
    social_medias = models.JSONField(default=list, verbose_name=_('social medias'))

    mobile_device_id = models.CharField(verbose_name=_('mobile device id'), max_length=254, null=True, blank=True, )

    language = LanguageField(verbose_name=_('language'), null=True, blank=True)

    cpu_type = models.CharField(null=True, blank=True, max_length=64)
    model_name = models.CharField(null=True, blank=True, max_length=120)
    os_platform = models.CharField(null=True, blank=True, max_length=120)
    screen_size = models.CharField(null=True, blank=True, max_length=120)
    device_brand = models.CharField(null=True, blank=True, max_length=120)
    fcm = models.CharField(null=True, blank=True, max_length=255, verbose_name=_('firebase token'))
    deleted_data = models.CharField(null=True, verbose_name=_('deleted data'), max_length=255)

    objects = UserManager()

    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = []

    def __str__(self):
        return self.email or str(self.phone_number) or self.mobile_device_id or '-'

    def is_guest(self):
        return self.email and len(self.email)

    def save(self, *args, **kwargs):
        if self.email:
            self.username = self.email
            if User.objects.filter(username=self.email).count(): 
                self.username = f'{self.email}:{self.id}'
        else:
            self.username = str(self.phone_number)
            if User.objects.filter(username=str(self.phone_number)).count():
                self.username = f'{self.phone_number}:{self.id}'
        return super().save(*args, **kwargs)

    class Meta:
        ordering = ("email",)
        verbose_name = _("customer user")
        verbose_name_plural = _("customer users")
        unique_together = (
            'email', 'device_os',
        )

    def soft_delete(self):
        self.is_active = False
        deleted_at = timezone.now().strftime('%Y-%m-%d %H:%M')
        self.deleted_data = f"{deleted_at}, {self.email}, {self.phone_number},{self.is_verified} ,{self.mobile_device_id}"
        number = str(random.randint(1000, 9999))  
        self.mobile_device_id = f'{self.mobile_device_id}:deleted{number}'
        self.phone_number = None
        self.email =  None
        self.username = None
        Token.objects.filter(user=self).delete()
        self.save()
        
    @property
    def get_user_settings(self):
        try:
            return self.settings.settings
        except UserSettings.DoesNotExist:
            return None
    @property
    def is_tester(self):
        try:
            return self.settings.is_tester
        except UserSettings.DoesNotExist:
            return False    

    @property
    def coin_balance(self):
        total_coin_balance = self.user_packages.filter(
            Q(expiration_date__gt=timezone.now()) | Q(expiration_date__isnull=True)
        ).aggregate(total=Sum('coin_count'))['total'] or 0
        
        return total_coin_balance
    
    
    def get_balance_in_dollars(self):
        from apps.wallet.models import Wallet
        try:
            apps = self.publisher_in()
            if not apps:
                return None
            wallet = Wallet.get_or_create_user_wallet(self.id)
            return wallet.balance_amount()
        except Exception as exp:
            return None
        
    def get_full_name(self):
        return self.first_name or self.name or self.email or self.phone_number 

    def get_notification_count(self):
        from apps.notification.models import UserNotification
        return UserNotification.get_notification_count(self)
        
    def publisher_in(self):
        apps = []
        if self.provider_profile_hussainiya.filter(is_verified=True).exists():
            apps.append('hussainiya')
        
        if self.provider_profile.filter(is_verified=True).exists():
            apps.append('meet')
        
        if hasattr(self, 'library_publisher') and self.library_publisher is not None:
            apps.append('library')
        
        consultant_info = Consultants.get_consultant_info(self)
        if consultant_info:
            apps.append('talk')
            
        return apps

    def active_plan(self):
        if last_user_plan := self.subscriptions.order_by('-activated_at').first():
            if last_user_plan.is_active():
                return last_user_plan

        return None
    

class AdminUserManager(UserManager):
    def get_queryset(self):
        return super().get_queryset().filter(
            is_staff=True,
        )


class AdminUser(User):
    objects = AdminUserManager()

    class Meta:
        ordering = ("email",)
        proxy = True
        verbose_name = _("admin")
        verbose_name_plural = _('admins')


class UserSettings(models.Model):
    user = models.OneToOneField("account.User", on_delete=models.CASCADE, related_name='settings', verbose_name='User')
    settings = models.TextField(blank=True, null=True, verbose_name='Settings')
    service_versions = models.JSONField(blank=True, null=True, verbose_name='Versions')  
    api_version = models.CharField(max_length=16, blank=True, null=True, verbose_name='API Version')
    is_tester = models.BooleanField(default=False)
    is_reminder = models.BooleanField(default=True, verbose_name='Reminder Active')
    
    bucket_services = MultiSelectField(
        choices=Service.Service.choices, 
        default=[], 
        null=True, 
        blank=True,
        verbose_name=_('Bucket Services')
    )

    notification_settings = MultiSelectField(
        choices=[
            ('hussainiyah', _('Hussainiyah')),
            ('meet', _('Meetings')),
            ('talk', _('Talks')),
            ('library', _('Library')),
        ],
        default=['hussainiyah', 'meet', 'talk', 'library'], 
        null=True, 
        blank=True,
        verbose_name=_('Notification setting')
    )
    

    
    class Meta:
        verbose_name = 'User Setting'
        verbose_name_plural = 'User Settings'