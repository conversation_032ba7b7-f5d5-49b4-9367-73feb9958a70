from django.db import models
class GeoNamesCity(models.Model):
    name = models.CharField(max_length=200)
    country_code = models.CharField(max_length=2)
    latitude = models.FloatField()
    longitude = models.FloatField()
    feature_class = models.CharField(max_length=1)
    population = models.BigIntegerField(null=True)

    class Meta:
        indexes = [
            models.Index(fields=['latitude', 'longitude']),
            models.Index(fields=['country_code']),
            models.Index(fields=['feature_class']),
            # ایندکس بهینه برای کوری‌های جستجوی مکان
            models.Index(fields=['feature_class', 'latitude', 'longitude'], name='idx_geonames_feature_lat_lon'),
            # ایندکس‌های جداگانه برای محدوده جغرافیایی
            models.Index(fields=['latitude'], condition=models.Q(feature_class='P'), name='idx_geonames_lat_populated'),
            models.Index(fields=['longitude'], condition=models.Q(feature_class='P'), name='idx_geonames_lon_populated'),
        ]
        db_table = 'geonames_city'

    def __str__(self):
        return f"{self.name}, {self.country_code}"