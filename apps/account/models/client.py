from django.contrib.auth.models import _user_has_perm, AbstractUser  # type: ignore
from django.db import models
from django.utils.translation import gettext as _

from apps.account.models import User


class GuestUser(User):
    class Meta:
        proxy = True
        verbose_name = _("guest user")
        verbose_name_plural = _("guest users")

    def __str__(self):
        return self.mobile_device_id


class AllUser(User):
    class Meta:
        proxy = True
        verbose_name = _("user")
        verbose_name_plural = _("all users")

    def __str__(self):
        return self.mobile_device_id or '-'


class LocationHistory(models.Model):
    user = models.ForeignKey("account.User", on_delete=models.CASCADE, related_name='location_history')
    lat = models.FloatField(verbose_name=_('lat'))
    lon = models.FloatField(verbose_name=_('lon'))
    country = models.CharField(max_length=255, verbose_name=_('country'), null=True, blank=True)
    city = models.Char<PERSON>ield(max_length=255, verbose_name=_('city'), null=True, blank=True)
    selected_manually = models.BooleanField(null=True, blank=True)
    ip = models.CharField(max_length=255, null=True)
    timezone = models.CharField(null=True, blank=True, max_length=60)
    at_time = models.DateTimeField(auto_now_add=True)


class LoginHistory(models.Model):
    class LocalizationModels(models.TextChoices):
        default = 'DEFAULT', 'DEFAULT'
        manual = 'MANUAL', 'MANUAL'
        auto = 'AUTO', 'AUTO'
        ip_detection = 'IP_DETECTION', 'IP_DETECTION'

    mobile_device_id = models.CharField(verbose_name=_('mobile device id'), max_length=254, null=True, blank=True)
    user = models.ForeignKey(User, verbose_name=_('user'), on_delete=models.CASCADE, related_name='login_history')
    lat = models.FloatField(verbose_name=_('lat'), null=True)
    lon = models.FloatField(verbose_name=_('lon'), null=True)
    country = models.CharField(max_length=255, verbose_name=_('country'), null=True)
    city = models.CharField(max_length=255, verbose_name=_('city'), null=True)
    ip = models.CharField(max_length=255, verbose_name=_('IP'))
    api_v = models.CharField(max_length=64, verbose_name=_('API Version'), null=True)
    timezone = models.CharField(null=True, blank=True, max_length=60)
    last_login_at = models.DateTimeField(null=True, blank=True, verbose_name=_('last login at'), auto_now_add=True)
    location_method = models.CharField(max_length=19, choices=LocalizationModels.choices, null=True, blank=True)

    def __str__(self):
        return self.ip

    class Meta:
        verbose_name = _("login history")
        verbose_name_plural = _('user login histories')
        ordering = ('-id',)
