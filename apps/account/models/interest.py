from django.db import models
from django.utils.translation import gettext_lazy as _


class Interest(models.Model):
    title = models.CharField(max_length=255, verbose_name=_('title (en)'))
    priority = models.PositiveSmallIntegerField(default=0, blank=True, )
    translations = models.JSONField(verbose_name=_('translations'), default=dict)
    icon = models.CharField(
        max_length=64, verbose_name=_('icon name'), help_text=_('Fontawesome icon name'), null=True, blank=True
    )
    tags = models.ManyToManyField('tag.Tag')

    def __str__(self):
        return self.title or '-'

    class Meta:
        verbose_name = _('interest')
        verbose_name_plural = _('interests')
        ordering = ('-priority', '-id')


class UserInterest(models.Model):
    class Gender(models.TextChoices):
        male = 'male', _('Male')
        female = 'female', _('Female')

    user = models.OneToOneField("account.User", related_name="interest", on_delete=models.CASCADE)
    gender = models.CharField(max_length=255, verbose_name=_('gender'), choices=Gender.choices)
    age = models.PositiveSmallIntegerField(verbose_name=_('age'), )
    tend_to_marry = models.BooleanField(verbose_name=_('tend to marry'), default=None, null=True, blank=True)
    marital_status = models.BooleanField(verbose_name=_('marital status'), default=None, null=True, blank=True)

    has_children = models.BooleanField(verbose_name=_('has children'), default=None, null=True, blank=True)
    interests = models.ManyToManyField(Interest, verbose_name=_('interests'))

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.user.get_full_name() or '-'

    class Meta:
        verbose_name = _('user interests')
        verbose_name_plural = _('users interest')
        ordering = ('-id',)
