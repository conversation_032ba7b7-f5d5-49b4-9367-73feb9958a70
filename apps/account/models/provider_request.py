from dj_language.field import LanguageField
from django.contrib.auth.base_user import BaseUserManager
from django.db.models import Case, When, IntegerField, Value, Subquery, OuterRef, JSONField
from django.db.models.functions import Concat
from django.contrib.auth.models import _user_has_perm, AbstractUser  # type: ignore
from django.db import models
from django.utils.translation import gettext as _
from django_countries.fields import CountryField
from filer.fields.image import FilerImageField
from phonenumber_field.modelfields import PhoneNumberField

from apps.account.validators import validate_possible_number
from apps.q_and_a.models import Consultants

class ProviderRequest(models.Model):
    class Status(models.TextChoices):
        pending = 'pending', 'Pending'
        rejected = 'rejected', 'Rejected'
        accepted = 'accepted', 'Accepted'

    class Service(models.TextChoices):
        hussainiya = 'hussainiya', 'Hussainiya'
        net = 'net', 'Net'
        library = 'library', 'Library'
        talk = 'talk', 'Talk'

        shop = 'shop', 'Shop'
        travel = 'travel', 'Travel'
        meet = 'meet', 'Meet'

    user = models.ForeignKey('account.User', on_delete=models.CASCADE, verbose_name=_('user'))

    status = models.CharField(_('status'), choices=Status.choices, default=Status.pending, max_length=16)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    service = models.CharField(_('service'), choices=Service.choices, max_length=16)

    public_name = models.CharField(_('public name'), max_length=255)
    description = models.TextField(_('description'))
    is_custom = models.BooleanField(_('is custom'), default=True)

    ai_score = models.IntegerField(_('AI score'), null=True, blank=True)
    ai_suitable_for_teaching = models.BooleanField(_('AI suitable for teaching'), null=True, blank=True)
    ai_suggested_categories = JSONField(_('AI suggested categories'), null=True, blank=True)
    ai_comment = models.TextField(_('AI comment'), null=True, blank=True)

    class Meta:
        verbose_name = _('provider request')
        verbose_name_plural = _('provider requests')
        ordering = ('-id',)

    def __str__(self):
        return f'user:{self.user.username},service:{self.service},status:{self.status}'
    


    @staticmethod
    def get_provider_profile_info(request, user):
        from apps.hussainiya.provider.models import ProviderProfile
        from apps.meet.models.provider import MeetProviderProfile

        if request.service == ProviderRequest.Service.hussainiya:
            profile = ProviderProfile.get_provider_info(user)
        elif request.service == ProviderRequest.Service.meet:
            profile = MeetProviderProfile.get_provider_info(user)
        elif request.service == ProviderRequest.Service.talk:
            profile = Consultants.get_consultant_info(user)
        else:
            profile = None  # یا یک مقدار پیش‌فرض مناسب
        
        return profile
    
    @staticmethod
    def get_provider_info(user):
                    
        # status requests 
        subquery = ProviderRequest.objects.filter(
            user=user,
            service=OuterRef('service'),
            public_name=Concat(OuterRef('service'), Value('-provider'))
        ).order_by('-created_at')

        latest_requests = ProviderRequest.objects.filter(
            id=Subquery(subquery.values('id')[:1])
        ).annotate(
            accepted_order=Case(
                When(status=ProviderRequest.Status.accepted, then=Value(0)),
                default=Value(1),
                output_field=IntegerField(),
            )
        ).order_by('accepted_order', '-created_at')
        
        if not latest_requests:
            return None
        
        result = []
        for request in latest_requests:
            if request.status == ProviderRequest.Status.accepted:
                result.append({
                    'service': request.service,
                    'status': request.status,
                    "profile": ProviderRequest.get_provider_profile_info(request, user)
                })
            else: 
                result.append({
                    'service': request.service,
                    'status': request.status,
                    "profile": None
                })

        return result
            

