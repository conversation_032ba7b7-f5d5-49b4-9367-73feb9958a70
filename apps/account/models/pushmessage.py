from django.utils import timezone
from django.db.models import Q
from django.contrib.auth.models import _user_has_perm, AbstractUser  # type: ignore
from django.db import models
from django.utils.translation import gettext as _
from dj_language.field import LanguageField
from filer.fields.image import FilerI<PERSON>Field

from apps.account.models import User

class PushMessage(models.Model):
    title = models.CharField(max_length=255, verbose_name=_('title'))
    message = models.TextField(max_length=512, verbose_name=_('message'))
    data = models.JSONField(default=dict, null=True, blank=True, verbose_name=_('data'))
    users = models.ManyToManyField("account.User", verbose_name=_('users'))
    result = models.JSONField(default=dict, null=True, verbose_name=_('result'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'), null=True)
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('updated at'), null=True)

    def __str__(self):
        return self.title


