import datetime
import logging
import time
import json
import requests
import argparse
from django.conf import settings
from pyfcm import FCMNotification
from pyfcm.errors import FCMServerError
# import firebase_admin
# from firebase_admin import credentials, messaging
from google.oauth2 import service_account
import google.auth.transport.requests

# تنظیمات لاگ‌گذاری
logging.basicConfig(
    level=logging.INFO,  # سطح لاگ‌گذاری (INFO برای لاگ‌های عمومی، DEBUG برای جزئیات بیشتر)
    format='%(asctime)s - %(levelname)s - %(message)s'  # فرمت لاگ
)


data = ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
PROJECT_ID = 'habib-73dad'
BASE_URL = 'https://fcm.googleapis.com'
SCOPES = ['https://www.googleapis.com/auth/firebase.messaging']
FCM_ENDPOINT = f'v1/projects/{PROJECT_ID}/messages:send'
FCM_URL = f'{BASE_URL}/{FCM_ENDPOINT}'

def _get_access_token():
  """Retrieve a valid access token that can be used to authorize requests.

  :return: Access token.
  """
  credentials = service_account.Credentials.from_service_account_info(
    data,   scopes=SCOPES)
  request = google.auth.transport.requests.Request()
  credentials.refresh(request)
  return credentials.token

# cred = credentials.Certificate(data)
# firebase_admin.initialize_app(cred)


    
def send_notification(ids: list, title: str = None, body: str = None, data=None,
                            extra_notification_kwargs: dict = None, max_retries: int = 10) -> list:
    if not ids:
        return []

    # دریافت توکن دسترسی یک بار برای تمام درخواست‌ها
    access_token = _get_access_token()
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json',
    }
    
    # تبدیل داده به فرمت مناسب برای ارسال
    formatted_data = {k: str(v) for k, v in (data or {}).items()}
    
    chunked_ids = [ids[i:i + 500] for i in range(0, len(ids), 500)]
    responses = []

    for chunk in chunked_ids:
        payload = {
            'message': {
                'token': chunk[0],
                'notification': {
                    'title': title,
                    'body': body,
                },
                'data': formatted_data,
                'android': {
                    'priority': 'high',
                    'notification': {
                        'title': title,
                        'body': body,
                        'visibility': 'public',
                    },
                },
            }
        }
        try:
            response = requests.post(FCM_URL, headers=headers, json=payload)
            response.raise_for_status()  # Raise an error for bad responses (4xx and 5xx)
            logging.info('Successfully sent message: %s', response.json())
            responses.append(response.json())
        except requests.exceptions.HTTPError as e:
            error_response = response.json()
            logging.warning(f'Failed to send message: {error_response.get("error", {}).get("message")}')
            # You can also handle specific error cases
            if error_response.get("error", {}).get("status") == "NOT_FOUND":
                logging.warning(f'Notification not sent. Reason: {error_response["error"]["message"]}')
            responses.append({'status': 'error', 'message': error_response.get("error", {}).get("message")})
        except Exception as e:
            logging.error(f'An unexpected error occurred: {str(e)}')
            responses.append({'status': 'error', 'message': str(e)})

    return responses


def send_silent_notification(ids: list, data=None, max_retries: int = 10) -> list:
    """
    ارسال نوتیفیکیشن سایلنت (بدون عنوان و بدنه) که فقط شامل داده است
    
    Args:
        ids: لیست توکن‌های FCM
        data: دیکشنری داده‌ها برای ارسال
        max_retries: حداکثر تعداد تلاش‌های مجدد
        
    Returns:
        لیست پاسخ‌های دریافتی از FCM
    """
    if not ids:
        return []

    # دریافت توکن دسترسی یک بار برای تمام درخواست‌ها
    access_token = _get_access_token()
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json',
    }
    
    # تبدیل داده به فرمت مناسب برای ارسال
    formatted_data = {k: str(v) for k, v in (data or {}).items()}
    
    chunked_ids = [ids[i:i + 500] for i in range(0, len(ids), 500)]
    responses = []
    logging.info(f'Formatted data for FCM: {formatted_data}')

    for chunk in chunked_ids:
        payload = {
            'message': {
                'token': chunk[0],
                'data': formatted_data,
                'android': {
                    'priority': 'high'
                },
            }
        }
        
        try:
            response = requests.post(FCM_URL, headers=headers, json=payload)
            response.raise_for_status()
            logging.info('Successfully sent silent notification: %s', response.json())
            responses.append(response.json())
        except requests.exceptions.HTTPError as e:
            error_response = response.json()
            logging.warning(f'Failed to send silent notification: {error_response.get("error", {}).get("message")}')
            if error_response.get("error", {}).get("status") == "NOT_FOUND":
                logging.warning(f'Silent notification not sent. Reason: {error_response["error"]["message"]}')
            responses.append({'status': 'error', 'message': error_response.get("error", {}).get("message")})
        except Exception as e:
            logging.error(f'An unexpected error occurred in silent notification: {str(e)}')
            responses.append({'status': 'error', 'message': str(e)})

    return responses


def send_custom_payload_notification(fcm_token: str, payload_data: dict) -> list:
    """
    ارسال نوتیفیکیشن با payload سفارشی که تمام کلیدهای payload_data را مستقیماً در payload قرار می‌دهد

    Args:
        fcm_token: توکن FCM دستگاه
        payload_data: دیکشنری حاوی payload سفارشی

    Returns:
        لیست پاسخ‌های دریافتی از FCM
    """
    if not fcm_token:
        return []

    # دریافت توکن دسترسی
    access_token = _get_access_token()
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json',
    }

    # ساخت payload سفارشی
    custom_payload = {
        'message': {
            'token': fcm_token,
            **payload_data  # تمام کلیدهای payload_data مستقیماً در message قرار می‌گیرند
        }
    }

    try:
        response = requests.post(FCM_URL, headers=headers, json=custom_payload)
        response.raise_for_status()
        logging.info(f'Successfully sent custom payload: {response.json()}')
        return [response.json()]
    except requests.exceptions.HTTPError as e:
        error_response = response.json()
        logging.warning(f'Failed to send custom payload: {error_response.get("error", {}).get("message")}')
        return [{'status': 'error', 'message': error_response.get("error", {}).get("message")}]
    except Exception as e:
        logging.error(f'An unexpected error occurred in custom payload: {str(e)}')
        return [{'status': 'error', 'message': str(e)}]


if __name__ == '__main__':
    # لیست توکن‌های دستگاه‌هایی که باید پیام به آن‌ها ارسال شود
    registration_tokens = [
        # "eWTbDQhsQUW12gESCBmAwL:APA91bGeypkhwLexNXVvkJgrqt9MoiwAdxUEFa-vEWBdji6oDzrjjwFL_G2BQtU5wpnxxl30K7RG7uOUfMwoxHegxvVdkmSNGn0ILGszTwSiM52639mKmyjbI84Agok3JrsLAsg21G3H",        
        # "fuQBX1EwTDOV9LZsKUYVdG:APA91bG8VqLDsVlZ06OVWbf2HjjU6qzbk1uqUJCLWlQJuuag6zabMnrs0E8ftL2GMEQHnQgNaBGbOhlblcLYPXVLAnZnZlGSGFThDIe0sUUl4qLEYq_iyrQkaXXgJu070muVRv-QFTqv",
        "fuQBX1EwTDOV9LZsKUYVdG:APA91bG8VqLDsVlZ06OVWbf2HjjU6qzbk1uqUJCLWlQJuuag6zabMnrs0E8ftL2GMEQHnQgNaBGbOhlblcLYPXVLAnZnZlGSGFThDIe0sUUl4qLEYq_iyrQkaXXgJu070muVRv-QFTqv",
    ]
    send_notification(registration_tokens[0], 'Notification Title', 'Notification Body', {'slam':'sla'})
