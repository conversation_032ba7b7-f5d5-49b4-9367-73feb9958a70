import os
import time
from django.db.models import Q
from django.core.wsgi import get_wsgi_application
from anthropic import Anthropic
from utils.telegram_logger import telegram_logger

# تنظیمات محیط Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
import django

django.setup()
application = get_wsgi_application()

from apps.account.models.interest import Interest
from dj_language.models import Language  # وارد کردن مدل زبان‌ها

# استخراج لیست زبان‌های فعال از مدل Language با استفاده از فیلد status
active_languages = Language.objects.filter(status=True).values_list('code', 'name')
target_languages = {code: name for code, name in active_languages}

# تابع ترجمه متن با استفاده از API کلاد
def translate_text(text, target_language, target_language_name):
    # تعیین پیام سفارشی برای زبان ازبکی با کاراکتر سیریلیک
    if target_language == 'uz':
        prompt = (
            f"Translate the following interest title into {target_language_name} using the Cyrillic alphabet. "
            f"For an Islamic Shi'a application, this is a user interest section that helps identify what the user is interested in."
            f"The translation should be accurate, capturing the cultural and contextual significance. "
            f"Please provide ONLY the translation without any extra text.\n\n"
            f"Please provide ONLY the translation.\n\n"
            f"Original Text: {text}"
        )
    elif target_language == 'ul':
        prompt = (
            f"Translate the following interest title into Roman Urdu. "
            f"For an Islamic Shi'a application, this is a user interest section that helps identify what the user is interested in."
            f"The translation should be accurate, capturing the cultural and contextual significance. "
            f"Use simple and clear vocabulary appropriate for Roman Urdu readers. "
            f"Be fluent and natural, not just a word-for-word rendering. "
            f"Please provide ONLY the translation without any extra text.\n\n"
            f"Original Text: {text}"
        )
    else:
        prompt = (
            f"Translate the following interest title into {target_language_name}. "
            f"For an Islamic Shi'a application, this is a user interest section that helps identify what the user is interested in."
            f"The translation should be accurate, capturing the cultural and contextual significance. "
            f"Please provide ONLY the translation without any extra text.\n\n"
            f"Original Text: {text}"
        )

    time.sleep(2)  # جلوگیری از محدودیت درخواست‌ها

    client = Anthropic(api_key="************************************************************************************************************")
    response = client.messages.create(
        model="claude-3-7-sonnet-latest",
        max_tokens=1024,
        messages=[{"role": "user", "content": prompt}]
    )
    translated_text = response.content[0].text
    return translated_text

# استخراج تمامی 'Interest' ها با اولویت خاص یا شرط مورد نیاز (در اینجا، بدون شرط خاص به‌عنوان نمونه)
interest_list = Interest.objects.all()

# شروع فرآیند ترجمه
telegram_logger(f"Starting the translation process for interests with missing translations...")

# شمارنده‌های موفقیت‌آمیز
successful_translations = 0
total_interests = 0

for interest in interest_list:
    translations = interest.translations
    # یافتن زبان‌های مفقود در لیست ترجمه‌های موجود
    missing_languages = [lang for lang in target_languages.keys() if
                         not any(tr['language_code'] == lang for tr in translations)]

    if not missing_languages:
        continue

    # استخراج عنوان انگلیسی
    english_title = interest.title

    if not english_title:
        continue

    for lang_code in missing_languages:
        # استفاده از پیام خاص برای ترجمه ازبکی با حروف سیریلیک
        translated_text = translate_text(english_title, lang_code, target_languages[lang_code])

        # اضافه کردن ترجمه جدید به فیلد translations
        translations.append({
            'language_code': lang_code,
            'title': translated_text
        })
        interest.save()

        # به‌روزرسانی شمارش و ثبت لاگ
        successful_translations += 1
        log_message = (
            f"Interest ID {interest.id} translated to {target_languages[lang_code]}.\n\n"
            f"English Title: {english_title}\n\n"
            f"{target_languages[lang_code]} Translation: {translated_text}\n\n"
            f"Total successful translations so far: {successful_translations}"
        )
        telegram_logger(log_message)

        # وقفه برای جلوگیری از درخواست‌های بیش از حد
        time.sleep(2)

    total_interests += 1

# ثبت لاگ تکمیل فرآیند
telegram_logger(f"Translation process completed with {successful_translations} successful translations for {total_interests} interests.")
