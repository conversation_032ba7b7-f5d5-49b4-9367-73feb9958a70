
import json
import pandas as pd
from django.conf import settings
from django.core.wsgi import get_wsgi_application

import os


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

from apps.account.models import User
from apps.account.models import LoginHistory, LocationHistory  # تنظیم با نام واقعی اپلیکیشن‌ها
from apps.donate.models import DonateUser  # در صورت وجود مدل دونیت در این مسیر
from datetime import datetime


def convert_dates_to_str(data):
    for item in data:
        for key, value in item.items():
            if isinstance(value, datetime):
                item[key] = value.isoformat()
    return data


# تابع برای دریافت اطلاعات کاربران
def get_user_data(user_ids):
    data = []

    for user_id in user_ids:
        try:
            user = User.objects.select_related('interest', 'settings').get(id=user_id)
            login_history = list(LoginHistory.objects.filter(user=user).values())
            location_history = list(LocationHistory.objects.filter(user=user).values())

            login_history = convert_dates_to_str(login_history)
            location_history = convert_dates_to_str(location_history)


            # استخراج شماره تلفن از مدل دونیت
            donation = DonateUser.objects.filter(user=user).first()  # در صورت وجود مدل دونیت
            phone_number = donation.phone_number if donation else None

            # بررسی وجود علاقه‌مندی‌ها
            interests = []
            gender = None
            age = None
            tend_to_marry = None
            marital_status = None
            has_children = None

            if hasattr(user, 'interest') and user.interest:
                interests = [interest.title for interest in user.interest.interests.all()]
                gender = user.interest.gender
                age = user.interest.age
                tend_to_marry = user.interest.tend_to_marry
                marital_status = user.interest.marital_status
                has_children = user.interest.has_children

            user_data = {
                'User ID': user.id,
                'Username': user.username,
                'Full Name': user.get_full_name(),
                'Email': user.email,
                'Phone Number': phone_number or user.phone_number,
                'Device OS': user.device_os,
                'Bio': user.bio,
                'Country': user.country,
                'City': user.city,
                'Date Joined': user.date_joined,
                'Login History': login_history,
                'Location History': location_history,
                'Social Media': user.social_medias,
                'Language': user.language,
                'Interests': interests,
                'Gender': gender,
                'Age': age,
                'Tend to Marry': tend_to_marry,
                'Marital Status': marital_status,
                'Has Children': has_children,
                'Settings': user.get_user_settings,
            }

            data.append(user_data)
        except User.DoesNotExist:
            print(f"User with ID {user_id} does not exist.")

    return data


# تابع برای ذخیره داده‌ها در اکسل
def save_to_excel(data, filename='user_data.xlsx'):
    df = pd.DataFrame(data)
    df['Login History'] = df['Login History'].apply(lambda x: json.dumps(x, ensure_ascii=False))
    df['Location History'] = df['Location History'].apply(lambda x: json.dumps(x, ensure_ascii=False))

    try:
        # اگر فایل وجود دارد، داده‌های جدید را به آن اضافه کنید
        with pd.ExcelWriter(filename, mode='a', engine='openpyxl', if_sheet_exists='overlay') as writer:
            df.to_excel(writer, index=False, startrow=writer.sheets['Sheet1'].max_row, header=False)
    except FileNotFoundError:
        # اگر فایل وجود ندارد، یک فایل جدید ایجاد کنید
        df.to_excel(filename, index=False)


# استفاده
# user_ids = [124239,120912,68292,122373,112636,90863,90869,77145,89890,7510,7605,7695,7766,118977,58944,57222,38908,4549]  # جایگزینی با آیدی‌های واقعی کاربران
user_ids = [38908,36421,35825,10204,7766,7694,508]

user_data = get_user_data(user_ids)
save_to_excel(user_data)
