$(document).ready(function () {
    let e = $('.tag-level-field').select2({
        tags: true,
        placeholder: "tags",
        tokenSeparators: [',', ';', '.'],
        ajax: {
            url: function () {
                url = $(this).attr('ajax-url');
                if ($(this).attr('model')) {
                    return `${url}?model=${this.attr('model')}`
                }
                return url
            },
            dataType: 'json',
            delay: 250,
        }
    })
})