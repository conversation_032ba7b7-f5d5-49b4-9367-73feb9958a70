from itertools import chain

from django.contrib import admin
from django.contrib.contenttypes.models import ContentType

from apps.tag.models import TaggingLog


class TagLevelingAdminExtension(admin.ModelAdmin):

    def get_related_tag_model(self, form):
        return getattr(form.instance, self.model.tags.through.__name__.lower() + '_set')

    def save_related(self, request, form, formsets, change):
        form.save_m2m()
        for formset in formsets:
            self.save_formset(request, form, formset, change=change)

        t1, t2, t3 = form.cleaned_data['tags_1'], form.cleaned_data['tags_2'], form.cleaned_data['tags_3']

        self.log_admin_tagging(form, request, t1, t2, t3)

        related_model = self.get_related_tag_model(form)
        related_model.all().delete()

        for tag1 in t1:
            related_model.update_or_create(level=1, tag=tag1)

        for tag2 in t2:
            related_model.update_or_create(level=2, tag=tag2)

        for tag3 in t3:
            related_model.update_or_create(level=3, tag=tag3)

    def log_admin_tagging(self, form, request, t1, t2, t3):
        form_tags_list = list(chain(t1, t2, t3))
        old_tags_list = list(form.instance.tags.values_list('id', flat=1))
        new_tags = list(filter(lambda x: x.id not in old_tags_list, form_tags_list))
        if new_tags:
            log = TaggingLog.objects.create(
                admin=request.user,
                model_object_id=form.instance.id,
                model=ContentType.objects.get_for_model(self.model)
            )
            log.tags.add(*new_tags)
