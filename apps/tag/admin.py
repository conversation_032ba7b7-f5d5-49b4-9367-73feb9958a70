from ajaxdatatable.admin import AjaxDatatable
from dj_language.models import Language
from django import forms
from django.contrib import admin
from django.db.models import Count, Q, F
from django.http import JsonResponse
from django.urls import reverse, path
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from apps.tag.models import Tag
from utils import get_translation_schema
from utils.json_editor_field import JsonEditorWidget


class TagForm(forms.ModelForm):
    # tags = forms.ModelMultipleChoiceField(
    #     widget=forms.SelectMultiple(attrs={'class': 'no-select2'}),
    #     queryset=Tag.objects.all(),
    # )

    class Meta:
        model = Tag
        exclude = ()
        widgets = {
            'translations': JsonEditorWidget(attrs={'schema': get_translation_schema, 'rows': 8999})
        }


@admin.register(Tag)
class TagAdmin(AjaxDatatable):
    list_display = ('title', 'created_at', 'updated_at', 'created_by', '_hadises')
    readonly_fields = ('created_by',)
    latest_by = 'created_at'
    form = TagForm
    search_fields = ('title',)

    @admin.display(description=_('Translation'), ordering='translations')
    def _translation(self, obj):
        return mark_safe(" | ".join([i['language_code'] for i in obj.translations]))

    @admin.display(description=_('Related hadis'), ordering='hadis_count')
    def _hadises(self, obj):
        link = reverse('admin:hadis_hadis_changelist') + f'?tag={obj.id}'
        return mark_safe(
            "<a href='%s' target='_blank' >%s حدیث</a>" % (link, obj.hadis_count)
        )

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.annotate(
            hadis_count=Count('hadis')
        )

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        urls = [
            path(
                'tags-datasource/',
                self.admin_site.admin_view(self.get_tags_datasource),
                name='%s_%s_datasource' % info
            )
        ]

        return urls + super().get_urls()

    def get_tags_datasource(self, request):
        search = request.GET.get('q')
        qs = Tag.objects.filter(
            Q(title__icontains=search) | Q(translations__icontains=search)
        ).annotate(text=F('title')).values('id', 'text')
        return JsonResponse({'results': list(qs)})
