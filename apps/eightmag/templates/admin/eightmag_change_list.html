{% extends 'admin/change_list_ajax.html' %}
{% load admin_urls %}
{% load i18n %}

{% block layout-buttons %}
    {% include 'admin/list_navigate_tools.html' %}
    {% if has_add_permission %}
        <button type="button" class="float-right btn bg-indigo-400 legitRipple mr-3 ml-3 btn btn-light"
                data-toggle="modal" data-target="#modal_default">
            Add Instagram Video <i class="icon-play3 ml-2"></i>
        </button>
    {% endif %}
{% endblock %}

{% block content %}
    {{ block.super }}
    <div id="modal_default" class="modal fade" tabindex="-1">
        <div class="modal-dialog">
            <form method="post" action="{% url cl.opts|admin_urlname:'add_insta_post' %}">

                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Import Instagram Post</h5>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>

                    <div class="modal-body">
                        <p>
                            Paste Instagram Post URL here
                            <br>
                            eg. https://www.instagram.com/p/Cag9UVpr8H_/
                        </p>

                        <hr>

                        {% csrf_token %}
                        <label for="">Url</label>
                        <input name="post_url" class="form-control" type="text" placeholder="">
                        <label class="mt-2" for="">Language</label>
                        <select class="form-control" name="language_code" id="">
                            {% for language in languages %}
                                <option value="{{ language.code }}">{{ language.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Get Post Data</button>
                    </div>

                </div>
            </form>
        </div>
    </div>

{% endblock %}