# Generated by Django 3.2.5 on 2021-10-17 00:28

import autoslug.fields
import dj_category.field
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import filer.fields.image
import limitless_dashboard.fields.summernote


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dj_category', '0001_initial'),
        migrations.swappable_dependency(settings.FILER_IMAGE_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EightmagCategory',
            fields=[
            ],
            options={
                'verbose_name': 'Eightmag Category',
                'verbose_name_plural': 'Eightmag Categories',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('dj_category.basecategory',),
        ),
        migrations.CreateModel(
            name='Eightmag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='title')),
                ('content', limitless_dashboard.fields.summernote.SummernoteField(verbose_name='content')),
                ('summary', models.TextField(blank=True, help_text='we use it for seo and short description of post', null=True, verbose_name='summary')),
                ('slug', autoslug.fields.AutoSlugField(editable=False, populate_from='title', unique=True)),
                ('as_special', models.BooleanField(default=False, help_text='enable as special blog for showing in home page', verbose_name='as special')),
                ('status', models.BooleanField(default=True, help_text='model status to be shown or not', verbose_name='status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('author', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='author')),
                ('categories', dj_category.field.CategoryM2mModelField(limit_choices_to={'is_active': True}, related_name='eightmags', to='eightmag.EightmagCategory', verbose_name='categories')),
                ('thumbnail', filer.fields.image.FilerImageField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.FILER_IMAGE_MODEL, verbose_name='thumbnail')),
            ],
            options={
                'verbose_name': 'Magazine',
                'verbose_name_plural': 'Eightmag',
                'ordering': ['-id'],
            },
        ),
    ]
