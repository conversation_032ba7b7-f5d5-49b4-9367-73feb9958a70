# Generated by Django 3.2.12 on 2022-03-03 11:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('eightmag', '0006_eightmagmedia'),
    ]

    operations = [
        migrations.CreateModel(
            name='SourceInfo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('IG', 'Instagram')], default='IG', max_length=2, verbose_name='type')),
                ('url', models.URLField(verbose_name='url')),
                ('name', models.CharField(max_length=255, verbose_name='name')),
                ('description', models.CharField(blank=True, max_length=500, null=True, verbose_name='description')),
                ('thumbnail_url', models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='thumbnail url')),
            ],
        ),
        migrations.Alter<PERSON>ield(
            model_name='eightmagmedia',
            name='media',
            field=models.FileField(upload_to='eightmag-medias/', verbose_name='media file'),
        ),
        migrations.AddField(
            model_name='eightmag',
            name='source',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='posts', to='eightmag.sourceinfo'),
        ),
    ]
