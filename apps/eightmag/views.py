from datetime import datetime

from django.contrib.contenttypes.models import ContentType
from django.db.models import F, Q
from django.http import JsonResponse
from rest_framework.generics import ListAPIView, RetrieveAPIView, get_object_or_404
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from apps.eightmag.models import Eightmag, EightmagCategory
from apps.eightmag.serializer import EightmagList<PERSON>erializer, EightmagDetailsSerializer, EightmagCategoriesSerializer


class EightmagListView(ListAPIView):
    serializer_class = EightmagListSerializer
    lookup_field = 'slug'

    # permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        if self.request.LANGUAGE_CODE == 'de':
            lang_filter = Q(categories__language__code='en') | Q(categories__language__code='de')
        else:
            lang_filter = Q(categories__language__code=self.request.LANGUAGE_CODE)

        return Eightmag.objects.filter(
            lang_filter,
            status=True,
            publication_date__isnull=False,
            publication_date__lte=datetime.today(),
        ).prefetch_related(
            'categories', 'thumbnail', 'event',
        ).distinct().order_by('-pin_top', '-id')


class EightmagDetailView(RetrieveAPIView):
    serializer_class = EightmagDetailsSerializer
    lookup_field = 'slug'

    # permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        return Eightmag.objects.filter(status=True, slug=self.kwargs.get('slug')).prefetch_related(
            'categories', 'thumbnail', 'event',
        )


class EightmagCategoriesListView(ListAPIView):
    serializer_class = EightmagCategoriesSerializer
    pagination_class = None

    # permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        content_type = ContentType.objects.get(model=EightmagCategory._meta.model_name)
        if self.request.LANGUAGE_CODE == 'de':
            lang_filter = Q(language__code='en') | Q(language__code='de')
        else:
            lang_filter = Q(language__code=self.request.LANGUAGE_CODE)

        return EightmagCategory.objects.filter(
            content_type=content_type,
            level=0,
            is_active=True,
            *lang_filter,
        )


class EightmagCategoryItemsListView(ListAPIView):
    serializer_class = EightmagListSerializer
    lookup_field = 'slug'

    # permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        obj = get_object_or_404(EightmagCategory, id_active=True, slug=self.kwargs.get('slug'))
        return Eightmag.objects.filter(
            categories__in=[obj]
        ).prefetch_related(
            'categories', 'thumbnail', 'event',
        )


class LikeEightmag(APIView):
    permission_classes = (IsAuthenticated,)

    def post(self, *args, **kwargs):
        return JsonResponse({
            'status': 'ok',
            'like_count': self.like(),
        })

    def like(self, slug='merry-christmas'):
        obj = get_object_or_404(Eightmag, slug=self.kwargs['slug'])
        obj.like_count += 1
        obj.save()
        return obj.like_count
