from dj_filer.admin import get_thumbs
from django.urls import reverse
from rest_framework import serializers

from apps.eightmag.models import Eightmag, EightmagCategory
from apps.najm_calendar.serializer import CalendarSerializer


class EightmagCategoriesInListSerializer(serializers.ModelSerializer):
    url = serializers.SerializerMethodField('get_relative_url')

    def get_relative_url(self, obj):
        return reverse('eightmag-category-items', kwargs={'slug': obj.slug})

    class Meta:
        model = EightmagCategory
        fields = ('name', 'url')


class EightmagListSerializer(serializers.ModelSerializer):
    url = serializers.SerializerMethodField('get_relative_url')
    categories = EightmagCategoriesInListSerializer(many=True, read_only=True)
    thumbnail = serializers.SerializerMethodField('get_thumbnail_object')
    event = CalendarSerializer(read_only=True)
    created_at = serializers.SerializerMethodField('get_created_at')

    def get_thumbnail_object(self, obj):
        return get_thumbs(obj.thumbnail, self.context.get('request'))

    @staticmethod
    def get_relative_url(obj: Eightmag):
        return reverse('eightmag-detail', kwargs={'slug': obj.slug})

    def get_created_at(self, obj):
        return obj.publication_date

    class Meta:
        model = Eightmag
        exclude = ('id', 'content', 'slug', 'publication_date')


class EightmagDetailsSerializer(serializers.ModelSerializer):
    url = serializers.SerializerMethodField('get_relative_url')
    thumbnail = serializers.SerializerMethodField('get_thumbnail_object')
    categories = EightmagCategoriesInListSerializer(many=True, read_only=True)
    author = serializers.StringRelatedField()
    event = CalendarSerializer(read_only=True)

    def get_thumbnail_object(self, obj):
        return get_thumbs(obj.thumbnail, self.context.get('request'))

    def get_relative_url(self, obj: Eightmag):
        return reverse('eightmag-detail', kwargs={'slug': obj.slug})

    class Meta:
        model = Eightmag
        exclude = ('id', 'slug', 'publication_date',)


class EightmagCategoriesSerializer(serializers.ModelSerializer):
    thumbnails = serializers.SerializerMethodField('get_thumbnail_object')
    children = serializers.SerializerMethodField('get_children')
    url = serializers.SerializerMethodField('get_relative_url')

    @staticmethod
    def get_relative_url(obj):
        return reverse('eightmag-category-items', kwargs={'slug': obj.slug})

    def get_thumbnail_object(self, obj: EightmagCategory):
        return get_thumbs(obj.thumbnail, self.context.get('request'))

    def get_children(self, obj: EightmagCategory):
        return [self.to_dict(cat) for cat in obj.get_children()]

    def to_dict(self, c):
        children = c.get_children()
        return {
            'id': c.id,
            'name': c.name,
            'thumbnails': self.get_thumbnail_object(c),
            'children': [] if not children else [self.to_dict(i) for i in children],
        }

    class Meta:
        model = EightmagCategory
        fields = ('url', 'name', 'thumbnails', 'children',)
