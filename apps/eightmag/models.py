import secrets
import urllib

from autoslug import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dj_category.field import CategoryM2m<PERSON><PERSON>l<PERSON>ield
from dj_category.models import BaseCategory
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils.translation import gettext_lazy as _
from filer.fields.image import Filer<PERSON><PERSON><PERSON><PERSON>
from limitless_dashboard.fields.summernote import Summer<PERSON><PERSON><PERSON>
from limitless_dashboard.fields.tinyeditor import TinyE<PERSON>or<PERSON>ield

from limitless_dashboard.models import PublishableModel
from nwh_seo.fields import SeoGenericRelation

from apps.najm_calendar.models import CalendarOccasions


# class EightmagCategory(BaseCategoryAbstract):
#     thumbnail = FilerImageField(related_name='+', on_delete=models.PROTECT, null=True, blank=True)
#     from_date = models.DateField(null=True, blank=True, verbose_name=_('from date'))
#     to_date = models.DateField(null=True, blank=True, verbose_name=_('to date'))
#     language = LanguageField()
#
#     class Meta:
#         verbose_name = _('Eightmag Category')
#         verbose_name_plural = _('Eightmag Categories')

class EightmagCategory(BaseCategory):
    class Meta:
        proxy = True
        verbose_name = _('Eightmag Category')
        verbose_name_plural = _('Eightmag Categories')

    def save(self, *args, **kwargs):
        self.content_type = ContentType.objects.get(model=EightmagCategory._meta.model_name)
        super(EightmagCategory, self).save(*args, **kwargs)


class Eightmag(PublishableModel):
    title = models.CharField(_('title'), max_length=255)
    event = models.ForeignKey(CalendarOccasions, on_delete=models.SET_NULL, null=True, blank=True,
                              verbose_name=_('event'))
    content = TinyEditorField(_('content'))
    summary = models.TextField(
        _('summary'), blank=True, null=True, help_text=_('we use it for seo and short description of post')
    )
    slug = AutoSlugField(populate_from='title', unique=True)
    categories = CategoryM2mModelField(EightmagCategory, verbose_name=_('categories'), related_name='eightmags')
    thumbnail = FilerImageField(blank=True, null=True, on_delete=models.CASCADE, verbose_name=_('thumbnail'))
    as_special = models.BooleanField(
        default=False, help_text=_('enable as special blog for showing in home page'), verbose_name=_('as special')
    )
    pin_top = models.BooleanField(default=False, verbose_name=_('pin top'), help_text=_('چسباندن پست به بالا'))
    status = models.BooleanField(verbose_name=_('status'), default=True, help_text=_('model status to be shown or not'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("created at"))

    author = models.CharField(null=True, verbose_name=_("author"), max_length=119)
    source = models.ForeignKey('SourceInfo', on_delete=models.CASCADE, related_name='posts', null=True, blank=True)
    publisher = models.ForeignKey(
        "account.User", on_delete=models.SET_NULL, related_name='mags', verbose_name=_('publisher'),
        blank=True, null=True,
    )

    like_count = models.PositiveIntegerField(default=1, verbose_name=_('like count'))

    seo_fields = SeoGenericRelation(verbose_name=_('seo fields'))

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['-id']
        verbose_name = _('Magazine')
        verbose_name_plural = _('Eightmag')


class EightmagMedia(models.Model):
    class MediaTypes(models.TextChoices):
        video = 'V', _('Video')
        image = 'I', _('Image')

    eightmag = models.ForeignKey(Eightmag, related_name='medias', on_delete=models.CASCADE)
    media = models.FileField(verbose_name=_('media file'), upload_to='eightmag-medias/')
    media_type = models.CharField(max_length=2, verbose_name=_('media type'), choices=MediaTypes.choices)

    def save(self, force_insert=False, force_update=False, using=None,
             update_fields=None):
        if self.media.name.startswith('https://'):
            file = urllib.request.urlretrieve(self.media.name)
            name = self.media.name
            file_extension = name[:name.find('?')]
            file_extension = file_extension[file_extension.rfind('.'):]
            self.media.save(
                name=secrets.token_urlsafe(6) + file_extension,
                content=open(file[0], 'rb'),
            )

        super(EightmagMedia, self).save(force_insert, force_update, using, update_fields)

    class Meta:
        verbose_name = _('media')
        verbose_name_plural = _('medias')
        ordering = ('-id',)


class SourceInfo(models.Model):
    """
        instagram page info
    """

    class Types(models.TextChoices):
        insta = 'IG', _('Instagram')

    type = models.CharField(choices=Types.choices, default=Types.insta, max_length=2, verbose_name=_('type'))
    url = models.URLField(verbose_name=_('url'))
    name = models.CharField(verbose_name=_('name'), max_length=255)
    description = models.CharField(verbose_name=_('description'), max_length=500, null=True, blank=True)
    thumbnail_url = models.CharField(verbose_name=_('thumbnail url'), max_length=500, null=True, blank=True)

    def __str__(self):
        return self.url
