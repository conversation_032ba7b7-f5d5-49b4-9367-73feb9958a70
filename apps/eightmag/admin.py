from datetime import datetime

from ajaxdatatable.admin import AjaxDatatable
from dj_category.admin import BaseCategoryAdmin
from dj_filer.admin import get_thumbs
from dj_language.models import Language
from django import forms
from django.contrib import admin, messages
from django.db.models import <PERSON>, ExpressionWrap<PERSON>, <PERSON><PERSON>an<PERSON>ield
from django.shortcuts import redirect
from django.urls import path, reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from nwh_seo.admin import SeoAdminInline

from apps.eightmag.models import Eightmag, EightmagCategory, EightmagMedia, SourceInfo


class EightmagForm(forms.ModelForm):
    summary = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}))

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['thumbnail'].required = True

    class Meta:
        model = Eightmag
        exclude = ()


class EightmagMediaAdmin(admin.StackedInline):
    model = EightmagMedia
    extra = 1


@admin.register(Eightmag)
class EightmagAdmin(AjaxDatatable):
    list_display = (
        'title', 'status', 'author', 'as_special', 'category', '_language', 'event',
        'created_at', 'publication_date', '_thumbnail', '_is_published', 'pin_top'
    )
    list_filter = ('status', 'as_special', 'categories', 'source', 'pin_top')
    inlines = [EightmagMediaAdmin, SeoAdminInline, ]
    fieldsets = (
        ('General Information', {
            'fields': ('publisher', 'title', 'event', 'author', 'categories', 'pin_top')
        }),
        ('Content', {'fields': ('summary', 'content')}),
        ('Publication',
         {'fields': ('thumbnail', 'publication_date', 'status', 'as_special'), 'classes': ('aside',)})
    )
    form = EightmagForm
    search_fields = ('title', 'categories__name', 'author', 'event__title')
    autocomplete_fields = ('event', 'publisher')
    change_list_template = 'admin/eightmag_change_list.html'
    ordering = ('-pin_top', '-id')

    @admin.display(description=_('thumbnail'), ordering='thumbnail')
    def _thumbnail(self, obj):
        try:
            url = get_thumbs(obj.thumbnail)['sm']
            return mark_safe(
                f"<img class='rounded' width=60 src='{url}'>"
            )
        except Exception:
            return "-"

    def changelist_view(self, request, extra_context=None):
        extra_context = {
            'languages': Language.objects.filter(status=True)
        }
        return super().changelist_view(request, extra_context)

    @admin.display(ordering='categories', description=_('Categories'))
    def category(self, obj: Eightmag):
        cats = [i.name for i in obj.categories.all()]
        return format_html("<br>".join(cats))

    @admin.display(description=_('Language'), ordering='category__language')
    def _language(self, obj):
        return "|".join(list(obj.categories.values_list('language__code', flat=True)))

    @admin.display(ordering='is_published', description=_('Is Published'))
    def _is_published(self, obj: Eightmag):
        r = mark_safe('%s')
        return r % '<span class="badge badge-success">Yes</span>' if obj.is_published else r'<span class="badge badge-danger">No</span>'

    def get_queryset(self, request):
        qs = super(EightmagAdmin, self).get_queryset(request).prefetch_related('event')
        return qs.annotate(
            is_published=ExpressionWrapper(
                Q(publication_date__isnull=True) | Q(publication_date__lte=datetime.today()),
                output_field=BooleanField()
            )
        ).all()

    def get_urls(self):
        info = self.model._meta.app_label, self.model._meta.model_name
        urls = [
            path(
                'insta-post/',
                self.admin_site.admin_view(self.youtube_playlist_add_view),
                name='%s_%s_add_insta_post' % info
            )
        ]
        return urls + super().get_urls()

    def youtube_playlist_add_view(self, request):
        if not request.POST:
            return redirect(reverse('admin:eightmag_eightmag_changelist'))

        video_url = request.POST.get('post_url').strip()
        language_code = request.POST.get('language_code')
        lang = Language.objects.get(code=language_code)
        shortcode = video_url[video_url.rfind('/p/') + 3:-1]
        post = self.get_instagram_post(shortcode)
        category, _ = EightmagCategory.objects.update_or_create(
            defaults={'name': 'uncategorized'},
            name='uncategorized',
            language=lang,
        )

        obj = Eightmag.objects.create(
            title=post['title'],
            content=post['caption'],
            summary=post['caption'][:130],
            source=SourceInfo.objects.create(**post['source']),
        )

        obj.categories.set([category])

        for media in post['medias']:
            EightmagMedia(
                media_type=EightmagMedia.MediaTypes.video if media['is_video'] else EightmagMedia.MediaTypes.image,
                media=media['url'],
                eightmag=obj,
            ).save()

        messages.success(request, "Instagram post has successfully downloaded")

        return redirect(reverse('admin:eightmag_eightmag_changelist'))

    def get_instagram_post(self, shortcode):
        import os
        import instaloader

        loader = instaloader.Instaloader()
        username = 'hossein__.py'
        password = 'insta13..'
        session = 'instagram.session'

        if not os.path.exists(session):
            loader.login(username, password)
            loader.save_session_to_file(session)

        else:
            print('using session')
            loader.load_session_from_file(username, session)

        post = instaloader.Post.from_shortcode(loader.context, shortcode)
        medias = []
        if post.mediacount == 1:
            pdict = post._asdict()
            medias.append({
                'is_video': pdict['is_video'],
                'url': pdict.get('video_url') or pdict['display_url']
            })
        else:
            for media in post._field('edge_sidecar_to_children', 'edges'):
                medias.append({
                    'is_video': media['node']['is_video'],
                    'url': media['node'].get('video_url') or media['node']['display_url']
                })

        return {
            'image': post.url,
            'is_video': post.is_video,
            'caption': post.caption,
            'medias': medias,
            'title': post.title or post.caption[:40],
            'source': {
                'url': f'https://www.instagram.com/{post.owner_username}/',
                'name': post.owner_profile.full_name,
                'description': post.owner_profile.biography,
                'thumbnail_url': post.owner_profile.profile_pic_url,
            }
        }


@admin.register(EightmagCategory)
class EightmagCategoryAdmin(BaseCategoryAdmin):
    pass
