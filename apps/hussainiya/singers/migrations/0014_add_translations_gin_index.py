# Generated manually for PostgreSQL optimization

import django.contrib.postgres.indexes
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('singers', '0013_alter_singer_slug'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='singer',
            index=django.contrib.postgres.indexes.GinIndex(
                fields=['translations'], 
                name='singers_translations_gin_idx'
            ),
        ),
    ]
