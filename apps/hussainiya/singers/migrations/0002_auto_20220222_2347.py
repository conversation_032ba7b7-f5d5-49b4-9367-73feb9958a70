# Generated by Django 3.2.11 on 2022-02-22 23:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import filer.fields.image


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.FILER_IMAGE_MODEL),
        ('singers', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='singer',
            options={'ordering': ('-id',), 'verbose_name': '<PERSON><PERSON>', 'verbose_name_plural': '<PERSON><PERSON>'},
        ),
        migrations.AddField(
            model_name='singer',
            name='source',
            field=models.CharField(blank=True, editable=False, max_length=64, null=True),
        ),
        migrations.AlterField(
            model_name='singer',
            name='avatar',
            field=filer.fields.image.FilerImageField(on_delete=django.db.models.deletion.PROTECT, related_name='+', to=settings.FILER_IMAGE_MODEL),
        ),
    ]
