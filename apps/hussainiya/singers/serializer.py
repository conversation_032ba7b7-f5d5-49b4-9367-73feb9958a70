from dj_filer.admin import get_thumbs
from rest_framework import serializers

from .models import Singer


class SingersListSerializer(serializers.ModelSerializer):
    avatar = serializers.SerializerMethodField()
    languages = serializers.StringRelatedField(many=True, read_only=True)
    songs_count = serializers.IntegerField(read_only=True)
    lang = serializers.CharField(read_only=True)
    name = serializers.SerializerMethodField()

    def get_name(self, obj):
        request = self.context.get('request')

        if name := obj.get_translation(request.LANGUAGE_CODE):
            return name

        try:
            return obj.translations[0]['title']
        except:
            return '-'

    class Meta:
        model = Singer
        fields = ('id', 'name', 'singer_type', 'songs_count', 'slug', 'languages', 'avatar', 'lang')

    def get_avatar(self, obj: Singer):
        return get_thumbs(obj.avatar, self.context.get('request'))


class SingerInListSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    def get_name(self, obj):
        request = self.context.get('request')

        if name := obj.get_translation(request.LANGUAGE_CODE):
            return name

        try:
            return obj.translations[0]['title']
        except:
            return '-'

    class Meta:
        model = Singer
        fields = ('name', 'singer_type')
