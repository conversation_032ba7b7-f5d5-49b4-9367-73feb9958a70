from rest_framework import serializers
from .models import HussainiyaCategory
from dj_filer.admin import get_thumbs


class CategoryListSerializer(serializers.ModelSerializer):
    thumbnail = serializers.SerializerMethodField('get_thumbnail_object')
    title = serializers.CharField(source='name')

    def get_thumbnail_object(self, obj: HussainiyaCategory):
        try:
            return get_thumbs(obj.thumbnail, self.context.get('request'))
        except Exception as e:
            print(e)

    class Meta:
        model = HussainiyaCategory
        fields = ('title', 'slug', 'category_type', 'thumbnail')
