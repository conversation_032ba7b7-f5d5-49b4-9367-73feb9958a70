from dj_category.admin import BaseCategoryAdmin
from django.contrib import admin

from apps.hussainiya.categories.models import HussainiyaCategory


# @admin.register(HussainiyaCategory)
class HussainiyaCategoryAdmin(BaseCategoryAdmin):
    # empty aside_fields to override global settings for thumbnail field
    def get_root_nodes(self, request=None):
        filter_data = {'level': 0, **self._filter_query}
        if request:
            if lang_id := request.GET.get('lang_id'):
                filter_data['language_id'] = lang_id

        return self.get_queryset(request).filter(**filter_data)
