from rest_framework.generics import ListAP<PERSON><PERSON>iew
from rest_framework.response import Response

from .models import HussainiyaCategory
from .serializer import CategoryListSerializer


class CategoryList(ListAPIView):
    """
        category list api
        -- params: language_code : default fa
    """
    serializer_class = CategoryListSerializer
    lookup_field = 'slug'
    pagination_class = None

    def get_queryset(self):
        return HussainiyaCategory.objects.filter(is_active=True, language__code=self.request.LANGUAGE_CODE)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'results': serializer.data
        })


class CategoryOccasionList(ListAPIView):
    """
        category occasion list api
        -- params: language_code : default fa
    """
    serializer_class = CategoryListSerializer
    lookup_field = 'slug'
    pagination_class = None

    def get_queryset(self):
        return HussainiyaCategory.objects.filter(
            is_active=True, category_type=HussainiyaCategory.CategoryType.occasion, language__code=self.request.LANGUAGE_CODE
        )

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'results': serializer.data
        })
