from dj_filer.admin import get_thumbs
from rest_framework import serializers

from apps.hussainiya.genre.models import Genre


class GenreListSerializer(serializers.ModelSerializer):
    thumbnail = serializers.SerializerMethodField('get_thumbnail_object')

    def get_thumbnail_object(self, obj):
        try:
            return get_thumbs(obj.thumbnail, self.context.get('request'))
        except Exception as e:
            print(e)

    class Meta:
        model = Genre
        fields = ('slug', 'title', 'thumbnail')
