from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from apps.hussainiya.genre.models import Genre


@admin.register(Genre)
class GenreAdmin(AjaxDatatable):
    list_display = ('title', '_thumbnail')
    search_fields = ('title', )

    @admin.display(description=_('Thumbnail'))
    def _thumbnail(self, obj):
        try:
            return mark_safe(
                f"<img src='{obj.thumbnail.url}' with=65 height=65 class='rounded-circle'>"
            )
        except Exception:
            return '-'
