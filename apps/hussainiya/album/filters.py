import django_filters
from apps.hussainiya.songs.models import <PERSON>, Singer


class SongFilter(django_filters.FilterSet):
    singer_type = django_filters.ChoiceFilter(
        field_name='singers__singer_type',
        choices=Singer.SingerType.choices,
        method='filter_by_singer_type'
    )
    
    
    class Meta:
        model = Song
        fields = ['singer_type']

    def filter_by_singer_type(self, queryset, name, value):
        return queryset.filter(singers__singer_type=value).distinct()
    