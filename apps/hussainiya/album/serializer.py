from dj_filer.admin import get_thumbs
from django.db.models import <PERSON>, Q, Subquery, OuterRef, F, <PERSON>, When, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Value
from django.db.models.functions import Coalesce

from drf_yasg import openapi
from hijri_converter import <PERSON><PERSON>
from rest_framework import serializers

from apps.hussainiya.album.models import Album
from apps.hussainiya.singers.models import <PERSON>
from apps.hussainiya.songs.models import Song, Like


def calculate_count_type(qs):
    first_singer_type_subquery = Singer.objects.filter(
        song__id=OuterRef('pk')
    ).order_by('id').values('singer_type')[:1]
    songs = qs.annotate(
        first_singer_type=Coalesce(
            Subquery(first_singer_type_subquery),
            Value(Singer.SingerType.singer),
            output_field=CharField()
        )
    ).annotate(
        adjusted_song_type=Case(
            When(first_singer_type=Singer.SingerType.lecture, then=Value(Singer.SingerType.lecture)),
            When(first_singer_type__in=[Singer.SingerType.madah, <PERSON>.SingerType.singer], then=Value(Singer.SingerType.madah)),
            output_field=CharField(),
        )
    )        
    counts = songs.aggregate(
        lecture_count=Count(Case(When(adjusted_song_type=Singer.SingerType.lecture, then=Value(1)), output_field=IntegerField())),
        madah_count=Count(Case(When(
            (Q(adjusted_song_type=Singer.SingerType.madah) | Q(adjusted_song_type=Singer.SingerType.singer)),
            then=Value(1)), output_field=IntegerField())
        ),
    )      
    lecture_count = counts.get('lecture_count', 0)  
    madah_count = counts.get('madah_count', 0)
    return lecture_count, madah_count

class AlbumListSerializer(serializers.ModelSerializer):
    title = serializers.SerializerMethodField()
    thumbnail = serializers.SerializerMethodField()
    songs_count = serializers.IntegerField(allow_null=True, read_only=True)
    singer_type_counts = serializers.SerializerMethodField()


    def get_singer_type_counts(self, obj):
        lecture_count, madah_count = calculate_count_type(obj.song_set.all())
        return {
            'madah': madah_count,
            'lecture': lecture_count,
        }        

    def get_thumbnail(self, obj):
        return get_thumbs(obj.thumbnail, self.context.get('request'))

    def get_title(self, obj):
        request = self.context.get('request')
        language_codes = request.LANGUAGE_CODE

        if isinstance(language_codes, str):
            language_codes = [code.strip() for code in language_codes.split(',')]
        elif not isinstance(language_codes, list):
            language_codes = [language_codes]
            
        for i in obj.translations:
            if i['language_code'] in language_codes:
                return i['title']
            
        return obj.translations[0]['title']                    
    class Meta:
        model = Album
        fields = ('id', 'title', 'thumbnail', 'dates', 'view_count', 'songs_count', 'singer_type_counts')
