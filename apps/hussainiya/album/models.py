from datetime import datetime
from django.db import models
from django.utils.translation import gettext_lazy as _
from filer.fields.image import Filer<PERSON><PERSON><PERSON><PERSON>
from nwh_seo.fields import SeoGenericRelation
from hijri_converter import convert

from apps.account.models import User


class Album(models.Model):
    translations = models.JSONField(verbose_name=_('title'), default=dict)
    dates = models.JSONField(verbose_name=_('dates'), default=dict)
    thumbnail = FilerImageField(related_name='+', on_delete=models.PROTECT, verbose_name=_('thumbnail'), null=True,
                                blank=True)
    status = models.BooleanField(default=True, verbose_name=_('status'))
    view_count = models.IntegerField(default=0, verbose_name=_('view count'))
    seo_fields = SeoGenericRelation(verbose_name=_('seo fields'))

    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('updated at'))
    created_by = models.ForeignKey(
        User, verbose_name=_('created by'), on_delete=models.PROTECT, related_name='+', editable=False, null=True)
    updated_by = models.ForeignKey(
        User, verbose_name=_('updated by'), on_delete=models.PROTECT, related_name='+', editable=False, null=True)

    def __str__(self):
        langs = ['fa', 'en', 'ar']
        for l in langs:
            for tr in self.translations:
                if tr['language_code'] == l:
                    return tr['title']

        return self.translations[0]['title']

    def get_translation(self, lang):
        for tr in self.translations:
            if tr['language_code'] == lang:
                return tr['title']

        return None

    def increment_view(self):
        Album.objects.update(view_count=self.view_count + 1)
        return True

    
    
    @classmethod
    def get_albums_by_hijri_date(cls, queryset):
        # Get the current Gregorian date
        today_gregorian = datetime.today()
        # Convert the current Gregorian date to Hijri date
        hijri_date = convert.Gregorian(today_gregorian.year, today_gregorian.month, today_gregorian.day).to_hijri()
        
        current_hijri_month = hijri_date.month
        current_hijri_day = hijri_date.day
        
        # Define a helper function to calculate the distance to the current Hijri date
        def hijri_date_distance(album):
            min_distance = float('inf')
            for date in album.dates:
                # Convert month and day to integers, handling leading zeros
                album_month = int(date.get('month', '0').lstrip('0') or '0')
                album_day = int(date.get('day', '0').lstrip('0') or '0')
                month_diff = abs(album_month - current_hijri_month)
                day_diff = abs(album_day - current_hijri_day)
                distance = month_diff * 30 + day_diff
                if distance < min_distance:
                    min_distance = distance
            return min_distance        
        # Sort the queryset based on the distance to the current Hijri date
        sorted_albums = sorted(queryset, key=hijri_date_distance)
        
        return sorted_albums