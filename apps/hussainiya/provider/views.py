from django.shortcuts import render
from rest_framework.generics import CreateAPIView, ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import ValidationError
from django.db.models import Case, When, Value, Q, Count, Exists, OuterRef, <PERSON>query, <PERSON>teger<PERSON>ield, F, <PERSON><PERSON><PERSON>ield, ExpressionWrapper
from dj_language.models import Language
from filer.models.imagemodels import Image
from rest_framework.exceptions import NotFound
from rest_framework import mixins, generics, status
from rest_framework.response import Response


from apps.hussainiya.singers.models import Singer
from apps.hussainiya.youtube_content import get_videos_info
from apps.hussainiya.tasks import save_youtube_video_task, download_audio
from apps.hussainiya.songs.serializer import SongsListSerializer
from apps.hussainiya.publisher.models import Publisher
from apps.hussainiya.songs.models import Song, Like
from django.core.files.base import ContentFile
from apps.account.models import ProviderRequest
from apps.hussainiya.provider.serializers import Create<PERSON>ong<PERSON>erializer, UpdateSongSerializer
from .serializers import ProviderProfileSerializer
from .models import ProviderProfile
from utils.download_file import download_file_from_remote
from utils import guess_file_type
from utils.filer_import import import_file
from utils import extract_id_of_youtube_url



class ProviderSongListView(generics.ListAPIView):
    serializer_class = SongsListSerializer
    permission_classes = (IsAuthenticated,)
    
    def get_queryset(self):
        provider = ProviderProfile.objects.filter(user=self.request.user, is_verified=True).first()
        if not provider:
            raise NotFound("Verified provider profile not found for the current user.")
        singer = Singer.objects.filter(
            slug=provider.slug
        ).first()
        # Define the subquery to count likes
        like_count_subquery = Like.objects.filter(
            song=OuterRef('pk')
        ).values('song').annotate(
            like_counts=Count('id')
        ).values('like_counts')
        qs = Song.objects.filter(singers=singer).annotate( 
            is_liked=Exists(
                Like.objects.filter(user=self.request.user, song=OuterRef('pk'))
            ),
            like_counts=Subquery(like_count_subquery, output_field=IntegerField()),
        ).prefetch_related(
            'thumbnail', 'singers', 'file', 'language', 'subtitle_languages', 'playlist_set',
            'singers__languages', 'genre', 'singers__avatar', 'publisher__languages',
        ).distinct().order_by('created_at')

        return qs
    
    



class ProviderSongUpdateDeleteView(generics.UpdateAPIView, generics.DestroyAPIView):
    serializer_class = UpdateSongSerializer
    permission_classes = (IsAuthenticated,)
    lookup_field = 'slug'
    
    def get_queryset(self):
        return Song.objects.all()

    def perform_update(self, serializer):
        instance = self.get_object()
        provider = ProviderProfile.objects.filter(user=self.request.user, is_verified=True).first()        
        singer = Singer.objects.filter(
            slug=provider.slug
        ).first()
        if not provider and not singer:
            raise NotFound("You are not a verified provider.")
        
        if not Song.objects.filter(slug=instance.slug, singers__slug=singer.slug).exists():
            raise NotFound("Song with this slug does not belong to your profile.")
        
                
        serializer.save()
        
    def destroy(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
        except Http404:
            return Response({"detail": "Not found."}, status=status.HTTP_404_NOT_FOUND)
        
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)
    

class ProviderCreateSongView(generics.CreateAPIView):
    serializer_class = CreateSongSerializer
    permission_classes = (IsAuthenticated,)

    def perform_create(self, serializer):
        link = self.request.data.get('youtube_link', None)
        if link and "youtu.be" in link:
            video_id = link.split('/')[-1]
            link = f"https://www.youtube.com/watch?v={video_id}"
    

        provider = ProviderProfile.objects.filter(user=self.request.user, is_verified=True).first()
        singer = Singer.objects.filter(
            slug=provider.slug
        ).first()
        song = serializer.save(
            youtube_link=link,
            status=False,
            created_by=self.request.user
        )        
        song.singers.add(singer)
        download_audio.delay(song.id)





class ProviderProfileCreateView(CreateAPIView):
    queryset = ProviderProfile.objects.all()
    serializer_class = ProviderProfileSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        user = self.request.user

        # if self.get_queryset().filter(user=user, status=ProviderRequest.Status.pending):
            # raise ValidationError("You already have a pending request to become a provider in Hussainiya. Please wait until your current request is processed.")

        if existing_profile := self.get_queryset().filter(user=user, is_verified=True).first():
            if existing_profile.request.status == ProviderRequest.Status.accepted:
                raise ValidationError("A ProviderProfile with an accepted ProviderRequest already exists for this user.")


        
        provider_request = ProviderRequest.objects.create(
            user=user,
            service=ProviderRequest.Service.hussainiya,
            public_name="hussainiya-provider",
            description=f"Hussainiya Provider Profile Request:\n"
                        f"Full Name: {serializer.validated_data.get('full_name', 'N/A')}\n"
                        f"Role: {serializer.validated_data.get('role')}\n"
                        f"Bio: {serializer.validated_data.get('bio', 'N/A')}\n"
                        f"Email: {serializer.validated_data.get('email', 'N/A')}\n"
                        f"Institution Name: {serializer.validated_data.get('institution_name', 'N/A')}\n"
                        f"Languages: {', '.join([language.name for language in serializer.validated_data.get('languages', [])])}\n"

        )               
        serializer.save(user=self.request.user, request=provider_request)

