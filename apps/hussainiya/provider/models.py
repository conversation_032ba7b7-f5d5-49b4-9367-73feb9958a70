import re
from django.db import models
from django.utils.translation import gettext as _
from filer.fields.image import Filer<PERSON><PERSON><PERSON><PERSON>
from django.utils import timezone
from django.core.exceptions import ValidationError
from dj_language.models import Language

from apps.account.models import User, ProviderRequest


def validate_wa_number(value):
    # Regular expression for validating WhatsApp number format without requiring "+"
    wa_regex = re.compile(r'^\d{9,15}$')
    if not wa_regex.match(value):
        raise ValidationError(_('Invalid WhatsApp number. It must be a number with 9 to 15 digits.'))

def default_social_media():
    return {"facebook": "", "x": "", "instagram": "", "youtube": ""}


class ProviderProfile(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="Provider Profile", related_name="provider_profile_hussainiya",help_text="Provider Profile of the hussainiya")
    request = models.ForeignKey(ProviderRequest, on_delete=models.CASCADE, verbose_name="Provider Request Hussainiya", related_name="provider_request_hussainiya",help_text="Provider Request of the hussainiya")
    full_name = models.CharField(verbose_name=_('full name'), max_length=254, null=True)
    role = models.CharField(max_length=55,help_text="Role Provider Profile of the hussainiya(lecturer, madah)")
    avatar = models.ImageField(upload_to='hussainiya/providers/%Y/%m/',  null=True, blank=True)
    bio = models.CharField(max_length=512, verbose_name=_('bio'), null=True, blank=True)
    birthdate = models.DateField(null=True, blank=True, verbose_name=_('birthdate'))
    email = models.EmailField(verbose_name=_('email'), null=True, blank=True)
    wa_number = models.CharField(verbose_name=_('whatsapp number'), max_length=255, null=True, blank=True, validators=[validate_wa_number])
    institution_name = models.CharField(max_length=255, verbose_name="Name", help_text="Name of the institute", null=True, blank=True)
    languages= models.ManyToManyField(Language, verbose_name=_('languages'), limit_choices_to={'status': True})
    social_medias = models.JSONField(
        default=default_social_media,
        verbose_name=_('social medias'),
        help_text="Provider social media profiles"
    )
    is_verified = models.BooleanField(default=False, verbose_name=_('is verified'), help_text="Is this profile verified?")
    slug = models.SlugField(max_length=255, allow_unicode=True, blank=True, null=True)  


    def __str__(self):
        return self.full_name if self.full_name else str(self.user)

    class Meta:
        verbose_name = _('Provider Profile')
        verbose_name_plural = _('Provider Profiles')
        db_table = 'hussainiya_provider_profile'



    @staticmethod
    def get_provider_info(user):
        provider = ProviderProfile.objects.filter(user=user, is_verified=True).first()
        if not provider:
            return None
        return {
            "full_name": provider.full_name,
            "avatar": provider.avatar.url if provider.avatar else None,
            "birthdate": provider.birthdate,
            "email": provider.email,   
            "bio": provider.bio,
            "wa_number": provider.wa_number,
            "institution_name": provider.institution_name,
            "languages": provider.languages.all().values_list('name','code'),
            "social_medias": provider.social_medias,
            "is_verified": provider.is_verified,
            "role": provider.role,
        }

