# Generated by Django 3.2.25 on 2024-08-08 14:01

import apps.hussainiya.provider.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
        ('account', '0041_usersettings'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProviderProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=254, null=True, verbose_name='full name')),
                ('role', models.CharField(help_text='Role Provider Profile of the hussainiya(lecturer, madah)', max_length=55)),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='meet/providers/%Y/%m/')),
                ('bio', models.Char<PERSON>ield(blank=True, max_length=512, null=True, verbose_name='bio')),
                ('birthdate', models.DateField(blank=True, null=True, verbose_name='birthdate')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='email')),
                ('wa_number', models.CharField(blank=True, max_length=255, null=True, validators=[apps.hussainiya.provider.models.validate_wa_number], verbose_name='whatsapp number')),
                ('institution_name', models.CharField(blank=True, help_text='Name of the institute', max_length=255, null=True, verbose_name='Name')),
                ('social_medias', models.JSONField(default=apps.hussainiya.provider.models.default_social_media, help_text='Provider social media profiles', verbose_name='social medias')),
                ('is_verified', models.BooleanField(default=False, help_text='Is this profile verified?', verbose_name='is verified')),
                ('languages', models.ManyToManyField(limit_choices_to={'status': True}, to='dj_language.Language', verbose_name='languages')),
                ('request', models.ForeignKey(help_text='Provider Request of the hussainiya', on_delete=django.db.models.deletion.CASCADE, related_name='provider_request_hussainiya', to='account.providerrequest', verbose_name='Provider Request Hussainiya')),
                ('user', models.ForeignKey(help_text='Provider Profile of the hussainiya', on_delete=django.db.models.deletion.CASCADE, related_name='provider_profile_hussainiya', to=settings.AUTH_USER_MODEL, verbose_name='Provider Profile')),
            ],
            options={
                'verbose_name': 'Hussainiya Provider Profile',
                'verbose_name_plural': 'Hussainiya Provider Profiles',
                'db_table': 'hussainiya_provider_profile',
            },
        ),
    ]
