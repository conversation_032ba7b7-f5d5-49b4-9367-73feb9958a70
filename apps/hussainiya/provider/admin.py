from django.contrib import admin

from apps.hussainiya.provider.models import ProviderProfile


# 
# @admin.register(ProviderProfile)
# class ProviderProfileAdmin(admin.ModelAdmin):
    # list_display = ('full_name', 'role','email', 'wa_number', 'is_verified')
    # search_fields = ('full_name', 'user__username', 'email', 'wa_number', 'role')
    # autocomplete_fields = ('user',)
    # list_filter = ('languages', 'birthdate', 'is_verified')
    # filter_horizontal = ('languages',)
    # fieldsets = (
        # (None, {
            # 'fields': ('user', 'request', 'full_name', 'role','avatar', 'bio', 'birthdate', 'email', 'wa_number', 'institution_name', 'languages', 'social_medias', 'is_verified')
        # }),
    # )
    # 
# 