import os
import sys
from time import sleep

sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

from apps.hussainiya.songs.models import Song
from utils.ytdl import YoutubeDownloader
from utils.ytdl.oneapi import OneApi
from datetime import datetime


def dl():
    songs = Song.objects.filter(youtube_link__isnull=False, audio_file__isnull=True).order_by('-id')

    for song in songs:
        try:
            obj = OneApi(song.youtube_link)
            yt = YoutubeDownloader(song.youtube_link)
            yt.drivers = [OneApi]
            if audio_path := yt.download_audio():
                file = yt.save_to_filer(audio_path)
                song.audio_file = file
                song.save()
                print(song.id, " saved at ", str(datetime.now()))
                sleep(30)
            else:
                print('failed to download', song.id)
        except Exception as e:
            print('error:', e)


def set_null_songs_under_100k_files():
    Song.objects.filter(audio_file___file_size__lte=100000).order_by('-id').update(audio_file=None)


def count_null():
    x = Song.objects.filter(audio_file=None).count()
    print(x)


dl()
