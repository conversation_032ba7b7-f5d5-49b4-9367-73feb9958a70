
from django.core.management.base import BaseCommand
from apps.hussainiya.songs.models import Playlist, Like, Song
from django.conf import settings
import logging
from utils.ytdl.oneapi import OneApi
from utils.save_to_filer import save_to_filer
import time

from ....tasks import download_audio

logger = logging.getLogger(__name__)


import requests




class Command(BaseCommand):
    help = 'Download missing audio files for songs with "unnamed file" label'

    def handle(self, *args, **options):
        # Query songs where audio_file label is 'unnamed file' and youtube_link is not empty
        songs_to_process = Song.objects.filter(
#            audio_file__label='unnamed file',
            youtube_link__isnull=False,
        ).order_by('-id')[:500]


        self.stdout.write(f'Found {songs_to_process.count()} songs to process.')
        for song in songs_to_process:
#            if song.audio_file.label == 'unnamed file':
            if song.audio_file is None or song.audio_file.label == 'unnamed file':

                try:
                    self.stdout.write(self.style.NOTICE(f'Starting download for song ID {song.id}:'))
                    download_audio(song.id)
                    time.sleep(3)
                    self.stdout.write(self.style.SUCCESS(f'Successfully downloaded audio for song ID {song.id}:'))

                except Exception as e:
                    logger.error(f'Error processing song ID {song.id}: {e}')
                    self.stdout.write(self.style.ERROR(f'Error processing song ID {song.id}: {e}'))

