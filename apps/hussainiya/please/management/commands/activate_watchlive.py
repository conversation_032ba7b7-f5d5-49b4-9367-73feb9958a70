from django.core.management.base import BaseCommand
from apps.hussainiya.please.models import WatchLive



class Command(BaseCommand):
    help = 'Activate all WatchLive records'

    def handle(self, *args, **kwargs):
        if WatchLive.enable_all_active():
            self.stdout.write(self.style.SUCCESS('All WatchLive records have been activated.'))
        else:
            self.stdout.write(self.style.ERROR('Failed to activate WatchLive records.'))
