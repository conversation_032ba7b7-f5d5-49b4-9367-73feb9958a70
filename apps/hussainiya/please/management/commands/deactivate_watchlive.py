from django.core.management.base import BaseCommand
from apps.hussainiya.please.models import WatchLive




class Command(BaseCommand):
    help = 'Deactivate all WatchLive records'

    def handle(self, *args, **kwargs):
        if WatchLive.disable_all_active():
            self.stdout.write(self.style.SUCCESS('All WatchLive records have been deactivated.'))
        else:
            self.stdout.write(self.style.ERROR('Failed to deactivate WatchLive records.'))
