from decimal import Decimal
from django.db import models
from django.utils.translation import gettext_lazy as _
from filer.fields.image import Filer<PERSON><PERSON><PERSON><PERSON>
from django_filters import rest_framework as filters
from hijri_converter import convert
from datetime import datetime
from django.db.models import F, IntegerField, Case, When, Value
from django.db.models.functions import Cast
from nwh_seo.fields import SeoGenericRelation
from apps.hussainiya.songs.models import Song
from django.contrib.postgres.fields.jsonb import KeyTextTransform



class WatchLiveQuerySet(models.QuerySet):
    def order_by_closest_date(self, reference_date=None):
        if reference_date is None:
            reference_date = datetime.today()
        
        reference_day = reference_date.day
        reference_month = reference_date.month
        
        # اضافه کردن annotation برای محاسبه اختلاف روزها با تاریخ مرجع
        return self.annotate(
            event_day=Cast(KeyTextTransform('day', F('dates__0')), IntegerField()),
            event_month=Cast(KeyTextTransform('month', F('dates__0')), IntegerField()),
        ).annotate(
            diff_days=Case(
                When(
                    event_month=reference_month,
                    then=F('event_day') - reference_day
                ),
                When(
                    event_month__gt=reference_month,
                    then=(F('event_month') - reference_month) * 30 + (F('event_day') - reference_day)
                ),
                When(
                    event_month__lt=reference_month,
                    then=(F('event_month') - reference_month + 12) * 30 + (F('event_day') - reference_day)
                ),
                output_field=IntegerField()
            )
        ).filter(
            diff_days__lte=0  # فقط مواردی که تاریخشان برابر یا کمتر از تاریخ مرجع است را انتخاب می‌کند
        ).order_by('diff_days')
        
class WatchLive(models.Model):
    class LiveType(models.TextChoices):
        PLEASE = 'please', 'Please'
        SHRINE = 'shrine', 'Shrine'
        
    live_type = models.CharField(choices=LiveType.choices, verbose_name=_('Live Type'), default=LiveType.PLEASE, max_length=10)
    translations = models.JSONField(verbose_name=_('title'), default=dict)
    dates = models.JSONField(verbose_name=_('dates'), default=dict) 
    thumbnail = FilerImageField(
        verbose_name=_('thumbnail'), related_name='+', on_delete=models.CASCADE, null=True, blank=True
    )
    songs = models.ManyToManyField(Song, through='WatchLiveSong', blank=True, related_name='lives')

    live_stream_link = models.CharField(verbose_name=_('live link'), max_length=255, null=True, blank=True)
    live_title = models.JSONField(verbose_name=_('live title'), default=dict)
    view_count = models.IntegerField(default=0, verbose_name=_('view count'))
    seo_fields = SeoGenericRelation(verbose_name=_('seo fields'))
    status = models.BooleanField(default=True, verbose_name=_('status'))
    pin_to_top = models.BooleanField(_('pin to top'), default=False)
    is_active = models.BooleanField(_('Active'), default=False,  help_text=_('Use this field to activate or deactivate this entry. If active, it will be shown to users.'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('updated at'))
    objects = WatchLiveQuerySet.as_manager()
    
    def __str__(self):
        langs = ['fa', 'en', 'ar']
        for l in langs:
            for tr in self.translations:
                if tr['language_code'] == l:
                    return tr['title']

        return self.translations[0]['title']

    def get_translation(self, lang):
        for tr in self.translations:
            if tr['language_code'] == lang:
                return tr['title']

        return None
    
    def get_live_title(self, lang):
        for tr in self.live_title:
            if tr['language_code'] == lang:
                return tr['title']

    def increment_view(self):
        WatchLive.objects.update(view_count=self.view_count + 1)
        return True
    
    def get_songs(self):
        return self.songs.all()

    @property    
    def song_count(self):
        return self.songs.count()    
    
    @classmethod
    def is_all_active(cls):
        # اگر هیچ رکوردی وجود ندارد، False برگردانیم
        if cls.objects.exists():
            # بررسی کنیم که آیا همه رکوردها is_active=True هستند یا نه
            return cls.objects.filter(is_active=True).count() == cls.objects.count()
        return False

    @classmethod
    def disable_all_active(cls):
        try:
            cls.objects.update(is_active=False)
            return True
        except Exception as e:
            return False
    @classmethod
    def enable_all_active(cls):
        try:
            cls.objects.update(is_active=True)
            return True
        except Exception as e:
            return False
        
    class Meta:
        verbose_name = _('Online Majlis')
        verbose_name_plural = _('Online Majlis')


    @classmethod
    def get_lives_by_hijri_date(cls, queryset):
        # Get the current Gregorian date
        today_gregorian = datetime.today()
        # Convert the current Gregorian date to Hijri date
        hijri_date = convert.Gregorian(today_gregorian.year, today_gregorian.month, today_gregorian.day).to_hijri()
        
        current_hijri_month = hijri_date.month
        current_hijri_day = hijri_date.day
        
        # Define a helper function to calculate the distance to the current Hijri date
        def hijri_date_distance(live):
            min_distance = float('inf')
            for date in live.dates:
                # Convert month and day to integers, handling leading zeros
                album_month = int(date.get('month', '0').lstrip('0') or '0')
                album_day = int(date.get('day', '0').lstrip('0') or '0')
                month_diff = abs(album_month - current_hijri_month)
                day_diff = abs(album_day - current_hijri_day)
                distance = month_diff * 30 + day_diff
                if distance < min_distance:
                    min_distance = distance
            return min_distance
        
        # Sort the queryset based on the distance to the current Hijri date
        sorted_lives = sorted(queryset, key=hijri_date_distance)
        
        return sorted_lives
    
    
    
class WatchLiveSong(models.Model):
    watchlive = models.ForeignKey(WatchLive, on_delete=models.CASCADE, related_name='watchlive_songs')
    song = models.ForeignKey(Song, on_delete=models.CASCADE, related_name='song_watchlive')
    priority = models.DecimalField(max_digits=5, decimal_places=1, default=Decimal('1.0'), null=True, blank=True)


class ShrineLives(WatchLive):
    class Meta:
        ordering = ("-pin_to_top",)
        proxy = True
        verbose_name = _("Shrine Live")
        verbose_name_plural = _('Shrines Live')

