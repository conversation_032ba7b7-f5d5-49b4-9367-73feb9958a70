# Generated by Django 3.2.25 on 2024-08-12 23:44

from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('songs', '0049_remove_playlist_temp_songs'),
        ('please', '0004_remove_watchlive_songs'),
    ]

    operations = [
        migrations.CreateModel(
            name='WatchLiveSong',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('priority', models.DecimalField(blank=True, decimal_places=1, default=Decimal('1.0'), max_digits=5, null=True)),
                ('song', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='song_watchlive', to='songs.song')),
                ('watchlive', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='watchlive_songs', to='please.watchlive')),
            ],
        ),
        migrations.AddField(
            model_name='watchlive',
            name='songs',
            field=models.ManyToManyField(blank=True, related_name='lives', through='please.WatchLiveSong', to='songs.Song'),
        ),
    ]
