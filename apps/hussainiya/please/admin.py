import json

from ajaxdatatable.admin import AjaxDatatable
from dj_filer.admin import get_thumbs
from dj_language.models import Language
from django.contrib import admin
from django.db.models import Count
from django import forms
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from django.forms import BaseInlineFormSet, ValidationError
from django import forms
from django.db.models import Case, When, Value, IntegerField

from apps.hussainiya.please.models import WatchLive, WatchLiveSong, ShrineLives
from utils.json_editor_field import JsonEditorWidget
from django.utils.safestring import mark_safe




class WatchLiveSongInline(admin.TabularInline):
    model = WatchLiveSong
    extra = 1
    autocomplete_fields = ('song',)


        
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        formfield = super().formfield_for_foreignkey(db_field, request, **kwargs)
        if db_field.name == 'song':
            formfield.widget.attrs.update({'style': 'height: 200px; width: 450px;'})  # تنظیم اندازه input

        else:
            formfield.widget.attrs.update({'style': 'width: 70px;'})  # تنظیم اندازه input
            
        return formfield




def get_translation_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Translation')),
            'properties': {
                'title': {'type': 'string', 'title': str(_('Title'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }

DATES_FIELD_SCHEMA = {
    'type': "array",
    'format': 'table',
    'title': ' ',
    'items': {
        'type': 'object',
        'title': 'date',
        'properties': {
            'month': {'type': 'string', 'format': 'number'},
            'day': {'type': 'string', 'format': 'number'},
        }
    },
}

class WatchLiveForm(forms.ModelForm):
    class Meta:
        model = WatchLive
        exclude = ()
        widgets = {
            'translations': JsonEditorWidget(attrs={'schema': get_translation_schema}),
            'dates': JsonEditorWidget(attrs={'schema': json.dumps(DATES_FIELD_SCHEMA)}),
            'live_title' : JsonEditorWidget(attrs={'schema': get_translation_schema}),
        }
        

@admin.register(WatchLive)
class WatchLiveAdmin(AjaxDatatable):
    list_display = ('_title', 'view_count', 'created_at', 'songs_count', '_translation', '_date','_thumbnail')
    form = WatchLiveForm
    search_fields = ('translations',)
    inlines = [WatchLiveSongInline]

    
    @admin.display(description=_('Dates'))
    def _date(self, obj):
        if not obj.dates:
            return "-"

        formatted_list = []
        for entry in obj.dates:
            month = entry.get('month', 'N/A')
            day = entry.get('day', 'N/A')
            formatted_list.append(f"{month}/{day}")
        return "\n".join(formatted_list)




    @admin.display(description=_('thumbnail'), ordering='thumbnail')
    def _thumbnail(self, obj):
        if obj.thumbnail:
            if sm := get_thumbs(obj.thumbnail).get('sm'):
                return mark_safe(
                    f"<img class='rounded' width=60 src='{sm}'>"
                )
        else:
            return "-"

    @admin.display(description=_('Title'), ordering='translations')
    def _title(self, obj):
        return str(obj)

    @admin.display(description=_('Songs Count'), ordering='song_count')
    def songs_count(self, obj):
        return obj.song_count

    @admin.display(description=_('Translation'), ordering='translations')
    def _translation(self, obj):
        return mark_safe(" | ".join([i['language_code'] for i in obj.translations]))

    def get_queryset(self, request):
        return super().get_queryset(request).filter(live_type=WatchLive.LiveType.PLEASE)


@admin.register(ShrineLives)
class ShrineLiveAdmin(WatchLiveAdmin):
    list_display = ('_title', '_translation', '_thumbnail')
    ordering = None
    
    def get_queryset(self, request):
        return ShrineLives.objects.filter(live_type=WatchLive.LiveType.SHRINE)
    def get_ordering(self, request):
        # ترتیب پیش‌فرض نمایش رکوردها با اولویت‌دهی به pin_to_top
        return [
            Case(
                When(pin_to_top=True, then=Value(0)),  # رکوردهایی که pin_to_top=True دارند در اولویت
                default=Value(1),
                output_field=IntegerField(),
            ),
            '-created_at'  # پس از اولویت‌دهی، بر اساس تاریخ ایجاد مرتب می‌شود
        ]