# Generated by Django 3.2.11 on 2022-02-17 14:27

import dj_language.field
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import filer.fields.image


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.FILER_IMAGE_MODEL),
        ('dj_language', '0001_initial'),
        ('sliders', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='slider',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='created at'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='slider',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='updated at'),
        ),
        migrations.AlterField(
            model_name='slider',
            name='language',
            field=dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language'),
        ),
        migrations.AlterField(
            model_name='slider',
            name='link',
            field=models.CharField(blank=True, help_text='could be empty', max_length=255, null=True, verbose_name='link'),
        ),
        migrations.AlterField(
            model_name='slider',
            name='photo',
            field=filer.fields.image.FilerImageField(on_delete=django.db.models.deletion.PROTECT, related_name='+', to=settings.FILER_IMAGE_MODEL, verbose_name='photo'),
        ),
        migrations.AlterField(
            model_name='slider',
            name='secondary_title',
            field=models.CharField(blank=True, help_text='could be empty', max_length=255, null=True, verbose_name='secondary title'),
        ),
        migrations.AlterField(
            model_name='slider',
            name='status',
            field=models.BooleanField(default=True, verbose_name='status'),
        ),
        migrations.AlterField(
            model_name='slider',
            name='title',
            field=models.CharField(max_length=255, verbose_name='title'),
        ),
    ]
