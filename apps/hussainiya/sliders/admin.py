from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from .models import Slider


@admin.register(Slider)
class SliderAdmin(AjaxDatatable):
    list_display = ('title', 'language', 'secondary_title', 'status', '_photo')
    list_filter = ('status', 'language')
    search_fields = ('title', 'secondary_title', 'link')
    latest_by = 'created_at'

    @admin.display(description=_('Photo'), ordering='photo')
    def _photo(self, obj):
        return mark_safe(f'<img width=65 height=65 src="{obj.photo.url}">')
