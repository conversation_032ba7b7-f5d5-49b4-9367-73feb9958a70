from dj_language.field import LanguageField
from django.db import models
from django.utils.translation import gettext_lazy as _
from filer.fields.image import FilerImageField


class Slider(models.Model):
    title = models.CharField(max_length=255, verbose_name=_('title'))
    secondary_title = models.CharField(verbose_name=_('secondary title'), max_length=255, null=True, blank=True,
                                       help_text=_("could be empty"))
    link = models.CharField(max_length=255, null=True, blank=True, help_text=_("could be empty"),
                            verbose_name=_('link'))
    photo = FilerImageField(related_name="+", on_delete=models.PROTECT, verbose_name=_('photo'))
    language = LanguageField()
    status = models.BooleanField(default=True, verbose_name=_('status'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('updated at'))

    def __str__(self):
        return self.title
