{% extends 'admin/change_form.html' %}
{% block scripts %}
    {{ block.super }}

    <script>
        $(document).ready(function () {
            {% comment %}$(document).on('click', '.add-row a', function (event) {
                $('.field-object_id select').each(function () {
                    if (!$(this).hasClass('select2-hidden-accessible')) {

                    }
                })
            }){% endcomment %}

            $('.field-object_id select').select2({
                ajax: {
                    url: function () {
                        let content_type_id = $(this).parents('.field-object_id').prev('.field-content_type').find('input:checked').val()
                        return '{% url 'admin:collection_collection_datasource' %}?content_type_id=' + content_type_id
                    },
                    dataType: 'json',
                    delay: 250,
                }
            })

        })
    </script>
{% endblock %}