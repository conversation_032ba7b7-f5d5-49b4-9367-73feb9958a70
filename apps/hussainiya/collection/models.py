from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models import Count, Q, When, Value, Case, Exists, OuterRef, Subquery
from django.utils.translation import gettext_lazy as _
from filer.fields.image import FilerImageField

from apps.hussainiya.album.models import Album
from apps.hussainiya.genre.models import Genre
from apps.hussainiya.publisher.models import Publisher
from apps.hussainiya.singers.models import Singer
from apps.hussainiya.songs.models import Song, Like
from utils.model_extention import ModelExtension


class Collection(ModelExtension):
    class Types(models.TextChoices):
        all = 'all', 'All'
        audio = 'audio', 'Audio'
        video = 'video', 'Video'
        youtube = 'youtube', 'Youtube'

    title = models.JSONField(default=dict, verbose_name=_('title'))
    collection_type = models.CharField(
        _('collection type'), max_length=24, choices=Types.choices, null=True,
        blank=True, default=Types.all,
    )
    thumbnail = FilerImageField(
        verbose_name=_('thumbnail'), null=True, blank=True, on_delete=models.SET_NULL,
        related_name='+',
    )
    status = models.BooleanField(_('status'), default=True, )
    pin_to_top = models.BooleanField(_('pin to top'), default=False)

    # filters
    songs = models.ManyToManyField(Song, blank=True, related_name='+')
    albums = models.ManyToManyField(Album, blank=True, related_name='+')
    singers = models.ManyToManyField(Singer, blank=True, related_name='+')
    publishers = models.ManyToManyField(Publisher, blank=True, related_name='+')
    genres = models.ManyToManyField(Genre, blank=True, related_name='+')
    songs_count = models.PositiveSmallIntegerField(null=True, blank=True, editable=False)

    def __str__(self):
        for lang in ['fa', 'en', 'ar']:
            for tr in self.title:
                if tr['language_code'] == lang:
                    return tr['title']

        return self.title[0]['title']

    def get_songs(self, user=None):
        filterset = {
            'status': True,
        }
        if self.collection_type != Collection.Types.all:
            filterset['song_type'] = self.collection_type

        if singers := self.singers.all():
            filterset['singers__in'] = singers

        if publishers := self.publishers.all():
            filterset['publisher__in'] = publishers

        if genres := self.genres.all():
            filterset['genre__in'] = genres

        if albums := self.albums.all():
            filterset['albums__in'] = albums

        elif songs := self.songs.all():
            filterset['id__in'] = list(songs.values_list('id', flat=True))

        qs = Song.objects.filter(**filterset).prefetch_related(
            'thumbnail', 'singers', 'file', 'language', 'subtitle_languages', 'playlist_set',
            'singers__languages', 'genre', 'singers__avatar', 'publisher__languages',
        ).distinct()
        if user:
            qs = qs.annotate(
                is_liked=Exists(Like.objects.filter(user=user, song=OuterRef('pk'))) if user.is_authenticated else Value(False),
                # is_liked=Exists(
                #     Like.objects.filter(user=user, song=OuterRef('pk'))
                # ),
            )

        if self.collection_type != Collection.Types.all:
            qs = qs.filter(song_type=self.collection_type)

        return qs

    class Meta:
        verbose_name = _('collection')
        verbose_name_plural = _('collections')
        ordering = ('-id',)
