from ajaxdatatable.admin import AjaxDatatable
from dj_filer.admin import get_thumbs
from django.contrib import admin
from django.db.models import Count
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from apps.hussainiya.publisher.models import Publisher


@admin.register(Publisher)
class PublisherAdmin(AjaxDatatable):
    list_display = ('name', '_type', 'url', 'created_at', 'updated_at', '_avatar', '_songs')
    list_filter = ('type',)
    search_fields = ('name', 'type', 'url',)
    ordering = ('-id',)
    autocomplete_fields = ('user', 'languages')

    @admin.display(description=_('Type'), ordering='type')
    def _type(self, obj):
        return obj.get_type_display()

    @admin.display(description=_('Songs'), ordering='songs')
    def _songs(self, obj):
        return str(obj.songs_count)

    @admin.display(description=_('Avatar'), ordering='avatar')
    def _avatar(self, obj):
        try:
            url = get_thumbs(obj.avatar)['sm']
            return mark_safe(
                f"<img class='rounded' width=60 src='{url}'>"
            )
        except Exception:
            return "-"

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            songs_count=Count('songs')
        )
