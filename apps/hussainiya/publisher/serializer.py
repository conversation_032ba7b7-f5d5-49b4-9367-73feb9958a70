from dj_language.serializer import LanguageSerializer
from rest_framework import serializers

from .models import Publisher


class PublisherSerializer(serializers.ModelSerializer):
    languages = LanguageSerializer(read_only=True, many=True)
    songs_count = serializers.IntegerField(allow_null=True)
    thumbnail_url = serializers.SerializerMethodField()

    def get_thumbnail_url(self, obj):
        if obj.avatar:
            return self.context['request'].build_absolute_uri(obj.avatar.url)

        return obj.thumbnail_url

    class Meta:
        model = Publisher
        fields = ('id', 'url', 'name', 'description', 'thumbnail_url', 'type', 'languages', 'songs_count')


class PublisherCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Publisher
        fields = (
            'url', ''
        )
