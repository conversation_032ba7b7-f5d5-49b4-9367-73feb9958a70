# Generated by Django 3.2.13 on 2022-08-05 01:06

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Publisher',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('YT', 'Youtube')], default='YT', max_length=2, verbose_name='type')),
                ('url', models.URLField(verbose_name='url')),
                ('name', models.Char<PERSON>ield(max_length=255, verbose_name='name')),
                ('description', models.Char<PERSON>ield(blank=True, max_length=1024, null=True, verbose_name='description')),
                ('thumbnail_url', models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='thumbnail url')),
            ],
            options={
                'verbose_name': 'publisher',
                'verbose_name_plural': 'publishers',
            },
        ),
    ]
