
import re

from dj_filer.admin import get_thumbs
from dj_language.serializer import LanguageSerializer
from django.db.models import Expression<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework import serializers

from apps.mafatih.models import <PERSON><PERSON><PERSON><PERSON>D<PERSON>, <PERSON><PERSON><PERSON><PERSON>Part, <PERSON><PERSON><PERSON>h<PERSON>ategory, DuaAudio, DuaReciter
from ..serializers.v3 import DuaReciterSerializer
from utils import absolute_url



class MafatihCategoriesSerializer(serializers.ModelSerializer):
    thumbnails = serializers.SerializerMethodField('get_thumbnail_object')
    children = serializers.SerializerMethodField('get_children')
    language = LanguageSerializer()
    name = serializers.SerializerMethodField()

    def get_name(self, obj):
        return re.sub(r"^\*\d+\*\s*", "", obj.name)

    def get_thumbnail_object(self, obj: MafatihCategory):
        try:
            return get_thumbs(obj.thumbnail, self.context.get('request'))
        except Exception:
            return {}

    def get_children(self, obj: Ma<PERSON>tihCategory):
        children = obj.get_children().order_by('order')
        return [self.to_dict(cat) for cat in children]

    def to_dict(self, c):
        children = c.get_children().order_by('order')        
        return {
            'id': c.id,
            'name': re.sub(r"^\*\d+\*\s*", "", c.name),
            'slug': c.slug,
            'thumbnails': self.get_thumbnail_object(c),
            'children': [] if not children else [self.to_dict(i) for i in children],
        }

    class Meta:
        model = MafatihCategory
        fields = ('id', 'name', 'slug', 'thumbnails', 'language', 'children')
        ref_name = 'Web_MafatihCategoriesSerializer'


class MafatihDuaPartSerializer(serializers.ModelSerializer):
    dua_part_index = serializers.SerializerMethodField()

    def get_dua_part_index(self, obj):
        return int(obj.dua_part_index or 1)

    class Meta:
        model = MafatihPart
        fields = ('id', 'dua_part_index', 'text', 'translation', 'local_alpha', 'mafatih_dua_id', 'description')
        ref_name = 'Web_MafatihDuaPartSerializer'


class WebDuaAudioDetailSerializer(serializers.ModelSerializer):
    audio = serializers.SerializerMethodField()
    reciter = serializers.SerializerMethodField()

    def get_audio(self, obj):
        return absolute_url(self.context['request'], obj.audio.url)

    class Meta:
        model = DuaAudio
        fields = ('id', 'reciter', 'audio', 'audio_sync_data')

    def get_reciter(self, obj):
        return DuaReciterSerializer(obj.reciter_relation, context=self.context).data


class WebMafatihDuaSerializer(serializers.ModelSerializer):
    not_synced = serializers.BooleanField(default=False)
    # reciters = serializers.SerializerMethodField()
    # audios =  serializers.SerializerMethodField()
    
    # def get_audios(self, obj):
        # return DuaAudioDetailSerializer(obj.audios, many=True, context=self.context).data    
    
    class Meta:
        model = MafatihDua
        fields = ('id', 'title', 'not_synced')
        ref_name = 'Web_MafatihDuaSerializer'



