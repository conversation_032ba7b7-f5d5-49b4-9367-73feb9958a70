import re

from dj_filer.admin import get_thumbs
from dj_language.serializer import LanguageSerializer
from django.db.models import ExpressionWrapper, <PERSON>, <PERSON><PERSON>an<PERSON><PERSON>
from rest_framework import serializers

from apps.mafatih.models import <PERSON><PERSON><PERSON>hD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>art, <PERSON><PERSON><PERSON>h<PERSON>ategory, <PERSON>a<PERSON>udi<PERSON>
from utils import absolute_url


class MafatihDuaSerializer(serializers.ModelSerializer):
    duas_part_list = serializers.PrimaryKeyRelatedField(
        source='mafatih_part',
        read_only=True,
        many=True
    )
    weekdays = serializers.SerializerMethodField(method_name='get_weekdays')
    audio_sync_data = serializers.SerializerMethodField(method_name='get_audio_sync_data')

    audio = serializers.SerializerMethodField(method_name='get_audio_url')

    def get_audio_sync_data(self, obj):
        return obj.audio_sync_data_s

    def get_weekdays(self, obj):
        return obj.weekdays.get('weekdays', [])

    def get_audio_url(self, obj):

        if audio_s := getattr(obj, 'audio_s', None):
            return f"/static/uploads/main/{audio_s}"
        elif obj.audio:
            return obj.audio.url

    class Meta:
        model = MafatihDua
        fields = ('id', 'title', 'duas_part_list', 'dates', 'weekdays', 'audio_sync_data', 'audio')


class MafatihDuaPartSerializer(serializers.ModelSerializer):
    dua_part_index = serializers.SerializerMethodField()

    def get_dua_part_index(self, obj):
        return int(obj.dua_part_index or 1)

    class Meta:
        model = MafatihPart
        fields = ('id', 'dua_part_index', 'text', 'translation', 'local_alpha', 'mafatih_dua_id', 'description')


class MafatihCategoriesSerializer(serializers.ModelSerializer):
    duas_index = serializers.SerializerMethodField('get_duas_index')
    thumbnails = serializers.SerializerMethodField('get_thumbnail_object')
    children = serializers.SerializerMethodField('get_children')
    language = LanguageSerializer()
    name = serializers.SerializerMethodField()

    def get_name(self, obj):
        return re.sub(r"^\*\d+\*\s*", "", obj.name)

    def get_thumbnail_object(self, obj: MafatihCategory):
        try:
            return get_thumbs(obj.thumbnail, self.context.get('request'))
        except Exception:
            return {}

    def get_duas_index(self, obj: MafatihCategory):
        # cannot access tot obj.mafatih bcz of mptt module limitation
        # for proxy models in mptt.managers.TreeManager line 54
        # so we directly query to mafatihDua model
        return list(
            MafatihDua.objects.filter(category_second__id=obj.id, status=True).annotate(
                not_synced=ExpressionWrapper(Q(audio_sync_data=[]), output_field=BooleanField())
            ).order_by('priority','not_synced', ).values_list('id', flat=True)
        )

    def get_children(self, obj: MafatihCategory):
        children = obj.get_children().order_by('order')
        return [self.to_dict(cat) for cat in children]

    def to_dict(self, c):
        children = c.get_children().order_by('order')
        return {
            'id': c.id,
            'name': re.sub(r"^\*\d+\*\s*", "", c.name),
            'thumbnails': self.get_thumbnail_object(c),
            'children': [] if not children else [self.to_dict(i) for i in children],
            'duas_index': self.get_duas_index(c),
        }

    class Meta:
        model = MafatihCategory
        fields = ('id', 'name', 'thumbnails', 'duas_index', 'language', 'children')


class DuaAudioListSerializer(serializers.ModelSerializer):
    class Meta:
        model = DuaAudio
        fields = ('id', 'reciter',)


class DuaAudioDetailSerializer(serializers.ModelSerializer):
    audio = serializers.SerializerMethodField()

    def get_audio(self, obj):
        return absolute_url(self.context['request'], obj.audio.url)

    class Meta:
        model = DuaAudio
        fields = ('reciter', 'audio', 'audio_sync_data')
