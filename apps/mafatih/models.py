from dj_category.field import Category<PERSON><PERSON><PERSON><PERSON>ield
from dj_category.models import BaseCategory
from dj_language.field import LanguageField
from django.db import models
from django.utils.translation import gettext_lazy as _
from filer.fields.file import Filer<PERSON>ileField
from nwh_seo.fields import SeoGenericRelation
from filer.fields.image import Filer<PERSON><PERSON><PERSON><PERSON>
from django.utils.text import slugify

from apps.najm_calendar.fields import JsonDateField


class MafatihDua(models.Model):
    title = models.CharField(max_length=192)
    dates = JsonDateField(help_text=_('لیستی از تاریخ های قمری مربوط به قطعه'), null=True, blank=True)
    weekdays = models.JSONField(default=dict, blank=True, verbose_name=_('weekdays'))

    category_second = models.ManyToManyField(
        'MafatihCategory', related_name='mafatih_second', blank=True
    )
    audio = FilerFileField(
        verbose_name=_('audio file'), null=True, blank=True, help_text=_('nullable'), related_name='+',
        on_delete=models.SET_NULL,
    )
    audio_sync_data = models.JSONField(default=list, blank=True)
    seo_fields = SeoGenericRelation()
    updated_at = models.DateTimeField(auto_now=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    max_similar_to_dua = models.CharField(max_length=192, default=None, null=True, blank=True)
    priority = models.FloatField(null=True, blank=True, help_text=_('اولویت نمایش بر اساس این فیلد است'))
    status = models.BooleanField(default=True, verbose_name=_('visibility'), )
    ai_commands = models.JSONField(default=list, blank=True, verbose_name=_('ai_commands'))

    lat = models.FloatField(null=True, blank=True, verbose_name=_('Latitude'), help_text=_('Latitude of the location for the dua'))
    lon = models.FloatField(null=True, blank=True, verbose_name=_('Longitude'), help_text=_('Longitude of the location for the dua'))
    location_name = models.CharField(max_length=255, null=True, blank=True, verbose_name=_('Location Name'), help_text=_('Name of the shrine or pilgrimage site based on the coordinates'))

    similar_titles = models.JSONField(null=True, blank=True, verbose_name=_('Similar Titles'), default=dict)

    class Meta:
        verbose_name = _('Dua')
        verbose_name_plural = _('Duas')
        indexes = [
            models.Index(fields=['status']),  
            models.Index(fields=['title']),  
        ]
    def __str__(self):
        return self.title


class MafatihPart(models.Model):
    dua_part_index = models.FloatField(help_text=_('شماره قطعه در دعای مورد نظر'), null=True, blank=True)
    mafatih_dua = models.ForeignKey(MafatihDua, on_delete=models.CASCADE, related_name='mafatih_part')
    text = models.TextField(null=True, blank=True)
    local_alpha = models.TextField(null=True, blank=True)
    translation = models.TextField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    correction_status = models.PositiveSmallIntegerField(choices=(
        (0, 'wrong'),
        (1, 'corrected'),
        (2, 'not corrected'),
    ), default=2)

    def __str__(self):
        return str(self.dua_part_index)

    def short_text(self):
        text = self.text or self.local_alpha or self.description or self.translation
        if text:
            words = text.split(" ")
            short_text = " ".join(words[:11])
            if len(words) > 11:
                short_text += ' ...'
            return short_text
        else:
            return '-'

    class Meta:
        verbose_name = _('mafatih dua part')
        verbose_name_plural = _('mafatih dua parts')
        ordering = ('dua_part_index', 'id')
        indexes = [
            models.Index(fields=['correction_status', 'dua_part_index']),  
            models.Index(fields=['mafatih_dua']),  
        ]
        

models.PositiveSmallIntegerField(verbose_name=_('order'), null=True, blank=True).contribute_to_class(BaseCategory,
                                                                                                     'order')


class MafatihCategory(BaseCategory):
    class Meta:
        proxy = True
        verbose_name_plural = _('Duas categories')
        verbose_name = _('category')
        ordering = ('order',)


class DuaAudio(models.Model):
    mafatih_dua = models.ForeignKey(MafatihDua, on_delete=models.CASCADE, related_name='audios')
    reciter = models.CharField(_('reciter'), max_length=119, )
    reciter_relation = models.ForeignKey('DuaReciter', on_delete=models.SET_NULL, related_name='dua_audios', verbose_name=_('reciter relation'), null=True, blank=True)    
    audio = FilerFileField(
        verbose_name=_('audio file'), related_name='+',
        on_delete=models.PROTECT,
    )
    audio_sync_data = models.JSONField(default=list, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))

    def __str__(self):
        return self.reciter

    class Meta:
        verbose_name = _('dua audio')
        verbose_name_plural = _('duas audios')
        ordering = ('-id',)
        indexes = [
            models.Index(fields=['mafatih_dua']),
            models.Index(fields=['reciter']),  
        ]
        
class DuaReciter(models.Model):
    name = models.CharField(_('reciter name'), max_length=120)
    slug = models.SlugField(_('slug'), max_length=120, unique=True, blank=True)
    translations = models.JSONField(verbose_name=_('translations'), default=dict)
    avatar = FilerImageField(related_name='+', on_delete=models.PROTECT, verbose_name=_('thumbnail'), null=True, blank=True)

    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)
        
    def get_translation(self, lang):
        for tr in self.translations:
            if tr['language_code'] == lang:
                return tr['title']
        return None
    
    class Meta:
        verbose_name = _('Dua Reciter')
        verbose_name_plural = _('Dua Reciters')


class TranslationLog(models.Model):
    model_name = models.CharField(
        max_length=100,
        verbose_name=_('Model Name'),
        help_text=_('The name of the model being translated (e.g., dua, part, category)')
    )
    source_id = models.PositiveIntegerField(
        verbose_name=_('Source ID'),
        help_text=_('ID of the original object being translated')
    )
    translated_id = models.PositiveIntegerField(
        verbose_name=_('Translated ID'),
        help_text=_('ID of the translated object')
    )
    language = LanguageField(verbose_name=_('language'), null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('Created At'))

    class Meta:
        verbose_name = _('Translation Log')
        verbose_name_plural = _('Translation Logs')
        unique_together = ('model_name', 'source_id', 'translated_id')  # جلوگیری از تکرار
        indexes = [
            models.Index(fields=['model_name', 'source_id']),  # بهینه‌سازی جستجو
            models.Index(fields=['model_name', 'translated_id']),
        ]

    def __str__(self):
        return f"{self.model_name}: {self.source_id} -> {self.translated_id}"
