from django.urls import path, include

from apps.mafatih.views.v1 import *
from apps.mafatih.views.v2 import MafatihDuaViewV2
from apps.mafatih.views.v3 import MafatihDuaViewV3
from apps.mafatih.views.web import *


# cache_duration = 60 * 60
cache_duration = 60

web_urlpatterns = [
    path('mafatih-categories/', WebMafatihCategoriesView.as_view()),
    path('mafatih-duas/', WebMafatihDuaListView.as_view()),    
    path('mafatih-duas/<int:pk>/parts/', WebMafatihDuaPartListView.as_view()),    
    path('mafatih-duas/dhikrs/', MafatihDhikrView.as_view()),
    path('mafatih/<int:pk>/reciters/', WebMafatihDuaReciterList.as_view()),
    path('mafatih/<int:pk>/audios/', WebDuaAudioDetailList.as_view()),
    
   
]


urlpatterns = [
    path('mafatih-duas/', cache_page(cache_duration)(MafatihDuaView.as_view())),
    path('mafatih-duas/v2/', cache_page(cache_duration)(MafatihDuaViewV2.as_view())),
    # path('mafatih-duas/v3/', cache_page(cache_duration)(MafatihDuaViewV3.as_view())),
    path('mafatih-duas/<int:pk>/audios/', cache_page(cache_duration)(MafatihDuaAudioView.as_view())),
    path('mafatih-duas/audios/<int:pk>/', cache_page(cache_duration)(MafatihDuaAudioDetailView.as_view())),
    path('mafatih-dua-parts/', MafatihDuaPartsView.as_view()),
    path('mafatih-categories/', cache_page(cache_duration)(MafatihCategoriesView.as_view())),
    # path('mafatih-duas/famous/', cache_page(cache_duration)(MafatihFamousView.as_view())),
    # path('mafatih-duas/dhikrs/', cache_page(cache_duration)(MafatihDhikrView.as_view())),
    # path('mafatih-duas/location/', MafatihLocationView.as_view()),
    path('mafatih/<int:mafatih_dua_id>/reciters/', cache_page(cache_duration)(MafatihDuaRecitersView.as_view())),

    # path('web/', include((web_urlpatterns, 'web'), namespace='web-duas')),

]
