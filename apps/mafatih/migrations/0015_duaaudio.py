# Generated by Django 3.2.22 on 2023-11-07 15:32

from django.db import migrations, models
import django.db.models.deletion
import filer.fields.file


class Migration(migrations.Migration):

    dependencies = [
        ('filer', '0014_folder_permission_choices'),
        ('mafatih', '0014_alter_mafatihcategory_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='DuaAudio',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reciter', models.CharField(default='Unknown', max_length=119, verbose_name='reciter')),
                ('audio_sync_data', models.JSONField(blank=True, default=list)),
                ('audio', filer.fields.file.FilerFileField(blank=True, help_text='nullable', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='filer.file', verbose_name='audio file')),
                ('mafatih_dua', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='audios', to='mafatih.mafatihdua')),
            ],
            options={
                'verbose_name': 'dua audio',
                'verbose_name_plural': 'duas audios',
                'ordering': ('-id',),
            },
        ),
    ]
