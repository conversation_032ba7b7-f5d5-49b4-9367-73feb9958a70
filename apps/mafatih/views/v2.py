from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import Exists, Subquery, OuterRef, <PERSON>oleanField
from rest_framework.permissions import IsAuthenticated
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page

from apps.mafatih.views.v1 import Base
from ..models import Ma<PERSON>tihDua, DuaAudio
from ..serializers.v2 import MafatihDuaSerializerV2


class MafatihDuaViewV2(Base):
    serializer_class = MafatihDuaSerializerV2
    permission_classes = (IsAuthenticated,)

    @method_decorator(cache_page(60 * 60))  # Cache for 1 Hour
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)

    def get_queryset(self):
        return MafatihDua.objects.filter(
            category_second__language__code=self.request.LANGUAGE_CODE,
            status=True
        ).prefetch_related(
            'audios', 'mafatih_part', 'category_second',
        ).annotate(
            not_synced=Exists(Subquery(DuaAudio.objects.filter(mafatih_dua=OuterRef('pk'))),
                              output_field=BooleanField()),
        ).order_by('not_synced', 'title')
