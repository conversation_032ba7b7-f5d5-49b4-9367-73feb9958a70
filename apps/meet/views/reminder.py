import logging
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
# Removed unused imports
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from apps.meet.models import Meet
from apps.meet.serializers import MeetSerializer
from apps.meet.serializers.reminder import MeetReminderSerializer
from apps.notification.models import Reminder

logger = logging.getLogger(__name__)


class AddReminderView(generics.CreateAPIView):
    # TODO: must meet free or user access meet
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        meet_id = kwargs.get('meet_id')
        meet = get_object_or_404(Meet, id=meet_id)

        # Check if reminder already exists
        existing_reminder = Reminder.objects.filter(
            user=request.user,
            service_name='meet',
            object_id=str(meet_id),
            status=True
        ).first()

        if existing_reminder:
            return Response({'detail': 'Reminder already exists.'}, status=status.HTTP_400_BAD_REQUEST)

        # Create new reminder
        reminder = Reminder.objects.create(
            user=request.user,
            service_name='meet',
            object_id=str(meet_id),
            text=f"Reminder for meet: {meet.name}",
            call_time=meet.next_session_schedule or timezone.now(),
            status=True,
            notif_data={'meet_id': meet_id, 'meet_name': meet.name}
        )

        return Response({"message": "Meet Reminder successfully."}, status=status.HTTP_201_CREATED)


class UserRemindersListView(generics.ListAPIView):
    serializer_class = MeetSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        # Get reminders for meet service
        reminders = Reminder.objects.filter(
            user=user,
            service_name='meet',
            # status=True
        ).order_by('-created_at')

        # Extract meet IDs from object_id field
        meet_ids = []
        for reminder in reminders:
            try:
                meet_id = int(reminder.object_id)
                meet_ids.append(meet_id)
            except (ValueError, TypeError):
                continue

        # Return meets ordered by reminder creation time
        # Since we need to match string object_id with integer pk, we'll do a simpler ordering
        return Meet.objects.filter(id__in=meet_ids).order_by('-id')
    
    
    
class RemoveReminderView(generics.DestroyAPIView):
    serializer_class = MeetReminderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def delete(self, request, *args, **kwargs):
        meet_id = kwargs.get('meet_id')

        # Find and delete the reminder
        reminder = get_object_or_404(
            Reminder,
            user=request.user,
            service_name='meet',
            object_id=str(meet_id)
        )
        reminder.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class UpdateReminderStatusView(generics.GenericAPIView):
    serializer_class = MeetReminderSerializer
    permission_classes = [permissions.IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Update or create a reminder for a meet with specified active status",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['is_active'],
            properties={
                'is_active': openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    description='Whether the reminder is active or not'
                )
            }
        ),
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Reminder status updated successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING)
                    }
                )
            ),
            status.HTTP_201_CREATED: openapi.Response(
                description="Reminder created with specified status",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING)
                    }
                )
            ),
            status.HTTP_400_BAD_REQUEST: openapi.Response(
                description="Invalid request parameters",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'detail': openapi.Schema(type=openapi.TYPE_STRING)
                    }
                )
            )
        }
    )
    def put(self, request, *args, **kwargs):
        return self._update_reminder_status(request, *args, **kwargs)

    def _update_reminder_status(self, request, *args, **kwargs):
        meet_id = kwargs.get('meet_id')
        logger.info(f"Request UpdateReminderStatusView: {meet_id}/{request.data}")
        is_active = request.data.get('is_active')
        # Validate is_active parameter
        if is_active is None:
            return Response({'detail': 'is_active parameter is required.'}, status=status.HTTP_400_BAD_REQUEST)

        # Convert is_active to boolean
        if isinstance(is_active, str):
            is_active = is_active.lower() == 'true'

        meet = get_object_or_404(Meet, id=meet_id)

        # Check if reminder exists for this user and meet
        reminder = Reminder.objects.filter(
            user=request.user,
            service_name='meet',
            object_id=str(meet_id)
        ).first()

        if reminder:
            # Update existing reminder status
            reminder.status = is_active
            reminder.save()
            message = "Reminder activated." if is_active else "Reminder deactivated."
            return Response({"message": message}, status=status.HTTP_200_OK)
        else:
            # Create new reminder with specified status
            reminder = Reminder.objects.create(
                user=request.user,
                service_name='meet',
                object_id=str(meet_id),
                text=f"Reminder for meet: {meet.name}",
                call_time=meet.next_session_schedule or timezone.now(),
                status=is_active,
                notif_data={'meet_id': meet_id, 'meet_name': meet.name}
            )
            message = "Reminder created and activated." if is_active else "Reminder created but deactivated."
            return Response({"message": message}, status=status.HTTP_201_CREATED)
        