
from rest_framework.generics import <PERSON><PERSON><PERSON><PERSON><PERSON>ie<PERSON>, ListAPIView, RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from apps.meet.models import MeetProviderProfile, Meet
from rest_framework.response import Response
from rest_framework import status
from apps.meet.serializers import Meet<PERSON>rov<PERSON>ProfileSerializer, MeetProviderRoomSerializer, ProviderSerializer, ProviderDetailMeetSerializer
from apps.account.models import User, ProviderRequest
from rest_framework.exceptions import ValidationError
from django.db.models import Q
from apps.meet.permissions import IsVerifiedMeetProvider



class MeetProviderProfileCreateView(CreateAPIView):
    queryset = MeetProviderProfile.objects.all()
    serializer_class = MeetProviderProfileSerializer
    permission_classes = [IsAuthenticated]


    def create(self, request, *args, **kwargs):
        user = request.user
        existing_profile = self.get_queryset().filter(user=user).first()
        
        # Check if an accepted profile exists
        if existing_profile and existing_profile.request and (
            existing_profile.request.status == ProviderRequest.Status.accepted or 
            existing_profile.request.status == ProviderRequest.Status.pending
        ):
            serializer = self.get_serializer(existing_profile)
            return Response(serializer.data, status=status.HTTP_200_OK)
        
        # Proceed with creating a new profile
        return super().create(request, *args, **kwargs)

    def perform_create(self, serializer):
        user = self.request.user
        provider_request = ProviderRequest.objects.create(
            user=user,
            service=ProviderRequest.Service.meet,
            public_name="meet-provider",
            description=f"Meet Provider Profile Request:\n"
                        f"Full Name: {serializer.validated_data.get('full_name', 'N/A')}\n"
                        f"Bio: {serializer.validated_data.get('bio', 'N/A')}\n"
                        f"Email: {serializer.validated_data.get('email', 'N/A')}\n"
                        f"Institution Name: {serializer.validated_data.get('institution_name', 'N/A')}\n"
                        f"Languages: {', '.join([language.name for language in serializer.validated_data.get('languages', [])])}\n"
        )   
        serializer.save(user=user, request=provider_request)
            

class MeetProviderRoomView(ListAPIView):
    serializer_class = MeetProviderRoomSerializer
    permission_classes = [IsAuthenticated, IsVerifiedMeetProvider]

    def get_queryset(self):
        user = self.request.user
        return Meet.objects.filter(provider=user).order_by('-created_at')
    
class ProviderListView(ListAPIView):
    serializer_class = ProviderSerializer
    permission_classes = [IsAuthenticated,]

    def get_queryset(self):
        user = self.request.user
        language_code = self.request.LANGUAGE_CODE
        provider_profiles = MeetProviderProfile.objects.filter(
            Q(languages__code=language_code) &
            Q(is_verified=True) &
            (Q(user__settings__isnull=True) | Q(user__settings__is_tester=False))
        ).distinct() 
        return provider_profiles


class ProviderDetailView(RetrieveAPIView):
    serializer_class = ProviderDetailMeetSerializer
    permission_classes = [IsAuthenticated,]
    queryset = MeetProviderProfile.objects.all()
    lookup_field = 'id'
    