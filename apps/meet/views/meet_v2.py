import logging
from django.db.models import Q
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import ValidationError

from apps.meet.models import Meet, MeetApproval
from apps.meet.serializers.meet import MeetCreateV2Serializer
from apps.meet.permissions import IsProviderOrReadOnly, IsVerifiedMeetProvider
from apps.dua.pagination import NoPagination


class MeetCreateV2View(generics.CreateAPIView):
    """
    Create a new meet with access control (public/private) and optional password.
    """
    queryset = Meet.objects.all()
    serializer_class = MeetCreateV2Serializer
    permission_classes = [IsAuthenticated, IsProviderOrReadOnly, IsVerifiedMeetProvider]
    pagination_class = NoPagination

    def perform_create(self, serializer):
        user = self.request.user

        is_free = serializer.validated_data.get('is_free', False)  
        price = serializer.validated_data.get('price', 0)
        access = serializer.validated_data.get('access', 'public')
        
        # Adjust is_free based on price
        if price > 0:
            is_free = False
        else:
            is_free = True
            
        # Handle test users
        if user.is_tester: 
            serializer.validated_data['name'] = f"{serializer.validated_data['name']}-(Tester)"
            is_active = True if is_free else False
            meet_instance = serializer.save(provider=user, is_active=is_active, is_test=True, is_free=is_free)
            if not is_active:
                MeetApproval.objects.create(
                    meet=meet_instance,
                    user=user,
                    is_verified=False  
                )
        else:
            # Handle regular users
            if is_free:
                meet_instance = serializer.save(provider=user, is_active=True, is_free=is_free)
            else:
                if price <= 0:
                    raise ValidationError("Price must be greater than zero if the meet is not free.")

                meet_instance = serializer.save(provider=user, is_active=False, is_free=is_free)
                MeetApproval.objects.create(
                    meet=meet_instance,
                    user=user,
                    is_verified=False  
                )