"""
Documentation functions for Meet app API endpoints.
Contains swagger documentation for cleaner view files.
"""

from drf_yasg import openapi


def get_meet_detail_description():
    """
    Returns the operation description for MeetDetailView.
    """
    return """
    Retrieve detailed information about a specific meet including recordings.
    
    Returns complete meet details with all associated session recordings.
    Each recording includes session information, file details, and metadata.
    
    ### Response Structure:
    ```json
    {
        "id": 174,
        "name": "تست-(Tester)",
        "description": "Meeting description",
        "next_session_subject": "Next session topic",
        "next_session_schedule": "2024-01-01T10:00:00Z",
        "is_suggested": false,
        "provider": {
            "id": 1,
            "username": "provider_name",
            "email": "<EMAIL>"
        },
        "is_free": true,
        "price": "0.00",
        "is_live": false,
        "is_closed": false,
        "online_users_count": 0,
        "language": "fa",
        "categories": [],
        "organizer": "Provider Name",
        "is_provider": true,
        "live_session_id": null,
        "has_archive": true,
        "recordings": [
            {
                "id": 1,
                "title": "Session Recording",
                "file": "https://habibapp.com/media/recorded_sessions/recording.mp4",
                "file_time": "01:30:00",
                "recording_type": "video",
                "created_at": "2024-01-01T10:00:00Z",
                "session_id": 548,
                "session_subject": "تلت"
            }
        ],
        "access": "public",
        "password": null,
        "has_recording": true
    }
    ```
    
    ### Features:
    - Complete meet information
    - All active session recordings included
    - Provider and organizer details
    - Live session status and ID
    - Access control information
    - Recording availability status
    """


def get_meet_detail_parameters():
    """
    Returns manual parameters for MeetDetailView.
    """
    return [
        openapi.Parameter(
            'meet_id',
            openapi.IN_PATH,
            description="ID of the meet to retrieve details for",
            type=openapi.TYPE_INTEGER,
            required=True,
        )
    ]


def get_meet_detail_responses():
    """
    Returns response schema for MeetDetailView.
    """
    return {
        200: openapi.Response(
            description="Meet details retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Meet ID'),
                    'name': openapi.Schema(type=openapi.TYPE_STRING, description='Meet name'),
                    'description': openapi.Schema(type=openapi.TYPE_STRING, description='Meet description'),
                    'next_session_subject': openapi.Schema(type=openapi.TYPE_STRING, description='Next session subject'),
                    'next_session_schedule': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='Next session schedule'),
                    'is_suggested': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Whether meet is suggested'),
                    'provider': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Provider ID'),
                            'username': openapi.Schema(type=openapi.TYPE_STRING, description='Provider username'),
                            'email': openapi.Schema(type=openapi.TYPE_STRING, description='Provider email')
                        }
                    ),
                    'is_free': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Whether meet is free'),
                    'price': openapi.Schema(type=openapi.TYPE_STRING, description='Meet price'),
                    'is_live': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Whether meet is currently live'),
                    'is_closed': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Whether meet is closed'),
                    'online_users_count': openapi.Schema(type=openapi.TYPE_INTEGER, description='Number of online users'),
                    'language': openapi.Schema(type=openapi.TYPE_STRING, description='Meet language code'),
                    'categories': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_STRING), description='Meet categories'),
                    'organizer': openapi.Schema(type=openapi.TYPE_STRING, description='Organizer name'),
                    'is_provider': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Whether current user is the provider'),
                    'live_session_id': openapi.Schema(type=openapi.TYPE_INTEGER, description='ID of current live session', nullable=True),
                    'has_archive': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Whether meet has archived sessions'),
                    'recordings': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Recording ID'),
                                'title': openapi.Schema(type=openapi.TYPE_STRING, description='Recording title'),
                                'file': openapi.Schema(type=openapi.TYPE_STRING, description='Recording file URL'),
                                'file_time': openapi.Schema(type=openapi.TYPE_STRING, description='Recording duration'),
                                'recording_type': openapi.Schema(type=openapi.TYPE_STRING, description='Recording type: voice or video'),
                                'created_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='Recording creation timestamp'),
                                'session_id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Associated session ID'),
                                'session_subject': openapi.Schema(type=openapi.TYPE_STRING, description='Session subject/title')
                            }
                        ),
                        description='List of all active recordings for this meet'
                    ),
                    'access': openapi.Schema(type=openapi.TYPE_STRING, description='Meet access level'),
                    'password': openapi.Schema(type=openapi.TYPE_STRING, description='Meet password if required', nullable=True),
                    'has_recording': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Whether meet has any recordings')
                }
            )
        ),
        401: openapi.Response(description="Authentication required"),
        403: openapi.Response(description="Permission denied"),
        404: openapi.Response(description="Meet not found")
    }


def get_session_recordings_description():
    """
    Returns the operation description for SessionRecordingListView.
    """
    return """
    Retrieve a list of recorded meeting sessions for a specific meet.

    Returns all active session recordings for the specified meet ID with their
    associated session details, file information, duration, and recording type (voice/video).

    ### Response Structure:
    ```json
    [
        {
            "id": 1,
            "title": "Meeting Recording",
            "file": "https://habibapp.com/media/recorded_sessions/recording.mp4",
            "file_time": "01:30:00",
            "recording_type": "video",
            "created_at": "2024-01-01T10:00:00Z",
            "session_id": 123,
            "session_subject": "Weekly Team Meeting"
        }
    ]
    ```

    ### Features:
    - Only returns active recordings for the specified meet (is_active=True)
    - Includes session information for context
    - Supports filtering and pagination
    - Ordered by creation date (newest first)
    """


def get_session_recordings_parameters():
    """
    Returns manual parameters for SessionRecordingListView.
    """
    return [
        openapi.Parameter(
            'id',
            openapi.IN_PATH,
            description="ID of the meet to retrieve recordings for",
            type=openapi.TYPE_INTEGER,
            required=True,
        )
    ]


def get_session_recordings_responses():
    """
    Returns response schema for SessionRecordingListView.
    """
    return {
        200: openapi.Response(
            description="List of session recordings for the specified meet retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Recording ID'),
                        'title': openapi.Schema(type=openapi.TYPE_STRING, description='Recording title'),
                        'file': openapi.Schema(type=openapi.TYPE_STRING, description='File URL'),
                        'file_time': openapi.Schema(type=openapi.TYPE_STRING, description='Recording duration'),
                        'recording_type': openapi.Schema(type=openapi.TYPE_STRING, description='Type: voice or video'),
                        'created_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='Creation timestamp'),
                        'session_id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Associated session ID'),
                        'session_subject': openapi.Schema(type=openapi.TYPE_STRING, description='Session subject/title')
                    }
                )
            )
        ),
        401: openapi.Response(description="Authentication required"),
        403: openapi.Response(description="Permission denied"),
        404: openapi.Response(description="Meet not found")
    }
