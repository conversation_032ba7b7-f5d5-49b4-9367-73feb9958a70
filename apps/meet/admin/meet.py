from django.contrib import admin
from ajaxdatatable.admin import AjaxDatatable
from django.utils.translation import gettext_lazy as _
from django.utils.safestring import mark_safe
from django import forms
from django.utils.html import format_html
from django.urls import reverse
from django.contrib.admin import SimpleListFilter

from apps.meet.models import Meet, Session, UserSession, Report, Observer, UserPurchases, UserDialogue, MeetApproval, MeetProviderProfile, SessionRecording


class MeetFilter(SimpleListFilter):
    title = 'Meet'
    parameter_name = 'meet_id'

    def lookups(self, request, model_admin):
        # Get all meets that have recordings
        meets_with_recordings = Meet.objects.filter(
            session__recordings__is_active=True
        ).distinct().order_by('name')[:50]  # Limit to 50 for performance

        return [(meet.id, f"{meet.name[:50]}...") for meet in meets_with_recordings]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(session__meet__id=self.value())
        return queryset


@admin.register(MeetApproval)
class MeetApprovalAdmin(AjaxDatatable):
    list_display = ('meet', '_provider', '_status','_is_verified', 'created_at')
    readonly_fields = ('meet', '_provider', 'is_verified','meet_info', 'provider_info','created_at', 'updated_at')

    fieldsets = (
    (None, {
        'fields': ('meet', '_provider', 'status','is_verified', 'created_at', 'updated_at')
    }),
    ('Request Information', {
        'fields': ('meet_info', 'provider_info', ),
    }),
    )

    @admin.display(description='IsVerified', ordering='is_verified')
    def _is_verified(self, obj):
        if obj.is_verified:
            m = "<span class='badge badge-success'>YES</span>"
        else:
            m = "<span class='badge badge-danger'>NO</span>"
        return mark_safe(m)


    @admin.display(description='Status', ordering='status')
    def _status(self, obj):
        if obj.status == "pending":
            m = "<span class='badge badge-secondary'>Pending</span>"
        elif obj.status == 'successed':
            m = "<span class='badge badge-success'>Successed</span>"
        elif obj.status == 'rejected':
            m = "<span class='badge badge-danger'>Rejected</span>"
        else:
            m = '-'
        return mark_safe(m)
    
    @admin.display(description=_('Provider'))
    def _provider(self, obj):    
        profile = MeetProviderProfile.objects.filter(user=obj.user).first()
        if profile:
            url = (
                reverse('admin:meet_meetproviderprofile_changelist')
                + f'{profile.id}/change'
            )
            return format_html('<a href="{}">{}</a>', url, profile.full_name)
        return obj.user.email

    def meet_info(self, obj):
        if obj.meet:
            return mark_safe(f'''
                <div style="
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    padding: 10px;
                    margin-bottom: 15px;
                    background-color: #f9f9f9;
                ">
                    <p><strong>Name:</strong> {obj.meet.name}</p>
                    <p><strong>Next Session Subject:</strong> {obj.meet.next_session_subject or "N/A"}</p>
                    <p><strong>Next Session Schedule:</strong> {obj.meet.next_session_schedule or "N/A"}</p>
                    <p><strong>Provider:</strong> {obj.meet.provider.email}</p>
                    <p><strong>Price:</strong> {obj.meet.price if not obj.meet.is_free else "Free"}</p>
                    <p><strong>Language:</strong> {obj.meet.language}</p>
                    <p><strong>Categories:</strong> {obj.meet.categories or "N/A"}</p>
                    <p><strong>Description:</strong> {obj.meet.description or "N/A"}</p>
                </div>
            ''')
        return "No Meet information available."

    def provider_info(self, obj):
        profile = MeetProviderProfile.objects.filter(user=obj.user).first()
        if profile:
            avatar_html = ""
            if profile.avatar:
                avatar_html = f'''
                    <div style="text-align: center; margin-bottom: 10px;">
                        <img src="{profile.avatar.url}" style="border-radius: 50%; width: 150px; height: 150px; object-fit: cover;" alt="Avatar">
                    </div>
                '''
            return mark_safe(f'''
                <div style="
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    padding: 10px;
                    margin-bottom: 15px;
                    background-color: #f9f9f9;
                ">
                    {avatar_html}
                    <p><strong>Full Name:</strong> {profile.full_name or "N/A"}</p>
                    <p><strong>Email:</strong> {profile.email or "N/A"}</p>
                    <p><strong>Institution Name:</strong> {profile.institution_name or "N/A"}</p>
                    <p><strong>Bio:</strong> {profile.bio or "N/A"}</p>
                    <p><strong>Birthdate:</strong> {profile.birthdate or "N/A"}</p>
                    <p><strong>Languages:</strong> {", ".join([str(lang) for lang in profile.languages.all()])}</p>
                    <p><strong>WhatsApp Number:</strong> {profile.wa_number or "N/A"}</p>
                    <p><strong>Social Medias:</strong> {profile.social_medias}</p>
                    <p><strong>Verified:</strong> {'Yes' if profile.is_verified else 'No'}</p>
                </div>
            ''')
        return "No Provider Profile available."

    provider_info.short_description = "Provider Information"

class SessionInline(admin.StackedInline):
    model = Session
    extra = 1

@admin.register(Meet)
class MeetAdmin(AjaxDatatable):
    inlines = [SessionInline]
    autocomplete_fields = ['provider']
    search_fields = ['name', 'provider__fullname',]
    list_display = ['name', 'provider', '_is_live', 'is_free', 'price', 'get_sessions', 'get_recordings', 'get_chats']
    list_filter = ('is_live', 'is_active', 'is_free', 'is_suggested')
    
    
    def get_sessions(self, obj):
        url = (
            reverse('admin:meet_session_changelist')
            + f'?meet_id__exact={obj.id}'
        )
        session_count = Session.objects.filter(meet__id=obj.id).count()
        return format_html('<a href="{}">{}</a>', url, session_count)
    get_sessions.short_description = 'Sessions'

    def get_recordings(self, obj):
        # Count all active SessionRecording objects that belong to sessions of this meet
        recording_count = SessionRecording.objects.filter(
            session__meet=obj,
            is_active=True
        ).count()

        # Create URL to filter recordings by meet ID using custom filter
        url = (
            reverse('admin:meet_sessionrecording_changelist')
            + f'?meet_id={obj.id}'
        )
        return format_html('<a href="{}">{}</a>', url, recording_count)
    get_recordings.short_description = 'Recordings'

    def get_chats(self, obj):
        """View Chats button for Meet"""
        from apps.meet.models.chat import Message

        # Count messages for this meet
        message_count = Message.objects.filter(meet=obj).count()

        # Create URL to view chats
        url = reverse('meet_chats_view', kwargs={'meet_id': obj.id})

        if message_count > 0:
            return format_html(
                '<a href="{}" style="text-decoration: none; color: #333;">💬 View Chats ({})</a>',
                url, message_count
            )
        else:
            return format_html(
                '<span style="color: #999;">No chats</span>'
            )
    get_chats.short_description = 'Chats'

    @admin.display(description='is_live', ordering='is_live')
    def _is_live(self, obj):
        if obj.is_live:
            m = "<span class='badge badge-danger'>Live</span>"
        else:
            m = "<span class='badge badge-secondary'>Off</span>"
        return mark_safe(m)

class SessionRecordingInline(admin.TabularInline):
    model = SessionRecording
    extra = 1
    fields = ['title', 'file', 'file_time', 'recording_type', 'thumbnail', 'created_at']
    readonly_fields = ['created_at']


@admin.register(Session)
class SessionAdmin(AjaxDatatable):
    inlines = [SessionRecordingInline]
    autocomplete_fields = ['meet']
    search_fields = ['subject']
    list_display = ['meet', 'subject', '_is_live','started_at', 'ended_at', 'get_user_sessions', 'get_recordings']
    list_filter = ('meet', )

    @admin.display(description='is_live', ordering='is_live')
    def _is_live(self, obj):
        if obj.meet.is_live and obj.started_at and not obj.ended_at:
            m = "<span class='badge badge-danger'>Live</span>"
        else:
            m = "<span class='badge badge-secondary'>Off</span>"
        return mark_safe(m)


    def get_user_sessions(self, obj):
        url = (
            reverse('admin:meet_usersession_changelist')
            + f'?session_id__exact={obj.id}'
        )
        user_session_count = UserSession.objects.filter(session__id=obj.id).count()
        return format_html('<a href="{}">{}</a>', url, user_session_count)
    get_user_sessions.short_description = 'UserSessions'
    
    def get_recordings(self, obj):
        url = (
            reverse('admin:meet_sessionrecording_changelist')
            + f'?session_id__exact={obj.id}'
        )
        recording_count = SessionRecording.objects.filter(session__id=obj.id).count()
        return format_html('<a href="{}">{}</a>', url, recording_count)
    get_recordings.short_description = 'Recordings'



@admin.register(UserSession)
class UserSessionAdmin(AjaxDatatable):
    autocomplete_fields = ['user', 'session']
    search_fields = ['user__username', 'session__subject']
    list_display = ['user', 'session', 'role', 'exited_at', '_is_online','audio_access', 'video_access', 'is_raise_hand']

    @admin.display(description='is_online', ordering='is_online')
    def _is_online(self, obj):
        if obj.is_online:
            m = "<span class='badge badge-danger'>Online</span>"
        else:
            m = "<span class='badge badge-secondary'>Offline</span>"
        return mark_safe(m)

@admin.register(Report)
class ReportAdmin(AjaxDatatable):
    autocomplete_fields = ['user', 'meet']
    search_fields = ['user__username', 'meet__name']
    list_display = ['user', 'meet', 'description', 'report_type']

@admin.register(Observer)
class ObserverAdmin(AjaxDatatable):
    autocomplete_fields = ['user',]
    search_fields = ['user__username',]
    list_display = ['user', 'is_active']

@admin.register(UserPurchases)
class UserPurchasesAdmin(AjaxDatatable):
    autocomplete_fields = ['user', 'meet']
    search_fields = ['user__username', 'meet__name']
    list_display = ['user', 'meet', 'amount']

@admin.register(UserDialogue)
class UserDialogueAdmin(AjaxDatatable):
    autocomplete_fields = ['user', 'session']
    search_fields = ['user__username', 'session__subject']
    list_display = ['user', 'session', 'from_time', 'to_time', 'dialogue_type']


@admin.register(SessionRecording)
class SessionRecordingAdmin(AjaxDatatable):
    autocomplete_fields = ['session']
    search_fields = ['title', 'session__subject', 'session__meet__name']
    list_display = ['title', 'session', 'file_time', 'recording_type', 'created_at']
    list_filter = ('recording_type', 'created_at', MeetFilter)
    readonly_fields = ['created_at']
    fields = ['title', 'session', 'file', 'file_time', 'recording_type', 'thumbnail', 'is_active', 'created_at']
