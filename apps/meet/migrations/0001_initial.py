# Generated by Django 3.2.25 on 2024-06-15 07:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Institute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the institute', max_length=255, verbose_name='Name')),
                ('avatar', models.ImageField(help_text='Avatar image of the institute', upload_to='avatars/', verbose_name='Avatar')),
                ('owner', models.ForeignKey(help_text='Owner of the institute', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
            ],
        ),
        migrations.CreateModel(
            name='Meet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the meet', max_length=255, verbose_name='Name')),
                ('next_session_subject', models.CharField(help_text='Subject of the next session', max_length=255, verbose_name='Next Session Subject')),
                ('next_session_schedule', models.DateTimeField(help_text='Scheduled date and time for the next session', verbose_name='Next Session Schedule')),
                ('is_suggested', models.BooleanField(default=False, help_text='Is this meet suggested?', verbose_name='Is Suggested')),
                ('is_free', models.BooleanField(default=False, help_text='Is this meet free?', verbose_name='Is Free')),
                ('price', models.DecimalField(decimal_places=2, help_text='Price of the meet if it is not free', max_digits=10, verbose_name='Price')),
                ('is_live', models.BooleanField(default=False, help_text='Is the meet currently live?', verbose_name='Is Live')),
                ('language', models.CharField(help_text='Language of the meet', max_length=100, verbose_name='Language')),
                ('categories', models.CharField(help_text='Categories related to the meet', max_length=255, verbose_name='Categories')),
                ('organizer', models.ForeignKey(help_text='Organizer of the meet', on_delete=django.db.models.deletion.CASCADE, to='meet.institute', verbose_name='Organizer')),
                ('provider', models.ForeignKey(help_text='Owner of the meet', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Provider')),
            ],
        ),
        migrations.CreateModel(
            name='Session',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('started_at', models.DateTimeField(help_text='Session start time', verbose_name='Started At')),
                ('ended_at', models.DateTimeField(help_text='Session end time', verbose_name='Ended At')),
                ('recorded_file', models.FileField(help_text='File of the recorded session', upload_to='recorded_sessions/', verbose_name='Recorded File')),
                ('subject', models.CharField(help_text='Subject of the session', max_length=255, verbose_name='Subject')),
                ('meet', models.ForeignKey(help_text='The meet this session belongs to', on_delete=django.db.models.deletion.CASCADE, to='meet.meet', verbose_name='Meet')),
            ],
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('participant', 'Participant'), ('moderator', 'Moderator')], help_text='Role of the user in the session', max_length=50, verbose_name='Role')),
                ('entered_at', models.DateTimeField(help_text='Time the user entered the session', verbose_name='Entered At')),
                ('exited_at', models.DateTimeField(help_text='Time the user exited the session', verbose_name='Exited At')),
                ('audio_access', models.BooleanField(default=True, help_text='Does the user have audio access?', verbose_name='Audio Access')),
                ('video_access', models.BooleanField(default=True, help_text='Does the user have video access?', verbose_name='Video Access')),
                ('is_raise_hand', models.BooleanField(default=False, help_text='Has the user raised their hand?', verbose_name='Is Raise Hand')),
                ('session', models.ForeignKey(help_text='Session the user is attending', on_delete=django.db.models.deletion.CASCADE, to='meet.session', verbose_name='Session')),
                ('user', models.ForeignKey(help_text='User attending the session', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
        ),
        migrations.CreateModel(
            name='UserPurchases',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, help_text='Amount of the purchase', max_digits=10, verbose_name='Amount')),
                ('meet', models.ForeignKey(help_text='Meet being purchased', on_delete=django.db.models.deletion.CASCADE, to='meet.meet', verbose_name='Meet')),
                ('user', models.ForeignKey(help_text='User making the purchase', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
        ),
        migrations.CreateModel(
            name='UserDialogue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_time', models.DateTimeField(help_text='Start time of the dialogue', verbose_name='From')),
                ('to_time', models.DateTimeField(help_text='End time of the dialogue', verbose_name='To')),
                ('dialogue_type', models.CharField(choices=[('audio', 'Audio'), ('video', 'Video')], help_text='Type of the dialogue (audio or video)', max_length=50, verbose_name='Type')),
                ('session', models.ForeignKey(help_text='Session in which the dialogue occurs', on_delete=django.db.models.deletion.CASCADE, to='meet.session', verbose_name='Session')),
                ('user', models.ForeignKey(help_text='User participating in the dialogue', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(help_text='Description of the report', verbose_name='Description')),
                ('report_type', models.CharField(choices=[('bug', 'Bug'), ('feedback', 'Feedback'), ('other', 'Other')], help_text='Type of the report', max_length=50, verbose_name='Type')),
                ('meet', models.ForeignKey(help_text='Meet being reported on', on_delete=django.db.models.deletion.CASCADE, to='meet.meet', verbose_name='Meet')),
                ('user', models.ForeignKey(help_text='User reporting', on_delete=django.db.models.deletion.CASCADE, related_name='reporter', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
        ),
        migrations.CreateModel(
            name='Observer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language', models.CharField(help_text='Language preference of the observer', max_length=100, verbose_name='Language')),
                ('meet', models.ForeignKey(help_text='Meet being observed', on_delete=django.db.models.deletion.CASCADE, to='meet.meet', verbose_name='Meet')),
                ('user', models.ForeignKey(help_text='User observing the meet', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
        ),
    ]
