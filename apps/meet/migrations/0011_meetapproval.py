# Generated by Django 3.2.25 on 2024-07-22 15:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0040_alter_providerrequest_service'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('meet', '0010_meet_is_active'),
    ]

    operations = [
        migrations.CreateModel(
            name='MeetApproval',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_approved', models.BooleanField(default=False, help_text='Has the request been approved?', verbose_name='Is Approved')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('meet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='approvals', to='meet.meet')),
                ('request', models.ForeignKey(help_text='Meet Verification of the meet', on_delete=django.db.models.deletion.CASCADE, related_name='meet_request', to='account.providerrequest', verbose_name='Meet Request')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meet_approvals', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('meet', 'user')},
            },
        ),
    ]
