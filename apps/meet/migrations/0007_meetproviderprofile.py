# Generated by Django 3.2.25 on 2024-07-22 12:45

import apps.meet.models.provider
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import filer.fields.image


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dj_language', '0002_auto_20220120_1344'),
        migrations.swappable_dependency(settings.FILER_IMAGE_MODEL),
        ('account', '0039_activity'),
        ('meet', '0006_usersession_is_microphone_active'),
    ]

    operations = [
        migrations.CreateModel(
            name='MeetProviderProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=254, null=True, verbose_name='full name')),
                ('bio', models.CharField(blank=True, max_length=512, null=True, verbose_name='bio')),
                ('birthdate', models.DateField(blank=True, null=True, verbose_name='birthdate')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='email')),
                ('wa_number', models.CharField(blank=True, max_length=255, null=True, validators=[apps.meet.models.provider.validate_wa_number], verbose_name='whatsapp number')),
                ('institution_name', models.CharField(blank=True, help_text='Name of the institute', max_length=255, null=True, verbose_name='Name')),
                ('social_medias', models.JSONField(default=apps.meet.models.provider.default_social_media, help_text='Provider social media profiles', verbose_name='social medias')),
                ('avatar', filer.fields.image.FilerImageField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.FILER_IMAGE_MODEL, verbose_name='avatar')),
                ('languages', models.ManyToManyField(limit_choices_to={'status': True}, to='dj_language.Language', verbose_name='languages')),
                ('request', models.ForeignKey(help_text='Provider Request of the meet', on_delete=django.db.models.deletion.CASCADE, related_name='provider_request', to='account.providerrequest', verbose_name='Provider Request')),
                ('user', models.ForeignKey(help_text='Provider Profile of the meet', on_delete=django.db.models.deletion.CASCADE, related_name='provider_profile', to=settings.AUTH_USER_MODEL, verbose_name='Provider Profile')),
            ],
            options={
                'verbose_name': 'Meet Provider Profile',
                'verbose_name_plural': 'Meet Provider Profiles',
            },
        ),
    ]
