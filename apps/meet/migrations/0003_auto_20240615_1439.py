# Generated by Django 3.2.25 on 2024-06-15 14:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('meet', '0002_auto_20240615_0719'),
    ]

    operations = [
        migrations.AlterField(
            model_name='report',
            name='description',
            field=models.TextField(default=None, help_text='Description of the report', null=True, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='session',
            name='ended_at',
            field=models.DateTimeField(default=None, help_text='Session end time', null=True, verbose_name='Ended At'),
        ),
        migrations.AlterField(
            model_name='session',
            name='recorded_file',
            field=models.FileField(default=None, help_text='File of the recorded session', null=True, upload_to='recorded_sessions/', verbose_name='Recorded File'),
        ),
        migrations.AlterField(
            model_name='session',
            name='subject',
            field=models.Char<PERSON>ield(default=None, help_text='Subject of the session', max_length=255, null=True, verbose_name='Subject'),
        ),
        migrations.AlterField(
            model_name='userdialogue',
            name='to_time',
            field=models.DateTimeField(default=None, help_text='End time of the dialogue', null=True, verbose_name='To'),
        ),
        migrations.AlterField(
            model_name='usersession',
            name='exited_at',
            field=models.DateTimeField(default=None, help_text='Time the user exited the session', null=True, verbose_name='Exited At'),
        ),
    ]
