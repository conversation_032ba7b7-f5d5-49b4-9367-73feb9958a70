# Generated manually to update recording_type choices from 'audio' to 'voice'

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('meet', '0027_add_thumbnail_to_session_recording'),
    ]

    operations = [
        # Update existing 'audio' values to 'voice'
        migrations.RunSQL(
            "UPDATE meet_sessionrecording SET recording_type = 'voice' WHERE recording_type = 'audio';",
            reverse_sql="UPDATE meet_sessionrecording SET recording_type = 'audio' WHERE recording_type = 'voice';"
        ),
        
        # Update the field choices
        migrations.AlterField(
            model_name='sessionrecording',
            name='recording_type',
            field=models.CharField(
                choices=[('voice', 'Voice'), ('video', 'Video')], 
                help_text='Type of the recording (voice or video)', 
                max_length=10, 
                verbose_name='Recording Type'
            ),
        ),
    ]
