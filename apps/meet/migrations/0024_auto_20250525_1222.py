# Generated by Django 3.2.25 on 2025-05-25 12:22

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('meet', '0023_auto_20250430_0015'),
    ]

    operations = [
        migrations.AddField(
            model_name='remindermeet',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Whether this reminder is active or not', verbose_name='Is Active'),
        ),
        migrations.AddField(
            model_name='remindermeet',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, help_text='The datetime when the reminder was last updated', verbose_name='Updated At'),
        ),
        migrations.CreateModel(
            name='SessionRecording',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, default=None, help_text='Title of the recording', max_length=255, null=True, verbose_name='Title')),
                ('file', models.FileField(help_text='File of the recorded session', upload_to='recorded_sessions/', verbose_name='Recording File')),
                ('recording_type', models.CharField(choices=[('audio', 'Audio'), ('video', 'Video')], help_text='Type of the recording (audio or video)', max_length=10, verbose_name='Recording Type')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Time the recording was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='The datetime when the recording was last updated', verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this recording is active or not', verbose_name='Is Active')),
                ('session', models.ForeignKey(help_text='The session this recording belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='recordings', to='meet.session', verbose_name='Session')),
            ],
        ),
    ]
