# Generated by Django 3.2.25 on 2024-06-15 07:19

import dj_language.field
from django.db import migrations
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
        ('meet', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='meet',
            name='language',
            field=dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language'),
        ),
        migrations.AlterField(
            model_name='observer',
            name='language',
            field=dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language'),
        ),
    ]
