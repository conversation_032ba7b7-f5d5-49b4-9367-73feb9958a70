
from rest_framework import permissions

from apps.meet.models import MeetProviderProfile
class IsProviderOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow providers of a Meet to edit or delete it.
    """

    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the provider of the Meet.
        return obj.provider == request.user
    

class IsVerifiedMeetProvider(permissions.BasePermission):
    """
    Custom permission to only allow access to users with a verified MeetProviderProfile.
    """

    def has_permission(self, request, view):
        # Check if the user has a verified MeetProviderProfile
        return MeetProviderProfile.objects.filter(user=request.user, is_verified=True).exists()