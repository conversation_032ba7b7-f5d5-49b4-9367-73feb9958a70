{% if messages %}
    <!-- Messages Summary -->
    <div class="messages-summary">
        <i class="fas fa-info-circle"></i>
        Showing all {{ total_messages }} message{{ total_messages|pluralize }}
        {% if search %}for search: "{{ search }}"{% endif %}
    </div>

    {% for message in messages %}
        <div class="message-item">
            <div class="message-header">
                <div class="user-avatar">
                    {% if message.user_session.user.first_name %}
                        {{ message.user_session.user.first_name|first|upper }}
                    {% elif message.user_session.user.name %}
                        {{ message.user_session.user.name|first|upper }}
                    {% else %}
                        {{ message.user_session.user.username|first|upper }}
                    {% endif %}
                </div>
                <div class="user-info">
                    <div class="user-name">
                        {% if message.user_session.user.first_name %}
                            {{ message.user_session.user.first_name }}
                        {% elif message.user_session.user.name %}
                            {{ message.user_session.user.name }}
                        {% else %}
                            {{ message.user_session.user.username }}
                        {% endif %}
                        {% if message.user_session.user.email %}
                            <span style="color: #6c757d; font-weight: normal; font-size: 13px;">
                                ({{ message.user_session.user.email }})
                            </span>
                        {% endif %}
                    </div>
                    <div class="message-time">
                        <i class="fas fa-clock"></i>
                        {{ message.created_at|date:"M d, Y H:i" }}
                        {% if message.user_session.session %}
                            <span style="margin-left: 10px;">
                                <i class="fas fa-video"></i>
                                Session: {{ message.user_session.session.subject|default:"Untitled" }}
                            </span>
                        {% endif %}
                    </div>
                </div>
                {% if message.has_read %}
                    <div style="color: #28a745; font-size: 12px;">
                        <i class="fas fa-check-double"></i>
                        Read
                    </div>
                {% else %}
                    <div style="color: #6c757d; font-size: 12px;">
                        <i class="fas fa-check"></i>
                        Unread
                    </div>
                {% endif %}
            </div>
            <div class="message-content">
                {{ message.content|linebreaks }}
            </div>
        </div>
    {% endfor %}

{% else %}
    <div class="no-messages">
        <i class="fas fa-comment-slash"></i>
        <h3>No messages found</h3>
        {% if search %}
            <p>No messages match your search: "{{ search }}"</p>
            <button class="pagination-btn" 
                    hx-get="{% url 'meet_chats_messages' meet.id %}"
                    hx-target="#messages-content"
                    onclick="document.querySelector('.search-input').value = ''">
                <i class="fas fa-times"></i>
                Clear Search
            </button>
        {% else %}
            <p>This meet doesn't have any chat messages yet.</p>
        {% endif %}
    </div>
{% endif %}
