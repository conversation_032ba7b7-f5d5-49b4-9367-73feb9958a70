<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Messages - {{ meet.name }}</title>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .chat-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            height: 90vh;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .chat-stats {
            display: flex;
            gap: 20px;
            font-size: 14px;
            opacity: 0.9;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
        }

        .search-container {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .search-box {
            position: relative;
            max-width: 400px;
        }

        .search-input {
            width: 100%;
            padding: 12px 45px 12px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }

        .search-input:focus {
            outline: none;
            border-color: #1e40af;
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            cursor: pointer;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px 30px;
            background: #f8f9fa;
        }

        .message-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .message-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 12px;
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 16px;
            flex-shrink: 0;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
            margin-bottom: 2px;
        }

        .message-time {
            color: #6c757d;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .message-content {
            color: #495057;
            line-height: 1.6;
            font-size: 15px;
            word-wrap: break-word;
        }

        /* Persian/Arabic text direction */
        .message-content.rtl {
            direction: rtl;
            text-align: right;
        }

        /* English text direction */
        .message-content.ltr {
            direction: ltr;
            text-align: left;
        }

        /* Summary box styles */
        .messages-summary {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 20px;
            margin-bottom: 25px;
            border-radius: 15px;
            text-align: center;
            color: #1565c0;
            font-weight: 600;
            border: 2px solid #90caf9;
            box-shadow: 0 4px 12px rgba(21, 101, 192, 0.1);
        }

        .messages-summary i {
            font-size: 18px;
            margin-right: 8px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .loading i {
            font-size: 24px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-messages {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .no-messages i {
            font-size: 48px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .chat-container {
                height: 95vh;
                border-radius: 15px;
            }
            
            .chat-header {
                padding: 20px;
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .chat-stats {
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .search-container,
            .messages-container,
            .pagination-container {
                padding: 15px 20px;
            }
            
            .message-item {
                padding: 15px;
            }
            
            .user-avatar {
                width: 40px;
                height: 40px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- Header -->
        <div class="chat-header">
            <div>
                <h1>
                    <i class="fas fa-comments"></i>
                    Chat Messages: {{ meet.name }}
                </h1>
            </div>
            <div class="chat-stats">
                <div class="stat-item">
                    <i class="fas fa-message"></i>
                    <span>{{ total_messages }} Messages</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <span>{{ unique_users }} Users</span>
                </div>
                <a href="{% url 'admin:meet_meet_changelist' %}" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    Back to Meets
                </a>
            </div>
        </div>

        <!-- Search -->
        <div class="search-container">
            <div class="search-box">
                <input 
                    type="text" 
                    class="search-input" 
                    placeholder="Search messages, users..."
                    hx-get="{% url 'meet_chats_messages' meet.id %}"
                    hx-target="#messages-content"
                    hx-trigger="keyup changed delay:500ms"
                    name="search"
                >
                <i class="fas fa-search search-icon"></i>
            </div>
        </div>

        <!-- Messages Container -->
        <div class="messages-container">
            <div id="messages-content" 
                 hx-get="{% url 'meet_chats_messages' meet.id %}"
                 hx-trigger="load">
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>Loading messages...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to detect text direction
        function detectTextDirection(text) {
            // Persian/Arabic Unicode ranges
            const persianArabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
            return persianArabicRegex.test(text) ? 'rtl' : 'ltr';
        }

        // Function to apply text direction to messages
        function applyTextDirection() {
            const messageContents = document.querySelectorAll('.message-content');
            messageContents.forEach(function(element) {
                const text = element.textContent || element.innerText;
                const direction = detectTextDirection(text);
                element.classList.remove('rtl', 'ltr');
                element.classList.add(direction);
            });
        }

        // Auto-refresh every 30 seconds
        setInterval(function() {
            htmx.trigger('#messages-content', 'load');
        }, 30000);

        // Handle search input focus
        document.querySelector('.search-input').addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        document.querySelector('.search-input').addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });

        // Apply text direction when page loads
        document.addEventListener('DOMContentLoaded', function() {
            applyTextDirection();
        });

        // Apply text direction after HTMX loads new content
        document.body.addEventListener('htmx:afterSwap', function() {
            applyTextDirection();
        });
    </script>
</body>
</html>
