<!DOCTYPE html>
<html lang="en" x-data="roomObserver()">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title x-text="roomData.title || 'Observer Dashboard'">Observer Dashboard</title>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --background-dark: #1a1a2e;
            --background-light: #16213e;
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
        }
        
        body {
            background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-light) 100%);
            color: var(--text-primary);
            font-family: 'Inter', sans-serif;
            min-height: 100vh;
        }
        
        .glass-morphism {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
        }
        
        .gradient-text {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .participant-card {
            transition: all 0.3s ease;
        }
        
        .participant-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online { background-color: #10b981; }
        .status-offline { background-color: #ef4444; }
        .status-away { background-color: #f59e0b; }
        
        .mic-active { color: #10b981; }
        .mic-muted { color: #ef4444; }
        
        .hand-raised { color: #f59e0b; animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .loading-spinner {
            border: 3px solid var(--glass-border);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div class="sidebar w-64 glass-morphism m-4 p-6 hidden lg:block" id="sidebar">
            {% include 'observer/components/sidebar.html' %}
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Header -->
            <div class="glass-morphism m-4 mb-0">
                {% include 'observer/components/header.html' %}
            </div>
            
            <!-- Content Area -->
            <div class="flex-1 p-4">
                <!-- Room Info -->
                <div class="glass-morphism p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h1 class="text-2xl font-bold gradient-text" x-text="roomData.title || 'Loading...'">Loading...</h1>
                            <p class="text-gray-300 mt-2" x-text="roomData.topic || 'No topic available'">No topic available</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-400" x-text="participantCount">0</div>
                                <div class="text-sm text-gray-400">Participants</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-400" x-text="onlineCount">0</div>
                                <div class="text-sm text-gray-400">Online</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status Indicators -->
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center">
                            <span class="status-indicator" :class="roomData.isLive ? 'status-online' : 'status-offline'"></span>
                            <span x-text="roomData.isLive ? 'Live' : 'Offline'">Offline</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2 text-gray-400"></i>
                            <span x-text="formatTime(roomData.startTime)">--:--</span>
                        </div>
                        <div class="flex items-center" x-show="isLoading">
                            <div class="loading-spinner mr-2"></div>
                            <span>Updating...</span>
                        </div>
                    </div>
                </div>
                
                <!-- Participants Grid -->
                <div class="glass-morphism p-6">
                    {% include 'observer/components/participant_grid.html' %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Mobile Sidebar Toggle -->
    <button class="lg:hidden fixed top-4 left-4 z-50 glass-morphism p-3 rounded-lg" @click="toggleSidebar()">
        <i class="fas fa-bars text-white"></i>
    </button>
    
    <script>
        function roomObserver() {
            return {
                roomData: {
                    title: '',
                    topic: '',
                    isLive: false,
                    startTime: null
                },
                participants: [],
                isLoading: false,
                refreshInterval: null,
                
                init() {
                    this.loadRoomData();
                    this.startAutoRefresh();
                },
                
                async loadRoomData() {
                    this.isLoading = true;
                    try {
                        const response = await fetch(window.location.pathname + 'api/');
                        if (response.ok) {
                            const data = await response.json();
                            this.roomData = data.room || {};
                            this.participants = data.participants || [];
                        }
                    } catch (error) {
                        console.error('Failed to load room data:', error);
                    } finally {
                        this.isLoading = false;
                    }
                },
                
                startAutoRefresh() {
                    this.refreshInterval = setInterval(() => {
                        this.loadRoomData();
                    }, 2000);
                },
                
                stopAutoRefresh() {
                    if (this.refreshInterval) {
                        clearInterval(this.refreshInterval);
                        this.refreshInterval = null;
                    }
                },
                
                get participantCount() {
                    return this.participants.length;
                },
                
                get onlineCount() {
                    return this.participants.filter(p => p.isOnline).length;
                },
                
                formatTime(timestamp) {
                    if (!timestamp) return '--:--';
                    return new Date(timestamp).toLocaleTimeString();
                },
                
                toggleSidebar() {
                    const sidebar = document.getElementById('sidebar');
                    sidebar.classList.toggle('open');
                },
                
                destroy() {
                    this.stopAutoRefresh();
                }
            }
        }
    </script>
</body>
</html>
