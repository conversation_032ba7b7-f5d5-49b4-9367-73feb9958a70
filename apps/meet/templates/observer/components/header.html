<div class="flex items-center justify-between p-4">
    <!-- Left Section: Room Title and Topic -->
    <div class="flex items-center space-x-4">
        <!-- Room Title -->
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-video text-white text-lg"></i>
            </div>
            <div>
                <h2 class="text-xl font-semibold text-white" x-text="roomData.title || 'Room Title'">
                    Room Title
                </h2>
                <p class="text-sm text-gray-300" x-text="roomData.topic || 'No topic set'">
                    No topic set
                </p>
            </div>
        </div>
        
        <!-- Live Status Badge -->
        <div class="flex items-center space-x-2">
            <div class="flex items-center px-3 py-1 rounded-full text-sm font-medium"
                 :class="roomData.isLive ? 'bg-green-500/20 text-green-400 border border-green-500/30' : 'bg-gray-500/20 text-gray-400 border border-gray-500/30'">
                <span class="w-2 h-2 rounded-full mr-2"
                      :class="roomData.isLive ? 'bg-green-400 animate-pulse' : 'bg-gray-400'"></span>
                <span x-text="roomData.isLive ? 'LIVE' : 'OFFLINE'">OFFLINE</span>
            </div>
        </div>
    </div>
    
    <!-- Center Section: Room Stats -->
    <div class="hidden md:flex items-center space-x-6">
        <!-- Participant Count -->
        <div class="flex items-center space-x-2 text-gray-300">
            <i class="fas fa-users text-blue-400"></i>
            <span class="text-sm">
                <span x-text="participantCount" class="font-semibold text-white">0</span>
                <span>participants</span>
            </span>
        </div>
        
        <!-- Online Count -->
        <div class="flex items-center space-x-2 text-gray-300">
            <i class="fas fa-circle text-green-400"></i>
            <span class="text-sm">
                <span x-text="onlineCount" class="font-semibold text-white">0</span>
                <span>online</span>
            </span>
        </div>
        
        <!-- Duration -->
        <div class="flex items-center space-x-2 text-gray-300" x-show="roomData.isLive">
            <i class="fas fa-clock text-yellow-400"></i>
            <span class="text-sm font-mono" x-text="formatDuration(roomData.startTime)">00:00</span>
        </div>
    </div>
    
    <!-- Right Section: Controls and Menu -->
    <div class="flex items-center space-x-3">
        <!-- Refresh Button -->
        <button @click="loadRoomData()" 
                class="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200 group"
                :disabled="isLoading">
            <i class="fas fa-sync-alt text-gray-300 group-hover:text-white transition-colors duration-200"
               :class="{ 'animate-spin': isLoading }"></i>
        </button>
        
        <!-- Auto-refresh Toggle -->
        <button @click="toggleAutoRefresh()" 
                class="p-2 rounded-lg transition-colors duration-200"
                :class="refreshInterval ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30' : 'bg-white/10 text-gray-300 hover:bg-white/20 hover:text-white'">
            <i class="fas fa-play" x-show="!refreshInterval"></i>
            <i class="fas fa-pause" x-show="refreshInterval"></i>
        </button>
        
        <!-- Settings Menu -->
        <div class="relative" x-data="{ open: false }">
            <button @click="open = !open" 
                    class="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200 group">
                <i class="fas fa-cog text-gray-300 group-hover:text-white transition-colors duration-200"></i>
            </button>
            
            <!-- Dropdown Menu -->
            <div x-show="open" 
                 @click.away="open = false"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 scale-95"
                 x-transition:enter-end="opacity-100 scale-100"
                 x-transition:leave="transition ease-in duration-75"
                 x-transition:leave-start="opacity-100 scale-100"
                 x-transition:leave-end="opacity-0 scale-95"
                 class="absolute right-0 mt-2 w-48 glass-morphism rounded-lg shadow-lg z-50">
                <div class="py-2">
                    <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-white/10 hover:text-white transition-colors duration-200">
                        <i class="fas fa-download mr-2"></i>
                        Export Data
                    </button>
                    <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-white/10 hover:text-white transition-colors duration-200">
                        <i class="fas fa-bell mr-2"></i>
                        Notifications
                    </button>
                    <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-white/10 hover:text-white transition-colors duration-200">
                        <i class="fas fa-palette mr-2"></i>
                        Theme
                    </button>
                    <hr class="my-2 border-gray-600">
                    <button class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-white/10 hover:text-white transition-colors duration-200">
                        <i class="fas fa-question-circle mr-2"></i>
                        Help
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu Toggle -->
        <button @click="toggleSidebar()" 
                class="lg:hidden p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200 group">
            <i class="fas fa-bars text-gray-300 group-hover:text-white transition-colors duration-200"></i>
        </button>
    </div>
</div>

<script>
    // Additional header-specific functions
    function formatDuration(startTime) {
        if (!startTime) return '00:00';
        
        const start = new Date(startTime);
        const now = new Date();
        const diff = Math.floor((now - start) / 1000);
        
        const hours = Math.floor(diff / 3600);
        const minutes = Math.floor((diff % 3600) / 60);
        const seconds = diff % 60;
        
        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    
    function toggleAutoRefresh() {
        if (this.refreshInterval) {
            this.stopAutoRefresh();
        } else {
            this.startAutoRefresh();
        }
    }
</script>
