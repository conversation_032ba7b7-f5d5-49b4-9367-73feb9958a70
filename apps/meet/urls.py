from django.urls import path
from . import views
from .views.meet_v2 import MeetCreateV2View


urlpatterns = [
    # URL for retrieving a list of meets with complete details.
    path('habibmeet/meets/', views.MeetListView.as_view(), name='meet-list'),
    path('habibmeet/meets/create/', views.MeetCreateView.as_view(), name='meet-create'),
    path('habibmeet/meets/create/v2/', MeetCreateV2View.as_view(), name='meet-create-v2'),
    path('habibmeet/meets/room/', views.MeetProviderRoomView.as_view(), name='meet-rooms'),

    path('meets/<int:meet_id>/invite/', views.meet_link, name='meet-rooms'),

    path('habibmeet/meets/<int:meet_id>/session/', views.SessionCreateView.as_view(), name='meet-session'),
    
    path('habibmeet/meets/<int:meet_id>/session/', views.SessionCreateView.as_view(), name='meet-session'),
    path('habibmeet/meets/<int:meet_id>/schedule/', views.SessionScheduleDeleteView.as_view(), name='meet-session-schedule-delete'),
    path('habibmeet/meets/<int:meet_id>/close/', views.CloseSessionView.as_view(), name='meet-session-close'),
    # path('habibmeet/meets/<int:meet_id>/reminder/', views.AddReminderView.as_view(), name='add-reminder'),
    # path('habibmeet/meets/<int:meet_id>/reminder/remove/', views.RemoveReminderView.as_view(), name='remove-reminder'),
    path('habibmeet/meets/<int:meet_id>/reminder/update/', views.UpdateReminderStatusView.as_view(), name='update-reminder-status'),

    path('habibmeet/meets/reminders/', views.UserRemindersListView.as_view(), name='user-reminders'),

    # URL for posting a report on a meet.
    path('habibmeet/meets/<int:meet_id>/report/', views.MeetReportCreateView.as_view(), name='meet-report'),
    
    # URL for retrieving a particular meet with complete information.
    path('habibmeet/meets/<int:meet_id>/', views.MeetDetailView.as_view(), name='meet-detail'),

    # URL for archiving recorded sessions of a meet.
    path('habibmeet/meets/<int:meet_id>/archive/', views.SessionArchiveListView.as_view(), name='meet-list-archive'),
    path('habibmeet/sessions/<int:id>/upload/', views.SessionFileUploadView.as_view(), name='session-file-upload'),

    path('habibmeet/meets/archive/<int:session_id>/', views.SessionArchiveDeleteView.as_view(), name='session-archive-delete'),

    # URL for listing session recordings by meet ID
    path('habibmeet/<int:id>/recordings/', views.SessionRecordingListView.as_view(), name='session-recordings-list'),

    # URL for deleting session recordings by recording ID (provider only)
    path('habibmeet/recordings/<int:recording_id>/delete/', views.SessionRecordingDeleteView.as_view(), name='session-recording-delete'),

    # URL for creating session recordings
    path('habibmeet/recordings/create/', views.SessionRecordingCreateView.as_view(), name='session-recording-create'),

    # URL for updating session recordings by recording ID (provider only)
    path('habibmeet/recordings/<int:recording_id>/update/', views.SessionRecordingUpdateView.as_view(), name='session-recording-update'),

    path('habibmeet/meets/provider/profile/', views.MeetProviderProfileCreateView.as_view(), name='meet-provider-profile'),
    path('habibmeet/meets/providers/', views.ProviderListView.as_view(), name='meet-providers'),
    path('habibmeet/meets/providers/<int:id>/', views.ProviderDetailView.as_view(), name='meet-provider-detail'),
    

    path('habibmeet/guidelines/', views.GuidelineListView.as_view(), name='meet-guideline'),

    # Admin Chat URLs
    path('admin/meet/<int:meet_id>/chats/', views.AdminChatView.as_view(), name='meet_chats_view'),
    path('admin/meet/<int:meet_id>/chats/messages/', views.AdminChatMessagesView.as_view(), name='meet_chats_messages'),

]