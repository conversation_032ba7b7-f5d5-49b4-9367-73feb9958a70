
from rest_framework import serializers

from apps.notification.models import <PERSON>minder
from apps.meet.models import Meet


class MeetReminderSerializer(serializers.ModelSerializer):
    """
    Backward compatible serializer for meet reminders.
    Maps new Reminder model fields to old ReminderMeet response format.
    """
    user = serializers.IntegerField(source='user.id', read_only=True)
    meet = serializers.SerializerMethodField()
    notification_sent = serializers.BooleanField(source='is_sent', read_only=True)
    notification_sent_at = serializers.DateTimeField(source='created_at', read_only=True)
    is_active = serializers.BooleanField(source='status', read_only=True)

    class Meta:
        model = Reminder
        fields = ['id', 'user', 'meet', 'created_at', 'notification_sent', 'notification_sent_at', 'is_active']
        read_only_fields = ['id', 'user', 'meet', 'created_at', 'notification_sent', 'notification_sent_at']
        ref_name = 'MeetReminderSerializer'

    def get_meet(self, obj):
        """Return meet ID from object_id field"""
        try:
            return int(obj.object_id)
        except (ValueError, TypeError):
            return None
        
        
        