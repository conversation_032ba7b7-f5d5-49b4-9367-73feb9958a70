from dj_language.serializer import LanguageSerializer
from rest_framework import serializers
from dj_filer.admin import get_thumbs
from dj_language.models import Language
from rest_framework.exceptions import ValidationError
from utils.tmp_media import FileFieldSerializer

from apps.account.models import User, PushMessage
from apps.meet.models import Meet, Session, Report, Institute, MeetProviderProfile, UserSession, UserPurchases, SessionRecording
from apps.notification.models import <PERSON>minder
from .provider import ProviderSerializer



class MeetCreateSerializer(serializers.ModelSerializer):
    language = serializers.CharField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False,default=0, allow_null=True)

    class Meta:
        model = Meet
        fields = [
            'name', 'price','language','description', 'is_free'
        ]
        
    def validate(self, data):
        """
        Ensure that the price is set to 0 or None if the meet is free.
        """
        if data.get('is_free'):
            data['price'] = 0
        language_code = data.get('language')
        if not Language.objects.filter(code=language_code).exists():
            raise serializers.ValidationError({"language": "Invalid language code."})

        data['language'] = Language.objects.get(code=language_code)

        return data

class InstituteSerializer(serializers.ModelSerializer):
    owner = serializers.SerializerMethodField()
    class Meta:
        model = Institute
        fields = ['id', 'name', 'owner','avatar']
    
    def get_owner(self, obj):
        return ProviderSerializer(obj.owner, context=self.context).data

class MeetSerializer(serializers.ModelSerializer):
    provider = serializers.SerializerMethodField()
    language = LanguageSerializer(read_only=True)
    organizer = serializers.SerializerMethodField()
    online_users_count = serializers.IntegerField(source="get_online_users_count")
    is_closed = serializers.SerializerMethodField()
    is_provider = serializers.SerializerMethodField()
    has_access = serializers.SerializerMethodField()
    is_reminder = serializers.SerializerMethodField()
    live_session_id = serializers.SerializerMethodField()
    has_archive = serializers.SerializerMethodField()
    has_recordings = serializers.SerializerMethodField()

    class Meta:
        ref_name = 'MeetSerializer'
        model = Meet
        fields = ['id', 'name', 'description','next_session_subject', 'next_session_schedule', 'is_suggested', 'provider', 'is_free', 'price', 'is_live', 'is_closed','online_users_count','language', 'categories', 'organizer', 'is_provider','has_access', 'is_reminder', 'live_session_id', 'has_archive', 'has_recordings', 'access', 'password']
    
    

    def get_has_archive(self, obj):
        # Check if any session of this meet has active recordings
        sessions = obj.session_set.filter(ended_at__isnull=False)
        for session in sessions:
            if SessionRecording.objects.filter(session=session, is_active=True).exists():
                return True
        return False
    
    
    def get_live_session_id(self, obj):
        if obj.is_live:
            live_session = Session.objects.filter(meet=obj, ended_at__isnull=True).order_by('-started_at').first()
            if live_session:
                return live_session.id
        return None
            
    def get_has_access(self, obj):
        user = self.context.get('request').user
        if obj.is_free or obj.price == 0:
            return True
        # Check if the user has purchased the meet
        if obj.provider == user:
            return True
        return UserPurchases.objects.filter(user=user, meet=obj).exists()
    
    def get_is_provider(self, obj):
        user = self.context.get('request').user
        if obj.provider == user:
            return True
        return False

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['language'] = instance.language.code
        return representation
    
    def get_provider(self, obj):
        provider_profile = MeetProviderProfile.objects.filter(user=obj.provider, is_verified=True).first()
        if provider_profile:
            return ProviderSerializer(provider_profile, context=self.context).data
        return None

    def get_organizer(self, obj):
        return InstituteSerializer(obj.organizer, context=self.context).data
    
    def get_is_closed(self, obj):
        return obj.is_closed()
    
    def get_is_reminder(self, obj):
        user = self.context.get('request').user
        if user.is_authenticated:
            # Check if user has an active reminder for this meet
            return Reminder.objects.filter(
                user=user,
                service_name='meet',
                status=True,
                object_id=str(obj.id)
            ).exists()
        return False

    def get_has_recordings(self, obj):
        # Check if any session of this meet has active recordings
        sessions = obj.session_set.filter(ended_at__isnull=False)
        for session in sessions:
            if SessionRecording.objects.filter(session=session, is_active=True).exists():
                return True
        return False

    
class MeetDetailSerializer(serializers.ModelSerializer):
    provider = serializers.SerializerMethodField()
    language = serializers.CharField()
    organizer = serializers.SerializerMethodField()
    online_users_count = serializers.IntegerField(source="get_online_users_count")
    is_closed = serializers.SerializerMethodField()
    is_provider = serializers.SerializerMethodField()
    live_session_id = serializers.SerializerMethodField()
    has_archive = serializers.SerializerMethodField()
    recordings = serializers.SerializerMethodField()
    has_recording = serializers.SerializerMethodField()
    access = serializers.ChoiceField(choices=Meet.ACCESS_CHOICES, required=False)
    password = serializers.CharField(max_length=50, required=False, allow_null=True, allow_blank=True)

    class Meta:
        model = Meet
        fields = ['id', 'name', 'description','next_session_subject', 'next_session_schedule', 'is_suggested', 'provider', 'is_free', 'price', 'is_live', 'is_closed','online_users_count','language', 'categories', 'organizer', 'is_provider', 'live_session_id', 'has_archive', 'recordings', 'access', 'password', 'has_recording']
        ref_name = 'MeetDetailSerializer'

    def get_has_archive(self, obj):
        return obj.session_set.filter(recorded_file__isnull=False, ended_at__isnull=False).exists()

    def get_has_recording(self, obj):
        # Check if any session of this meet has active recordings
        sessions = obj.session_set.filter(ended_at__isnull=False)
        for session in sessions:
            if SessionRecording.objects.filter(session=session, is_active=True).exists():
                return True
        return False

    def get_live_session_id(self, obj):
        if obj.is_live:
            live_session = Session.objects.filter(meet=obj, ended_at__isnull=True).order_by('-started_at').first()
            if live_session:
                return live_session.id
        return None

    def get_is_provider(self, obj):
        user = self.context.get('request').user
        if obj.provider == user:
            return True
        return False

    def get_provider(self, obj):
        provider_profile = MeetProviderProfile.objects.filter(user=obj.provider, is_verified=True).first()
        if provider_profile:
            return ProviderSerializer(provider_profile, context=self.context).data
        return None

    def get_organizer(self, obj):
        return InstituteSerializer(obj.organizer, context=self.context).data
    
    def get_is_closed(self, obj):
        return obj.is_closed()

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['language'] = instance.language.code
        return representation
    
    def validate(self, data):
        language_code = data.get('language', None)
        if language_code:
            data['language'] = Language.objects.get(code=language_code)

        # Only validate access/password if access field is being updated
        if 'access' in data:
            # Validate that password is provided if access is private
            if data.get('access') == 'private' and not data.get('password'):
                raise ValidationError({"password": "Password is required for private meets."})

            # Clear password if access is public
            if data.get('access') == 'public':
                data['password'] = None

        return data
        
    def get_recordings(self, obj):
        # Get all completed sessions for this meet
        sessions = obj.session_set.filter(ended_at__isnull=False)
        
        # Get all active recordings for these completed sessions
        recordings = []
        for session in sessions:
            session_recordings = SessionRecording.objects.filter(session=session, is_active=True)
            if session_recordings.exists():
                recordings.extend(SessionRecordingSerializer(session_recordings, many=True, context=self.context).data)
                
        return recordings

class SessionRecordingSerializer(serializers.ModelSerializer):
    session_id = serializers.IntegerField(source="session.id", read_only=True)
    session_subject = serializers.CharField(source="session.subject", read_only=True)
    file = FileFieldSerializer(required=True)
    thumbnail = FileFieldSerializer(required=False)

    class Meta:
        model = SessionRecording
        fields = ['id', 'title', 'file', 'file_time', 'recording_type', 'thumbnail', 'created_at', 'session_id', 'session_subject']
        ref_name = 'SessionRecordingSerializer'

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        # Set recording_type to appropriate value
        if instance.recording_type == 'voice':
            representation['thumbnail'] = None

        return representation


class SessionRecordingCreateUpdateSerializer(serializers.ModelSerializer):
    session = serializers.PrimaryKeyRelatedField(queryset=Session.objects.all())
    file = FileFieldSerializer(required=True)
    thumbnail = FileFieldSerializer(required=False, allow_null=True)
    recording_type = serializers.ChoiceField(choices=SessionRecording.RECORDING_TYPE_CHOICES)

    class Meta:
        model = SessionRecording
        fields = ['id', 'title', 'session', 'file', 'file_time', 'recording_type', 'thumbnail', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
        ref_name = 'SessionRecordingCreateUpdateSerializer'

    def validate(self, data):
        # Clear thumbnail if recording type is voice
        if data.get('recording_type') == 'voice':
            data['thumbnail'] = None

        return data

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        # Add session details
        representation['session_id'] = instance.session.id
        representation['session_subject'] = instance.session.subject

        # Set thumbnail to None if recording type is voice
        if instance.recording_type == 'voice':
            representation['thumbnail'] = None

        return representation


class ReportSerializer(serializers.ModelSerializer):
    meet_id = serializers.IntegerField(source="meet.id", read_only=True)
    class Meta:
        model = Report
        fields = ['id', 'meet_id','description', 'report_type']
        ref_name = 'ReportSerializer'


class MeetCreateV2Serializer(serializers.ModelSerializer):
    language = serializers.CharField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, default=0, allow_null=True)
    access = serializers.ChoiceField(choices=Meet.ACCESS_CHOICES, default='public')
    password = serializers.CharField(max_length=50, required=False, allow_null=True, allow_blank=True)

    class Meta:
        model = Meet
        fields = [
            'name', 'price', 'language', 'description', 'is_free', 'access', 'password'
        ]
        
    def validate(self, data):
        """
        Ensure that the price is set to 0 or None if the meet is free.
        Ensure that password is provided if access is private.
        """
        if data.get('is_free'):
            data['price'] = 0
            
        language_code = data.get('language')
        if not Language.objects.filter(code=language_code).exists():
            raise ValidationError({"language": "Invalid language code."})

        data['language'] = Language.objects.get(code=language_code)
        
        # Validate that password is provided if access is private
        if data.get('access') == 'private' and not data.get('password'):
            raise ValidationError({"password": "Password is required for private meets."})

        return data


