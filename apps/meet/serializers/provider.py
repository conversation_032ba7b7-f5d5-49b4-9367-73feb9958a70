
from rest_framework import serializers
from dj_language.models import Language
from dj_filer.admin import get_thumbs
from utils.tmp_media import FileFieldSerializer
from apps.meet.models import MeetProviderProfile
from apps.account.models import User, ProviderRequest
from django.core.exceptions import ValidationError
from dj_language.serializer import LanguageSerializer
from apps.meet.models import Meet, Session





class ProviderSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    avatar = serializers.SerializerMethodField()
    email = serializers.SerializerMethodField()
    
    class Meta:
        model =  MeetProviderProfile
        fields = ['id', 'email', 'name', 'avatar', 'bio', 'slogn']
        ref_name = 'ProviderMeetSerializer'
        
    def get_email(self, obj):
        return obj.bio
        
    def get_name(self, obj):
        request = self.context.get('request')
        return obj.get_translation(request.LANGUAGE_CODE)
    
    def get_avatar(self, obj):
        request = self.context.get('request')
        if obj.avatar:
            # تبدیل URL از /media/ به /static/ برای سازگاری
            # اما اگر URL شامل /media/meet/ باشد، تبدیل نکن
            url = obj.avatar.url
            if url.startswith('/media/') and '/media/meet/' not in url:
                url = url.replace('/media/', '/static/')
            return request.build_absolute_uri(url)
        return None


class ProviderDetailMeetSerializer(ProviderSerializer):
    other_provider_services = serializers.SerializerMethodField()
    
    class Meta:
        model = MeetProviderProfile
        fields = ['id', 'email', 'name', 'avatar', 'slogn', 'bio', 'other_provider_services', ]
        ref_name = 'ProviderDetailMeetSerializer'
    
    def get_other_provider_services(self, obj):
        from apps.account.models import ProviderRequest
        from apps.q_and_a.models import Consultants
        provider_info = ProviderRequest.get_provider_info(obj.user)

        if not provider_info:
            return []

        result = []

        for service in provider_info:
            if service.get('status') == ProviderRequest.Status.accepted:
                if service.get('service') == 'talk':
                    profile = service.get('profile', {})
                    username = profile.get('username', '') if profile else ''

                    # Check if consultant is visible in talk service
                    if username:
                        consultant = Consultants.objects.filter(username=username).first()
                        if consultant and consultant.visible:
                            result.append({
                                'service': 'talk',
                                'username': username
                            })

        return result
    
class LanguageCodeField(serializers.Field):
    def to_representation(self, value):
        return value.code  # Assuming your Language model has a 'code' field

    def to_internal_value(self, data):
        try:
            if isinstance(data, list):
                return [Language.objects.get(code=item.code) for item in data]
            return Language.objects.get(code=data)
        except Language.DoesNotExist:
            raise serializers.ValidationError(f"Language with code '{data}' does not exist.")
        

class MeetProviderProfileSerializer(serializers.ModelSerializer):
    avatar = FileFieldSerializer(required=False)
    languages = serializers.ListField(child=LanguageCodeField(), write_only=True)
     
    class Meta:
        model = MeetProviderProfile
        fields = ['full_name', 'bio', 'birthdate', 'email', 'wa_number', 'institution_name', 'languages', 'avatar','social_medias',]


    
class MeetProviderRoomSerializer(serializers.ModelSerializer):
    purchase_count = serializers.IntegerField(source="get_users_purchase_count")
    provider = serializers.SerializerMethodField()
    archive_count = serializers.SerializerMethodField()
    is_closed = serializers.SerializerMethodField()
    language = LanguageSerializer(read_only=True)
    reminder_count = serializers.IntegerField(source="get_reminder_count")
    status = serializers.SerializerMethodField()
    access = serializers.ChoiceField(choices=Meet.ACCESS_CHOICES, read_only=True)
    password = serializers.CharField(max_length=50, read_only=True, allow_null=True)

    class Meta:
        model = Meet
        fields = [
            'id', 'name', 'next_session_subject', 'next_session_schedule', 'language',
            'is_free', 'price', 'is_closed', 'description', 'provider', 'reminder_count',
            'categories', 'purchase_count', 'archive_count', 'is_active', 'status', 'access', 'password'
        ]

    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['language'] = instance.language.code
        return representation

    def get_provider(self, obj):
        provider_profile = MeetProviderProfile.objects.filter(user=obj.provider, is_verified=True).first()
        if provider_profile:
            return ProviderSerializer(provider_profile, context=self.context).data
        return None


    def get_is_closed(self, obj):
        return obj.is_closed()

    def get_status(self, obj):
        meet_approval = obj.approvals.first()
        if meet_approval:
            return meet_approval.status
        return None

    def get_archive_count(self, obj):
        """
        Count archived sessions based on SessionRecord model structure.
        A session is considered archived if:
        1. It has started (started_at is not null)
        2. It has ended (ended_at is not null)
        3. It has either:
           - A recorded_file in the Session model, OR
           - At least one active SessionRecording
        """
        from apps.meet.models import SessionRecording

        # Get all completed sessions (started and ended)
        completed_sessions = obj.session_set.filter(
            started_at__isnull=False,
            ended_at__isnull=False
        )

        archive_count = 0
        for session in completed_sessions:
            # Check if session has recorded_file OR has active SessionRecording
            has_session_file = session.recorded_file and session.recorded_file.name
            has_session_recording = SessionRecording.objects.filter(
                session=session,
                is_active=True
            ).exists()

            if has_session_file or has_session_recording:
                archive_count += 1

        return archive_count