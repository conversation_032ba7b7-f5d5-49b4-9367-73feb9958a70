import json
from django import forms
from django.forms.widgets import Widget
from django.utils.safestring import mark_safe
from django.utils.html import format_html


class WeeklyScheduleWidget(Widget):
    """
    Custom widget for weekly scheduling field
    Displays a form with 7 days and time slots for each day
    """
    
    template_name = 'admin/widgets/weekly_schedule.html'
    
    # English day names mapping
    DAYS = {
        'saturday': 'Saturday',
        'sunday': 'Sunday',
        'monday': 'Monday',
        'tuesday': 'Tuesday',
        'wednesday': 'Wednesday',
        'thursday': 'Thursday',
        'friday': 'Friday'
    }
    
    def __init__(self, attrs=None):
        default_attrs = {
            'class': 'weekly-schedule-widget'
        }
        if attrs:
            default_attrs.update(attrs)
        super().__init__(default_attrs)
    
    def format_value(self, value):
        """Format the value for display"""
        if value is None or value == '':
            return self.get_default_schedule()

        if isinstance(value, str):
            try:
                parsed = json.loads(value)
                if isinstance(parsed, dict):
                    # Ensure all days are present
                    schedule = self.get_default_schedule()
                    schedule.update(parsed)
                    return schedule
                else:
                    return self.get_default_schedule()
            except (json.JSONDecodeError, TypeError):
                return self.get_default_schedule()

        if isinstance(value, dict):
            # Ensure all days are present
            schedule = self.get_default_schedule()
            schedule.update(value)
            return schedule

        # Fallback for any other type
        return self.get_default_schedule()

    def get_project_timezone_info(self):
        """
        Get project timezone information for display in widget
        """
        from django.conf import settings

        # Get Django project timezone
        project_timezone = getattr(settings, 'TIME_ZONE', 'Asia/Tehran')

        # Convert timezone name to offset and description
        if project_timezone == 'Asia/Tehran':
            return "UTC+3.5 (Asia/Tehran - Iran Standard Time)"
        elif project_timezone == 'UTC':
            return "UTC+0.0 (Coordinated Universal Time)"
        else:
            # For other timezones, show the timezone name
            return f"{project_timezone}"

    
    def get_default_schedule(self):
        """Get default empty schedule"""
        return {
            'saturday': [],
            'sunday': [],
            'monday': [],
            'tuesday': [],
            'wednesday': [],
            'thursday': [],
            'friday': []
        }
    
    def render(self, name, value, attrs=None, renderer=None):
        """Render the widget"""
        if attrs is None:
            attrs = {}
        
        # Format the value
        schedule_data = self.format_value(value)

        # Ensure schedule_data is not None
        if schedule_data is None:
            schedule_data = self.get_default_schedule()

        # Generate unique ID for this widget
        widget_id = attrs.get('id', f'id_{name}')
        
        # Build the HTML
        html_parts = []
        
        # Hidden input to store the actual JSON value
        json_value = json.dumps(schedule_data, ensure_ascii=False)
        # Escape quotes for HTML attribute
        escaped_json = json_value.replace('"', '&quot;')
        html_parts.append(
            f'<input type="hidden" name="{name}" id="{widget_id}" value="{escaped_json}" />'
        )
        
        # Container div
        html_parts.append(f'<div class="weekly-schedule-container" data-target="{widget_id}">')
        
        # Title and help text with project timezone info
        project_timezone_info = self.get_project_timezone_info()
        html_parts.append('<h3 style="margin: 10px 0; color: #333;">Weekly Schedule</h3>')
        html_parts.append(f'<p style="margin: 5px 0; color: #666; font-size: 12px;">Maximum 2 time slots allowed per day</p>')
        html_parts.append(f'<div style="margin: 5px 0; padding: 8px; background: #e8f4fd; border-left: 4px solid #2196F3; border-radius: 4px;">')
        html_parts.append(f'<strong style="color: #1976D2;">⏰ Project Timezone: {project_timezone_info}</strong>')
        html_parts.append(f'<br><small style="color: #666;">Enter all times according to project timezone (Iran time)</small>')
        html_parts.append(f'<br><small style="color: #666;">Example: 08:00-10:00 means 8 AM to 10 AM Iran time</small>')
        html_parts.append(f'<br><small style="color: #888;">Times will be automatically converted for users in different countries</small>')
        html_parts.append(f'</div>')
        
        # Days container
        html_parts.append('<div class="schedule-days">')
        
        for day_key, day_name in self.DAYS.items():
            day_slots = schedule_data.get(day_key, [])
            html_parts.append(self.render_day(day_key, day_name, day_slots, widget_id))
        
        html_parts.append('</div>')  # Close schedule-days
        html_parts.append('</div>')  # Close container
        
        # Add CSS and JavaScript
        html_parts.append(self.get_media_html())
        
        return mark_safe(''.join(html_parts))
    
    def render_day(self, day_key, day_name, slots, widget_id):
        """Render a single day's schedule"""
        html = f'''
        <div class="schedule-day" data-day="{day_key}">
            <h4 class="day-title">{day_name}</h4>
            <div class="time-slots" data-day="{day_key}">
        '''
        
        # Render existing time slots
        for i, slot in enumerate(slots):
            html += self.render_time_slot(day_key, i, slot, widget_id)
        
        # Add button to add new slot (max 2 slots per day)
        add_button_style = 'style="display: none;"' if len(slots) >= 2 else ''
        html += f'''
            </div>
            <button type="button" class="add-slot-btn" data-day="{day_key}" data-target="{widget_id}" {add_button_style}>
                + Add Time Slot
            </button>
        </div>
        '''
        
        return html
    
    def render_time_slot(self, day_key, index, slot, widget_id):
        """Render a single time slot"""
        # Parse time slot (format: "HH:MM-HH:MM")
        start_time = ""
        end_time = ""

        if slot and '-' in slot:
            try:
                start_time, end_time = slot.split('-', 1)
                # Validate and normalize time format
                start_time = self.normalize_time_format(start_time)
                end_time = self.normalize_time_format(end_time)
            except ValueError:
                pass

        return f'''
        <div class="time-slot" data-day="{day_key}" data-index="{index}">
            <input type="time" class="start-time" value="{start_time}"
                   placeholder="Start" data-target="{widget_id}" />
            <span class="time-separator">to</span>
            <input type="time" class="end-time" value="{end_time}"
                   placeholder="End" data-target="{widget_id}" />
            <button type="button" class="remove-slot-btn" data-target="{widget_id}">×</button>
        </div>
        '''

    def normalize_time_format(self, time_str):
        """
        Normalize time format to HH:MM (24-hour format)
        Handles various input formats including AM/PM
        """
        if not time_str or not time_str.strip():
            return ""

        time_str = time_str.strip()

        # If already in HH:MM format (24-hour), validate and return
        if ':' in time_str and not ('AM' in time_str.upper() or 'PM' in time_str.upper()):
            try:
                parts = time_str.split(':')
                if len(parts) == 2:
                    hour = int(parts[0])
                    minute = int(parts[1])
                    if 0 <= hour <= 23 and 0 <= minute <= 59:
                        return f"{hour:02d}:{minute:02d}"
            except (ValueError, IndexError):
                pass

        # Handle AM/PM format
        if 'AM' in time_str.upper() or 'PM' in time_str.upper():
            try:
                # Remove AM/PM and clean up
                is_pm = 'PM' in time_str.upper()
                clean_time = time_str.upper().replace('AM', '').replace('PM', '').strip()

                if ':' in clean_time:
                    hour_str, minute_str = clean_time.split(':')
                    hour = int(hour_str)
                    minute = int(minute_str)

                    # Convert to 24-hour format
                    if is_pm and hour != 12:
                        hour += 12
                    elif not is_pm and hour == 12:
                        hour = 0

                    if 0 <= hour <= 23 and 0 <= minute <= 59:
                        return f"{hour:02d}:{minute:02d}"

            except (ValueError, IndexError):
                pass

        # If all else fails, return empty string
        return ""
    
    def get_media_html(self):
        """Get CSS and JavaScript for the widget"""
        return '''
        <style>
        .weekly-schedule-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            background: #f9f9f9;
            margin: 10px 0;
            direction: ltr;
        }
        
        .schedule-days {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .schedule-day {
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            background: white;
        }
        
        .day-title {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            background: #f0f0f0;
            padding: 5px;
            border-radius: 3px;
        }
        
        .time-slot {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            padding: 5px;
            background: #f8f8f8;
            border-radius: 3px;
        }
        
        .time-slot input[type="time"] {
            flex: 1;
            padding: 4px 6px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .time-separator {
            font-size: 12px;
            color: #666;
            white-space: nowrap;
        }
        
        .remove-slot-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 3px;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 14px;
            line-height: 1;
        }
        
        .remove-slot-btn:hover {
            background: #c82333;
        }
        
        .add-slot-btn {
            background: #28a745;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            width: 100%;
            margin-top: 8px;
        }
        
        .add-slot-btn:hover {
            background: #218838;
        }
        </style>
        
        <script>
        (function() {
            function normalizeTimeFormat(timeStr) {
                /**
                 * Normalize time format to HH:MM (24-hour format)
                 * HTML time input should already provide 24-hour format
                 */
                if (!timeStr || !timeStr.trim()) {
                    return "";
                }

                timeStr = timeStr.trim();

                // Check if already in HH:MM format
                const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;
                const match = timeStr.match(timeRegex);

                if (match) {
                    const hour = parseInt(match[1], 10);
                    const minute = parseInt(match[2], 10);
                    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                }

                return timeStr; // Return as-is if format is unexpected
            }

            function updateScheduleData(targetId) {
                const container = document.querySelector(`[data-target="${targetId}"]`);
                const hiddenInput = document.getElementById(targetId);
                
                if (!container || !hiddenInput) return;
                
                const schedule = {};
                const days = ['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
                
                days.forEach(day => {
                    schedule[day] = [];
                    const daySlots = container.querySelectorAll(`[data-day="${day}"] .time-slot`);
                    
                    daySlots.forEach(slot => {
                        const startTime = slot.querySelector('.start-time').value;
                        const endTime = slot.querySelector('.end-time').value;

                        if (startTime && endTime) {
                            // Ensure times are in HH:MM format (24-hour)
                            const normalizedStart = normalizeTimeFormat(startTime);
                            const normalizedEnd = normalizeTimeFormat(endTime);

                            if (normalizedStart && normalizedEnd) {
                                schedule[day].push(`${normalizedStart}-${normalizedEnd}`);
                            }
                        }
                    });
                });
                
                hiddenInput.value = JSON.stringify(schedule);
                console.log('DEBUG: Updated schedule data:', JSON.stringify(schedule));
            }
            
            function updateAddButtons(targetId) {
                const container = document.querySelector(`[data-target="${targetId}"]`);
                if (!container) return;

                const days = ['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'];

                days.forEach(day => {
                    const dayContainer = container.querySelector(`[data-day="${day}"]`);
                    const slotsContainer = dayContainer.querySelector('.time-slots');
                    const addButton = dayContainer.querySelector('.add-slot-btn');
                    const currentSlots = slotsContainer.querySelectorAll('.time-slot').length;

                    // Show/hide add button based on slot count (max 2 slots per day)
                    if (currentSlots >= 2) {
                        addButton.style.display = 'none';
                    } else {
                        addButton.style.display = 'block';
                    }
                });
            }

            // Add event listeners
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('add-slot-btn')) {
                    const day = e.target.dataset.day;
                    const targetId = e.target.dataset.target;
                    const dayContainer = e.target.parentElement;
                    const slotsContainer = dayContainer.querySelector('.time-slots');

                    // Check if already has 2 slots
                    const currentSlots = slotsContainer.querySelectorAll('.time-slot').length;
                    if (currentSlots >= 2) {
                        alert('Maximum 2 time slots allowed per day');
                        return;
                    }

                    const newSlot = document.createElement('div');
                    newSlot.className = 'time-slot';
                    newSlot.dataset.day = day;
                    newSlot.innerHTML = `
                        <input type="time" class="start-time" placeholder="Start" data-target="${targetId}" />
                        <span class="time-separator">to</span>
                        <input type="time" class="end-time" placeholder="End" data-target="${targetId}" />
                        <button type="button" class="remove-slot-btn" data-target="${targetId}">×</button>
                    `;

                    slotsContainer.appendChild(newSlot);
                    updateScheduleData(targetId);
                    updateAddButtons(targetId);
                }
                
                if (e.target.classList.contains('remove-slot-btn')) {
                    const targetId = e.target.dataset.target;
                    e.target.parentElement.remove();
                    updateScheduleData(targetId);
                    updateAddButtons(targetId);
                }
            });
            
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('start-time') || e.target.classList.contains('end-time')) {
                    const targetId = e.target.dataset.target;
                    updateScheduleData(targetId);
                }
            });
        })();
        </script>
        '''
    
    def value_from_datadict(self, data, files, name):
        """Extract value from form data and return as dict for JSONField"""
        value = data.get(name)

        if value and value.strip():
            try:
                # Decode HTML entities
                import html
                decoded_value = html.unescape(value)

                # Check if it's already a valid JSON string
                if decoded_value.startswith('{') and decoded_value.endswith('}'):
                    # Try to parse as JSON first to validate
                    try:
                        parsed_data = json.loads(decoded_value)
                        # Return as dict for JSONField
                        return parsed_data
                    except json.JSONDecodeError:
                        # If JSON parsing fails, it might be Python dict format
                        try:
                            # Use ast.literal_eval for safe evaluation
                            import ast
                            parsed_data = ast.literal_eval(decoded_value)
                            # Return as dict
                            return parsed_data
                        except (ValueError, SyntaxError):
                            return None
                else:
                    return None
            except Exception:
                return None
        return None
