from ajaxdatatable.admin import AjaxDatatable
from django import forms
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
import json
from dj_language.models import Language
from django.utils.safestring import mark_safe

from apps.aboutservice.models import <PERSON><PERSON>er<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, S<PERSON><PERSON>pp, Faq, FaqCategory
from utils.model_extention import AdminModelExtension
from utils.json_editor_field import JsonEditorWidget


class SupportersAdmin(admin.TabularInline):
    model = Supporter
    extra = 1


@admin.register(AboutService)
class AboutServiceAdmin(AdminModelExtension, AjaxDatatable):
    list_display = ('service', 'language', '_created_by')
    list_filter = ('language', 'service',)
    readonly_fields = ('created_by', )
    search_fields = ('service',)

    inlines = [
        SupportersAdmin,
    ]
    
    @admin.display(description=_('Created By'))
    def _created_by(self, obj):
        return str(obj.created_by)


class SelectMultipleWidget(forms.SelectMultiple):
    def get_context(self, name, value, attrs):
        if value:
            value = value.split(',')
        data = super(SelectMultipleWidget, self).get_context(name, value, attrs)
        return data


class DontKillAppForm(forms.ModelForm):
    class Meta:
        model = DontKillApp
        exclude = ()
        widgets = {
            'brand': SelectMultipleWidget(choices=(
                ('other device', 'Other Device'),
                ('samsung', 'Samsung'),
                ('iphone', 'Iphone'),
                ('oneplus', 'OnePlus'),
                ('huawei', 'Huawei'),
                ('xiaomi', 'Xiaomi'),
                ('meizu', 'Meizu'),
                ('asus', 'Asus'),
                ('wiko', 'Wiko'),
                ('lenovo', 'Lenovo'),
                ('oppo', 'Oppo'),
                ('vivo', 'Vivo'),
                ('realme', 'realme'),
                ('blackview', 'Blackview'),
                ('sony', 'Sony'),
                ('unihertz', 'Unihertz'),
                ('android one', 'Android One'),
                ('pixel', 'Pixel'),
                ('nexus', 'Nexus'),
                ('nokia', 'Nokia'),
                ('htc', 'HTC'),
                ('lg', 'LG'),
                ('gplus', 'GPLUS'),
                ('glx', 'GLX'),
                ('infinix', 'Infinix'),
                ('smart', 'SMART'),
                ('tecno', 'Tecno'),
            )),
            'sdk_version': SelectMultipleWidget(choices=tuple(zip(range(14, 36), range(14, 36))))
        }


@admin.register(DontKillApp)
class DontKillAppAdmin(AdminModelExtension, AjaxDatatable):
    list_display = ('_brand', '_sdk_version', 'language', 'created_at', 'updated_at', 'created_by')
    list_filter = ('created_by',)
    search_fields = ('brand', 'description',)
    form = DontKillAppForm

    @admin.display(description=_('Brand'))
    def _brand(self, obj):
        return ", ".join(obj.brand)

    @admin.display(description=_('Sdk Version'))
    def _sdk_version(self, obj):
        return ", ".join(map(str, obj.sdk_version))


@admin.register(ShareApp)
class ShareAppAdmin(AjaxDatatable):
    list_display = ('title', 'language', )
    list_filter = ('language',)
    search_fields = ('title', 'description')
    autocomplete_fields = ('language',)


def get_translations_schema():
    return {
        "type": "array",
        "title": " ",
        "format": "tabs",
        "items": {
            "type": "object",
            "title": "Translation",
            "properties": {
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                },
                "text": {
                    "type": "string",
                    "title": "Text",
                    "format": "textarea"
                }
            },
            "required": ["language_code", "text"]
        }
    }


class FaqCategoryAdminForm(forms.ModelForm):
    class Meta:
        model = FaqCategory
        fields = '__all__'
        widgets = {
            'translations': JsonEditorWidget(attrs={'schema': json.dumps(get_translations_schema())})
        }


@admin.register(FaqCategory)
class FaqCategoryAdmin(AjaxDatatable):
    list_display = ('display_title', '_translations', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('title',)
    form = FaqCategoryAdminForm
    
    @admin.display(description=_('Title'))
    def display_title(self, obj):
        return obj.title
    
    def save_model(self, request, obj, form, change):
        # Ensure translations is a list, not a dict
        if isinstance(obj.translations, dict) and not obj.translations:
            obj.translations = []
        super().save_model(request, obj, form, change)

    @admin.display(description='languages')
    def _translations(self, obj):
        try:
            return mark_safe(" | ".join([i.get('language_code') for i in obj.translations] or '-'))
        except:
            return '-'


class FaqAdminForm(forms.ModelForm):
    class Meta:
        model = Faq
        fields = '__all__'
        widgets = {
            'question': JsonEditorWidget(attrs={'schema': json.dumps(get_translations_schema())}),
            'answer': JsonEditorWidget(attrs={'schema': json.dumps(get_translations_schema())}),
        }


@admin.register(Faq)
class FaqAdmin(AjaxDatatable):
    list_display = ('display_question', 'display_answer', 'category', 'priority', 'is_active', '_translations')
    list_filter = ('category', 'is_active')
    search_fields = ('question',)
    form = FaqAdminForm
    ordering = ('priority',)
    
    
    def save_model(self, request, obj, form, change):
        # Ensure question and answer are lists, not dicts
        if isinstance(obj.question, dict) and not obj.question:
            obj.question = []
        if isinstance(obj.answer, dict) and not obj.answer:
            obj.answer = []
        super().save_model(request, obj, form, change)
        
    @admin.display(description='languages')
    def _translations(self, obj):
        try:
            return mark_safe(" | ".join([i.get('language_code') for i in obj.question] or '-'))
        except:
            return '-'

    @admin.display(description=_('Question'))
    def display_question(self, obj):
        return str(obj)
    

    @admin.display(description=_('Answer'))
    def display_answer(self, obj):
        answer = obj.get_faq('fa', self.answer)
        if answer:
            return answer[:50]
        else:
            return '-'
    