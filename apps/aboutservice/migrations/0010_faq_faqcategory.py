# Generated by Django 3.2.25 on 2025-04-13 13:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('aboutservice', '0009_alter_shareapp_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='FaqCategory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=192)),
                ('translations', models.J<PERSON><PERSON>ield(default=dict, verbose_name='translations')),
                ('is_active', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='Faq',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.JSONField(default=dict, verbose_name='questions')),
                ('answer', models.J<PERSON><PERSON>ield(default=dict, verbose_name='answers')),
                ('priority', models.IntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='aboutservice.faqcategory')),
            ],
        ),
    ]
