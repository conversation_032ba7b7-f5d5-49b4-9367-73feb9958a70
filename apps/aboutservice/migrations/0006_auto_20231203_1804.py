# Generated by Django 3.2.22 on 2023-12-03 18:04

import dj_language.field
from django.db import migrations
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
        ('aboutservice', '0005_alter_dontkillapp_languages'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='dontkillapp',
            name='languages',
        ),
        migrations.AddField(
            model_name='dontkillapp',
            name='language',
            field=dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language'),
        ),
    ]
