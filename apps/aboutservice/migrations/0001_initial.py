# Generated by Django 3.2.13 on 2022-08-28 14:37

import dj_language.field
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import limitless_dashboard.fields.summernote


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dj_language', '0002_auto_20220120_1344'),
    ]

    operations = [
        migrations.CreateModel(
            name='AboutService',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('description', limitless_dashboard.fields.summernote.SummernoteField(verbose_name='Description')),
                ('service', models.CharField(choices=[('main', 'main'), ('hussainiyah', 'hussainiyah'), ('quran', 'quran'), ('mafatih', 'mafatih'), ('qiblah_finder', 'qiblah finder'), ('ahkam', 'ahkam'), ('calendar', 'calendar')], max_length=16, verbose_name='service')),
                ('supporters', models.JSONField(default=dict, verbose_name='supporters')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='created by')),
                ('language', dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='Language')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='updated by')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
