# Generated by Django 3.2.13 on 2022-09-18 18:17

from django.conf import settings
import django.contrib.postgres.fields
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import limitless_dashboard.fields.summernote


class Migration(migrations.Migration):

    replaces = [('aboutservice', '0004_dontkillapp'), ('aboutservice', '0005_auto_20220918_1810')]

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
        ('aboutservice', '0003_auto_20220828_1452'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DontKillApp',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('brand', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=100, verbose_name='brand'), size=None)),
                ('sdk_version', django.contrib.postgres.fields.ArrayField(base_field=models.PositiveSmallIntegerField(validators=[django.core.validators.MaxValueValidator(35)], verbose_name='sdk version'), size=None)),
                ('description', limitless_dashboard.fields.summernote.SummernoteField(verbose_name='description')),
                ('created_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='created by')),
                ('updated_by', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='updated by')),
                ('languages', models.ManyToManyField(to='dj_language.Language', verbose_name='languages')),
            ],
            options={
                'verbose_name': 'Note',
                'verbose_name_plural': 'Dont Kill App',
            },
        ),
    ]
