# Generated by Django 3.2.13 on 2022-08-28 14:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import filer.fields.image


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.FILER_IMAGE_MODEL),
        ('aboutservice', '0002_auto_20220828_1438'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='aboutservice',
            name='supporters',
        ),
        migrations.AlterField(
            model_name='aboutservice',
            name='service',
            field=models.CharField(choices=[('main', 'Main'), ('hussainiyah', '<PERSON>iyah'), ('quran', 'Quran'), ('mafatih', 'Mafatih'), ('qiblah', 'Qiblah Finder'), ('ahkam', 'Ahkam'), ('calendar', 'Calendar')], max_length=16, verbose_name='service'),
        ),
        migrations.CreateModel(
            name='Supporter',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='title')),
                ('url', models.CharField(blank=True, help_text='https://example.com', max_length=255, null=True, verbose_name='url')),
                ('logo', filer.fields.image.FilerImageField(on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.FILER_IMAGE_MODEL, verbose_name='Logo')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='aboutservice.aboutservice', verbose_name='Service')),
            ],
        ),
    ]
