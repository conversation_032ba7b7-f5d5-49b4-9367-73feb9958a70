from django.shortcuts import render
from rest_framework.generics import ListAPIView, RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from apps.aboutservice.models import AboutService, DontKillApp, ShareApp
from apps.aboutservice.serializer import AboutServiceSerializer, DontKillAppSerializer, ShareAppSerializer
from rest_framework.exceptions import NotFound


class AboutServiceView(ListAPIView):
    """
        -- params: service
    """
    serializer_class = AboutServiceSerializer
    # permission_classes = (IsAuthenticated,)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter('service', openapi.IN_QUERY, description="Service type to filter by", type=openapi.TYPE_STRING, enum=[choice[0] for choice in AboutService.Service.choices])
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    def get_queryset(self):
        language = self.request.LANGUAGE_CODE
        service = self.request.query_params.get('service', None)
        queryset = AboutService.objects.filter(language__code=language)
        if service:
            queryset = queryset.filter(service=service)
        return queryset
    

class DontKillHelpView(RetrieveAPIView):
    serializer_class = DontKillAppSerializer
    permission_classes = (IsAuthenticated,)

    def get_object(self):
        user = self.request.user
        sdk_version = user.os_platform or '0'
        if "." in sdk_version:
            sdk_version = int(sdk_version.split('.')[0])

        device_brand = user.device_brand or ''
        qs = DontKillApp.objects.filter(
            language__code=self.request.LANGUAGE_CODE,
            sdk_version__contains=[sdk_version],
            brand__contains=[device_brand.lower()],
        ).first()
        if not qs:
            qs = DontKillApp.objects.filter(
                language__code='en',
                sdk_version__contains=[sdk_version],
                brand__contains=[device_brand.lower()],
            ).first()

        return qs


def dont_kill_html(request):
    device_brand, sdk_version = request.GET.get('d', 'samsung'), request.GET.get('v', '16')
    lang = request.GET.get('l', 'en')

    obj = DontKillApp.objects.filter(
        language__code=lang,
        sdk_version__contains=[sdk_version],
        brand__contains=[device_brand.lower()],
    ).first()

    if not obj:
        obj = DontKillApp.objects.filter(
            language__code='en',
            sdk_version__contains=[sdk_version],
            brand__contains=[device_brand.lower()],
        ).first()

    return render(request, "dont_kill.html", context={
        'obj': obj
    })


class ShareAppView(RetrieveAPIView):
    serializer_class = ShareAppSerializer

    def get_object(self):
        language = self.request.LANGUAGE_CODE

        # Try to get the instance for the requested language
        instance = ShareApp.objects.filter(language__code=language).first()
        print(instance)
        # If not found, try to get the English version as fallback
        if not instance:
            instance = ShareApp.objects.filter(language__code='en').first()

        # If still not found, return None
        if not instance:
            return None
        
        return instance
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance is None:
            return Response({}, status=status.HTTP_200_OK)
        serializer = self.get_serializer(instance)
        return Response(serializer.data)