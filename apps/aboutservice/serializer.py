from dj_filer.admin import get_thumbs
from rest_framework import serializers

from apps.aboutservice.models import AboutSer<PERSON>, Supporter, Dont<PERSON>illA<PERSON>, ShareApp


class SupporterSerializer(serializers.ModelSerializer):
    logo = serializers.SerializerMethodField()

    def get_logo(self, obj):
        try:
            return get_thumbs(obj.logo, self.context.get('request'))
        except Exception:
            return {}

    class Meta:
        model = Supporter
        fields = ('title', 'url', 'logo')


class AboutServiceSerializer(serializers.ModelSerializer):
    language = serializers.CharField(source='language.code', )
    supporters = SupporterSerializer(many=True, source='supporter_set')

    class Meta:
        model = AboutService
        fields = (
            'description', 'language', 'service', 'supporters'
        )


class DontKillAppSerializer(serializers.ModelSerializer):
    class Meta:
        model = DontKillApp
        fields = (
            'brand', 'sdk_version', 'description',
        )


class ShareAppSerializer(serializers.ModelSerializer):
    image = serializers.SerializerMethod<PERSON>ield()

    def get_image(self, obj):
        try:
            return get_thumbs(obj.image, self.context.get('request'))
        except Exception:
            return {}

    class Meta:
        model = ShareApp
        fields = ('description', 'image')
