from dj_language.field import LanguageField
from dj_language.models import Language
from django.contrib.postgres.fields import ArrayField
from django.core import validators
from django.db import models
from django.utils.translation import gettext_lazy as _
from filer.fields.image import FilerImageField
from limitless_dashboard.fields.summernote import SummernoteField

from utils.model_extention import ModelExtension


class AboutService(ModelExtension):
    class Service(models.TextChoices):
        main = 'main', 'Main'
        hussainiyah = 'hussainiyah', 'Hussainiyah'
        quran = 'quran', 'Quran'
        mafatih = 'mafatih', 'Mafatih'
        qiblah_finder = 'qiblah', 'Qiblah Finder'
        ahkam = 'ahkam', 'Ahkam'
        calendar = 'calendar', 'Calendar'
        talk = 'talk', 'Talk'
        meet = 'meet', 'Meet'
        library = 'library', 'Library'
        hadis = 'hadis', 'Hadis'
        account = 'account', 'Account'


    description = SummernoteField(verbose_name=_('Description'))
    language = LanguageField(verbose_name=_('Language'))
    service = models.CharField(max_length=16, choices=Service.choices, verbose_name=_('service'))

    def __str__(self):
        return f"{self.get_service_display()} <{self.id}>"


class Supporter(models.Model):
    service = models.ForeignKey(AboutService, verbose_name=_('Service'), on_delete=models.CASCADE)
    title = models.CharField(max_length=255, verbose_name=_('title'))
    url = models.CharField(verbose_name='url', help_text='https://example.com', null=True, blank=True, max_length=255)
    logo = FilerImageField(verbose_name=_('Logo'), on_delete=models.CASCADE, related_name='+')

    def __str__(self):
        return self.title


class DontKillApp(ModelExtension):
    brand = ArrayField(models.CharField(max_length=100, verbose_name=_('brand')))
    sdk_version = ArrayField(
        models.PositiveSmallIntegerField(
            verbose_name=_("sdk version"),
            validators=[validators.MaxValueValidator(35)]
        )
    )
    language = LanguageField()
    description = SummernoteField(verbose_name=_('description'))

    def __str__(self):
        return self.description[:30]

    class Meta:
        verbose_name = _("Note")
        verbose_name_plural = _("Dont Kill App")


class ShareApp(ModelExtension):
    title = models.CharField(max_length=255, verbose_name=_('Title'))
    description = models.TextField(verbose_name=_('Description'))
    language = LanguageField(verbose_name=_('Language'))
    image = FilerImageField(verbose_name=_('Image'), on_delete=models.CASCADE, related_name='+',  null=True, blank=True,)

    def __str__(self):
        return f"{self.title} ({self.language})"

    class Meta:
        verbose_name = _("Share App")
        verbose_name_plural = _("Share Apps")

class FaqCategory(models.Model):
    title = models.CharField(max_length=192)
    translations = models.JSONField(verbose_name=_('translations'), default=dict)
    is_active = models.BooleanField(default=True)
    

    def get_translations(self, lang):
        try:
            for tr in self.translations:
                if tr['language_code'] == lang:
                    return tr['text']                  
            return self.translations[0]['text']
        except Exception as exp:
            print(f'---> {exp}')
            return self.title


    def __str__(self):
        return self.title


class Faq(models.Model):
    category = models.ForeignKey(FaqCategory, on_delete=models.CASCADE)
    question = models.JSONField(verbose_name=_('questions'), default=dict)
    answer = models.JSONField(verbose_name=_('answers'), default=dict)
    priority = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    metafield = models.TextField(null=True, blank=True)
    
    def __str__(self):
        try:
            for lang in ['fa', 'en', 'ar']:
                for tr in self.question:
                    if tr['language_code'] == lang:
                        return tr['text'][:72] 

            return self.question[0]['text'][:72]
        except Exception as exp:
            return f"{self.category}: {self.id}"
        

    def get_faq(self, lang, faq_field):
        try:
            if isinstance(faq_field, list) and faq_field:
                for tr in faq_field:
                    if isinstance(tr, dict) and tr.get('language_code') == lang:
                        return tr.get('text')
            return self.answer[0]['text']
        except Exception as exp:
            print(f'---> Error in get_faq: {exp}')
            return None        
