from django.contrib import admin

from ajaxdatatable.admin import AjaxDatatable
from django.utils.translation import gettext_lazy as _

from .models import Bookmark



@admin.register(Bookmark)
class BookmarkAdmin(admin.ModelAdmin):
    list_display = ('user', 'service', 'content_id', 'status', 'created_at', 'updated_at')
    list_filter = ('status', 'service')
    search_fields = ('user__username', 'user__email', 'service', 'content_id')
    autocomplete_fields = ('user', )
