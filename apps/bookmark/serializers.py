
from rest_framework import serializers
from .models import Bookmark
from apps.library.serializers.book import BookListV2Serializer
from django.conf import settings
from apps.quran.models import QuranVerse
from apps.mafatih.models import <PERSON><PERSON>tihPart
from apps.hadis.models import Hadis
from apps.ahkam.models import Ma<PERSON>elContent
from apps.library.models import Book, BookFile
from apps.library.views.books.filters import *



class BookmarkSerializer(serializers.ModelSerializer):
    related_object = serializers.SerializerMethodField()


    class Meta:
        model = Bookmark
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'user', 'status', 'related_object') 
        ref_name = "BookmarkSerializers"
        

    def get_related_object(self, obj):
        service = obj.service
        content_id = obj.content_id
        request = self.context.get('request')

        # مورد خاص سرویس book
        if service == 'book':
            if not request:
                return None
            
            # تعیین نوع content_id (عددی یا اسلاگ)
            book_filter = Q(id=content_id) if content_id.isdigit() else Q(slug=content_id)
            
            # پارامترهای زبان و فایل
            language_code = request.LANGUAGE_CODE
            languages_sort_expression = [
                When(Q(languages__code__in=[lang_code]), then=Value(i + 1))
                for i, lang_code in enumerate(settings.LANGUAGES_MAP.get(language_code, []))
            ]
            valid_file_types = [BookFile.FileType.pdf, BookFile.FileType.epub, BookFile.FileType.audio]
            
            # کوئری اصلی
            book = Book.objects.filter(book_filter).annotate(
                avg_rate=Avg('rates__rate'),
                lang=Case(*languages_sort_expression, output_field=models.IntegerField()),
                file_types=ArrayAgg('files__type', distinct=True, filter=Q(files__type__in=valid_file_types))
            ).select_related(
                'publisher'
            ).prefetch_related(
                'authors',
                'thumbnail',
                'authors__thumbnail',
            ).first()
            
            return BookListV2Serializer(book, context=self.context).data if book else None

        # سایر سرویسها
        try:
            content_id_int = int(content_id)
            
            if service == 'quran':
                return QuranVerse.objects.filter(index=content_id_int).values().first()
            elif service == 'mafatih':
                return MafatihPart.objects.filter(id=content_id_int).values().first()
            elif service == 'hadith':
                return Hadis.objects.filter(id=content_id_int).values().first()
            elif service == 'ahkam':
                return MasaelContent.objects.filter(id=content_id_int).values().first()
                
        except (ValueError, TypeError):
            return None