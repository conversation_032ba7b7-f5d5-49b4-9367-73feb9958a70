from django.shortcuts import render
from django.contrib.postgres.aggregates import ArrayAgg  
from django.db import models
from rest_framework import status
from rest_framework.exceptions import NotFound
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.generics import CreateAPIView, DestroyAPIView, ListAPIView
from rest_framework.permissions import IsAuthenticated
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from django.conf import settings

from .models import Bookmark
from .serializers import BookmarkSerializer



class ListBookmarksView(ListAPIView):
    """
    get:
    List all bookmarks for the authenticated user.
    
    Can be filtered by service using the `service` query parameter.
    
    Returns a list of bookmarks.
    """
    serializer_class = BookmarkSerializer
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="List all bookmarks for the authenticated user, optionally filtered by service",
        manual_parameters=[
            openapi.Parameter(
                'service',
                openapi.IN_QUERY,
                description="Filter bookmarks by service",
                type=openapi.TYPE_STRING
            ),
        ]
    )    
    def get_queryset(self):
        queryset = Bookmark.objects.filter(user=self.request.user, status=True)
        service = self.request.query_params.get('service', None)
        if service is not None:
            queryset = queryset.filter(service=service)
        return queryset
    
    def get_serializer(self, *args, **kwargs):
        # اطمینان از ارسال context به سریالایزر
        kwargs['context'] = self.get_serializer_context()
        return super().get_serializer(*args, **kwargs)


class AddBookmarkView(CreateAPIView):
    """
    post:
    Add a new bookmark for a service.
    
    Request should contain:
    - service: The service to be bookmarked.
    - content_id: The content ID of the service to be bookmarked.
    
    Returns the created bookmark.
    """
    queryset = Bookmark.objects.all()
    serializer_class = BookmarkSerializer
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(operation_description="Add a new bookmark for a service")
    def perform_create(self, serializer):
        user = self.request.user
        service = serializer.validated_data['service']
        content_id = serializer.validated_data['content_id']
        text = serializer.validated_data.get('text', None)  # بررسی فیلد text

        bookmark = Bookmark.objects.filter(user=user, service=service, content_id=content_id).first()
        if not bookmark:
            bookmark = Bookmark(user=user, service=service, content_id=content_id)

        bookmark.status = True
        bookmark.text = text
        bookmark.save()
        serializer.instance = bookmark

class RemoveBookmarkView(DestroyAPIView):
    """
    delete:
    Deactivate a bookmark by setting its status to False using content_id and user.

    The request should specify the content_id of the bookmark to be deactivated.

    Returns no content.
    """
    serializer_class = BookmarkSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'content_id'  # Specify that we are looking up by content_id
    
    def get_object(self):
        # Fetch the content_id from the URL
        content_id = self.kwargs.get('content_id')
        service = self.kwargs.get('service')
        user = self.request.user

        # Find the active bookmark by content_id, service, and user
        bookmark = Bookmark.objects.filter(
            content_id=content_id,
            service=service,
            user=user,
            status=True
        ).first()  

        if not bookmark:
            raise NotFound("Active bookmark with the specified content_id not found for the user.")

        return bookmark
        
    @swagger_auto_schema(
        operation_description="Deactivate a bookmark by setting its status to False using content_id and user",
        manual_parameters=[
            openapi.Parameter(
                'content_id',
                openapi.IN_PATH,
                description="Content ID of the bookmark to be deactivated",
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'service',
                openapi.IN_QUERY,
                description="Name of the service from which the bookmark is being removed",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    def perform_destroy(self, instance):
        # Deactivate the bookmark
        instance.status = False
        instance.save()
        
        



class BulkModifyBookmarksView(APIView):
    permission_classes = [IsAuthenticated]


    @swagger_auto_schema(
        operation_description="Bulk modify bookmarks by adding and removing them.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'add': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'service': openapi.Schema(type=openapi.TYPE_STRING, description='The service to be bookmarked (e.g., quran, mafatih, etc.)'),
                            'content_id': openapi.Schema(type=openapi.TYPE_STRING, description='The content ID of the service'),
                            'text': openapi.Schema(type=openapi.TYPE_STRING, description='Optional text for the bookmark', nullable=True),
                        }
                    ),
                    description="List of items to add to bookmarks"
                ),
                'remove': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'content_id': openapi.Schema(type=openapi.TYPE_STRING, description='The content ID of the service to be removed'),
                            'service': openapi.Schema(type=openapi.TYPE_STRING, description='The service to be bookmarked (e.g., quran, mafatih, etc.)'),
                        }
                    ),
                    description="List of items to remove from bookmarks"
                )
            },
            required=['add', 'remove'],  # Specify which fields are required
            example={
                'add': [
                    {'service': 'quran', 'content_id': '123', 'text': 'My favorite verse'},
                    {'service': 'mafatih', 'content_id': '456', 'text': 'Daily dua'}
                ],
                'remove': [
                    {'content_id': '789', 'service': 'quran'},
                ]
            }
        ),
        responses={200: openapi.Response(description="Bulk modify completed.")}
    )
    def post(self, request):
        user = request.user
        data = request.data
        # text = item.get('text')  # بررسی فیلد text

        # پردازش آیتم‌های افزودن
        add_items = data.get('add', [])
        for item in add_items:
            service = item.get('service')
            content_id = item.get('content_id')
            text = item.get('text')  # بررسی فیلد text
            if service and content_id:
                bookmark = Bookmark.objects.filter(user=user, service=service, content_id=content_id).first()
                if bookmark:
                    bookmark.status = True
                    if text:
                        bookmark.text = text
                    bookmark.save()
                else:
                    bookmark = Bookmark.objects.create(user=user, service=service, content_id=content_id, text=text, status=True)

        # پردازش آیتم‌های حذف
        remove_items = data.get('remove', [])
        for item in remove_items:
            service = item.get('service')
            content_id = item.get('content_id')
            if content_id:
                bookmark = Bookmark.objects.filter(user=user, service=service, content_id=content_id, status=True).first()
                if bookmark:
                    bookmark.status = False
                    bookmark.save()

        return Response({"message": "Bulk modify completed."}, status=status.HTTP_200_OK)
