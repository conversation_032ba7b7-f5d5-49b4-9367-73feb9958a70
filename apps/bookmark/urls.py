
from django.urls import path, include

from .views import AddBookmarkView, RemoveBookmarkView, ListBookmarksView, BulkModifyBookmarksView



urlpatterns = [
    path('add/', AddBookmarkView.as_view(), name='add-bookmark'),
    path('remove/<str:service>/<str:content_id>/', RemoveBookmarkView.as_view(), name='remove-bookmark'),
    path('list/', ListBookmarksView.as_view(), name='list-bookmarks'),
    path('bulk/modify/', BulkModifyBookmarksView.as_view(), name='bulk-modify-bookmarks'),

]