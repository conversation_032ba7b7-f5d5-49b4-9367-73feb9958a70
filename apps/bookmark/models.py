from django.db import models

from apps.account.models import User




class Bookmark(models.Model):
    SERVICE_CHOICES = (
        ('quran', 'quran'),
        ('mafatih', 'mafatih'),
        ('ahkam', 'ahkam'),
        ('hadith', 'hadith'),
        ('book', 'book')
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="bookmark_bookmarks")
    service = models.CharField(max_length=255)
    content_id = models.CharField(max_length=255)
    text = models.TextField(null=True, blank=True)    
    status = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f'{self.user.username} - {self.service} - {self.content_id}'
    
    class Meta:
        verbose_name = 'Bookmark'
        verbose_name_plural = 'Bookmarks'