from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated

from apps.adhan.models import Adhan
from apps.adhan.serializer import AdhanSerializer


class AdhanList(ListAPIView):
    """
        adhan list api
    """
    serializer_class = AdhanSerializer
    # permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        return Adhan.objects.filter(
            adhan_type=Adhan.Types.adhan,
        )


class PreAdhanList(ListAPIView):
    """
        adhan list api
    """
    serializer_class = AdhanSerializer
    # permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        return Adhan.objects.filter(
            adhan_type=Adhan.Types.pre_adhan,
        ).all()
