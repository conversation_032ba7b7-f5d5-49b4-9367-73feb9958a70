from rest_framework import serializers
from .models import Adhan


class AdhanSerializer(serializers.ModelSerializer):
    file_path = serializers.SerializerMethodField('get_file_path')

    class Meta:
        model = <PERSON>han
        fields = ('id', 'name', 'description', 'file_path')

    def get_file_path(self, obj: <PERSON><PERSON>):
        try:
            return self.context.get('request').build_absolute_uri(
                obj.file.url
            )
        except Exception:
            return ''
