import os
from django.core.management.base import BaseCommand, CommandError


class Command(BaseCommand):
    help = (
        "removes created migration files for all or specific app \n"
        "usage: python manage.py rmigrations all\n"
        "usage: python manage.py rmigrations {app name} for multiple \n"
        "usage: python manage.py rmigrations {app name} {app name} ... for multiple \n"
    )

    def add_arguments(self, parser):
        parser.add_argument('app', nargs='+', type=str)

    def handle(self, **options):
        apps = options.get('app')
        if apps[0] == 'all':
            print('------------- remove all migration files in apps dir -------------')
            command = f'find apps/**/migrations/ -type f -name \*.py ! -name "__init__.py" -delete -print'
            os.system(command)

            command = f'find apps/**/**/migrations/ -type f -name \*.py ! -name "__init__.py" -delete -print'
            os.system(command)

            return

        for app in apps:
            print(f'------------- remove all migration of app {app} -------------')
            command = f'find apps/{app}/migrations/ -type f -name \*.py ! -name "__init__.py" -delete -print'
            os.system(command)
