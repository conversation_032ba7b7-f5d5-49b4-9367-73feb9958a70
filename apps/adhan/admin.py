from django.contrib import admin
from ajaxdatatable.admin import AjaxDatatable
from apps.adhan.models import <PERSON><PERSON>
from django import forms


# from ffmanager.field import FFmanagerWidget
#
#
# class AdhanF(forms.ModelForm):
#     file = forms.CharField(widget=FFmanagerWidget)
#
#     class Meta:
#         model = Adhan
#         fields = '__all__'


@admin.register(Adhan)
class AdhanAdmin(AjaxDatatable):
    list_display = ('name', 'description', 'created_at', 'adhan_type')
