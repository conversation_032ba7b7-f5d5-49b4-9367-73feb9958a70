# Generated by Django 3.2.5 on 2021-10-17 00:28

from django.db import migrations, models
import django.db.models.deletion
import filer.fields.file


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('filer', '0012_file_mime_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='Adhan',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('description', models.CharField(blank=True, help_text='could be null', max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('file', filer.fields.file.FilerFileField(on_delete=django.db.models.deletion.CASCADE, related_name='adhan_files', to='filer.file')),
            ],
        ),
    ]
