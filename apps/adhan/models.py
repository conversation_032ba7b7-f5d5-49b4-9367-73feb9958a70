from django.db import models
from filer.fields.file import FilerFileField
from django.utils.translation import gettext as _


class Adhan(models.Model):
    class Types(models.TextChoices):
        pre_adhan = 'pre-adhan', 'Pre-Adhan'
        adhan = 'adhan', 'Adhan'

    name = models.CharField(max_length=64)
    description = models.CharField(max_length=255, null=True, blank=True, help_text=_('could be null'))
    file = FilerFileField(related_name="adhan_files", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    adhan_type = models.CharField(max_length=255, choices=Types.choices, default=Types.adhan)

    def __str__(self):
        return self.name
