# Generated by Django 3.2.17 on 2023-08-01 15:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('account', '0029_user_deleted_at'),
    ]

    operations = [
        migrations.CreateModel(
            name='ElalHabib',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.JSONField(default=list, verbose_name='نام موکب')),
                ('nationalities', models.JSONField(blank=True, default=dict, verbose_name='ملیت')),
                ('capacity', models.PositiveSmallIntegerField(verbose_name='ظرفیت اسکان')),
                ('after_amood', models.PositiveSmallIntegerField(verbose_name='بعد از عمود شماره')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.adminuser')),
            ],
        ),
        migrations.CreateModel(
            name='ElalHabibOptions',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('icon', models.CharField(max_length=119, verbose_name='آیکون')),
                ('title', models.CharField(max_length=119, verbose_name='عنوان')),
            ],
        ),
        migrations.CreateModel(
            name='ElalHabibImages',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='elalhabib/', verbose_name='عکس')),
                ('priority', models.PositiveSmallIntegerField(default=10, verbose_name='اولویت')),
                ('mookeb', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='elalhabib.elalhabib', verbose_name='موکب')),
            ],
        ),
        migrations.AddField(
            model_name='elalhabib',
            name='options',
            field=models.ManyToManyField(blank=True, to='elalhabib.ElalHabibOptions', verbose_name='امکانات'),
        ),
    ]
