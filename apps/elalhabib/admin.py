import json

from ajaxdatatable.admin import AjaxDatatable, AjaxDatatableWithExcelExport
from dj_language.models import Language
from django import forms
from django.contrib import admin

from apps.elalhabib.models import ElalHabib, ElalHabibImages, ElalHabibOptions, MadahEvent
from apps.elalhabib.resource import ElalHabibResource
from utils import get_translation_schema
from utils.json_editor_field import JsonEditorWidget

Nationalities_field = {
    "type": "object",
    "format": "table",
    "title": " ",
    "required": ["nationalities", ],
    "properties": {
        "nationalities": {
            "type": "array",
            "uniqueItems": True,
            "format": "checkbox",
            "items": {
                "type": "string",
                "enum": [
                    "Iraq",
                    "Iran",
                    "Pakistan",
                    "Kuwait",
                    "Oman",
                    "Saudi Arabia",
                    "Lebanon",
                    "Syria",
                    "Azerbaijan",
                    "Russia",
                    "Turkey",
                    "other"
                ]
            }
        },
    }
}




class ElalHabibOptionForm(forms.ModelForm):
    class Meta:
        model = ElalHabibOptions
        exclude = ()
        widgets = {
            'translation': JsonEditorWidget(attrs={'schema': get_translation_schema}),
        }


@admin.register(ElalHabibOptions)
class OptionsAdmin(AjaxDatatable):
    list_display = ('title', 'icon',)
    search_fields = ('title', 'icon')
    form = ElalHabibOptionForm


@admin.register(MadahEvent)
class MadahEventAdmin(AjaxDatatable):
    list_display = ('madah', 'date',)
    search_fields = ('madah', 'date')


class ElalHabibForm(forms.ModelForm):
    class Meta:
        model = ElalHabib
        exclude = ()
        widgets = {
            'title': JsonEditorWidget(attrs={'schema': get_translation_schema}),
            'nationalities': JsonEditorWidget(attrs={'schema': json.dumps(Nationalities_field)}),
            'options': forms.CheckboxSelectMultiple(),
            'place': forms.RadioSelect(),
        }


class ElalHabibImagesAdmin(admin.TabularInline):
    model = ElalHabibImages
    extra = 1


class MadahEventAdmin(admin.TabularInline):
    model = MadahEvent
    autocomplete_fields = ('madah',)

    def get_extra(self, request, obj=None, **kwargs):
        if not obj:
            return 3
        return 1


@admin.register(ElalHabib)
class ElalHabibAdmin(AjaxDatatable):
    resource_class = ElalHabibResource
    list_display = ('_title', '_nationalities', 'after_amood', 'created_at', 'status', 'is_yearly')
    search_fields = ('title', 'nationalities', 'after_amood')
    list_filter = ('status', 'place', 'is_yearly', 'nationalities',)
    form = ElalHabibForm
    inlines = [
        # ElalHabibImagesAdmin,
        MadahEventAdmin
    ]
    readonly_fields = ('created_by', 'created_at', 'updated_at')
    change_form_template = "admin/elalhabib_change.html"

    fieldsets = (
        (None, {
            'fields': ('title', 'nationalities', 'city', 'options', 'description','status',)
        }),
        (None, {
            'fields': (
                ('place',),
                ('lat', 'lng',),
                ('capacity', 'after_amood',),
                ('phone_number', 'is_yearly',),
            )
        })
    )

    def get_import_formats(self):
        """
            Returns available export formats.
        """
        exclude_formats = ['yaml', 'tsv', 'json', 'ods', 'html']
        return [f for f in self.formats if f().can_export() if not f().get_title() in exclude_formats]

    @admin.display(description='عنوان')
    def _title(self, obj):
        return str(obj)

    @admin.display(description='ملیت')
    def _nationalities(self, obj):
        try:
            return ', '.join(obj.nationalities.get('nationalities', []))
        except Exception:
            return ''

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user

        return super().save_model(request, obj, form, change)
