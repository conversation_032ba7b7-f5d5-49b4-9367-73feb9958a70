{% load i18n %}
{% load static %}
<!doctype html>
<html lang="{{ LANGUAGE_CODE }}"
      dir="{% if LANGUAGE_CODE == 'fa' or LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{{ translations.submit_button }}</title>
    <script src="{% static "admin/panel/global_assets/js/main/jquery.min.js" %}"></script>

    <link href="{% static 'admin/panel/global_assets/css/icons/icomoon/styles.min.css' %}" rel="stylesheet"
          type="text/css">
    <link href="{% static 'admin/panel/global_assets/css/icons/material/styles.min.css' %}" rel="stylesheet"
          type="text/css">
    <link href="{% static 'admin/panel/assets/css/bootstrap.min.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'admin/panel/assets/css/bootstrap_limitless.min.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'admin/panel/global_assets/css/extras/animate.min.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'admin/panel/assets/css/layout.min.css' %}?ver=2" rel="stylesheet" type="text/css">
    <link href="{% static 'admin/panel/assets/css/components.min2.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'admin/panel/assets/css/colors.min.css' %}" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vazir-font/30.1.0/font-face.css">
    <link href="{% static 'admin/panel/assets/css/styles.css' %}?ver=2" rel="stylesheet" type="text/css">
    <link href="{% static 'admin/panel/assets/css/persian.datepicker.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'admin/panel/assets/css/main-custom-style.css' %}" rel="stylesheet" type="text/css">
    <link rel="manifest"
          href="https://habibapp.com/static/uploads/main/97/7f/977fdf26-59eb-4e96-ac1d-351430ffb783/manifest.json">


    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css"/>
    <!-- Custom CSS for a minimalistic look -->
    <style>
        #map {
            height: 400px;
            width: 100%;
            margin-top: 20px;

        }
    </style>


    {% block styles %}{% endblock %}

    <!-- base-js -->
    {% block djangoMedia %}{% endblock djangoMedia %}
    <script src="{% static "admin/panel/global_assets/js/main/bootstrap.bundle.min.js" %}"></script>
    <script src="{% static "admin/panel/global_assets/js/plugins/loaders/blockui.min.js" %}"></script>
    <script src="{% static "admin/panel/global_assets/js/plugins/ui/ripple.min.js" %}"></script>
    <script src="{% static "admin/panel/assets/js/app.js" %}"></script>
    <script src="{% static "admin/panel/js/app.js" %}"></script>
    <script src="{% static "admin/panel/assets/js/vue.js" %}"></script>
    <script src="{% static "admin/panel/assets/js/moment.js" %}"></script>
    <script src="{% static "admin/panel/assets/js/moment-jalaali.js" %}"></script>
    <script src="{% static "admin/panel/assets/js/vue-persian-datetime-picker-browser.js" %}"></script>

    <!-- /core JS files -->
    <script src="{% static "admin/panel/global_assets/js/plugins/tables/datatables/datatables.min.js" %}"></script>
    <script src="{% static "admin/panel/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js" %}"></script>
    <script src="{% static "admin/panel/global_assets/js/plugins/forms/selects/select2.min.js" %}"></script>
    <script src="{% static "admin/panel/global_assets/js/plugins/notifications/jgrowl.min.js" %}"></script>
    <script src="{% static "admin/panel/global_assets/js/plugins/notifications/noty.min.js" %}"></script>
    <!-- Theme JS files -->
    <script src="{% static "admin/panel/global_assets/js/plugins/forms/styling/uniform.min.js" %}"></script>
    <script src="{% static "admin/panel/global_assets/js/demo_pages/form_checkboxes_radios.js" %}"></script>
    <script src="{% static "admin/panel/assets/js/custom.js" %}"></script>
    <script src="{% static 'admin/panel/js/jsoneditor.min.js' %}"></script>
    <script src="{% static 'admin/panel/tinymce/tinymce.min.js' %}"></script>

    <style>
        #date-view-editor-id_title thead,
        #date-view-editor-id_title .btn-group,
        #date-view-editor-id_title td[data-schemapath="root.0.language_code"],
        #date-view-editor-id_title td:nth-child(3) {
            display: none;
        }

        #date-view-editor-id_title .bg-light {
            background: none;
            padding: 0;
        }

        #date-view-editor-id_title td[data-schemapath="root.0.title"] {
            padding: 0;
            border: 0 !important;
        }

        #date-view-editor-id_title .card-title {
            display: none !important;
        }

        #date-view-editor-id_title td[data-schemapath="root.0.title"] input {
            background: #fff;
        }

        #date-view-editor-id_nationalities .bg-light {
            background: none !important;
        }

        #date-view-editor-id_nationalities .card-title {
            display: none !important;
        }

        #date-view-editor-id_nationalities label.required {
            display: none !important;
        }

        #date-view-editor-id_nationalities .je-object__controls {
            display: none;
        }

        #date-view-editor-id_nationalities div[data-schemapath="root.nationalities"] > div > div {
            column-count: 2;
        }

        #date-view-editor-id_nationalities div[data-schemapath="root.nationalities"] > div > div .form-check {
            padding: 10px 0 10px 23px;
        }

        #date-view-editor-id_nationalities div[data-schemapath="root.nationalities"] > div > div .form-check label {
            width: 100%;
        }

        .form-check label, .form-check-label input {
            padding: 1px 20px !important;
        }

        @media (max-width: 420px) {
            .field-after_amood {
                margin-top: 15px !important;
            }

            .btn.btn-success.legitRipple {
                position: fixed;
                bottom: 0;
                width: 100%;
                height: 60px;
                padding: 0 !important;
                margin: 0 !important;
                right: 0;
                left: 0;
                border: 0;
                border-radius: 0 !important;
            }

            body {
                overflow-x: hidden;
            }

            .div-card-body-forms {
                padding: 0 !important;
            }

            #id_options {
                column-count: 1 !important;
            }
        }
    </style>
</head>
<body>
<main>

    <form method="post" action="{% url 'submit_elalhabib' %}" id="elalhabib_form" novalidate="novalidate">
        {% csrf_token %}
        <div class="row justify-content-center mt-md-5">
            <div class="col-lg-8 col-sm-12 div-card-body-forms">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div id="accordion-default-None">
                                <div class="card-body">
                                    {% if errors %}
                                        <div class="alert alert-danger" role="alert">
                                            {{ errors }}
                                        </div>
                                    {% endif %}

                                    {% if messages %}
                                        <div class="alert alert-success" role="alert">
                                            {% for message in messages %}
                                                {{ message }} <br>
                                            {% endfor %}
                                        </div>
                                    {% endif %}


                                <b>{{ translations.long_text }}</b>
                                    <fieldset id="None" data-parent="#accordion-default-None"
                                              class="module aligned collapsed mt-3">
                                        <div class="col-md-12 field-title">
                                            <div class="form-group ">
                                                <div class="row"><span class="col-form-label col-lg-2 "><label
                                                        for="id_title" class="required">نام موکب:</label></span>
                                                    <div class="col-lg-12 pl-0"><textarea name="title" cols="40"
                                                                                          rows="10"
                                                                                          schema="{&quot;type&quot;: &quot;array&quot;, &quot;format&quot;: &quot;table&quot;, &quot;title&quot;: &quot; &quot;, &quot;items&quot;: {&quot;type&quot;: &quot;object&quot;, &quot;title&quot;: &quot;Title&quot;, &quot;properties&quot;: {&quot;title&quot;: {&quot;type&quot;: &quot;string&quot;, &quot;title&quot;: &quot;Title&quot;}, &quot;language_code&quot;: {&quot;type&quot;: &quot;string&quot;, &quot;enum&quot;: [&quot;en&quot;, &quot;es&quot;, &quot;de&quot;, &quot;fa&quot;, &quot;az&quot;, &quot;ur&quot;, &quot;fr&quot;, &quot;tr&quot;, &quot;id&quot;, &quot;sw&quot;, &quot;ru&quot;, &quot;ar&quot;], &quot;default&quot;: &quot;en&quot;, &quot;title&quot;: &quot;Language Code&quot;}}}}"
                                                                                          required="required"
                                                                                          id="id_title"
                                                                                          class="form-control"
                                                                                          style="display: none;">[{&quot;title&quot;: &quot;&quot;, &quot;language_code&quot;: &quot;fa&quot;}]</textarea>
                                                        <div id="date-view-editor-id_title" class="json-view-editor"
                                                             data-theme="bootstrap4">


                                                        </div>
                                                        <input type="hidden" name="initial-title"
                                                               value="[{'title': '','language_code': 'fa'}]"
                                                               id="initial-id_title" class="form-control"></div>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="col-md-12 field-nationalities">
                                            <div class="form-group">
                                                <div class="row">
                                                    <span class="col-form-label col-lg-3">
                                                        <label for="id_nationalities">
                                                            {{ translations.nationality }}
                                                        </label>
                                                    </span>
                                                    <div class="col-lg-12 pl-0">
                                                        <textarea name="nationalities" cols="40"
                                                                  rows="10"
                                                                  schema="{&quot;type&quot;: &quot;object&quot;, &quot;format&quot;: &quot;table&quot;, &quot;title&quot;: &quot; &quot;, &quot;required&quot;: [&quot;nationalities&quot;], &quot;properties&quot;: {&quot;nationalities&quot;: {&quot;type&quot;: &quot;array&quot;, &quot;uniqueItems&quot;: true, &quot;format&quot;: &quot;checkbox&quot;, &quot;items&quot;: {&quot;type&quot;: &quot;string&quot;, &quot;enum&quot;: [&quot;Iraq&quot;, &quot;Iran&quot;, &quot;Pakistan&quot;, &quot;Kuwait&quot;, &quot;Oman&quot;, &quot;Saudi Arabia&quot;, &quot;Lebanon&quot;, &quot;Syria&quot;, &quot;Azerbaijan&quot;, &quot;Russia&quot;, &quot;Turkey&quot;, &quot;other&quot;]}}}}"
                                                                  id="id_nationalities"
                                                                  class="form-control"
                                                                  style="display: none;">{}</textarea>
                                                        <div id="date-view-editor-id_nationalities"
                                                             class="json-view-editor" data-theme="bootstrap4">
                                                        </div>
                                                        <script defer="defer">
                                                            $(document).ready(function () {
                                                                function init_editor_json_editor() {
                                                                    let editor_ = document.getElementById("id_nationalities")
                                                                    let schema_str = JSON.parse(editor_.value)
                                                                    let json_viewer_div = $('#date-view-editor-id_nationalities')

                                                                    let jsoneditor__ = new JSONEditor(
                                                                        json_viewer_div[0], {
                                                                            theme: 'bootstrap4',
                                                                            schema: {
                                                                                "type": "object",
                                                                                "format": "table",
                                                                                "title": " ",
                                                                                "required": ["nationalities"],
                                                                                "properties": {
                                                                                    "nationalities": {
                                                                                        "type": "array",
                                                                                        "uniqueItems": true,
                                                                                        "format": "checkbox",
                                                                                        "items": {
                                                                                            "type": "string",
                                                                                            "enum": ["Iraq", "Iran", "Pakistan", "Kuwait", "Oman", "Saudi Arabia", "Lebanon", "Syria", "Azerbaijan", "Russia", "Turkey", "other"]
                                                                                        }
                                                                                    }
                                                                                }
                                                                            },
                                                                            disable_edit_json: true,
                                                                            disable_properties: true,
                                                                            disable_array_delete_all_rows: true,
                                                                            disable_array_delete_last_row: true,
                                                                            disable_array_reorder: true,
                                                                            grid_columns: 3,
                                                                            prompt_before_delete: false,
                                                                            disable_collapse: true,
                                                                            startval: schema_str || []
                                                                        })
                                                                    editor_.editor = jsoneditor__
                                                                    jsoneditor__.on('change', () => {
                                                                        $(editor_).val(JSON.stringify(jsoneditor__.getValue()))
                                                                    })
                                                                }

                                                                init_editor_json_editor()
                                                            })
                                                        </script>
                                                        <input type="hidden" name="initial-nationalities" value="{}"
                                                               id="initial-id_nationalities" class="form-control">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="col-md-12">
                                            <div class="row">


                                                <div class="col-md-6 col-sm-12 pt-2">
                                                    <div class="row">
                                                        <span class="col-form-label col-lg-3">
                                                            <label>
                                                                {{ translations.location }}
                                                            </label>
                                                        </span>
                                                        {{ form.place }}
                                                    </div>
                                                </div>

                                                <div class="form-group d-none">
                                                    <label for="latitude">Latitude</label>
                                                    <input type="text" class="form-control" id="latitude" name="lat"
                                                           readonly>
                                                </div>
                                                <div class="form-group d-none">
                                                    <label for="longitude">Longitude</label>
                                                    <input type="text" class="form-control" id="longitude" name="lng"
                                                           readonly>
                                                </div>
                                                <div class="col-md-6 col-sm-12" id="map-group">
                                                    <div id="map"></div>
                                                </div>


                                                <div class="col-md-6 col-sm-12 field-after_amood" id="after-amood-group">
                                                    <div class="form-group">
                                                        <div class="row">
                                                            <span class="col-form-label col-lg-3">
                                                                <label for="id_after_amood" class="required">
                                                                    {{ translations.after_column_number }}
                                                                </label>
                                                            </span>
                                                            <div class="col-lg-12 pl-0">
                                                                <input type="number" name="after_amood" min="0"
                                                                       required="required"
                                                                       id="id_after_amood"
                                                                       class="vIntegerField form-control">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-12 field-address" id="address-group">
                                                    <div class="form-group">
                                                        <div class="row">
                                                    <span class="col-form-label col-lg-3">
                                                        <label for="id_address">
                                                            {{ translations.address }}
                                                        </label>
                                                    </span>
                                                            <div class="col-lg-12 pl-0">
                                                        <textarea name="address" rows="10" cols="40"
                                                                  id="id_address"
                                                                  class="vLargeTextField form-control"></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <script>
                                                    $(document).ready(function() {
                                                        function toggleFields() {
                                                            var selectedPlace = $('#id_place').val();
                                                            if (selectedPlace === 'karbala' || selectedPlace === 'najaf' || selectedPlace === 'iran') {
                                                                $('#map-group').removeClass('d-none');
                                                                $('#address-group').removeClass('d-none');
                                                                $('#after-amood-group').addClass('d-none');
                                                            } else {
                                                                $('#map-group').addClass('d-none');
                                                                $('#address-group').addClass('d-none');
                                                                $('#after-amood-group').removeClass('d-none');
                                                            }
                                                        }

                                                        // Initialize the field visibility based on the current selection
                                                        toggleFields();

                                                        // Listen for changes in the place dropdown
                                                        $('#id_place').change(function() {
                                                            toggleFields();
                                                        });
                                                    });
                                                </script>

                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="row">
                                                <div class="col-md-6 col-sm-12 field-capacity">
                                                    <div class="form-group">
                                                        <div class="row">
                                                            <span class="col-form-label col-lg-3">
                                                                <label for="id_capacity" class="required">
                                                                    {{ translations.capacity }}
                                                                </label>
                                                            </span>
                                                            <div class="col-lg-12 pl-0">
                                                                <input type="number" name="capacity" min="0"
                                                                       required="required"
                                                                       id="id_capacity"
                                                                       class="vIntegerField form-control">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-sm-12 field-phone_number">
                                                    <div class="form-group">
                                                        <div class="row">
                                                            <span class="col-form-label col-lg-3">
                                                                <label for="id_phone_number">
                                                                    {{ translations.manager_contact }}
                                                                </label>
                                                            </span>
                                                            <div class="col-lg-12 pl-0">
                                                                <input type="text" name="phone_number" maxlength="255"
                                                                       id="id_phone_number"
                                                                       class="vTextField form-control">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 field-options">
                                            <div class="form-group">
                                                <div class="row">
                                                    <span class="col-form-label col-lg-3">
                                                        <label>
                                                            {{ translations.facilities }}
                                                        </label>
                                                    </span>
                                                    <div class="col-lg-12 pl-0">
                                                        <div class="related-widget-wrapper row">
                                                            <div class="col">
                                                                <ul id="id_options"
                                                                    style="columns: 2; padding-inline-start: 0px">
                                                                    {% for option in options %}
                                                                        <li style="padding: 10px 5px; list-style: none;">
                                                                            <input type="checkbox" name="options"
                                                                                   value="{{ option.id }}"
                                                                                   id="id_options_{{ option.id }}"
                                                                                   class="">
                                                                            <label for="id_options_{{ option.id }}"
                                                                                   class="">{{ option.title }}</label>
                                                                        </li>
                                                                    {% endfor %}
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 field-description">
                                            <div class="form-group">
                                                <div class="row">
                                                    <span class="col-form-label col-lg-3">
                                                        <label for="id_description">
                                                            {{ translations.description }}
                                                        </label>
                                                    </span>
                                                    <div class="col-lg-12 pl-0">
                                                        <textarea name="description" rows="10" cols="40"
                                                                  id="id_description"
                                                                  class="vLargeTextField form-control"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 field-is_yearly">
                                            <div class="form-group col-12 col-sm-6 col-xl">
                                                <div class="row">
                                                    <input type="checkbox" name="is_yearly" id="id_is_yearly"
                                                           class="mx-2">
                                                    <label for="id_is_yearly" class="vCheckboxLabel">
                                                        {{ translations.annual }}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                                <div class="col-md-12 field-is_yearly">
                                    <div class="form-group col-12 col-sm-6 col-xl">
                                        <div class="row">
                                            <button class="btn btn-success">{{ translations.submit_button }}</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</main>

<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script>
    var map;
    var marker;

    // تابع مقداردهی اولیه نقشه
    function initializeMap(lat, lng) {
        lat = 32.6160;
        lng = 44.0249;
        map = L.map('map').setView([lat, lng], 13);


        // استفاده از لایه CartoDB Positron برای استایل ساده
        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        // افزودن لیسنر برای کلیک روی نقشه
        map.on('click', onMapClick);
    }

    function onMapClick(e) {
        var lat = e.latlng.lat;
        var lng = e.latlng.lng;

        // تنظیم مقدار فیلدهای فرم
        document.getElementById('latitude').value = lat;
        document.getElementById('longitude').value = lng;

        // اگر مارکر وجود نداشت، آن را اضافه کن، در غیر اینصورت موقعیت آن را بروزرسانی کن
        if (!marker) {
            marker = L.marker(e.latlng).addTo(map);
        } else {
            marker.setLatLng(e.latlng);
        }
    }

    $(document).ready(function() {
        function updateMapLocation() {
            var selectedPlace = $('#id_place').val();
            var lat, lng;

            if (selectedPlace === 'karbala') {
                lat = 32.6160;
                lng = 44.0249; // مختصات کربلا
            } else if (selectedPlace === 'najaf') {
                lat = 31.9959;
                lng = 44.3140; // مختصات نجف
            } else if (selectedPlace === 'iran') {
                lat = 35.6892;
                lng = 51.3890; // مختصات تهران
            } else {
                lat = 32.6160;
                lng = 44.0249; // مختصات کربلا
            }

            if (map) {
                map.setView([lat, lng], 13);
                if (marker) {
                    marker.setLatLng([lat, lng]);
                } else {
                    marker = L.marker([lat, lng]).addTo(map);
                }
            } else {
                initializeMap(lat, lng);
            }
        }

        // Initialize the map based on the current selection
        updateMapLocation();

        // Update the map location whenever the selected place changes
        $('#id_place').change(function() {
            updateMapLocation();
        });
    });
</script>


<script>
    $(document).ready(function () {
        function init_editor_json_editor() {
            let editor_ = document.getElementById("id_title")
            let schema_str = JSON.parse(editor_.value)
            let json_viewer_div = $('#date-view-editor-id_title')

            let jsoneditor__ = new JSONEditor(
                json_viewer_div[0], {
                    theme: 'bootstrap4',
                    schema: {
                        "type": "array",
                        "format": "table",
                        "title": " ",
                        "items": {
                            "type": "object",
                            "title": "Title",
                            "properties": {
                                "title": {"type": "string", "title": "Title", "required": true},
                                "language_code": {
                                    "type": "string",
                                    "enum": ["en", "es", "de", "fa", "az", "ur", "fr", "tr", "id", "sw", "ru", "ar"],
                                    "default": "en",
                                    "title": "Language Code"
                                }
                            }
                        }
                    },
                    disable_edit_json: true,
                    disable_properties: true,
                    disable_array_delete_all_rows: true,
                    disable_array_delete_last_row: true,
                    disable_array_reorder: true,
                    grid_columns: 3,
                    prompt_before_delete: false,
                    disable_collapse: true,
                    startval: schema_str || []
                })
            editor_.editor = jsoneditor__
            jsoneditor__.on('change', () => {
                $(editor_).val(JSON.stringify(jsoneditor__.getValue()))
            })
        }

        init_editor_json_editor()
    })


    $("#root[0][title]").attr('required', true)

</script>

</body>
</html>

