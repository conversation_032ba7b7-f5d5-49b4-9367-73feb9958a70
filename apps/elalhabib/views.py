import json

import requests
from django.contrib import messages
from django.db.models import Value, Case, When, IntegerField, Q, Count
from django.http import JsonResponse
from django.shortcuts import render, redirect
from rest_framework.generics import ListAPIView
from captcha.fields import <PERSON><PERSON><PERSON><PERSON>a<PERSON>ield
from captcha.widgets import <PERSON><PERSON><PERSON>tchaV2Checkbox

from apps.elalhabib.serializer import ElalHabibSerializer, ElalHabibSingerInListSerializer
from apps.account.models import User, AdminUser
from .models import ElalHabib, ElalHabibOptions
from ..hussainiya.singers.models import Singer
from django import forms

class ElalHabibView(ListAPIView):
    serializer_class = ElalHabibSerializer

    def get_queryset(self):
        return ElalHabib.objects.exclude(
            after_amood__isnull=True, place=ElalHabib.PlaceType.Road
        ).filter(status=ElalHabib.StatusType.ACTIVE)


class ElalHabibMadahView(ListAPIView):
    serializer_class = ElalHabibSingerInListSerializer

    def get_queryset(self):
        lang = self.request.LANGUAGE_CODE

        return Singer.objects.annotate(
            user_lang=Case(
                When(languages__code__in=[lang], then=Value(1)),
                default=Value(0),
                output_field=IntegerField()
            ),
            songs_count=Count('song', distinct=True),
        ).filter(
            Q(singer_type=Singer.SingerType.madah, ) | Q(singer_type=Singer.SingerType.singer, )
        ).order_by('-user_lang', '-songs_count')


class ElalHabibLecturesView(ListAPIView):
    serializer_class = ElalHabibSingerInListSerializer

    def get_queryset(self):
        lang = self.request.LANGUAGE_CODE

        return Singer.objects.annotate(
            user_lang=Case(
                When(languages__code__in=[lang], then=Value(1)),
                default=Value(0),
                output_field=IntegerField()
            )
        ).filter(
            singer_type=Singer.SingerType.lecture,
        ).order_by('-user_lang')


class ElalForm(forms.ModelForm):
    class Meta:
        model = ElalHabib
        fields = ['title', 'nationalities', 'city', 'start_year',
                  'capacity', 'options', 'after_amood', 'phone_number',
                  'lat', 'lng',
                  'is_yearly', 'description', 'place', 'address',
        ]
        # exclude = ('created_by',)



translations = {
    "fa": {
        "mawkeb_name": "نام موکب:",
        "nationality": "ملیت:",
        "location": "محل موکب:",
        "after_column_number": "بعد از عمود شماره:",
        "capacity": "ظرفیت اسکان:",
        "manager_contact": "شماره تماس مدیر:",
        "facilities": "امکانات:",
        "description": "توضیحات:",
        "annual": "این موکب هرساله در این مکان برپا میشود",
        "submit_button": "ثبت موکب",
        "address": "آدرس موکب",
        "long_text": "در طلیعه‌ای از خدمت عاشقانه به آستان سیدالشهدا علیه السلام، کارگروه الی الحبیب به عنوان یکی از نهادهای پویای پلتفرم حبیب، سالانه می‌کوشد نام خود را در کنار آن خادمان گرامی و موکبداران عزیز به ثبت رساند. این سرافرازی، هرچند به فراخور سختی‌ها و کمبودها دست‌خوش چالش‌هایی می‌شود، اما به یاری و همراهی شما خادم محترم، این مسیر بیش از پیش کامل و درخشان خواهد شد. باشد که این تلاش‌ها، چراغی برای رهروان این راه نورانی باشد."
    },
    "de": {
        "mawkeb_name": "Name der Karawane:",
        "nationality": "Nationalität:",
        "location": "Ort der Karawane:",
        "after_column_number": "Nach der Säulennummer:",
        "capacity": "Unterkunftskapazität:",
        "manager_contact": "Kontakt des Managers:",
        "facilities": "Einrichtungen:",
        "description": "Beschreibung:",
        "annual": "Dieses Mawkib wird jedes Jahr an diesem Ort aufgebaut",
        "submit_button": "Karawane registrieren",
        "address": "Adresse der Mawkib",
        "long_text": "In unserem unermüdlichen Bestreben, dem verehrten Imam al-Husayn (Friede sei mit ihm) zu dienen, bemüht sich das „eilal-Habib“-Komitee, als eine der aktiven Gruppen innerhalb der Habib-Plattform, jedes Jahr darum, seinen Namen neben jenen ehrenwerten Dienern und geschätzten Mawkib-Organisatoren zu verewigen. Obwohl dieses edle Vorhaben jedes Jahr mit eigenen Herausforderungen und Unvollkommenheiten konfrontiert ist, wird es durch Ihre geschätzte Unterstützung und Teilnahme, verehrter Diener, umso vollständiger und strahlender. Mögen diese Bemühungen als leuchtender Wegweiser für jene dienen, die diesen gesegneten Pfad beschreiten."
    },
    "tr": {
        "mawkeb_name": "Kafile Adı:",
        "nationality": "Milliyet:",
        "location": "Kafile Konumu:",
        "after_column_number": "Sütun Numarasından Sonra:",
        "capacity": "Konaklama Kapasitesi:",
        "manager_contact": "Yönetici İletişim Numarası:",
        "facilities": "Tesisler:",
        "description": "Açıklama:",
        "annual": "Bu Mawkib her yıl bu yerde kuruluyor",
        "submit_button": "Kafileyi Kaydet",
        "address": "Mawkib'in Adresi",
        "long_text": "İmam Hüseyin’e (aleyhisselam) hizmet etme yolundaki sarsılmaz çabalarımızda, \"eilal-Habib\" komitesi, Habib platformasının aktif gruplarından biri olarak her yıl adını o saygıdeğer hizmetkârlar ve değerli Mawkib organizatörlerinin yanında yazdırmaya çalışır. Bu yüce gayretler, her yıl kendi zorlukları ve eksiklikleriyle karşılaşsa da, sizin değerli desteğiniz ve katılımınızla, kıymetli hizmetkâr, bu yol daha da mükemmel ve parlak hale gelecektir. Bu çabalar, bu kutsal yolda yürüyenler için bir ışık rehberi olsun."
    },
    "az": {
        "mawkeb_name": "Karvanın adı:",
        "nationality": "Milliyyət:",
        "location": "Karvanın yeri:",
        "after_column_number": "Sütun nömrəsindən sonra:",
        "capacity": "Yaşayış qabiliyyəti:",
        "manager_contact": "Müdirin əlaqə nömrəsi:",
        "facilities": "Təsisatlar:",
        "description": "Təsvir:",
        "annual": "Bu Mawkib hər il bu məkanda qurulur",
        "submit_button": "Karvanı Qeydiyyatdan Keçirin",
        "address": "Mawkib ünvanı",
        "long_text": "İmam Hüseynin (əleyhissəlam) müqəddəs yolunda xidmət etmək üçün yorulmaz səylərimizdə, \"eilal-Habib\" komitəsi Habib platformasının fəal qruplarından biri olaraq hər il öz adını o hörmətli xidmətkarların və dəyərli Mawkib təşkilatçılarının yanında qeyd etdirməyə çalışır. Bu nəcib səylər hər il öz çətinlikləri və nöqsanları ilə üzləşsə də, sizin dəyərli dəstəyiniz və iştirakınızla, əziz xidmətkar, bu yol daha da kamil və parlaq olacaq. Bu cəhdlər, bu mübarək yolda addımlayanlar üçün bir nur mayakı olsun."
    },
    "ar": {
        "mawkeb_name": "اسم الموكب:",
        "nationality": "الجنسية:",
        "location": "مكان الموكب:",
        "after_column_number": "بعد العمود رقم:",
        "capacity": "سعة السكن:",
        "manager_contact": "رقم الاتصال بالمدير:",
        "facilities": "التسهيلات:",
        "description": "الوصف:",
        "annual": "يُقام هذا الموكب في هذا المكان كل عام.",
        "submit_button": "تسجيل الموكب",
        "address": "عنوان الموكب",
        "long_text": "في ظل سعينا لخدمة سيد الشهداء عليه السلام، تسعى لجنة \"الي الحبيب\" كإحدى الفرق النشطة ضمن منصة حبيب، سنوياً إلى تسجيل اسمها بجانب أولئك الخدام الكرام وأصحاب المواكب الأعزاء. ورغم ما يواجه هذا السعي من تحديات ونواقص، فإن هذه المسيرة ستصبح أكثر كمالاً وإشراقاً بفضل دعمكم ومشاركتكم أيها الخادم الكريم. لعلّ هذه الجهود تكون نوراً يهتدي به السائرون في هذا الطريق المبارك."
    },
    "en": {
        "mawkeb_name": "Mawkeb Name:",
        "nationality": "Nationality:",
        "location": "Mawkeb Location:",
        "after_column_number": "After Column Number:",
        "capacity": "Accommodation Capacity:",
        "manager_contact": "Manager Contact Number:",
        "facilities": "Facilities:",
        "description": "Description:",
        "annual": "This Mawkib is set up at this location every year",
        "submit_button": "Register Mawkeb",
        "address": "Address of the Mawkib",
        "long_text": "In our unwavering devotion to serve the revered Imam al-Husayn (peace be upon him), the \"eilal-Habib\" committee, as one of the active groups within the Habib platform, strives annually to inscribe its name alongside those honorable servants and cherished Mawkib organizers. Though this noble endeavor faces its own set of challenges and shortcomings each year, it is through your esteemed support and participation, dear servant, that this journey will grow ever more complete and radiant. May these efforts serve as a guiding light for those who tread this blessed path."
    }
}



IP_API_URL = "http://ip-api.com/json/"
def get_location_by_ip(ip):
    response = requests.get(f"{IP_API_URL}{ip}")
    if response.status_code == 200:
        return response.json()
    return None

def get_language(request):
    # ip="**********"
    ip = request.META.get('HTTP_X_FORWARDED_FOR').split(',')[0]
    location_data = get_location_by_ip(ip)

    if location_data:
        country_code = location_data.get('countryCode')

        if country_code in ['IR']:
            language = 'fa'
        elif country_code in ['TR']:
            language = 'tr'
        elif country_code in ['AZ']:
            language = 'az'
        elif country_code in ['SA', 'AE', 'IQ', 'KW', 'OM', 'QA', 'BH', 'JO', 'LB', 'SY', 'YE', 'DZ', 'EG', 'LY',
                              'MA', 'MR', 'TN']:
            language = 'ar'
        elif country_code in ['FR']:
            language = 'fr'
        elif country_code in ['DE']:
            language = 'de'
        else:
            language = 'en'
    else:
        language = 'en'
    return language


def load_translations(request):
    lang_code = get_language(request)  # دریافت کد زبان فعلی کاربر
    return translations.get(lang_code, translations['fa']),lang_code


def submit_elalhabib(request):
    options = ElalHabibOptions.objects.all()
    current_translations = load_translations(request)  # بارگذاری ترجمه‌های مربوط به زبان فعلی
    options_with_translation = []
    for option in options:
        translated_title = None
        for translation in option.translation:
            if current_translations[1] == 'fa':
                translated_title = option.title
                break
            if translation.get('language_code') == current_translations[1]:
                translated_title = translation.get('title')
                break
        if not translated_title:
            # اگر زبان مورد نظر پیدا نشد، از زبان انگلیسی به عنوان پیش‌فرض استفاده می‌کنیم
            for translation in option.translation:
                if translation.get('language_code') == 'en':
                    translated_title = translation.get('title')
                    break
        options_with_translation.append({
            'id': option.id,
            'title': translated_title or option.title  # اگر هیچ ترجمه‌ای پیدا نشد، از عنوان اصلی استفاده می‌کنیم
        })
    if request.method == 'POST':
        form = ElalForm(data=request.POST)
        if form.is_valid():
            try:
                obj = form.save(commit=False)
                obj.save()
                messages.success(request, f" موکب {obj} با موفقیت ثبت شد ")
                form = ElalForm()  # reset form after successful submission
            except ValueError as e:
                messages.error(request, str(e))
            except Exception as e:
                messages.error(request, "یک خطای غیرمنتظره رخ داده است. لطفاً دوباره تلاش کنید.")
            return render(request, "front_elalhabib.html", {
                'options': options_with_translation,
                'form': form,
                'errors': form.errors.as_ul(),
                'translations': current_translations[0],
                'language_code': current_translations[1]
            })
        else:
            request.session['form_success'] = False
            return render(request, "front_elalhabib.html", {
                'options': options_with_translation,
                'form': form,
                'errors': form.errors.as_ul(),
                'translations': current_translations[0],
                'language_code': current_translations[1]
            })
    else:
        form = ElalForm()  # display empty form for GET request
        return render(request, "front_elalhabib.html", {
            'options': options_with_translation,
            'form': form,
            'translations': current_translations[0],
            'language_code': current_translations[1]
        })

def elalhabib_filters(request):
    data = list(ElalHabibOptions.objects.values('title', 'translation', 'icon'))
    o = []
    for l in data:
        title = None
        for t in l['translation']:
            if t['language_code'] == request.LANGUAGE_CODE:
                title = t['title']
        if title == None or title == '':
            title = l['title']

        res = {'title': title, 'icon': l['icon']}
        o.append(res)

    return JsonResponse(data={
        'count': len(data),
        'results': o,
    })
