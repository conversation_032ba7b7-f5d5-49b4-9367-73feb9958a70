from django.db import models
from django.utils.translation import gettext_lazy as _

from apps.hussainiya.singers.models import <PERSON>


def get_default_title():
    return [{
        'title': '',
        'language_code': 'fa',
    }]


class ElalHabib(models.Model):
    class PlaceType(models.TextChoices):
        Karbala = 'karbala', 'کربلا'
        Road = 'road', 'در مسیر'
        Najaf = 'najaf', 'نجف'
        Iran = 'iran', 'ایران'

    class StatusType(models.TextChoices):
        ACTIVE = 'active', 'فعال'
        INACTIVE = 'inactive', 'غیرفعال'
        PENDING = 'pending', 'در انتظار'

    title = models.JSONField(verbose_name=_('نام موکب'), default=get_default_title)
    nationalities = models.JSONField(default=dict, blank=True, verbose_name=_('ملیت'))
    city = models.CharField(verbose_name='شهر', max_length=255, null=True, blank=True)
    start_year = models.CharField(verbose_name='سال شروع فعالیت', max_length=255, null=True, blank=True)
    capacity = models.PositiveSmallIntegerField(verbose_name=_('ظرفیت اسکان'), null=True, blank=True)
    options = models.ManyToManyField("ElalHabibOptions", blank=True, verbose_name=_('امکانات'))
    after_amood = models.PositiveSmallIntegerField(verbose_name=_('بعد از عمود شماره'), null=True, blank=True)
    phone_number = models.CharField(verbose_name='شماره تماس مدیر', max_length=255, null=True, blank=True)
    is_yearly = models.BooleanField(default=False, verbose_name='هر ساله')
    description = models.TextField(verbose_name='توضیحات', null=True, blank=True)
    address = models.TextField(verbose_name='آدرس', null=True, blank=True)
    place = models.CharField(choices=PlaceType.choices, verbose_name='مکان موکب', max_length=32, default=PlaceType.Karbala)
    lat = models.DecimalField(verbose_name='عرض جغرافیایی', max_digits=30, decimal_places=25, null=True, blank=True)
    lng = models.DecimalField(verbose_name='طول جغرافیایی', max_digits=30, decimal_places=25, null=True, blank=True)
    status = models.CharField(choices=StatusType.choices, verbose_name='وضعیت', max_length=32,
                              default=StatusType.ACTIVE)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('updated at'))
    created_by = models.ForeignKey("account.AdminUser", on_delete=models.SET_NULL, null=True)

    class Meta:
        verbose_name = 'موکب'
        verbose_name_plural = 'موکب ها'
        ordering = ('-id',)

    def __str__(self):
        langs = ['fa', 'en', 'ar']
        for l in langs:
            for tr in self.title:
                if tr['language_code'] == l:
                    return tr['title']

        return self.title[0]['title']

    def get_translation(self, lang):
        for tr in self.title:
            if tr['language_code'] == lang:
                return tr['title']

        return None


class ElalHabibImages(models.Model):
    image = models.ImageField(upload_to='elalhabib/', verbose_name=_('عکس'))
    priority = models.PositiveSmallIntegerField(default=10, verbose_name=_('اولویت'))
    mookeb = models.ForeignKey(ElalHabib, on_delete=models.CASCADE, verbose_name=_('موکب'), related_name='images')

    class Meta:
        verbose_name = 'عکس'
        verbose_name_plural = 'عکس های موکب'
        ordering = ('-priority', '-id')


def get_default_title_options():
    return [{
        'title': '',
        'language_code': 'en',
    }]


class ElalHabibOptions(models.Model):
    icon = models.CharField(verbose_name=_('آیکون'), max_length=119)
    title = models.CharField(verbose_name=_('عنوان'), max_length=119)
    translation = models.JSONField(verbose_name=_('ترجمه عنوان'), default=get_default_title_options)

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = 'امکانات'
        verbose_name_plural = 'امکانات موکب'
        ordering = ('-id',)


class MadahEvent(models.Model):
    mookeb = models.ForeignKey(ElalHabib, on_delete=models.CASCADE, verbose_name=_('موکب'), related_name='events')
    madah = models.ForeignKey(Singer, on_delete=models.CASCADE, verbose_name=_('مداح'), related_name='events')
    date = models.CharField(max_length=255, verbose_name=_('تاریخ برگذاری'))
