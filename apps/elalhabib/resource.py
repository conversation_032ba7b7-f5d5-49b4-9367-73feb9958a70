import json

from import_export.resources import ModelResource

from apps.elalhabib.models import ElalHabib, ElalHabibOptions


class ElalHabibResource(ModelResource):
    class Meta:
        skip_diff = True
        model = ElalHabib
        import_id_fields = ()
        exclude = ('created_at', 'id', 'created_by', 'updated_at')

    def before_import_row(self, row, row_number=None, **kwargs):
        nationalities = [] if not row['nationalities'] else row.pop('nationalities', '').split(',')
        row['nationalities'] = {'nationalities': nationalities}
        row['title'] = [{'title': row['title'], 'language_code': 'fa'}]
        local_fields = [f.name for f in ElalHabib._meta.local_fields]
        options = []
        to_remove_fields = []
        for k, v in row.items():
            if k not in local_fields:
                to_remove_fields.append(k)
                if v and v.strip():
                    options.append(k.strip())

        for f in to_remove_fields:
            row.pop(f)

        options = ElalHabibOptions.objects.filter(title__in=options).values_list('id', flat=True)
        row['options'] = ",".join(map(str, options))
