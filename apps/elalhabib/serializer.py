from rest_framework import serializers

from apps.elalhabib.models import ElalHabib, MadahEvent
from apps.hussainiya.singers.models import Singer


class ElalHabibSingerInListSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    def get_name(self, obj):
        request = self.context.get('request')

        if name := obj.get_translation(request.LANGUAGE_CODE):
            return name

        try:
            return obj.translations[0]['title']
        except:
            return '-'

    class Meta:
        model = Singer
        fields = ('name', 'slug')


class MadahEventSerializer(serializers.ModelSerializer):
    madah = ElalHabibSingerInListSerializer(many=False)

    class Meta:
        model = MadahEvent
        fields = ('madah', 'date')


class ElalHabibSerializer(serializers.ModelSerializer):
    options = serializers.SerializerMethodField()
    nationalities = serializers.ListField(source='nationalities.nationalities', allow_null=True, allow_empty=True,
                                          default=[])
    after_amood = serializers.SerializerMethodField()
    events = serializers.SerializerMethodField()

    def get_events(self, obj):
        return MadahEventSerializer(obj.events.order_by('-madah__priority'), context=self.context, many=True).data

    def get_after_amood(self, obj):
        if obj.place == ElalHabib.PlaceType.Najaf:
            return 0

        elif obj.place == ElalHabib.PlaceType.Karbala:
            return 1500

        elif obj.place == ElalHabib.PlaceType.Iran:
            return 1600

        return obj.after_amood

    def get_options(self, obj):
        request = self.context.get('request')
        a = []
        for i in list(obj.options.all().values()):
            title = None
            for t in i['translation']:
                if t['language_code'] == request.LANGUAGE_CODE:
                    title = t['title']
            if title == None or title == '':
                title = i['title']

            a.append({'icon': i['icon'], 'title': title})
        return a

    def get_images(self, obj):
        return [
            self.context['request'].build_absolute_uri(i.image.url) for i in obj.images.all()
        ]

    class Meta:
        model = ElalHabib
        fields = (
            'title', 'nationalities', 'capacity', 'options', 'after_amood', 'events',
            'description'
        )
