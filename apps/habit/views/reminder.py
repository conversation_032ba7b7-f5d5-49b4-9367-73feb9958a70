from rest_framework import generics, permissions, status
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.utils import timezone
from datetime import datetime, time
import re

from apps.notification.models import Reminder
from apps.habit.serializers.reminder import HabitReminderSerializer
from apps.habit.doc import swagger_reminder_info


def to_bool(value):
    """Convert various input types to boolean with comprehensive validation."""
    if isinstance(value, bool):
        return value
    elif isinstance(value, str):
        return value.lower() in ('true', '1', 'yes', 'on')
    elif isinstance(value, int):
        return bool(value)
    else:
        return bool(value)


def parse_call_time(call_time_str):
    """
    Parse call_time from HH:MM format and return a proper datetime.
    Returns None if parsing fails or input is invalid.
    """
    if not call_time_str:
        return None

    try:
        # Parse time format (HH:MM only)
        time_pattern = re.compile(r'^(\d{1,2}):(\d{2})$')
        match = time_pattern.match(str(call_time_str).strip())

        if match:
            hour = int(match.group(1))
            minute = int(match.group(2))

            # Validate time values
            if not (0 <= hour <= 23 and 0 <= minute <= 59):
                return None

            # Create datetime for today with the specified time
            today = timezone.now().date()
            call_time = timezone.datetime.combine(today, time(hour, minute))

            # Make it timezone aware
            return timezone.make_aware(call_time) if timezone.is_naive(call_time) else call_time

        # If format doesn't match, return None
        return None

    except (ValueError, TypeError):
        # If parsing fails, return None
        return None


class HabitReminderUpdateView(generics.UpdateAPIView):
    """
    API endpoint for updating habit reminder status.
    Creates reminder if it doesn't exist, updates if it exists.
    """
    permission_classes = [permissions.IsAuthenticated]
    queryset = Reminder.objects.all()

    def get_queryset(self):
        """Return reminders for the current user and habit service only."""
        return Reminder.objects.filter(
            user=self.request.user,
            service_name='habit',
            object_id=''
        )
    
    @swagger_auto_schema(
        tags=['habit'],
        operation_description="""
        Partially update or create a habit reminder with specified status and optional call time.

        ### Request Structure (PATCH):
        ```json
        {
            "status": true,           // Required: boolean - Whether reminder is active
            "call_time": "09:30"      // Optional: string - Time in HH:MM format (24-hour)
        }
        ```

        ### Behavior:
        - If reminder doesn't exist: Creates new reminder with provided fields
        - If reminder exists: Updates only the provided fields (partial update)
        - call_time only updates when valid time format is provided (HH:MM)
        - Invalid, empty, or null call_time values are ignored (field remains unchanged)

        ### Examples:
        - `{"status": true}` - Only update status, keep existing call_time
        - `{"status": false, "call_time": "14:30"}` - Update status and set call_time to 14:30
        - `{"status": false, "call_time": ""}` - Only update status, ignore empty call_time
        - `{"status": false, "call_time": null}` - Only update status, ignore null call_time
        - `{"status": true, "call_time": "invalid"}` - Only update status, ignore invalid time
        """,
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['status'],
            properties={
                'status': openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    description='Whether the habit reminder is active or not'
                ),
                'call_time': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description='Time when reminder should be triggered. Format: "HH:MM" (24-hour format). Optional - leave empty to clear.',
                    example='09:30'
                )
            }
        ),
        responses={
            status.HTTP_200_OK: openapi.Response(
                description="Reminder status updated successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'status': openapi.Schema(type=openapi.TYPE_BOOLEAN)
                    }
                )
            ),
            status.HTTP_201_CREATED: openapi.Response(
                description="Reminder created with specified status",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'status': openapi.Schema(type=openapi.TYPE_BOOLEAN)
                    }
                )
            ),
            status.HTTP_400_BAD_REQUEST: openapi.Response(
                description="Invalid request parameters",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'detail': openapi.Schema(type=openapi.TYPE_STRING)
                    }
                )
            )
        }
    )
    def patch(self, request):
        """Update or create habit reminder status."""
        reminder_status = request.data.get('status')
        call_time_str = request.data.get('call_time')

        if reminder_status is None:
            return Response({'detail': 'status parameter is required.'},
                          status=status.HTTP_400_BAD_REQUEST)

        # Convert to boolean with comprehensive validation
        reminder_status = to_bool(reminder_status)

        # Parse call_time if provided (returns None if invalid)
        call_time = parse_call_time(call_time_str)

        # Prepare defaults for reminder creation
        defaults = {
            'text': 'Daily Checklist Reminder',
            'status': reminder_status,
            'call_time': call_time  # Will be None if not provided or invalid
        }

        # Get or create reminder
        reminder, created = Reminder.objects.get_or_create(
            user=request.user,
            service_name='habit',
            object_id='',
            defaults=defaults
        )

        # Update fields if reminder already existed
        if not created:
            update_fields = []

            # Always update status if provided
            if reminder.status != reminder_status:
                reminder.status = reminder_status
                update_fields.append('status')

            # Only update call_time if it was provided AND has valid value
            if 'call_time' in request.data and call_time is not None:
                if reminder.call_time != call_time:
                    reminder.call_time = call_time
                    update_fields.append('call_time')

            if update_fields:
                reminder.save(update_fields=update_fields)

        # Generate response message
        action = "created" if created else "updated"
        state = "activated" if reminder_status else "deactivated"
        message = f"Reminder {action} and {state}." if created else f"Reminder {state}."

        return Response({
            "message": message,
            "status": reminder_status
        }, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)


class HabitReminderInfoView(generics.GenericAPIView):
    """
    API endpoint for retrieving habit reminder information.
    Returns whether an active reminder exists and the reminder object if active.
    """
    permission_classes = [permissions.IsAuthenticated]

    @swagger_reminder_info()
    def get(self, request):
        """Get habit reminder information."""
        try:
            # Check if a habit reminder exists for the current user
            reminder = Reminder.objects.filter(
                user=request.user,
                service_name='habit',
                object_id=''
            ).first()

            # Check if reminder exists and is active
            if reminder and reminder.status:
                # Active reminder exists
                serializer = HabitReminderSerializer(reminder)
                return Response({
                    "has_active_reminder": True,
                    "reminder": serializer.data
                }, status=status.HTTP_200_OK)
            else:
                # No reminder or inactive reminder
                return Response({
                    "has_active_reminder": False,
                    "reminder": None
                }, status=status.HTTP_200_OK)

        except Exception:
            return Response({
                "has_active_reminder": False,
                "reminder": None
            }, status=status.HTTP_200_OK)
