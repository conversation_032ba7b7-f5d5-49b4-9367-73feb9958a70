from rest_framework import serializers
from apps.notification.models import Reminder


class HabitReminderSerializer(serializers.ModelSerializer):
    """
    Serializer for habit reminders that returns complete reminder object.
    Used for the reminder info endpoint to provide full reminder details.
    """
    
    class Meta:
        model = Reminder
        fields = [
            'id', 'service_name', 'object_id', 'text', 'created_at',
            'call_time', 'is_sent', 'is_read', 'status', 'notif_data'
        ]
        read_only_fields = [
            'id', 'service_name', 'object_id', 'text', 'created_at',
            'call_time', 'is_sent', 'is_read', 'status', 'notif_data'
        ]
        ref_name = 'HabitReminderSerializer'
