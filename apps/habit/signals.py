import sys
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings

from apps.habit.models.challenge import Challenge
from apps.habit.models.checklist import ChecklistTemplate

# Safe import with fallback
try:
    from apps.command.tasks.model_translation import translate_model_fields_task
except ImportError:
    print("⚠️  Translation task not available - translation will be skipped")
    translate_model_fields_task = None


@receiver(post_save, sender=Challenge)
def auto_translate_challenge(sender, instance, created, **kwargs):
    """
    Signal handler to automatically translate Challenge fields when saved.
    Uses Celery task for background processing.
    """


    # Quick check for content (basic validation)
    has_content = False
    for field_name in ['title', 'summary', 'description']:
        field_data = getattr(instance, field_name, [])
        if field_data and isinstance(field_data, list):
            for item in field_data:
                if isinstance(item, dict) and item.get('text', '').strip():
                    has_content = True
                    break
        if has_content:
            break

    if not has_content:
        print(f"ℹ️  No translatable content found for Challenge ID {instance.id}")
        return

    # Check if translation task is available
    if translate_model_fields_task is None:
        print(f"⚠️  Translation task not available for Challenge ID {instance.id}")
        return

    try:
        print(f"🚀 Queuing background translation for Challenge ID {instance.id}")

        # Configure field configurations for the task
        field_configs = [
            {
                "field_name": "title",
                "keys": ['text', 'language_code']
            },
            {
                "field_name": "summary",
                "keys": ['text', 'language_code']
            },
            {
                "field_name": "description",
                "keys": ['text', 'language_code']
            }
        ]

        # Queue Celery task for background processing
        translate_model_fields_task.delay(
            model_name='Challenge',
            app_name='habit',
            object_id=instance.id,
            field_configs=field_configs,
            use_gemini=False,  # Use Gemini for faster translation
            use_mock=False    # Set to True for testing
        )

        print(f"✅ Translation task queued for Challenge ID {instance.id}")

    except Exception as e:
        print(f"❌ Error queuing translation task for Challenge ID {instance.id}: {e}")


@receiver(post_save, sender=ChecklistTemplate)
def auto_translate_checklist_template(sender, instance, created, **kwargs):
    """
    Signal handler to automatically translate ChecklistTemplate title field when saved.
    Uses Celery task for background processing.
    """


    # Quick check for content (basic validation)
    has_content = False
    field_data = getattr(instance, 'title', [])
    if field_data and isinstance(field_data, list):
        for item in field_data:
            if isinstance(item, dict) and item.get('text', '').strip():
                has_content = True
                break

    if not has_content:
        print(f"ℹ️  No translatable content found for ChecklistTemplate ID {instance.id}")
        return

    # Check if translation task is available
    if translate_model_fields_task is None:
        print(f"⚠️  Translation task not available for ChecklistTemplate ID {instance.id}")
        return

    try:
        print(f"🚀 Queuing background translation for ChecklistTemplate ID {instance.id}")

        # Configure field configurations for the task
        field_configs = [
            {
                "field_name": "title",
                "keys": ['text', 'language_code']
            }
        ]

        # Queue Celery task for background processing
        translate_model_fields_task.delay(
            model_name='ChecklistTemplate',
            app_name='habit',
            object_id=instance.id,
            field_configs=field_configs,
            use_gemini=False,  # Use Gemini for faster translation
            use_mock=False    # Set to True for testing
        )

        print(f"✅ Translation task queued for ChecklistTemplate ID {instance.id}")

    except Exception as e:
        print(f"❌ Error queuing translation task for ChecklistTemplate ID {instance.id}: {e}")