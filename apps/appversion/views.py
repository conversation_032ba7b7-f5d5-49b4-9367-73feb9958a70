from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import OuterRef, Subquery
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from apps.appversion.serializer import AppVersionSerializer, ServiceVersionSerializer
from apps.account.models import UserSettings
from .models import AppVersion, ServiceVersion
from .doc import get_markdown_content

class AppVersionViewV2(APIView):
    def get(self, request, *args, **kwargs):
        return Response(
            AppVersionSerializer(instance=AppVersion.objects.last(), context={'request': request}).data
        )


class AppVersionView(APIView):
    def get(self, request, *args, **kwargs):
        return Response(
            {"content": [{"title": "Version 0.82 beta released",
                          "description": "new features:\n- Improve the efficiency of the calendar service\n- Improving the performance of Quran service\n- Debug of Hosseinieh section\n- Improve the efficiency of the calendar service\n- Improving the performance of Quran service\n- Debug of Hosseinieh section\n- Improve the efficiency of the calendar service\n- Improving the performance of Quran service\n- Debug of Hosseinieh section\n- Improve the efficiency of the calendar service\n- Improving the performance of Quran service\n- debugging Hosseinieh section - Improving the efficiency of the calendar service\n- Improving the performance of Quran service\n- debugging Hosseinieh section - Improving the efficiency of the calendar service\n- Improving the performance of Quran service\n- Debug of Hosseinieh section",
                          "language_code": "en"}], "version_num": "0.85", "last_requirement_version": "0.8",
             "google_play": "https://play.google.com/store/apps/details?id=com.otoreport.apponereload114",
             "x86_file": "167", "x86_size": "31 MB", "arm64_file": "167", "arm64_size": "31 MB", "armV7_file": "167",
             "armV7_size": "31 MB", "ios_file": None, "ios_size": "0", "created_at": "2022-01-06T15:39:54.458883"}
        )




class ServiceVersionView(APIView):
    @swagger_auto_schema(
        operation_description=get_markdown_content(),
        responses={200: ServiceVersionSerializer(many=True)},
        manual_parameters=[
            openapi.Parameter(
                'Authorization', openapi.IN_HEADER, description="Bearer token for authentication", type=openapi.TYPE_STRING
            )
        ]
    )
    def get(self, request, *args, **kwargs):
        language = self.request.LANGUAGE_CODE

        latest_versions = ServiceVersion.objects.filter(
            service=OuterRef('service'),
            language__code=language
        ).order_by('-created_at').values('pk')[:1]

        queryset = ServiceVersion.objects.filter(
            pk__in=Subquery(latest_versions),
            language__code=language
        )

        serializer = ServiceVersionSerializer(queryset, many=True)
        
        if request.user and request.user.is_authenticated:
            user_settings, _ = UserSettings.objects.get_or_create(user=request.user)

            # Check for build-number header and save to api_version
            # Using request.headers (Django 2.2+) - case insensitive
            build_number = request.headers.get('build-number')
            # Alternative using request.META:
            # build_number = request.META.get('HTTP_BUILD_NUMBER')
            if build_number:
                user_settings.api_version = build_number
                user_settings.save()

            existing_versions = {v["service"]: v["version_number"] for v in (user_settings.service_versions or [])}

            updated_versions = existing_versions.copy()
            changes_made = False

            for sv in queryset:
                service_name, version_number = sv.service.name, sv.version_number
                if existing_versions.get(service_name) != version_number:
                    updated_versions[service_name] = version_number
                    changes_made = True

            if changes_made:
                user_settings.service_versions = [{"service": svc, "version_number": ver} for svc, ver in updated_versions.items()]
                user_settings.save()
                                                        
        return Response(serializer.data)