from django.db import models
from django.utils.translation import gettext_lazy as _
from dj_language.field import LanguageField
from dj_language.models import Language
from apps.report.models import Service


class AppVersion(models.Model):
    content = models.JSONField(verbose_name=_('content'), null=True)
    version_num = models.PositiveIntegerField(verbose_name=_('build number'))
    last_requirement_version = models.PositiveIntegerField(
        null=True, blank=True, verbose_name='last required build number'
    )
    google_play = models.URLField(verbose_name=_('google play link'), null=True, blank=True)
    apple_store = models.URLField(verbose_name=_('apple store link'), null=True, blank=True)

    # android
    x86_file = models.CharField(null=True, blank=True, max_length=255)
    x86_size = models.CharField(max_length=64, null=True, blank=True, )

    arm64_file = models.CharField(null=True, blank=True, max_length=255)
    arm64_size = models.Char<PERSON>ield(max_length=64, null=True, blank=True, )

    armV7_file = models.CharField(null=True, blank=True, max_length=255)
    armV7_size = models.CharField(max_length=64, null=True, blank=True, )

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        try:
            return self.content[0]['title']
        except:
            return 'None'


class ServiceVersion(models.Model):
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name="versions", verbose_name="Service")
    version_number = models.CharField(max_length=20, verbose_name="Version Number", help_text="Version number in format 1.3")
    description = models.TextField(verbose_name="New Version Description")
    language = LanguageField(verbose_name=_('language'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Created At")

    class Meta:
        verbose_name = "Service Version"
        verbose_name_plural = "Service Versions"
        unique_together = ('service', 'version_number', 'language')  # Ensures unique version per service and language


    def __str__(self):
        return f"{self.service} - Version {self.version_number} - {self.language}"

    @classmethod
    def get_latest_version(cls, service, language):
        """
        Get the latest version for the given service and language, ordered by creation date.
        """
        latest_version = cls.objects.filter(service=service, language=language).order_by('-created_at').first()
        return latest_version    