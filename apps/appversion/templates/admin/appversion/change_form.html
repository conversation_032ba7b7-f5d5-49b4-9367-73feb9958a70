{% extends 'admin/change_form.html' %}
{% block scripts %}
    {{ block.super }}
    <style>
        .json-view-editor .row .form-group {
            display: flex;
            align-items: baseline;
        }

        .json-view-editor .row .form-group label {
            margin-bottom: 0;
            margin-right: 10px;
        }

        .json-view-editor textarea {
            width: 100%;
            height: 150px;
        }
    </style>
    <script>

        let editor = document.getElementById('id_content')
        let schema_str = window.sc = JSON.parse(editor.value)
        $(editor).addClass("hidden")
        let json_viewer_div = $(`<div class="json-view-editor" id='date-view-editor'></div>`)
        $(editor).parent().append(json_viewer_div)

        let properties_with_year = {
            title: {type: 'string', format: 'string'},
            description: {type: 'string', format: 'textarea'},
            language_code: {
                "type": "string",
                "enum": JSON.parse("{{ languages | escapejs }}"),
                "default": "en"
            }
        }

        let properties = {
            month: {type: 'string'},
            day: {type: 'string'},
        }


        function init(properties, start_value = []) {
            if (window.jsoneditor) {
                window.jsoneditor.destroy()
            }
            window.jsoneditor = new JSONEditor(
                json_viewer_div[0], {
                    theme: 'bootstrap4',
                    schema: {
                        type: "array",
                        format: 'table',
                        title: ' ',
                        items: {
                            type: 'object',
                            title: 'Description',
                            properties: properties
                        },
                    },
                    disable_edit_json: true,
                    disable_properties: false,
                    disable_array_delete_all_rows: true,
                    disable_array_delete_last_row: true,
                    disable_array_reorder: true,
                    grid_columns: 3,
                    prompt_before_delete: false,
                    disable_collapse: true,
                    startval: start_value
                })
            window.jsoneditor.on('change', () => {
                $(editor).val(JSON.stringify(jsoneditor.getValue()))
            })
        }

        init(properties_with_year, schema_str || [])

    </script>
{% endblock %}