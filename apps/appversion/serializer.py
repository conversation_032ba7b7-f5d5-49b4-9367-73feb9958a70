from rest_framework import serializers

from dj_language.serializer import LanguageSerializer

from apps.appversion.models import AppVersion, ServiceVersion


class AppVersionSerializer(serializers.ModelSerializer):
    class Meta:
        model = AppVersion
        fields = (
            'content', 'version_num', 'last_requirement_version', 'google_play', 'apple_store', 'x86_file', 'x86_size',
            'arm64_file',
            'arm64_size', 'armV7_file',
            'armV7_size',
            'created_at',
        )


class ServiceVersionSerializer(serializers.ModelSerializer):
    service = serializers.SerializerMethodField()
    language = LanguageSerializer()
    class Meta:
        model = ServiceVersion
        fields = ['service', 'version_number', 'language', 'created_at']
        
    def get_service(self, obj):
        return obj.service.slug