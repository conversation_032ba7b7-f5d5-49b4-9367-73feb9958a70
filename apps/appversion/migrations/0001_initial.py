# Generated by Django 3.2.5 on 2022-01-05 17:37

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AppVersion',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_created=True)),
                ('title', models.Char<PERSON>ield(max_length=255, verbose_name='title')),
                ('description', models.Char<PERSON>ield(max_length=512, verbose_name='description')),
                ('version_num', models.Char<PERSON>ield(max_length=32, verbose_name='version number')),
                ('force_update', models.BooleanField(default=False, verbose_name='force client to update')),
                ('google_play', models.URLField(verbose_name='google play link')),
                ('x86_link', models.URL<PERSON>ield(verbose_name='x86_link')),
                ('x86_size', models.Char<PERSON><PERSON>(max_length=64, verbose_name='x86_size')),
                ('arm64_link', models.URL<PERSON>ield(verbose_name='arm64_link')),
                ('arm64_size', models.CharField(max_length=64, verbose_name='arm64_size')),
                ('armV7_link', models.URLField(verbose_name='armV7_link')),
                ('armV7_size', models.CharField(max_length=64, verbose_name='armV7_size')),
            ],
        ),
    ]
