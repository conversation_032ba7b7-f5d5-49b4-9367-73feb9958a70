# Generated by Django 3.2.5 on 2022-01-06 10:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appversion', '0003_alter_appversion_description'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='appversion',
            name='arm64_link',
        ),
        migrations.RemoveField(
            model_name='appversion',
            name='armV7_link',
        ),
        migrations.RemoveField(
            model_name='appversion',
            name='x86_link',
        ),
        migrations.AddField(
            model_name='appversion',
            name='arm64_file',
            field=models.FileField(null=True, upload_to='static/uploads/app-versions/%d/', verbose_name='arm64 apk file'),
        ),
        migrations.AddField(
            model_name='appversion',
            name='armV7_file',
            field=models.FileField(null=True, upload_to='static/uploads/app-versions/%d/', verbose_name='armV7 apk file'),
        ),
        migrations.AddField(
            model_name='appversion',
            name='ios_file',
            field=models.FileField(blank=True, null=True, upload_to='static/uploads/app-versions/%d/', verbose_name='IOS apk file'),
        ),
        migrations.AddField(
            model_name='appversion',
            name='ios_size',
            field=models.CharField(blank=True, help_text='eg. 12MB', max_length=64, null=True, verbose_name='IOS size'),
        ),
        migrations.AddField(
            model_name='appversion',
            name='x86_file',
            field=models.FileField(null=True, upload_to='static/uploads/app-versions/%d/', verbose_name='x86 apk file'),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='arm64_size',
            field=models.CharField(help_text='eg. 12MB', max_length=64, verbose_name='arm64 size'),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='armV7_size',
            field=models.CharField(help_text='eg. 12MB', max_length=64, verbose_name='armV7 size'),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='x86_size',
            field=models.CharField(help_text='eg. 12MB', max_length=64, verbose_name='x86 size'),
        ),
    ]
