# Generated by Django 3.2.23 on 2024-01-04 17:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appversion', '0009_auto_20220212_1449'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='appversion',
            name='ios_file',
        ),
        migrations.RemoveField(
            model_name='appversion',
            name='ios_size',
        ),
        migrations.AddField(
            model_name='appversion',
            name='apple_store',
            field=models.URLField(blank=True, null=True, verbose_name='apple store link'),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='last_requirement_version',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='last required build number'),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='version_num',
            field=models.PositiveIntegerField(verbose_name='build number'),
        ),
    ]
