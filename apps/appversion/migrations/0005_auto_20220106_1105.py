# Generated by Django 3.2.5 on 2022-01-06 11:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appversion', '0004_auto_20220106_1049'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='appversion',
            name='arm64_size',
            field=models.Char<PERSON><PERSON>(editable=False, help_text='eg. 12MB', max_length=64, verbose_name='arm64 size'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='appversion',
            name='armV7_size',
            field=models.Char<PERSON><PERSON>(editable=False, help_text='eg. 12MB', max_length=64, verbose_name='armV7 size'),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name='appversion',
            name='google_play',
            field=models.URLField(blank=True, null=True, verbose_name='google play link'),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='ios_size',
            field=models.Cha<PERSON><PERSON><PERSON>(blank=True, editable=False, help_text='eg. 12MB', max_length=64, null=True, verbose_name='IOS size'),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='appversion',
            name='x86_size',
            field=models.CharField(editable=False, help_text='eg. 12MB', max_length=64, verbose_name='x86 size'),
        ),
    ]
