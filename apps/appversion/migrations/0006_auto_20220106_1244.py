# Generated by Django 3.2.5 on 2022-01-06 12:44

from django.db import migrations, models
import django.db.models.deletion
import filer.fields.file


class Migration(migrations.Migration):

    dependencies = [
        ('filer', '0012_file_mime_type'),
        ('appversion', '0005_auto_20220106_1105'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='appversion',
            name='description',
        ),
        migrations.RemoveField(
            model_name='appversion',
            name='title',
        ),
        migrations.AddField(
            model_name='appversion',
            name='content',
            field=models.JSONField(null=True, verbose_name='content'),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='arm64_file',
            field=filer.fields.file.FilerFileField(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='filer.file'),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='arm64_size',
            field=models.CharField(editable=False, max_length=64),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='armV7_file',
            field=filer.fields.file.FilerFileField(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='filer.file'),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='armV7_size',
            field=models.CharField(editable=False, max_length=64),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='ios_file',
            field=filer.fields.file.FilerFileField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='filer.file'),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='ios_size',
            field=models.CharField(blank=True, editable=False, max_length=64, null=True),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='x86_file',
            field=filer.fields.file.FilerFileField(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='filer.file'),
        ),
        migrations.AlterField(
            model_name='appversion',
            name='x86_size',
            field=models.CharField(editable=False, max_length=64),
        ),
    ]
