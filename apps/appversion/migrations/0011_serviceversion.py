# Generated by Django 3.2.25 on 2024-11-08 13:26

import dj_language.field
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('report', '0009_report_text'),
        ('dj_language', '0002_auto_20220120_1344'),
        ('appversion', '0010_auto_20240104_1722'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceVersion',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('version_number', models.CharField(help_text='Version number in format 1.3', max_length=20, verbose_name='Version Number')),
                ('description', models.TextField(verbose_name='New Version Description')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('language', dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='versions', to='report.service', verbose_name='Service')),
            ],
            options={
                'verbose_name': 'Service Version',
                'verbose_name_plural': 'Service Versions',
                'unique_together': {('service', 'version_number', 'language')},
            },
        ),
    ]
