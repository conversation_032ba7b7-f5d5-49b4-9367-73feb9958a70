import json
import re

from ajaxdatatable.admin import AjaxDatatable
from dj_language.models import Language
from django.db import models
from django.contrib import admin
from django.db.models import OuterRef, Subquery, Max
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django import forms

from apps.appversion.models import AppVersion, ServiceVersion


@admin.register(AppVersion)
class AppVersionAdmin(AjaxDatatable):
    list_display = ('_title', 'version_num', 'last_requirement_version', 'created_at')
    search_fields = ('content', 'version_num')

    @admin.display(description='title')
    def _title(self, obj):
        return str(obj)

    fieldsets = (
        ('General', {'fields': ('content', 'version_num', 'last_requirement_version')}),
        ('Google play', {'fields': ('google_play',)}),
        ('Apple Store', {'fields': ('apple_store',)}),
        ('x86', {'fields': ('x86_file', 'x86_size')}),
        ('arm64', {'fields': ('arm64_file', 'arm64_size')}),
        ('arm version 7', {'fields': ('armV7_file', 'armV7_size')}),
        # ('IOS', {'fields': ('ios_file', 'ios_size')}),
    )

    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):
        languages = list(Language.objects.filter(status=True).values_list('code', flat=True))
        extra_context = {
            "languages": json.dumps(languages)
        }

        return super().changeform_view(request, object_id, form_url, extra_context)

    # def save_model(self, request, obj, form, change):
    #     obj.x86_size = str(obj.x86_file.size // 1000_000) + ' MB' if obj.x86_file else 0
    #     obj.arm64_size = str(obj.arm64_file.size // 1000_000) + ' MB' if obj.arm64_file else 0
    #     obj.armV7_size = str(obj.armV7_file.size // 1000_000) + ' MB' if obj.armV7_file else 0
    #     obj.ios_size = str(obj.ios_file.size // 1000_000) + ' MB' if obj.ios_file else 0
    #
    #     obj.save()
    #
    #     return super().save_model(request, obj, form, change)


class LanguageFilter(admin.SimpleListFilter):
    title = _('Language')
    parameter_name = 'language'

    def lookups(self, request, model_admin):
        """
        Returns a list of available languages as filter options.
        """
        languages = ServiceVersion.objects.values_list('language__code', flat=True).distinct()
        return [(lang, lang) for lang in languages]
    
    def queryset(self, request, queryset):
        if self.value():
            queryset = queryset.filter(language__code=self.value())
        
        return queryset
    
class ServiceVersionForm(forms.ModelForm):
    class Meta:
        model = ServiceVersion
        fields = '__all__'

    def clean_version_number(self):
        version_number = self.cleaned_data.get('version_number')
        
        if not re.match(r'^\d+(\.\d+)?$', version_number):
            raise ValidationError("Version number must be a numeric value and can contain a decimal point.")
        
        return version_number
    
@admin.register(ServiceVersion)
class ServiceVersionAdmin(AjaxDatatable):
    list_display = ('service', 'version_number', 'language', 'created_at')
    
    search_fields = ('service__name', 'version_number', 'description')
    list_filter = (LanguageFilter, 'service')

    autocomplete_fields = ('language',)
    
    
    def get_queryset(self, request):
        
        latest_versions = ServiceVersion.objects.filter(
            service=OuterRef('service'),
            language=OuterRef('language')
        ).order_by('-created_at').values('pk')[:1]
        
        queryset = super(ServiceVersionAdmin, self).get_queryset(request).filter(
            pk__in=Subquery(latest_versions)
        )
    
        return queryset
