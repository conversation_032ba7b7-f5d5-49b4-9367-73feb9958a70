from django.contrib.postgres.aggregates import Array<PERSON>gg
from django.db.models import <PERSON><PERSON>, Subquery, OuterRef, <PERSON><PERSON>an<PERSON>ield
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.core.cache import cache
import logging

from apps.dua.views.v1 import Base, put_key_render
from apps.dua.models import <PERSON><PERSON>, DuaAudio
from apps.dua.serializers.v3 import MafatihDuaSerializerV3
from utils import get_language_aware_cache_key
from utils.cache_utils import smart_cache_set, smart_cache_get

logger = logging.getLogger(__name__)



class MafatihDuaViewV3(Base):
    serializer_class = MafatihDuaSerializerV3
    permission_classes = (IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        # Language-aware data caching (faster than response caching)
        language_code = getattr(self.request, 'LANGUAGE_CODE', 'en')
        cache_key = get_language_aware_cache_key('mafatih_duas_v3', language_code)

        # Check cache first (with smart decompression)
        cached_data = smart_cache_get(cache_key)
        if cached_data:
            logger.info(f"MafatihDuaViewV3 (dua app) - Cache hit for language: {language_code}")
            return Response(cached_data)

        # Get fresh data
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        data = put_key_render(serializer.data, self.key)  # تبدیل Array به Object

        # Cache with smart compression (much faster for large data)
        cache_info = smart_cache_set(cache_key, data, 60 * 60)
        logger.info(f"MafatihDuaViewV3 (dua app) - Cached data for language: {language_code} ({cache_info})")

        return Response(data)

    def get_queryset(self):
        language_code = self.request.LANGUAGE_CODE
        logger.info(f"MafatihDuaViewV3 - Language: {language_code}")

        return Dua.objects.filter(
            status=True
        ).prefetch_related(
            'audios', 'Dua_part', 'category', 'audios__reciter_relation'
        ).annotate(
            not_synced=Exists(Subquery(DuaAudio.objects.filter(dua=OuterRef('pk'))),
                              output_field=BooleanField()),
        ).order_by('not_synced', 'title')
