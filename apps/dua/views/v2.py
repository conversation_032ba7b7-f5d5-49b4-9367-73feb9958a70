from django.contrib.postgres.aggregates import <PERSON><PERSON>y<PERSON>gg
from django.db.models import <PERSON><PERSON>, Subquery, OuterRef, <PERSON><PERSON>an<PERSON>ield
from rest_framework.permissions import IsAuthenticated
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.core.cache import cache
import logging

from apps.mafatih.views.v1 import Base
from ..models import Dua, DuaAudio
from ..serializers.v2 import MafatihDuaSerializerV2
from utils import get_language_aware_cache_key

logger = logging.getLogger(__name__)


class MafatihDuaViewV2(Base):
    serializer_class = MafatihDuaSerializerV2
    permission_classes = (IsAuthenticated,)

    def dispatch(self, *args, **kwargs):
        # Language-aware caching instead of simple cache_page
        language_code = getattr(self.request, 'LANGUAGE_CODE', 'en')
        cache_key = get_language_aware_cache_key('mafatih_dua_v2_dua_app', language_code)

        # Check cache first
        cached_response = cache.get(cache_key)
        if cached_response:
            logger.info(f"MafatihDuaViewV2 (dua app) - Cache hit for language: {language_code}")
            return cached_response

        # Get fresh response
        response = super().dispatch(*args, **kwargs)

        # Cache the response for 1 hour
        if hasattr(response, 'status_code') and response.status_code == 200:
            # Render the response before caching to avoid ContentNotRenderedError
            response.render()
            cache.set(cache_key, response, 60 * 60)
            logger.info(f"MafatihDuaViewV2 (dua app) - Cached response for language: {language_code}")

        return response

    def get_queryset(self):
        language_code = self.request.LANGUAGE_CODE
        logger.info(f"MafatihDuaViewV2 - Language: {language_code}")

        return Dua.objects.filter(
            status=True
        ).prefetch_related(
            'audios', 'Dua_part', 'category',
        ).annotate(
            not_synced=Exists(Subquery(DuaAudio.objects.filter(dua=OuterRef('pk'))),
                              output_field=BooleanField()),
        ).order_by('not_synced', 'title')
