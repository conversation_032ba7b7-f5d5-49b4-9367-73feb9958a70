

import os
import json
import logging
from django.contrib.contenttypes.models import ContentType
from django.db.models import <PERSON><PERSON>, F, Subquery, OuterRef, <PERSON><PERSON>an<PERSON><PERSON>
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.core.cache import cache
from rest_framework.views import APIView
from rest_framework import status
from rest_framework.generics import ListAPIView, RetrieveAPIView, get_object_or_404
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from dj_language.models import Language
from drf_yasg import openapi
from geopy.distance import geodesic
from datetime import datetime

from apps.dua.serializers import DuaCategoriesSerializer, MafatihDuaSerializer, DuaAudioListSerializer, DuaAudioDetailSerializer, MafatihDuaPartSerializer
from ..models import DuaCategory, DuaAudio, Du<PERSON>, <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>iter
from utils import get_language_safely, get_language_aware_cache_key
from utils.cache_utils import smart_cache_set, smart_cache_get

logger = logging.getLogger(__name__)






def put_key_render(data, key):
    new_data = {}
    for i in data:
        d = dict(i)
        _key = d[key]
        d.pop(key)
        new_data[_key] = d
    return new_data


class Base(ListAPIView):
    key = 'id'

    permission_classes = (IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        data = put_key_render(serializer.data, self.key)
        return Response(data)


class MafatihDuaView(Base):
    serializer_class = MafatihDuaSerializer
    permission_classes = (IsAuthenticated,)

    def dispatch(self, *args, **kwargs):
        # Language-aware caching instead of simple cache_page
        language_code = getattr(self.request, 'LANGUAGE_CODE', 'en')
        cache_key = get_language_aware_cache_key('mafatih_duas_v1', language_code)

        # Check cache first
        cached_response = cache.get(cache_key)
        if cached_response:
            logger.info(f"MafatihDuaView - Cache hit for language: {language_code}")
            return cached_response

        # Get fresh response
        response = super().dispatch(*args, **kwargs)

        # Cache the response for 1 hour
        if hasattr(response, 'status_code') and response.status_code == 200:
            # Render the response before caching to avoid ContentNotRenderedError
            response.render()
            cache.set(cache_key, response, 60 * 60)
            logger.info(f"MafatihDuaView - Cached response for language: {language_code}")

        return response

    def get_queryset(self):
        language_code = self.request.LANGUAGE_CODE
        logger.info(f"MafatihDuaView - Language: {language_code}")

        return Dua.objects.filter(
            status=True
        ).prefetch_related(
            'audios', 'Dua_part', 'category',
        ).annotate(
            not_synced=Exists(Subquery(DuaAudio.objects.filter(dua=OuterRef('pk'))),
                              output_field=BooleanField()),
            audio_sync_data_s=F('audios__audio_sync_data'),
            audio_s=F('audios__audio__file'),
        ).order_by('not_synced', 'title')


class MafatihDuaPartsView(Base):
    serializer_class = MafatihDuaPartSerializer
    permission_classes = (IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        # Language-aware data caching (faster than response caching)
        language_code = getattr(self.request, 'LANGUAGE_CODE', 'en')
        cache_key = get_language_aware_cache_key('mafatih_duas_parts', language_code)

        # Check cache first (with smart decompression)
        cached_data = smart_cache_get(cache_key)
        if cached_data:
            logger.info(f"MafatihDuaPartsView - Cache hit for language: {language_code}")
            return Response(cached_data)

        # Get fresh data
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        data = put_key_render(serializer.data, self.key)  # تبدیل Array به Object

        # Cache with smart compression (much faster for large data)
        cache_info = smart_cache_set(cache_key, data, 60 * 60)
        logger.info(f"MafatihDuaPartsView - Cached data for language: {language_code} ({cache_info})")

        return Response(data)

    def get_queryset(self):
        language_code = self.request.LANGUAGE_CODE
        logger.info(f"MafatihDuaPartsView - Language: {language_code}")

        return DuaPart.objects.order_by('dua_part_index', 'id')



class DuaCategoriesView(ListAPIView):
    serializer_class = DuaCategoriesSerializer
    pagination_class = None
    permission_classes = (IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        # Language-aware data caching (faster than response caching)
        language_code = getattr(self.request, 'LANGUAGE_CODE', 'en')
        cache_key = get_language_aware_cache_key('dua_categories_v2_data', language_code)

        # Check cache first (with smart decompression)
        cached_data = smart_cache_get(cache_key)
        if cached_data:
            logger.info(f"DuaCategoriesView - Cache hit for language: {language_code}")
            return Response(cached_data)

        # Get fresh data
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        data = serializer.data

        # Cache with smart compression (much faster for large data)
        cache_info = smart_cache_set(cache_key, data, 60 * 60)
        logger.info(f"DuaCategoriesView - Cached data for language: {language_code} ({cache_info})")

        return Response(data)

    def get_queryset(self):
        language_code = self.request.LANGUAGE_CODE
        logger.info(f"DuaCategoriesView - Language: {language_code}")

        return DuaCategory.objects.filter(
            is_active=True,
            level=0,
        ).order_by('order')



class MafatihDuaAudioView(ListAPIView):
    serializer_class = DuaAudioListSerializer

    # def get_permissions(self):
    #     if "duas.org" in self.request.META.get('HTTP_HOST'):
    #         return []    
    #     return [IsAuthenticated()]

    def get_queryset(self):
        return DuaAudio.objects.filter(dua_id=self.kwargs['pk'])[:10]


class MafatihDuaAudioDetailView(RetrieveAPIView):
    serializer_class = DuaAudioDetailSerializer

    # def get_permissions(self):
    #     if "duas.org" in self.request.META.get('HTTP_HOST'):
    #         return []
    #
    #     return [IsAuthenticated()]

    def get_queryset(self):
        return DuaAudio.objects.filter(id=self.kwargs['pk']).prefetch_related('audio')

class MafatihFamousView(APIView):
    def get(self, request, *args, **kwargs):
        language_code = self.request.LANGUAGE_CODE
        logger.info(f"MafatihFamousView - Requested language: {language_code}")

        # category id famous duas
        category_id = 176
        response_data = {
            "event": "famous",
            "duas_index": [],
            "language": {}
        }

        # Get language with proper fallback
        language = get_language_safely(language_code, fallback='ar')
        response_data["language"] = {
            "name": language.name,
            "code": language.code
        }

        try:
            # Get category and duas
            category = DuaCategory.objects.get(id=category_id)
            duas = Dua.objects.filter(category=category_id)
            response_data["duas_index"] = list(duas.values_list('id', flat=True))
            logger.info(f"MafatihFamousView - Found {len(response_data['duas_index'])} duas for category {category_id}")
        except DuaCategory.DoesNotExist:
            logger.error(f"MafatihFamousView - Category {category_id} not found")
        except Exception as e:
            logger.error(f"MafatihFamousView - Unexpected error: {e}")

        return Response(response_data, status=status.HTTP_200_OK)

class MafatihDhikrView(APIView):

    def get(self, request, *args, **kwargs):
        # Retrieve the user's language code
        language_code = self.request.LANGUAGE_CODE
        today_param = request.query_params.get('today', None)
        # Try to get cached data for dhikr with proper language-aware cache key
        cache_key = get_language_aware_cache_key('mafatih_dhikr', language_code)
        data = cache.get(cache_key)
        logger.info(f"MafatihDhikrView - Cache key: {cache_key}, Cache hit: {data is not None}")
        data = None  # Force refresh for testing
        if not data:
            try:
                # Load the dhikr data from the JSON file if not cached
                file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'mafatih_dhikr.json')
                with open(file_path, 'r', encoding='utf-8') as json_file:
                    all_data = json.load(json_file)
                current_day = datetime.now().strftime('%A')  # 'Monday', 'Tuesday', etc.
                data = []
                for entry in all_data:
                    if today_param and today_param.lower() == 'true':
                        print(f"cday: {entry['day'].lower()}// today: {current_day.lower()}")
                        if entry['day'].lower() == current_day.lower():  # Compare with today's day
                            data.append({
                                'day': entry['day'],
                                'text': entry['text'],
                                'translation': entry['translations']
                            })
                            break 
                    else:    
                        # translation = entry['translations'].get(language_code, entry['translations'].get('en'))
                        data.append({
                            'day': entry['day'],
                            'text': entry['text'],
                            'translation': entry['translations']
                        })
                cache.set(cache_key, data, timeout=3600)

            except FileNotFoundError:
                return Response({"error": "File not found."}, status=status.HTTP_404_NOT_FOUND)
            except json.JSONDecodeError:
                return Response({"error": "Error decoding JSON."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(data, status=status.HTTP_200_OK)

    
class MafatihLocationView(APIView):
    
    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter(
            'lat', openapi.IN_QUERY,
            description="Latitude of the location \
               lat = {Mashhad : 36.2605, \
                     Karbala : 32.6160326} \
            ",
            type=openapi.TYPE_NUMBER
        ),
        openapi.Parameter(
            'lon', openapi.IN_QUERY,
            description="Longitude of the location \
               lon = {Mashhad : 59.6168, \
                     Karbala : 44.0244229} \
            ",
            type=openapi.TYPE_NUMBER),
    ])
    def get(self, request):
        """
        Retrieve nearby duas based on the user's location.
        This view takes the user's latitude and longitude as input and returns a list of duas within 20 kilometers,
        sorted by distance. Additionally, duas within 2 kilometers are prioritized based on the priority field.
        
        Parameters:
        - request: HTTP GET request containing 'lat' and 'lon' as parameters.
        
        Returns:
        - JsonResponse: A JSON response containing a list of nearby duas.
        """
        try:
            user_lat = float(request.query_params.get('lat'))
            user_lon = float(request.query_params.get('lon'))
        except (TypeError, ValueError):
            return Response({'error': 'Invalid or missing latitude and longitude'}, status=status.HTTP_400_BAD_REQUEST)

        user_location = (user_lat, user_lon)
        radius = 30  # 15 km

        language_code = request.LANGUAGE_CODE
        logger.info(f"MafatihLocationView - Language: {language_code}")

        all_duas = Dua.objects.filter(
            status=True,
            lat__isnull=False,
            lon__isnull=False,
            lat__gte=-90,
            lat__lte=90,
            lon__gte=-180,
            lon__lte=180
        )
        nearby_duas = []
        for dua in all_duas:

            dua_location = (dua.lat, dua.lon)
            distance = geodesic(user_location, dua_location).km
            if distance <= radius:
                nearby_duas.append({
                    'id': dua.id,
                    'lat': dua.lat,
                    'lon': dua.lon,
                    'distance': distance,
                    'priority': dua.priority if hasattr(dua, 'priority') else None

                })
        nearby_duas.sort(key=lambda x: x['distance'])
        
        within_2km_duas = [dua for dua in nearby_duas if dua['distance'] <= 2]
        others = [dua for dua in nearby_duas if dua['distance'] > 2]
        
        max_priority = max([dua['priority'] for dua in within_2km_duas if dua['priority'] is not None], default=10)
        for dua in within_2km_duas:
            if dua['priority'] is None:
                dua['priority'] = max_priority + 1 if max_priority != 10 else 10
        
        within_2km_duas.sort(key=lambda x: x['priority'])
        
        sorted_nearby_duas = within_2km_duas + others


        return Response({'dua_ids': nearby_duas}, status=status.HTTP_200_OK)
    
class MafatihDuaRecitersView(APIView):
    
    reveiters = [
        {
            "name": "Ali_Fani",
            "avatar": "https://www.habibapp.com/static/uploads/main/30/a3/30a3c33d-891a-4349-bb0d-637a55568ebc/ali_fani.jpeg"        
        },
        {
            "name": "Mohammad Reza Miri",
            "avatar": "https://www.habibapp.com/static/uploads/main/dc/9d/dc9d5770-798b-4dcb-b2d4-5fae172c7c9d/ali_fani2.jpg"                    
        }

    ]
    
    

    @swagger_auto_schema(
        operation_description="Retrieve a list of unique reciters for a given MafatihDua.",
        manual_parameters=[
            openapi.Parameter(
                'show_all',
                openapi.IN_QUERY,
                description="If true, returns all reciters, otherwise returns only reciters related to the specified MafatihDua.",
                type=openapi.TYPE_BOOLEAN,
                default=False
            )
        ],
        responses={
            200: openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'name': openapi.Schema(type=openapi.TYPE_STRING, description="Name of the reciter"),
                        'avatar': openapi.Schema(type=openapi.TYPE_STRING, description="URL of the reciter's avatar")
                    }
                )
            )
        }
    )
    def get(self, request, mafatih_dua_id, *args, **kwargs):
        
        show_all = request.query_params.get('show_all', 'false').lower() == 'true'
        if show_all:
            reciters = DuaAudio.objects.all().values_list('reciter', flat=True).distinct()
        else:
            mafatih_dua = get_object_or_404(Dua, id=mafatih_dua_id)        
            reciters = DuaAudio.objects.filter(dua=mafatih_dua).exclude(reciter='Unknown').values_list('reciter', flat=True).distinct()

        unique_reciters = list(set(reciters))
        response_data = []
        for reciter in unique_reciters:
            avatar = None
            for reciter_info in self.reveiters:
                if reciter_info['name'] == reciter:
                    avatar = reciter_info['avatar']
                    break
            response_data.append({"name": reciter, "avatar": avatar})
            
        return Response(response_data, status=status.HTTP_200_OK)

        