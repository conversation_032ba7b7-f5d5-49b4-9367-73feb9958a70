
from django.db.models import <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>y, <PERSON>Ref, <PERSON><PERSON><PERSON><PERSON><PERSON>
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework.generics import ListAPIView
from geopy.distance import geodesic
from rest_framework import status
from rest_framework.response import Response
from django.db.models import Q


from apps.dua.models import DuaCategory
from apps.dua.serializers.web import *

from apps.dua.pagination import NoPagination



class WebDuaCategoriesView(ListAPIView):
    serializer_class = WebDuaCategoriesSerializer
    # permission_classes = (IsAuthenticated,)
    pagination_class = None
    
    # @method_decorator(cache_page(60 * 60))  # Cache for 1 Hour
    # def dispatch(self, *args, **kwargs):
    #     return super().dispatch(*args, **kwargs)
    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter(
            'language_code',
            openapi.IN_QUERY,
            type=openapi.TYPE_STRING
        ),
    ])
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    def get_queryset(self):
        return DuaCategory.objects.filter(
            is_active=True,
            level=0,
        ).order_by('order')
        
        



class WebDuaListView(ListAPIView):
    serializer_class = WebDuaSerializer
    pagination_class = NoPagination  # Use the custom pagination class
    # permission_classes = (IsAuthenticated,)

    # @method_decorator(cache_page(60 * 60))  # Cache for 1 Hour
    # def dispatch(self, *args, **kwargs):
    #     return super().dispatch(*args, **kwargs)
    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter(
            'category',
            openapi.IN_QUERY,
            description="ID دسته‌بندی برای فیلتر دعاها",
            type=openapi.TYPE_INTEGER
        ),
        openapi.Parameter(
            'search',
            openapi.IN_QUERY,
            description="جستجو بر اساس عنوان دعاها",
            type=openapi.TYPE_STRING
        ),
        openapi.Parameter(
            'today',
            openapi.IN_QUERY,
            description="",
            type=openapi.TYPE_STRING
        ),
        openapi.Parameter(
            'famous',
            openapi.IN_QUERY,
            description="",
            type=openapi.TYPE_STRING
        ),
        openapi.Parameter(
            'lat', openapi.IN_QUERY,
            description="Latitude of the location \
               lat = {Mashhad : 36.2605, \
                     Karbala : 32.6160326} \
            ",
            type=openapi.TYPE_NUMBER
        ),
        openapi.Parameter(
            'lon', openapi.IN_QUERY,
            description="Longitude of the location \
               lon = {Mashhad : 59.6168, \
                     Karbala : 44.0244229} \
            ",
            type=openapi.TYPE_NUMBER
        ),
        openapi.Parameter(
            'not_synced',
            openapi.IN_QUERY,
            description="",
            type=openapi.TYPE_BOOLEAN
        ),        
    ])
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    def get_queryset(self):
        queryset =  Dua.objects.filter(
            status=True
        ).prefetch_related(
            'audios', 'Dua_part', 'category', 'audios__reciter_relation'
        ).annotate(
            not_synced=Exists(Subquery(DuaAudio.objects.filter(dua=OuterRef('pk'))),
                              output_field=BooleanField()),
        ).order_by('not_synced', 'title')
        
        if category_id := self.request.query_params.get('category'):
            queryset = queryset.filter(category__id=category_id)
            
        if not_synced := self.request.query_params.get('not_synced'):
            queryset = queryset.filter(not_synced=True)
            
        if search_query := self.request.query_params.get('search'):            
            queryset = queryset.filter(
                Q(title_translations__icontains=search_query)
            )            
        if today := self.request.query_params.get('today'):
            queryset = queryset[:5]  

        if famous := self.request.query_params.get('famous'):
            category_id = 176
            queryset = queryset.filter(category__id=category_id)
            
        lat = self.request.query_params.get('lat')
        lon = self.request.query_params.get('lon')
        if lat and lon:
            all_duas = Dua.objects.filter(
                status=True,
                lat__isnull=False,
                lon__isnull=False,
                lat__gte=-90,
                lat__lte=90,
                lon__gte=-180,
                lon__lte=180
            )

            try:
                user_lat = float(lat)
                user_lon = float(lon)
                user_location = (user_lat, user_lon)
                radius = 30  
                nearby_duas = []
                for dua in all_duas:                    
                    if dua.lat is not None and dua.lon is not None:
                        dua_location = (dua.lat, dua.lon)
                        distance = geodesic(user_location, dua_location).km
                        if distance <= radius:
                            nearby_duas.append({
                                'id': dua.id,
                                'lat': dua.lat,
                                'lon': dua.lon,
                                'distance': distance,
                                'priority': dua.priority if hasattr(dua, 'priority') else None
                            })
                nearby_duas.sort(key=lambda x: x['distance'])
                within_2km_duas = [dua for dua in nearby_duas if dua['distance'] <= 2]
                others = [dua for dua in nearby_duas if dua['distance'] > 2]
                max_priority = max([dua['priority'] for dua in within_2km_duas if dua['priority'] is not None], default=10)
                for dua in within_2km_duas:
                    if dua['priority'] is None:
                        dua['priority'] = max_priority + 1 if max_priority != 10 else 10
                within_2km_duas.sort(key=lambda x: x['priority'])
                sorted_duas = within_2km_duas + others
                sorted_dua_ids = [dua['id'] for dua in sorted_duas]
                queryset = queryset.filter(id__in=sorted_dua_ids)
                
            except (ValueError, TypeError):
                return Dua.objects.none()               
        return queryset

    


class WebDuaPartListView(ListAPIView):
    serializer_class = DuaPartSerializer

    @swagger_auto_schema(
        operation_description="لیست تمامی قسمت‌های یک دعا بر اساس شناسه دعا",
        manual_parameters=[
            openapi.Parameter(
                'pk',
                openapi.IN_PATH,
                description="شناسه دعا",
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                'slug',
                openapi.IN_PATH,
                description="اسلاگ دعا",
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                'search',
                openapi.IN_QUERY,
                description="جستجو بر اساس متن قسمت دعا",
                type=openapi.TYPE_STRING
            ),
        ],
        responses={
            200: openapi.Response(
                description="لیست قسمت‌های دعا",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Items(type=openapi.TYPE_OBJECT, properties={
                        'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                        'dua_part_index': openapi.Schema(type=openapi.TYPE_NUMBER, format='float'),
                        'text': openapi.Schema(type=openapi.TYPE_STRING),
                        'local_alpha': openapi.Schema(type=openapi.TYPE_STRING),
                        'translation': openapi.Schema(type=openapi.TYPE_STRING),
                        'description': openapi.Schema(type=openapi.TYPE_STRING),
                        'correction_status': openapi.Schema(type=openapi.TYPE_INTEGER),
                    })
                )
            )
        }
    )
    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        lang = request.LANGUAGE_CODE
        slug = self.kwargs.get('slug')
        dua = Dua.objects.filter(slug__contains=[{'text': slug}]).first()
        title = dua.get_translation(lang) if dua else None
        seo_field = dua.get_seo_for_language(lang) if dua else None
        data = {
            "count": response.data.get("count"),
            "next": response.data.get("next"),
            "previous": response.data.get("previous"),
            "title": title,
            "seo_field": seo_field,
            "results": response.data.get("results")
        }
        return Response(data, status=status.HTTP_200_OK)

    def get_queryset(self):
        slug = self.kwargs.get('slug')
        dua = Dua.objects.filter(
            Q(slug__contains=[{'text': slug}])    
        ).first()
        if not dua:
            return DuaPart.objects.none()

        queryset = DuaPart.objects.filter(dua_id=dua.id)

        search_query = self.request.query_params.get('search')
        if search_query:
            queryset = queryset.filter(text__icontains=search_query)
        return queryset.order_by('dua_part_index')



class WebDuaAudioDetailList(ListAPIView):
    serializer_class = WebDuaAudioSerializer
    pagination_class = NoPagination  # Use the custom pagination class


    def get_queryset(self):
        dua_id = self.kwargs.get('pk')
        slug = self.kwargs.get('slug')

        if slug:
            dua = Dua.objects.filter(
                Q(slug__contains=[{'text': slug}])
            ).first()
        elif dua_id:
            dua = Dua.objects.filter(id=dua_id).first()
        else:
            dua = None

        if not dua:
            return DuaAudio.objects.none()

        queryset = DuaAudio.objects.filter(
            dua_id=dua.id
        ).distinct()

        return queryset

class WebDuaReciterList(ListAPIView):
    serializer_class = DuaReciterSerializer
    
    def get_queryset(self):
        dua_id = self.kwargs.get('pk')
        slug = self.kwargs.get('slug')

        # پیدا کردن Dua بر اساس slug یا id
        if slug:
            dua = Dua.objects.filter(
                Q(slug__contains=[{'text': slug}])
            ).first()
        elif dua_id:
            dua = Dua.objects.filter(id=dua_id).first()
        else:
            dua = None
        if not dua:
            return DuaReciter.objects.none()
        
        queryset = DuaReciter.objects.filter(
            dua_audios__dua_id=dua.id
        ).distinct()
        
        return queryset
