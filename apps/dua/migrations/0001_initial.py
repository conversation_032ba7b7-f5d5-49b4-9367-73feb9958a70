# Generated by Django 3.2.25 on 2024-11-24 13:32

import apps.najm_calendar.fields
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import filer.fields.file
import filer.fields.image
import mptt.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('filer', '0014_folder_permission_choices'),
        migrations.swappable_dependency(settings.FILER_IMAGE_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Dua',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=192)),
                ('title_translations', models.JSONField(default=dict, verbose_name='translation')),
                ('dates', apps.najm_calendar.fields.JsonDateField(blank=True, help_text='لیستی از تاریخ های قمری مربوط به قطعه', null=True)),
                ('weekdays', models.JSONField(blank=True, default=dict, verbose_name='weekdays')),
                ('audio_sync_data', models.J<PERSON>NField(blank=True, default=list)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('priority', models.FloatField(blank=True, help_text='اولویت نمایش بر اساس این فیلد است', null=True)),
                ('status', models.BooleanField(default=True, verbose_name='visibility')),
                ('lat', models.FloatField(blank=True, help_text='Latitude of the location for the dua', null=True, verbose_name='Latitude')),
                ('lon', models.FloatField(blank=True, help_text='Longitude of the location for the dua', null=True, verbose_name='Longitude')),
                ('location_name', models.CharField(blank=True, help_text='Name of the shrine or pilgrimage site based on the coordinates', max_length=255, null=True, verbose_name='Location Name')),
                ('similar_titles', models.JSONField(blank=True, default=dict, null=True, verbose_name='Similar Titles')),
                ('audio', filer.fields.file.FilerFileField(blank=True, help_text='nullable', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='filer.file', verbose_name='audio file')),
            ],
            options={
                'verbose_name': 'Dua',
                'verbose_name_plural': 'Duas',
            },
        ),
        migrations.CreateModel(
            name='DuaReciter',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=120, verbose_name='reciter name')),
                ('slug', models.SlugField(blank=True, max_length=120, unique=True, verbose_name='slug')),
                ('translations', models.JSONField(default=dict, verbose_name='translations')),
                ('avatar', filer.fields.image.FilerImageField(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to=settings.FILER_IMAGE_MODEL, verbose_name='thumbnail')),
            ],
            options={
                'verbose_name': 'Dua Reciter',
                'verbose_name_plural': 'Dua Reciters',
            },
        ),
        migrations.CreateModel(
            name='DuaPart',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dua_part_index', models.FloatField(blank=True, help_text='شماره قطعه در دعای مورد نظر', null=True)),
                ('text', models.TextField(blank=True, null=True)),
                ('local_alpha', models.JSONField(default=dict, verbose_name='translitrations')),
                ('translation', models.JSONField(default=dict, verbose_name='translations')),
                ('description', models.JSONField(default=dict, verbose_name='descriptions')),
                ('Dua_dua', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='Dua_part', to='dua.dua')),
            ],
            options={
                'verbose_name': 'Dua dua part',
                'verbose_name_plural': 'Dua dua parts',
                'ordering': ('dua_part_index', 'id'),
            },
        ),
        migrations.CreateModel(
            name='DuaCategory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='is active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('order', models.PositiveSmallIntegerField(blank=True, null=True, verbose_name='order')),
                ('name', models.JSONField(default=dict, verbose_name='name')),
                ('lft', models.PositiveIntegerField(editable=False)),
                ('rght', models.PositiveIntegerField(editable=False)),
                ('tree_id', models.PositiveIntegerField(db_index=True, editable=False)),
                ('level', models.PositiveIntegerField(editable=False)),
                ('parent', mptt.fields.TreeForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='dua.duacategory')),
            ],
            options={
                'verbose_name': 'category',
                'verbose_name_plural': 'Duas categories',
                'ordering': ('order',),
            },
        ),
        migrations.CreateModel(
            name='DuaAudio',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reciter', models.CharField(max_length=119, verbose_name='reciter')),
                ('audio_sync_data', models.JSONField(blank=True, default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('Dua_dua', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='audios', to='dua.dua')),
                ('audio', filer.fields.file.FilerFileField(on_delete=django.db.models.deletion.PROTECT, related_name='+', to='filer.file', verbose_name='audio file')),
                ('reciter_relation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dua_audios', to='dua.duareciter', verbose_name='reciter relation')),
            ],
            options={
                'verbose_name': 'dua audio',
                'verbose_name_plural': 'duas audios',
                'ordering': ('-id',),
            },
        ),
        migrations.AddField(
            model_name='dua',
            name='category',
            field=models.ManyToManyField(blank=True, related_name='Dua_second', to='dua.DuaCategory'),
        ),
        migrations.AddIndex(
            model_name='duapart',
            index=models.Index(fields=['dua_part_index'], name='dua_duapart_dua_par_d6504e_idx'),
        ),
        migrations.AddIndex(
            model_name='duapart',
            index=models.Index(fields=['Dua_dua'], name='dua_duapart_Dua_dua_486edf_idx'),
        ),
        migrations.AddIndex(
            model_name='duaaudio',
            index=models.Index(fields=['Dua_dua'], name='dua_duaaudi_Dua_dua_88ec17_idx'),
        ),
        migrations.AddIndex(
            model_name='duaaudio',
            index=models.Index(fields=['reciter'], name='dua_duaaudi_reciter_23aae0_idx'),
        ),
        migrations.AddIndex(
            model_name='dua',
            index=models.Index(fields=['status'], name='dua_dua_status_745f55_idx'),
        ),
        migrations.AddIndex(
            model_name='dua',
            index=models.Index(fields=['title'], name='dua_dua_title_efb0d1_idx'),
        ),
    ]
