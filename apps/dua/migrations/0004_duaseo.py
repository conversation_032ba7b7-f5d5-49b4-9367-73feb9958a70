# Generated by Django 3.2.25 on 2025-01-08 11:06

import dj_language.field
from django.db import migrations, models
import django.db.models.deletion
import limitless_dashboard.fields.comma_sep


class Migration(migrations.Migration):

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
        ('dua', '0003_auto_20241129_0713'),
    ]

    operations = [
        migrations.CreateModel(
            name='DuaSeo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, help_text='maximum length of page title is 70 characters and minimum length is 30', max_length=140, null=True, verbose_name='seo title')),
                ('keywords', limitless_dashboard.fields.comma_sep.CommaSepModelField(blank=True, help_text='keywords in the content that make it possible for people to find the site via search engines', max_length=255, null=True)),
                ('description', models.CharField(blank=True, help_text='describes and summarizes the contents of the page for the benefit of users and search engines', max_length=170, null=True)),
                ('dua', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dua_seos', to='dua.dua', verbose_name='dua')),
                ('language', dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language')),
            ],
            options={
                'verbose_name': 'dua seo fields',
                'verbose_name_plural': 'dua seo fields',
            },
        ),
    ]
