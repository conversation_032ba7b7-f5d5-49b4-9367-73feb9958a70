from django import forms

from apps.mafatih.models import MafatihPart


class MafatihPartForm(forms.ModelForm):
    class Meta:
        model = MafatihPart
        exclude = ()

    def clean(self):
        data = self.cleaned_data
        if data['translation']:
            if data['translation'].count('//') != data['text'].count('//'):
                self.add_error('text', 'should have equal count of "//" sign in both text and translation')

        return data
