from dj_filer.admin import get_thumbs

from rest_framework import serializers
from utils import absolute_url

from apps.dua.models import Du<PERSON>, DuaAudio, DuaReciter
from apps.dua.serializers.v1 import MafatihDuaSerializer, DuaAudioDetailSerializer



class DuaReciterSerializer(serializers.ModelSerializer):
    avatar = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    
    class Meta:
        model = DuaReciter
        fields = ['id', 'name', 'slug', 'avatar']


    def get_name(self, obj):
        try:
            request = self.context.get('request')
            for i in obj.translations:
                title = i.get('title')
                if i['language_code'] == request.LANGUAGE_CODE:
                    if not isinstance(title, dict) and title is not None:
                        return title
            return obj.name
        except Exception as exp:
            return None
        
    
        
    def get_avatar(self, obj):
        try:
            return get_thumbs(obj.avatar, self.context.get('request'))
        except Exception:
            return {}


class DuaAudioDetailV3Serializer(serializers.ModelSerializer):
    audio = serializers.SerializerMethodField()
    reciter = serializers.SerializerMethodField()

    def get_audio(self, obj):
        return absolute_url(self.context['request'], obj.audio.url)

    class Meta:
        model = DuaAudio
        fields = ('reciter', 'audio', 'audio_sync_data')

    def get_reciter(self, obj):
        return DuaReciterSerializer(obj.reciter_relation, context=self.context).data


class MafatihDuaSerializerV3(MafatihDuaSerializer):
    audios = DuaAudioDetailV3Serializer(many=True)

    class Meta:
        model = Dua
        fields = ('id', 'title', 'similar_titles', 'duas_part_list', 'dates', 'weekdays', 'audios',)
