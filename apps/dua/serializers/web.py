import re
from utils import absolute_url
from dj_filer.admin import get_thumbs
from dj_language.models import Language
from dj_language.serializer import LanguageSerializer
from django.db.models import <PERSON>ion<PERSON>rap<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>ield
from rest_framework import serializers

from ..models import <PERSON>aCategor<PERSON>, DuaAudio, Du<PERSON>, <PERSON>a<PERSON>art, <PERSON>aR<PERSON><PERSON>
from apps.dua.serializers import DuaReciterSerializer







class WebDuaCategoriesSerializer(serializers.ModelSerializer):
    thumbnails = serializers.SerializerMethodField('get_thumbnail_object')
    children = serializers.SerializerMethodField('get_children')
    language = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()

    def get_name(self, obj):
        request = self.context.get('request')
        return obj.get_translation(request.LANGUAGE_CODE)
    
    def get_language(self, obj):
        request = self.context.get('request')
        language = Language.objects.get(code=request.LANGUAGE_CODE)
        return LanguageSerializer(language).data
    
    
    def get_thumbnail_object(self, obj: <PERSON>a<PERSON>ategor<PERSON>):
        try:
            return get_thumbs(obj.thumbnail, self.context.get('request'))
        except Exception:
            return {}

    def get_children(self, obj: DuaCategory):
        children = obj.get_children().order_by('order')
        return [self.to_dict(cat) for cat in children]

    def to_dict(self, c):
        children = c.get_children().order_by('order')        
        return {
            'id': c.id,
            'name': self.get_name(c),
            'slug': c.slug,
            'thumbnails': self.get_thumbnail_object(c),
            'children': [] if not children else [self.to_dict(i) for i in children],
        }

    class Meta:
        model = DuaCategory
        fields = ('id', 'name', 'slug', 'thumbnails', 'language', 'children')
        ref_name = 'Web_DuaCategoriesSerializer'
        
        


class WebDuaSerializer(serializers.ModelSerializer):
    not_synced = serializers.BooleanField(default=False)
    title = serializers.SerializerMethodField('get_title')
    seo_field = serializers.SerializerMethodField()
    slug = serializers.SerializerMethodField('get_slug')
    
    def get_slug(self, obj):
        request = self.context.get('request')
        return obj.get_slug(request.LANGUAGE_CODE)
        
    def get_title(self, obj):
        request = self.context.get('request')
        return obj.get_translation(request.LANGUAGE_CODE)

    def get_seo_field(self, obj):
        request = self.context.get('request')        
        return obj.get_seo_for_language(request.LANGUAGE_CODE)
    
    class Meta:
        model = Dua
        fields = ('id', 'title', 'slug', 'not_synced', 'seo_field')
        ref_name = 'Web_DuaSerializer'
        
        
class DuaPartSerializer(serializers.ModelSerializer):
    dua_part_index = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    translation = serializers.SerializerMethodField()
    local_alpha = serializers.SerializerMethodField()
    mafatih_dua_id = serializers.SerializerMethodField()


    
    def get_mafatih_dua_id(self, obj):
        return obj.dua_id
    
    def get_dua_part_index(self, obj):
        return int(obj.dua_part_index or 1)

    def get_description(self, obj):
        request = self.context.get('request')
        return obj.get_part(request.LANGUAGE_CODE, obj.description)

    def get_translation(self, obj):
        request = self.context.get('request')
        return obj.get_part(request.LANGUAGE_CODE, obj.translation)
    
    def get_local_alpha(self, obj):
        request = self.context.get('request')
        return obj.get_part(request.LANGUAGE_CODE, obj.local_alpha)

    class Meta:
        model = DuaPart
        fields = ('id', 'dua_part_index', 'text', 'translation', 'local_alpha', 'mafatih_dua_id', 'description')
        ref_name = 'Web_DuaPartSerializer'
        
        
class WebDuaAudioSerializer(serializers.ModelSerializer):
    audio = serializers.SerializerMethodField()
    reciter = serializers.SerializerMethodField()

    def get_audio(self, obj):
        return absolute_url(self.context['request'], obj.audio.url)

    class Meta:
        model = DuaAudio
        fields = ('id', 'reciter', 'audio', 'audio_sync_data')

    def get_reciter(self, obj):
        return DuaReciterSerializer(obj.reciter_relation, context=self.context).data

        