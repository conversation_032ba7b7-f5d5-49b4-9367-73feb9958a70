

from dj_filer.admin import get_thumbs
from dj_language.serializer import LanguageSerializer
from django.db.models import Expression<PERSON>rap<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework import serializers
import logging

# from apps.mafatih.models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>h<PERSON>ategory, DuaAudio
from ..models import DuaCategory, DuaAudio, Dua, DuaPart, DuaReciter

from utils import absolute_url, get_language_safely

logger = logging.getLogger(__name__)



class DuaCategoriesSerializer(serializers.ModelSerializer):
    duas_index = serializers.SerializerMethodField('get_duas_index')
    thumbnails = serializers.SerializerMethodField('get_thumbnail_object')
    children = serializers.SerializerMethodField('get_children')
    language = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()

    def get_name(self, obj):
        request = self.context.get('request')
        return obj.get_translation(request.LANGUAGE_CODE)
    
    def get_language(self, obj):
        request = self.context.get('request')
        language = get_language_safely(request.LANGUAGE_CODE)
        return LanguageSerializer(language).data
    
    
    def get_thumbnail_object(self, obj: DuaCategory):
        try:
            return get_thumbs(obj.thumbnail, self.context.get('request'))
        except Exception:
            return {}

    def get_duas_index(self, obj: DuaCategory):
        return list(
            Dua.objects.filter(category__id=obj.id, status=True).annotate(
                not_synced=ExpressionWrapper(Q(audio_sync_data=[]), output_field=BooleanField())
            ).order_by('priority','not_synced', ).values_list('id', flat=True)
        )

    def get_children(self, obj: DuaCategory):
        request = self.context.get('request')
        children = obj.get_children().order_by('order')
        return [ self.to_dict(cat) for cat in children]

    def to_dict(self, c):
        children = c.get_children().order_by('order')
        return {
            'id': c.id,
            'name': self.get_name(c),
            'thumbnails': self.get_thumbnail_object(c),
            'children': [] if not children else [self.to_dict(i) for i in children],
            'duas_index': self.get_duas_index(c),
        }

    class Meta:
        model = DuaCategory
        fields = ('id', 'name', 'thumbnails', 'duas_index', 'language', 'children')


class MafatihDuaSerializer(serializers.ModelSerializer):
    duas_part_list = serializers.PrimaryKeyRelatedField(
        source='Dua_part',
        read_only=True,
        many=True
    )
    weekdays = serializers.SerializerMethodField(method_name='get_weekdays')
    audio_sync_data = serializers.SerializerMethodField(method_name='get_audio_sync_data')
    title = serializers.SerializerMethodField(method_name='get_title')
    audio = serializers.SerializerMethodField(method_name='get_audio_url')

    def get_title(self, obj):
        request = self.context.get('request')
        return obj.get_translation(request.LANGUAGE_CODE)
        
        
    def get_audio_sync_data(self, obj):
        return obj.audio_sync_data_s

    def get_weekdays(self, obj):
        return obj.weekdays.get('weekdays', [])

    def get_audio_url(self, obj):

        if audio_s := getattr(obj, 'audio_s', None):
            return f"/static/uploads/main/{audio_s}"
        elif obj.audio:
            return obj.audio.url

    class Meta:
        model = Dua
        fields = ('id', 'title', 'duas_part_list', 'dates', 'weekdays', 'audio_sync_data', 'audio')



class MafatihDuaPartSerializer(serializers.ModelSerializer):
    dua_part_index = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    translation = serializers.SerializerMethodField()
    local_alpha = serializers.SerializerMethodField()

    def get_description(self, obj):
        request = self.context.get('request')
        return obj.get_part(request.LANGUAGE_CODE, obj.description)

    def get_translation(self, obj):
        request = self.context.get('request')
        return obj.get_part(request.LANGUAGE_CODE, obj.translation)
    
    def get_local_alpha(self, obj):
        request = self.context.get('request')
        language_code = "en" if request.LANGUAGE_CODE == "ul" else request.LANGUAGE_CODE
        return obj.get_part(language_code, obj.local_alpha)
    
    
    def get_dua_part_index(self, obj):
        return int(obj.dua_part_index or 1)
    

    class Meta:
        model = DuaPart
        fields = ('id', 'dua_part_index', 'text', 'translation', 'local_alpha', 'dua_id', 'description')
        ref_name = "DuaPartSerializer"
        


class DuaAudioListSerializer(serializers.ModelSerializer):
    class Meta:
        model = DuaAudio
        fields = ('id', 'reciter',)


class DuaAudioDetailSerializer(serializers.ModelSerializer):
    audio = serializers.SerializerMethodField()

    def get_audio(self, obj):
        return absolute_url(self.context['request'], obj.audio.url)

    class Meta:
        model = DuaAudio
        fields = ('reciter', 'audio', 'audio_sync_data')
