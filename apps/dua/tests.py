# exporter
import pandas as pd

from .models import Ma<PERSON>tihDua


def export_mafatih():
    for i in MafatihDua.objects.filter(category__language__code='az'):
        data = []
        for j in i.mafatih_part.all():
            data.append([j.text, j.local_alpha, j.translation, j.description])

        dt = pd.DataFrame(data, columns=['text', 'local_alpha', 'translation', 'description'])
        dt.to_excel(f"files/{i.title}.xlsx", index=False)
    # dt.to_excel('hello.xlsx')

    #
    #
    # with open(f"csvs/{i.title}.csv", "w", encoding='utf-8') as f:
    #     columns = ['text', 'local_alpha', 'translation', 'description']
    #     writer = csv.DictWriter(f, fieldnames=columns)
    #     writer.writeheader()
    #     for j in i.mafatih_part.all():
    #         writer.writerow({
    #             'text': j.text,
    #             'local_alpha': j.local_alpha,
    #             'translation': j.translation,
    #             'description': j.description,
    #         })


export_mafatih()
