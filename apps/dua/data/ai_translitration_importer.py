import os
import time
from django.db.models import Q
import argparse
from utils.telegram_logger import telegram_logger  # Assuming you have a logging utility

# Set up environment for Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

import anthropic
from apps.mafatih.models import Ma<PERSON>tihDua, MafatihPart, MafatihCategory
from dj_language.models import Language  # Ensure Language model is imported

# Argument parser for language inputs
parser = argparse.ArgumentParser(description='Transliteration Script for Mafatih Parts')
parser.add_argument('--lang_code', type=str, help='Target language code', required=True)
parser.add_argument('--lang_name', type=str, help='Target language name', required=True)
args = parser.parse_args()

target_language_code = args.lang_code
target_language_name = args.lang_name

telegram_logger(f"Starting transliteration process for Mafatih Parts to {target_language_name}...")


# Transliteration function using Anthropic API
def get_transliteration(text):
    prompt = "Please transliterate the following Arabic text into Latin script exactly as it is pronounced, including phonetic markers to capture the correct sounds. Write it naturally as it would be spoken, so it’s easy to read for someone unfamiliar with Arabic. Provide only the transliteration without any additional text or explanations: "
    client = anthropic.Anthropic(
        api_key="************************************************************************************************************"  # replace with actual API key
    )

    message = client.messages.create(
        model="claude-3-5-sonnet-20240620",
        max_tokens=1024,
        messages=[{"role": "user", "content": f"{prompt} {text}"}]
    )

    transliteration = message.content[0].text
    return transliteration


# Transliterate and update Mafatih Parts without transliteration for the target language
def transliterate_mafatih_parts():
    # دریافت نمونه زبان هدف
    target_language = Language.objects.get(code=target_language_code)

    # فیلتر کردن دسته‌بندی‌هایی که زبان هدف دارند
    target_categories = MafatihCategory.objects.filter(language=target_language)

    # انتخاب دعاهایی که در دسته‌بندی‌های هدف قرار دارند
    target_duas = MafatihDua.objects.filter(category_second__in=target_categories)

    # انتخاب پارت‌هایی که به دعاهای فیلتر شده تعلق دارند و `local_alpha` آن‌ها خالی است
    parts_to_transliterate = MafatihPart.objects.filter(
        mafatih_dua__in=target_duas  # فیلتر بر اساس دعاهای هدف
    ).exclude(text='')  # اطمینان از اینکه متن دعا خالی نیست

    for part in parts_to_transliterate:
        if not part.local_alpha:  # اگر local_alpha خالی بود
            if part.text:
                transliteration = get_transliteration(part.text)
                part.local_alpha = transliteration
                part.save()
                telegram_logger(f"Transliterated text for part id {part.id} and saved.")
                time.sleep(0.5)  # Pause to avoid hitting rate limits

    telegram_logger("Transliteration process completed for Mafatih parts.")


# Run transliteration function
transliterate_mafatih_parts()
print("Transliteration process completed.")
