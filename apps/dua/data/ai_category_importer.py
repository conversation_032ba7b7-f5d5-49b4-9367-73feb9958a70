import os
import sys
import json
import time
from django.db import models
from django.db.models import Q
from openai import OpenAI

# اضافه کردن مسیر پروژه به sys.path
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
)

# تنظیم متغیر محیطی برای تنظیمات Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

# مطمئن شوید که مدل‌های لازم را import کرده‌اید
from apps.mafatih.models import MafatihCategory, BaseCategory
from dj_language.models import Language  # مطمئن شوید که مدل Language را ایمپورت کرده‌اید

# تنظیم API Key چت GPT
client = OpenAI(
    api_key='***********************************************************************************************'
)

# متغیر زبان مقصد
target_language = 'uz'
target_language_id = 4
# دیکشنری برای نگهداری معادل دسته‌بندی‌های فارسی و ازبکی
category_translation_map = {}

# تابع برای ترجمه به زبان مقصد با استفاده از API چت GPT
def translate_text(text, target_lang):
    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": "This title is a prayer in Persian. This prayer is for Shia Muslims. Please translate into Uzbek using the Cyrillic alphabet. Just give the translated text."},
            {"role": "user", "content": text}
        ],
        temperature=1,
        max_tokens=256,
        top_p=1,
        frequency_penalty=0,
        presence_penalty=0
    )
    # استخراج محتوای ترجمه‌شده از پاسخ
    translated_text = response.choices[0].message.content.strip()
    return translated_text

def get_or_create_uzbek_category(category):
    if category.id in category_translation_map:
        return category_translation_map[category.id]

    translated_name = translate_text(category.name, target_language)
    target_language_instance = Language.objects.get(pk=target_language_id)  # بازیابی نمونه‌ی زبان از مدل Language

    # بررسی وجود دسته‌بندی معادل ازبکی
    uzbek_category, created = MafatihCategory.objects.get_or_create(
        slug=category.slug + '-' + target_language,
        defaults={
            'name': translated_name,
            'language': target_language_instance,  # استفاده از نمونه‌ی زبان
            'thumbnail': category.thumbnail,  # استفاده از همان تصویر بندانگشتی
            'content_type': category.content_type,  # نوع محتوا را حفظ کنید
            'is_active': category.is_active,
            'created_at': category.created_at
        }
    )

    if category.parent:
        uzbek_parent = get_or_create_uzbek_category(category.parent)
        uzbek_category.parent = uzbek_parent  # تنظیم والد برای دسته‌بندی
        uzbek_category.save()

    category_translation_map[category.id] = uzbek_category
    return uzbek_category

print("Translating Persian categories to Uzbek...")

# استخراج دسته‌بندی‌هایی که به زبان فارسی هستند
persian_categories = MafatihCategory.objects.filter(language=69)

# ترجمه دسته‌بندی‌ها و ذخیره در دیکشنری
for category in persian_categories:
    get_or_create_uzbek_category(category)

# ذخیره دیکشنری در فایل JSON
with open(f'category_translation_map_{target_language}.json', 'w', encoding='utf-8') as f:
    json.dump({str(k): v.id for k, v in category_translation_map.items()}, f, ensure_ascii=False, indent=4)

print("Category translation completed and saved to category_translation_map.json")
