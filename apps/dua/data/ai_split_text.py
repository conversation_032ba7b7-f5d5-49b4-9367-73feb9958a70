# This Python file uses the following encoding: utf-8
import json
import os
import sys
from time import sleep

import openai

sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
import openai
openai.organization = "org-zOt9DlJOdCHEqrao6na057OD"
openai.api_key = "***************************************************"


def get_completion(ar, translate, translate_language, model="gpt-3.5-turbo", temperature=0):
    prompt = f"""
    don't include any explanations in your responses
    My Arabic text is inside the <AR> tag and the {translate_language} translation of the text is inside the <FA> tag.
     Divide the Arabic text into smaller parts so that each Arabic part has a maximum of 15 words.
     Divide the {translate_language} text corresponding to the Arabic text.
     Be careful that you should not delete or change the texts and you should bring all the text as it is.
     If you couldn't match the parts correspondingly, tell me that I can't
     And finally, give me json output and dont include any explantions in your response and just print out the json, so that each section and the corresponding {translate_language} translation of the same section are written in an object with the following keys:
    [
        {{
            "ar":"",
            "fa":""
        }}
    ]
    
    
    <AR>{ar}</AR>
    <FA>{translate}</FA>
    """
    messages = [{"role": "user", "content": prompt}]
    response = openai.ChatCompletion.create(
        model=model,
        messages=messages,
        temperature=temperature,
    )
    print("fa",translate)
    print(response.choices[0].message["content"].replace("Here is the JSON output:",""))
    return response.choices[0].message["content"].replace("Here is the JSON output:","")


from apps.mafatih.models import MafatihPart

x = MafatihPart.objects.filter(mafatih_dua__category__language__code='fa', text__isnull=False).order_by('id')

for d in x:
    if len(d.text) > 130:

        try:

            ar = d.text
            translate = d.translation.replace('\u200c',' ')
            result = get_completion(ar, translate, translate_language='فارسی')

            if err := "error" in result or "overloaded" in result:
                if "502" in result or "overloaded" in result:
                    result = get_completion(ar, translate, translate_language='فارسی')
                    result = json.loads(result)
                    if err := "error" in result or "overloaded" in result:
                        if "502" in result or "overloaded" in result:
                            result = get_completion(ar, translate, translate_language='فارسی')
                            result = json.loads(result)

            result = json.loads(result)
            insert_parts_len = len(result)
            other_part = MafatihPart.objects.filter(mafatih_dua_id=d.mafatih_dua_id, dua_part_index__gt=d.dua_part_index)
            for o in other_part:
                o.dua_part_index = o.dua_part_index + (insert_parts_len - 1)
                # print('o.dua_part_index', o.dua_part_index)
                o.save()

            u = 0
            for r in result:
                dua_part_index = d.dua_part_index + u
                # print('dua_part_index', d.dua_part_index)
                # print('mafatih_dua', d.mafatih_dua)
                # print('text', r['ar'])
                # print('translation', r['tr'])
                # print('______________')
                MafatihPart.objects.create(
                    dua_part_index=dua_part_index,
                    mafatih_dua=d.mafatih_dua,
                    text=r['ar'],
                    translation=r['fa'],
                )
                u += 1

            print(d.mafatih_dua_id)
            d.delete()
            # print(result)
        except Exception as e:
            print(e)

        sleep(10)
