import os
import json
import time
import argparse
from django.db import models
from utils.telegram_logger import telegram_logger
import anthropic

# Set up environment for Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
from apps.dua.models import Du<PERSON>, DuaPart, DuaCategory
from dj_language.models import Language

# Argument parser for language inputs
parser = argparse.ArgumentParser(description='Translation Script for Mafatih Categories and Duas')
parser.add_argument('--lang_code', type=str, help='Target language code', required=True)
parser.add_argument('--lang_name', type=str, help='Target language name', required=True)
parser.add_argument('--action', type=str, help='Batch Translation Manager', required=True)
parser.add_argument('--batch_id', type=str, help='Batch Translation Manager')
args = parser.parse_args()

target_language_code = args.lang_code
target_language_name = args.lang_name
action = args.action
batch_id = args.batch_id

telegram_logger(f"Starting the translation process for Mafatih Duas and categories to {target_language_name}...")
print(f"Starting the translation process for {target_language_name}...")

client = anthropic.Anthropic(api_key="************************************************************************************************************")

def prepare_translation_requests(text_items):
    requests = []
    for idx, (text, context, item_id) in enumerate(text_items):
        if target_language_code == "ul":
            if context == "description":
                prompt = f"""Translate this prayer description into Roman Urdu.

This text is from the prayer and ritual book of Shia Muslims, 'Mafatih al-Jinan,' which contains prayers, supplications, rituals, and recommended acts. The translation should:

Be faithful to the original meaning, while respecting Shia theological and cultural context.

Use simple and clear vocabulary appropriate for Roman Urdu readers.

Be fluent and natural, not just a word-for-word rendering.

Include brief clarifying phrases if needed to convey the full message — but avoid long commentary.

For Islamic terms and names:
- Use common Roman Urdu spellings for Islamic terms (e.g., "namaz" for نماز, "dua" for دعا)
- Keep Arabic supplications in their original form (e.g., "la ilaha illa Allah" for لا اله الا الله)
- Use familiar Roman Urdu spellings for names (e.g., "Hazrat Ali" for حضرت علی)
- For places and special terms, use commonly accepted Roman Urdu spellings

Provide only the translation, with no extra text or explanation."""
            elif context == "title":
                prompt = f"""Translate this prayer title into Roman Urdu.

This title is from the prayer and ritual book of Shia Muslims, 'Mafatih al-Jinan.' The translation should:

Be faithful to the original meaning, while respecting Shia theological and cultural context.

Use simple and clear vocabulary appropriate for Roman Urdu readers.

Be fluent and natural, not just a word-for-word rendering.

Include brief clarifying phrases if needed to convey the full message — but avoid long commentary.

For Islamic terms and names:
- Use common Roman Urdu spellings for Islamic terms (e.g., "namaz" for نماز, "dua" for دعا)
- Keep Arabic supplications in their original form (e.g., "la ilaha illa Allah" for لا اله الا الله)
- Use familiar Roman Urdu spellings for names (e.g., "Hazrat Ali" for حضرت علی)
- For places and special terms, use commonly accepted Roman Urdu spellings

Provide only the translation, with no extra text or explanation."""
            elif context == "category_name":
                prompt = f"""Translate this prayer category name into Roman Urdu.

This category name is from the prayer and ritual book of Shia Muslims, 'Mafatih al-Jinan.' The translation should:

Be faithful to the original meaning, while respecting Shia theological and cultural context.

Use simple and clear vocabulary appropriate for Roman Urdu readers.

Be fluent and natural, not just a word-for-word rendering.

Include brief clarifying phrases if needed to convey the full message — but avoid long commentary.

For Islamic terms and names:
- Use common Roman Urdu spellings for Islamic terms (e.g., "namaz" for نماز, "dua" for دعا)
- Keep Arabic supplications in their original form (e.g., "la ilaha illa Allah" for لا اله الا الله)
- Use familiar Roman Urdu spellings for names (e.g., "Hazrat Ali" for حضرت علی)
- For places and special terms, use commonly accepted Roman Urdu spellings

Provide only the translation, with no extra text or explanation."""
            else:  # text
                prompt = f"""Translate this prayer text into Roman Urdu.

This prayer is from the prayer and ritual book of Shia Muslims, 'Mafatih al-Jinan.' The translation should:

Be faithful to the original meaning, while respecting Shia theological and cultural context.

Use simple and clear vocabulary appropriate for Roman Urdu readers.

Be fluent and natural, not just a word-for-word rendering.

Include brief clarifying phrases if needed to convey the full message — but avoid long commentary.


Provide only the translation, with no extra text or explanation.
text:
"""
        else:
            if context == "description":
                prompt = f"""
                This phrase is from the prayer and ritual book of Shia Muslims, 'Mafatih al-Jinan,' which contains prayers, supplications, rituals, and recommended acts. It holds deep religious significance in Islamic and Shia culture.
                Translate this phrase into {target_language_name}, adhering to the following guidelines:
                - Accurately convey Shiite Islamic terms, practices, and names of significant figures or places, preserving their Arabic pronunciation and form. 
                - Remove diacritical marks from Arabic transliterations.
                - Use direct transliterations for key terms such as Salat, Ziyarat, Dua, Amal, and Qunut, and integrate them into sentences in accordance with {target_language_name} grammar while staying true to their Arabic meanings.
                Examples:
                - Translate "نماز شب" as "Night Salat."
                - Translate "نماز صبح" as "Morning Salat."
                - For names like Arabi, Hadith al-Nafs, Aqiqah, Ashura, Rawdah Munawwarah, and others, retain their Arabic transliterations without modifications.
                - Preserve supplicatory phrases and dhikr (e.g., "لا اله الا الله" as "la ilaha illa Allah") without translating or altering them.
                Important: Provide ONLY the translation without any extra text.
                Translate using the {target_language_name} script and alphabet.
                """
            elif context == "title":
                prompt = f"""
                This title is from the prayer and ritual book of Shia Muslims, 'Mafatih al-Jinan,' which includes prayers, supplications, rituals, and recommended acts.
                Translate this title into {target_language_name}, ensuring:
                - Shiite Islamic terms, practices, and names of significant figures or places retain their Arabic pronunciation and form.
                - Diacritical marks are removed from Arabic transliterations.
                - Use direct transliterations for key terms such as Salat, Ziyarat, Dua, Amal, and Qunut, integrating them into proper {target_language_name} grammar while preserving their Arabic meanings.
                Examples:
                - Translate "نماز شب" as "Night Salat."
                - Translate "نماز صبح" as "Morning Salat."
                - Retain Arabic transliterations for names like Aqiqah, Ashura, Rawdah Munawwarah, and others without modifications.
                Important: Provide ONLY the translation without any extra text.
                Translate using the {target_language_name} script and alphabet.
                """
            elif context == "category_name":
                prompt = f"""
                This category name is from the prayer and ritual book of Shia Muslims, 'Mafatih al-Jinan,' which includes prayers, supplications, rituals, and recommended acts.
                Translate this category name into {target_language_name} while adhering to the following:
                - Shiite Islamic terms, practices, and names of figures or places must retain their Arabic pronunciation and form.
                - Remove diacritical marks from Arabic transliterations.
                - Use direct transliterations for terms such as Salat, Ziyarat, Dua, Amal, and Qunut, combining them with surrounding words to follow {target_language_name} grammar while staying faithful to their Arabic meanings.
                Examples:
                - Translate "نماز شب" as "Night Salat."
                - Translate "نماز صبح" as "Morning Salat."
                - Retain names like Arabi, Laylat al-Raghaib, Ruqat al-Jayb, and others in their original Arabic transliterations.
                Important: Provide ONLY the translation without any extra text.
                Translate using the {target_language_name} script and alphabet.
                """
            else:  # text
                prompt = f"""
                This prayer is from the prayer and ritual book of Shia Muslims, 'Mafatih al-Jinan,' which contains prayers, supplications, rituals, and recommended acts. It holds deep religious significance in Islamic and Shia culture.
                Translate this prayer into {target_language_name} with utmost accuracy, ensuring the meaning is conveyed precisely and aligns with the linguistic and cultural nuances of the {target_language_name} language.
                Your translation should be:
                - Clear and fluent for native speakers.
                - Faithful to the original meaning.
                - Using the {target_language_name} script and alphabet.
                Important: Provide ONLY the translation without any extra text or comments.
                """

        prompt += f"\n{text}"

        requests.append({
            "custom_id": f"{context}__{item_id}",
            "params": {
                "model": "claude-3-7-sonnet-latest",
                "max_tokens": 2048,
                "messages": [{"role": "user", "content": prompt}]
            }
        })
    return requests

def send_translation_requests():
    duas = Dua.objects.filter(status=True).distinct()

    text_items = []
    for dua in duas:
        if not any(t.get("language_code") == target_language_code for t in dua.title_translations):
            text_items.append((dua.title, "title", dua.id))

        for part in dua.Dua_part.all():
            if part.text:
                if not any(t.get("language_code") == target_language_code for t in part.translation):
                    text_items.append((part.text, "text", part.id))
            elif part.description:
                if not any(d.get("language_code") == target_language_code for d in part.description):
                    persian_description = next((d.get("text") for d in part.description if d.get("language_code") == "fa"), None)
                    text_items.append((persian_description, "description", part.id))

    for category in DuaCategory.objects.all():
        if not any(t.get("language_code") == target_language_code for t in category.name):
            text_items.append((category.name[0]["text"], "category_name", category.id))

    requests = prepare_translation_requests(text_items)
    with open(f'dua_translation_map_{target_language_code}.json', 'w', encoding='utf-8') as f:
        json.dump(requests, f, ensure_ascii=False, indent=4)

    batch = client.beta.messages.batches.create(requests=requests)
    telegram_logger(f"Batch translation requests sent. Batch detail: {batch}")
    print(f"Batch translation requests sent. Batch ID: {batch.id}")

def check_and_update_translations():
    results = client.beta.messages.batches.results(batch_id)
    successful_translations = 0

    for result in results:
        custom_id = result.custom_id
        translation = result.result.message.content[0].text if result.result.type == "succeeded" else None

        if not translation:
            continue

        # Extract context and item_id from custom_id
        context, item_id = custom_id.split("__")
        item_id = int(item_id)

        # Check if the language already exists in the database
        if context == "title":
            dua = Dua.objects.get(id=item_id)
            if any(t.get("language_code") == target_language_code for t in dua.title_translations):
                continue
            dua.title_translations.append({"text": translation, "language_code": target_language_code})
            dua.save()
        elif context == "category_name":
            category = DuaCategory.objects.get(id=item_id)
            if any(t.get("language_code") == target_language_code for t in category.name):
                continue
            category.name.append({"text": translation, "language_code": target_language_code})
            category.save()
        elif context == "text":
            part = DuaPart.objects.get(id=item_id)
            if any(t.get("language_code") == target_language_code for t in part.translation):
                continue
            part.translation.append({"text": translation, "language_code": target_language_code})
            part.save()
        elif context == "description":
            part = DuaPart.objects.get(id=item_id)
            if any(d.get("language_code") == target_language_code for d in part.description):
                continue
            part.description.append({"text": translation, "language_code": target_language_code})
            part.save()

        successful_translations += 1
        log_message = (
            f"Item ID {item_id} ({context}) translated to {target_language_name}.\n\n"
            f"Translation: {translation}\n\n"
            f"Total successful translations so far: {successful_translations}"
        )
        telegram_logger(log_message)

    telegram_logger(f"Batch {batch_id} processed and database updated with {successful_translations} successful translations.")
    print(f"Batch {batch_id} processed and database updated with {successful_translations} successful translations.")

def main():
    if action == "send":
        send_translation_requests()
    elif action == "check":
        check_and_update_translations()
    else:
        print("Invalid action. Use 'send' or 'check'.")

if __name__ == "__main__":
    main()
