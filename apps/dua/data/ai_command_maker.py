import os
import sys
import json
from time import sleep

from django.db import models
from openai import OpenAI

# Add project path to sys.path
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
)

# Set environment variable for Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

# Import necessary models
from apps.mafatih.models import Ma<PERSON>tihDua

# Set up the OpenAI API client
client = OpenAI(
    api_key='***********************************************************************************************'
)

def generate_phrases(content_name, language):
    # Set up the prompt for generating Persian phrases
    system_message = (
        "I'm building a voice assistant for an Islamic application. Now, I want to set up sentences that the user "
        f"might say in {language} to access prayers, rituals and pilgrimages. For example, the user might say:\n"
        f"\"Can you open the {content_name}?\" \"Would you show me the {content_name}?\" \"Play the {content_name}.\"\n\n"
        f"Please suggest other phrases that the user might say in {language} for this content. Provide at least 20 examples. "
        "Keep in mind that the user might speak respectfully, casually, or in different tones. "
        "Output the phrases in a JSON array format like [\"phrase1\", \"phrase2\", ...] with no extra text or formatting."

    )

    # Request GPT-4 to generate the phrases
    response = client.chat.completions.create(
        model="gpt-4",
        messages=[
            {"role": "system", "content": system_message}
        ],
        temperature=1,  # Lower temperature for more predictable output
        max_tokens=1500,  # Increase max tokens to ensure full response
        top_p=1,
        frequency_penalty=0,
        presence_penalty=0
    )

    # Extract the generated JSON phrases
    phrases_json = response.choices[0].message.content.strip()
    print(phrases_json)
    sleep(2)
    return json.loads(phrases_json)

print("Generating Persian phrases for all Duas...")

# Fetch all instances of MafatihDua
language = "Persian"
all_duas = MafatihDua.objects.filter(category_second__language__code='fa').all()
print("Total Duas:", all_duas.count())

for dua in all_duas:
    # Check if ai_commands field is empty or does not exist
    if not dua.ai_commands:
        # Generate phrases for each Dua
        phrases_data = generate_phrases(dua.title, language)

        # Update the ai_commands field with the generated phrases
        dua.ai_commands = phrases_data
        dua.save()

        print(f"Persian phrases for '{dua.title}' have been saved successfully.")
    else:
        print(f"Skipping '{dua.title}' as it already has ai_commands.")

print("Phrase generation and saving process completed for all Duas.")
