import os
import json
import time
import argparse
from django.db import models
from utils.telegram_logger import telegram_logger
import anthropic

# Set up environment for Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
from apps.dua.models import Du<PERSON>, DuaPart, DuaCategory
from dj_language.models import Language

# Argument parser for language inputs
parser = argparse.ArgumentParser(description='Translation Script for Mafatih Categories and Duas')
parser.add_argument('--lang_code', type=str, help='Target language code', required=True)
parser.add_argument('--lang_name', type=str, help='Target language name', required=True)
parser.add_argument('--action', type=str, help='Batch Translation Manager', required=True)
parser.add_argument('--batch_id', type=str, help='Batch Translation Manager')
args = parser.parse_args()

target_language_code = args.lang_code
target_language_name = args.lang_name
action = args.action
batch_id = args.batch_id

telegram_logger(f"Starting the translation process for Mafatih Duas and categories to {target_language_name}...")
print(f"Starting the translation process for {target_language_name}...")

client = anthropic.Anthropic(api_key="************************************************************************************************************")


# Function to prepare translation requests
def prepare_translation_requests(text_items):
    requests = []
    for idx, (text, context, item_id) in enumerate(text_items):
        prompt = f"""
        Please transliterate the following Arabic text into Russian Cyrillic script exactly as it is pronounced, using proper Russian phonetics and rules. Write it naturally as it would be spoken in Russian, so it’s easy to read for native Russian speakers. Provide only the transliteration without any additional text or explanations.
        """
        prompt += f"\n{text}"

        requests.append({
            "custom_id": f"{context}__{item_id}",
            "params": {
                "model": "claude-3-5-sonnet-20241022",
                "max_tokens": 2048,
                "messages": [{"role": "user", "content": prompt}]
            }
        })
    return requests


# Function to send batch translation requests
def send_translation_requests():
    duas = Dua.objects.filter(status=True).distinct()

    text_items = []
    for dua in duas:
        for part in dua.Dua_part.all():
            if part.text:
                if not any(t.get("language_code") == target_language_code for t in part.local_alpha):
                    text_items.append((part.text, "local_alpha", part.id))

    requests = prepare_translation_requests(text_items)
    with open(f'local_alpha_{target_language_code}.json', 'w', encoding='utf-8') as f:
        json.dump(requests, f, ensure_ascii=False, indent=4)

    batch = client.beta.messages.batches.create(requests=requests)
    telegram_logger(f"Batch translation requests sent. Batch detail: {batch}")
    # telegram_logger(f"Batch translation requests sent. Batch detail: ")
    print(f"Batch translation requests sent. Batch ID: {batch.id}")
    # print(f"Batch translation requests sent. Batch ID: ")


# Function to check batch status and update database
def check_and_update_translations():
    telegram_logger(f"Checking batch translations for batch ID: {batch_id}...")
    print(f"Checking batch translations for batch ID: {batch_id}...")

    try:

        results = client.beta.messages.batches.results(batch_id)
        for result in results:
            custom_id = result.custom_id
            local_alpha = result.result.message.content[0].text if result.result.type == "succeeded" else None


            if not local_alpha:
                telegram_logger(f"No translation available for custom_id: {custom_id}. Skipping...")
                continue

            # Extract context and item_id from custom_id
            context, item_id = custom_id.split("__")
            item_id = int(item_id)

            # Process translations based on context
            if context == "local_alpha":
                part = DuaPart.objects.get(id=item_id)
                if not any(t.get("language_code") == target_language_code for t in part.local_alpha):
                    part.local_alpha.append({"text": local_alpha, "language_code": target_language_code})
                    part.save()

        telegram_logger(f"Batch {batch_id} processed successfully and database updated.")
        print(f"Batch {batch_id} processed successfully and database updated.")

    except Exception as e:
        telegram_logger(f"An error occurred while processing batch {batch_id}: {e}")
        print(f"An error occurred while processing batch {batch_id}: {e}")


# Main function
def main():
    if action == "send":
        send_translation_requests()
    elif action == "check":
        check_and_update_translations()
    else:
        print("Invalid action. Use 'send' or 'check'.")


if __name__ == "__main__":
    main()
