import os
import json
import time
import argparse
from django.db import models
from utils.telegram_logger import telegram_logger  # Assuming you have a logging utility like in the Ahkam script
import anthropic
# Set up environment for Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
from apps.dua.models import Du<PERSON>, DuaPart, DuaCategory
from dj_language.models import Language  # Ensure Language model is imported

# Argument parser for language inputs
parser = argparse.ArgumentParser(description='Translation Script for Mafatih Categories and Duas')
parser.add_argument('--lang_code', type=str, help='Target language code', required=True)
parser.add_argument('--lang_name', type=str, help='Target language name', required=True)
args = parser.parse_args()

target_language_code = args.lang_code
target_language_name = args.lang_name

telegram_logger(f"Starting the translation process for <PERSON><PERSON><PERSON><PERSON> and categories to {target_language_name}...")
print(f"Starting the translation process for {target_language_name}...")


# Translation function (without the actual API prompt for you to add)
def translate_text(text, target_lang, translation_type):


    prompt = f"""
    
    This phrase is from the prayer and ritual book of Shia Muslims, 'Mafatih al-Jinan,' which contains prayers, supplications, rituals, and recommended acts. It holds deep religious significance within the context of Islamic and Shia culture.
     
    Please translate this phrase with great accuracy, ensuring that the meaning is conveyed precisely and is aligned with the language and cultural nuances of the {target_language_name} language.
      
    The goal is to provide a translation that is clear and fluent for native speakers.
       
    Be sure to double-check that the translation is accurate and fully conveys the intended meaning.

    When translating references to God, always use 'Allah'. 
        
    Important: Provide ONLY the translation without any extra text. \n

    Translate this phrase into {target_language_name}:\n

        
    """

    if translation_type == "description":
        prompt = f"""
        This phrase is from the prayer and ritual book of Shia Muslims, 'Mafatih al-Jinan,' which contains prayers, supplications, rituals, and recommended acts. It holds deep religious significance within the context of Islamic and Shia culture. 

        Translate Shiite Islamic terms, practices, or names of significant figures or places into the {target_language_name} language, preserving Arabic pronunciation and form. Remove diacritical marks from the translation. 

        For terms such as Salat, Ziyarat, Dua, Amal, and Qunut, use their {target_language_name} transliterations directly, and combine them with surrounding words in accordance with standard {target_language_name} grammar while aligning with the Arabic meaning. Examples: Translate "نماز شب" as "Night Salat" and "نماز صبح" as "Morning Salat."

        For names like Arabi, Hadith al-Nafs, Aqiqah, Ashura, Rawdah Munawwarah, Nafilah, Ghafilah, Musabbihat, Laylat al-Raghaib, Ayyam al-Bid, Ruqat al-Jayb, Amin Allah, and Silsilat al-Dhahab, retain their Arabic transliterations without additional modifications.

        Preserve supplicatory phrases and dhikr, such as "لااله الا الله" (la ilaha illa Allah) or "یا من ارجوه لکل خیر" (ya man arjuhu li-kulli khayr), without translation or alteration.

         When translating references to God, always use 'Allah'. 
        
        Important: Provide ONLY the translation without any extra text. \n
        
        Translate this phrase into {target_language_name}:\n

        
        """
    elif translation_type == "title":
        prompt = f"""
        This title is from the prayer and ritual book of Shia Muslims, 'Mafatih al-Jinan,' which includes prayers, supplications, rituals, and recommended acts. 

        Translate Shiite Islamic terms, practices, or names of significant figures or places into the {target_language_name} language, preserving Arabic pronunciation and form. Remove diacritical marks from the translation. 

        For terms such as Salat, Ziyarat, Dua, Amal, and Qunut, use their {target_language_name} transliterations directly, and combine them with surrounding words in accordance with standard {target_language_name} grammar while aligning with the Arabic meaning. Examples: Translate "نماز شب" as "Night Salat" and "نماز صبح" as "Morning Salat."

        For names like Arabi, Hadith al-Nafs, Aqiqah, Ashura, Rawdah Munawwarah, Nafilah, Ghafilah, Musabbihat, Laylat al-Raghaib, Ayyam al-Bid, Ruqat al-Jayb, Amin Allah, and Silsilat al-Dhahab, retain their Arabic transliterations without additional modifications.
         
         When translating references to God, always use 'Allah'. 
        
        Important: Provide ONLY the translation without any extra text.
         
        Translate this phrase into {target_language_name}:\n
        
        """
    elif translation_type == "category name":
        prompt = f"""
        This category name is from the prayer and ritual book of Shia Muslims, 'Mafatih al-Jinan,' which includes prayers, supplications, rituals, and recommended acts. 

        Translate Shiite Islamic terms, practices, or names of significant figures or places into the {target_language_name} language, preserving Arabic pronunciation and form. Remove diacritical marks from the translation. 

        For terms such as Salat, Ziyarat, Dua, Amal, and Qunut, use their {target_language_name} transliterations directly, and combine them with surrounding words in accordance with standard {target_language_name} grammar while aligning with the Arabic meaning. Examples: Translate "نماز شب" as "Night Salat" and "نماز صبح" as "Morning Salat."

        For names like Arabi, Hadith al-Nafs, Aqiqah, Ashura, Rawdah Munawwarah, Nafilah, Ghafilah, Musabbihat, Laylat al-Raghaib, Ayyam al-Bid, Ruqat al-Jayb, Amin Allah, and Silsilat al-Dhahab, retain their Arabic transliterations without additional modifications.

         When translating references to God, always use 'Allah'. 
        
        Important: Provide ONLY the translation without any extra text. \n
        
        Translate this phrase into {target_language_name}:\n


        """

    client = anthropic.Anthropic(api_key="************************************************************************************************************")
    message = client.messages.create(
        model="claude-3-5-sonnet-20240620",
        max_tokens=1024,
        messages=[{"role": "user", "content": f"{prompt} {text}"}]
    )

    time.sleep(1)

    translated_text = message.content[0].text
    return translated_text



# Utility to check if a translation already exists for a specific language
def is_translation_existing(translations, language_code):
    return any(translation.get("language_code") == language_code for translation in translations)


def is_translation_or_description_existing(part, language_code):
    # Check in both translation and description fields
    translation_exists = any(translation.get("language_code") == language_code for translation in part.translation)
    description_exists = any(description.get("language_code") == language_code for description in part.description)
    return translation_exists or description_exists


# Translate and get or create category with the correct translation
def translate_and_get_or_create_category(category):
    if not is_translation_existing(category.name, target_language_code):
        translated_name = translate_text(category.name[0]['text'], target_language_code, "category name")
        category.name.append({"text": translated_name, "language_code": target_language_code})
        category.save()
        telegram_logger(f"Category '{category.name[0]['text']}' translated to '{target_language_name}' \n https://habibapp.com/en/admin/dua/duacategory/{category.id}/change/.")
    return category


# Translate and create Dua with its parts and categories
def translate_and_create_dua(dua):
    # Translate title if needed
    if not is_translation_existing(dua.title_translations, target_language_code):
        translated_title = translate_text(dua.title, target_language_code, "title")
        dua.title_translations.append({"text": translated_title, "language_code": target_language_code})
        dua.save()
        telegram_logger(f"Dua '{dua.title}' translated to '{target_language_name}'. \n https://habibapp.com/en/admin/dua/dua/{dua.id}/change/")
    else:
        print(
            f"dua exist in {target_language_name} \n {dua.title}. \n https://habibapp.com/en/admin/dua/dua/{dua.id}/change/")

    # Translate and get categories for the Dua
    for category in dua.category.all():
        translate_and_get_or_create_category(category)


    # Translate and update Dua parts
    for part in dua.Dua_part.all():
        if not is_translation_or_description_existing(part, target_language_code):
            translated_text = translate_text(part.text, target_language_code, "text") if part.text else ""
            description_part = translate_text(part.description, target_language_code, "description") if part.description else ""


            # Append translations
            if translated_text:
                part.translation.append({"text": translated_text, "language_code": target_language_code})
            if description_part:
                part.description.append({"text": description_part, "language_code": target_language_code})

            part.save()
            telegram_logger(
                f"Translated and saved new Dua part for '{dua.title}' to '{target_language_name}'.\n{part.text}\n{translated_text}\n{description_part}\nhttps://habibapp.com/en/admin/dua/duapart/{part.id}/change/")
        else:
            print(
                f"dua part exist in {target_language_name}'.\n{part.text}\nhttps://habibapp.com/en/admin/dua/duapart/{part.id}/change/")


# Main process to extract and translate Duas
def main():
    duas = Dua.objects.filter(status=True).distinct()

    for dua in duas:
        translate_and_create_dua(dua)

    telegram_logger(f"Translation process completed for Mafatih Duas and categories to {target_language_name}.")
    print(f"Translation process completed for {target_language_name}.")


# Run the main function
if __name__ == "__main__":
    main()
