import os

# Set up environment for Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

from apps.mafatih.models import <PERSON><PERSON><PERSON>hDua, <PERSON><PERSON><PERSON>hPart, MafatihCategory ,TranslationLog

from apps.dua.models import Du<PERSON>, DuaPart, DuaCategory

# 1. انتقال ترجمه‌های عنوان دعاها (Dua Titles)
logs = TranslationLog.objects.filter(model_name='dua')

for log in logs:
    # پیدا کردن ادعیه فارسی بر اساس source_id
    source_dua = MafatihDua.objects.get(id=log.source_id)

    translated_dua = MafatihDua.objects.get(id=log.translated_id)

    # پیدا کردن ادعیه جدید بر اساس عنوان
    new_dua = Dua.objects.filter(title=source_dua.title).first()

    if new_dua:
        new_dua.title_translations.append({
            'language_code': 'en',
            'text': f'{translated_dua.title}'
        })
        new_dua.save()


# 2. انتقال ترجمه‌های قطعات دعاها (Dua Parts)
logs = TranslationLog.objects.filter(model_name='part')
for log in logs:

    try:
        # پیدا کردن قطعه دعا فارسی
        source_part = MafatihPart.objects.get(id=log.source_id)

        # پیدا کردن قطعه دعا جدید بر اساس translated_id
        translated_part = MafatihPart.objects.get(id=log.translated_id)

    except MafatihPart.DoesNotExist:
        print(f"قطعه ترجمه شده با شناسه {log.translated_id} پیدا نشد.")
        continue  # ادامه دادن به حلقه اگر رکورد پیدا نشد



    # ابتدا بررسی میکنیم که چه فیلدی موجود است و سپس بر اساس آن جستجو می‌کنیم
    if source_part.text:
        new_part = DuaPart.objects.filter(text=source_part.text, dua_part_index=source_part.dua_part_index).first()
    elif source_part.description:
        # در اینجا فرض می‌کنیم که description به صورت لیستی از دیکشنری‌ها ذخیره شده است
        # باید بررسی کنیم که آیا description شامل متنی است که ما نیاز داریم
        new_part = DuaPart.objects.filter(
            description__contains=[{'text': source_part.description, 'language_code': 'fa'}],
            dua_part_index=source_part.dua_part_index
        ).first()
    elif source_part.translation:
        # اگر translation موجود باشد
        new_part = DuaPart.objects.filter(
            translation__contains=[{'language_code': 'fa', 'text': source_part.translation}],
            dua_part_index=source_part.dua_part_index
        ).first()
    else:
        # اگر هیچکدام موجود نباشد، new_part را None قرار می‌دهیم
        new_part = None

    if new_part:
        if source_part.translation:
            new_part.translation.append({
                'language_code': 'en',
                'text': f'{translated_part.translation}'
            })
            new_part.save()

        if source_part.description:
            new_part.description.append({
                'language_code': 'en',
                'text': f'{translated_part.description}'
            })
            new_part.save()

# 3. انتقال ترجمه‌های دسته‌بندی دعاها (Dua Categories)
logs = TranslationLog.objects.filter(model_name='category')

for log in logs:
    # پیدا کردن دسته‌بندی فارسی
    source_category = MafatihCategory.objects.get(id=log.source_id)

    translated_category = MafatihCategory.objects.get(id=log.translated_id)

    # پیدا کردن دسته‌بندی جدید بر اساس نام
    new_category = DuaCategory.objects.filter(name__contains=[{'text': source_category.name, 'language_code': 'fa'}]).first()

    if new_category:
        new_category.name.append({
            'language_code': 'en',
            'text': f'{translated_category.name}'
        })
        new_category.save()