import os
import json
import time
from django.core.wsgi import get_wsgi_application
from utils.telegram_logger import telegram_logger

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
application = get_wsgi_application()

import anthropic
from apps.mafatih.models import Ma<PERSON>tihDua, MafatihCategory

# Anthropic API setup
client = anthropic.Anthropic(api_key="************************************************************************************************************")


def get_writing_styles(text):
    prompt = (
        f"Provide the writing style of the following phrase in the languages: Arabic, Persian, Urdu, French, Spanish, Turkish, "
        f"English, Russian, and Azeri. Return the result strictly as a JSON array in the following format without any additional text:\n\n"
        f"['Arabic writing', 'Persian writing', 'Urdu writing', 'French writing', 'Spanish writing', "
        f"'Turkish writing', 'English writing', 'Russian writing', 'Azeri writing']\n\nPhrase: {text}"
    )
    message = client.messages.create(
        model="claude-3-5-sonnet-20240620",
        max_tokens=1024,
        messages=[{"role": "user", "content": prompt}]
    )

    response_content = message.content[0].text
    if not response_content:
        telegram_logger(f"Received an empty response for text: {text}")
        return []

    try:
        similar_titles = json.loads(response_content)
        return similar_titles
    except json.JSONDecodeError:
        telegram_logger(f"Failed to decode JSON for response: {response_content}")
        # استفاده از یک آرایه پیش‌فرض در صورت بروز خطا
        return []


# بروزرسانی دعاهای دارای similar_titles خالی و ارسال لاگ به تلگرام
def update_similar_titles_for_empty(category_id):
    category = MafatihCategory.objects.get(id=category_id)
    duas = MafatihDua.objects.filter(category_second=category, similar_titles__isnull=True).union(
        MafatihDua.objects.filter(category_second=category, similar_titles=[])
    )

    for dua in duas:
        original_title = dua.title
        similar_titles = get_writing_styles(original_title)

        if similar_titles:  # چک کردن اینکه similar_titles خالی نباشد
            dua.similar_titles = similar_titles
            dua.save()

            # ارسال لاگ به تلگرام
            log_message = f"Updated 'similar_titles' for Dua '{dua.title}' (ID: {dua.id}) in category '{category.name}'"
            telegram_logger(log_message)
        else:
            telegram_logger(f"Skipping update for Dua '{dua.title}' (ID: {dua.id}) due to empty or invalid response.")

        time.sleep(0.2)

    telegram_logger(f"Update process completed for category ID {category_id}.")
    print(f"Update process completed for category ID {category_id}.")


# مثال استفاده
update_similar_titles_for_empty(category_id=6747)  # اینجا آی‌دی کتگوری را جایگزین کنید
