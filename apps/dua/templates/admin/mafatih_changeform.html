{% extends 'admin/change_form.html' %}
{% load i18n admin_urls static admin_modify %}


{% block content %}
    <div class="p-3">
        <form {% if has_file_field %}enctype="multipart/form-data" {% endif %}{% if form_url %}action="{{ form_url }}" {% endif %}method="post"
              id="{{ opts.model_name }}_form" novalidate>
            <div class="row">
                <div class="col-lg-6 div-card-body-forms">
                    <div class="card">
                        <div class="card-body">
                            {% csrf_token %}
                            {% block form_top %}{% endblock %}
                            <div class="col-md-12">
                                {% if is_popup %}<input type="hidden" name="{{ is_popup_var }}" value="1">{% endif %}
                                {% if to_field %}
                                    <input type="hidden" name="{{ to_field_var }}" value="{{ to_field }}">{% endif %}
                                {% if save_on_top %}{% block submit_buttons_top %}{% submit_row %}
                                {% endblock %}{% endif %}
                                {% block field_sets %}
                                    {% for fieldset in adminform %}
                                        {% include "admin/includes/fieldset.html" %}
                                    {% endfor %}
                                {% endblock %}
                                {% block after_field_sets %}{% endblock %}
                                {% block inline_field_sets %}
                                    {% for inline_admin_formset in inline_admin_formsets %}
                                        {% include inline_admin_formset.opts.template %}
                                    {% endfor %}
                                {% endblock %}
                                <div class="card-body d-flex justify-content-center flex-wrap">
                                    {% block submit_buttons_bottom %}{% submit_row %}{% endblock %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 div-card-body-forms">
                    <div class="card">
                        <div class="card-header bg-transparent header-elements-sm-inline py-sm-0">
                            <h6 class="card-title py-sm-3">{{ app_label }}  </h6>
                            <div class="header-elements">
                                <ul class="pagination pagination-pager justify-content-between">
                                    <li class="page-item">
                                        <button type="button" id="update-sort"
                                                class="d-none btn bg-purple-300 legitRipple"
                                                id="add_id_{{ part_app_label }}"
                                                href="/admin/{{ part_app_label }}/{{ part_model_name }}/add/?_to_field=id&_popup=1&mafatih={{ object_id }}">
                                            {% trans 'Update Sort' %}
                                            <i class="mi-sort ml-2"></i>
                                        </button>

                                        <a class=" btn bg-indigo-400 legitRipple add-related"
                                           id="add_id_{{ part_app_label }}"
                                           data-toggle="modal"
                                           data-target="#modal_add_id_{{ part_app_label }}"
                                           href="#modal_add_id_{{ part_app_label }}">
                                            {% trans 'Add New Part' %}
                                            <i class="mi-playlist-add ml-2"></i>
                                        </a>

                                        <a href='{% url opts|admin_urlname:"import" %}?mafatih={{ object_id }}'
                                           class="btn bg-info legitRipple mx-3">
                                            {% trans "Import From Excel" %}
                                            <i class="icon-database-insert"></i>
                                        </a>

                                        <div id="modal_add_id_{{ part_app_label }}" class="modal fade" tabindex="-1">
                                            <div class="modal-dialog modal-lg"
                                                 style="height: 90%; border-radius: 10px;">
                                                <div class="modal-content" style="height: 100%; border-radius: 10px;">
                                                    <iframe id="iframe_add_id_{{ part_app_label }}"
                                                            win-name="id_{{ part_app_label }}"
                                                            style="height: 100%; border-radius: 10px;">
                                                    </iframe>
                                                    <div class="iframe-loading" style="border-radius: 10px;"><i
                                                            class="icon-spinner2 spinner"></i></div>
                                                </div>
                                            </div>
                                        </div>
                                        <script defer>
                                            $(function () {
                                                var src = '/admin/{{ part_app_label }}/{{ part_model_name }}/add/?_to_field=id&_popup=1&mafatih={{ object_id }}'.replace('&amp;', '&') + '&child=' +
                                                    {% if child %}({{ request.GET.child }} +1){% else %}'1'{% endif %};

                                                $("#add_id_{{ part_app_label }}").click(function () {

                                                    $('#iframe_add_id_{{ part_app_label }}').attr('src', src);
                                                    setTimeout(function () {
                                                        $(".iframe-loading").hide();
                                                    }, 2000)
                                                });

                                            });

                                        </script>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            <ul class="list-group dropdown-menu-sortable border-x-0 rounded-0 border-top">
                                {% for mafatih_part in mafatih_parts %}
                                    <li data-id="{{ mafatih_part.id }}" data-index="{{ mafatih_part.dua_part_index }}"
    class="p-2 list-group-item-action border-bottom-1 border-bottom-grey-300"
                                        style="cursor: pointer;"
    onclick="window.open('/admin/mafatih/mafatihpart/{{ mafatih_part.id }}/change/?mafatih={{ mafatih_part.mafatih_dua_id }}', '_blank')">
                                        <div dir="rtl" class="font-weight-semibold">
                                         {% if mafatih_part.description %}
                                                {{ mafatih_part.description }}
                                         {% else %}
                                                {{ mafatih_part.text }}
                                         {% endif %}
                                        </div>
                                    <br>
                                    {% if mafatih_part.translation %}
                                        <div  class="text-muted  text-right" style="opacity: 0.7;">
                                            {{ mafatih_part.translation }}
                                        </div>
                                    {% endif %}



                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>


    {% for mafatih_part in mafatih_parts %}
        <div id="modal_change_id_{{ part_model_name }}_{{ mafatih_part.id }}" class="modal fade" tabindex="-1">
            <div class="modal-dialog modal-lg"
                 style="height: 90%; border-radius: 10px;">
                <div class="modal-content" style="height: 100%; border-radius: 10px;">
                    <iframe id="iframe_change_id_{{ part_model_name }}_{{ mafatih_part.id }}"
                            win-name="id_{{ part_model_name }}_{{ mafatih_part.id }}"
                            style="height: 100%; border-radius: 10px;">
                    </iframe>
                    <div class="iframe-loading" style="border-radius: 10px;"><i
                            class="icon-spinner2 spinner"></i></div>
                </div>
            </div>
        </div>
        <script defer>
            $(function () {
                var src = '/admin/{{ part_app_label }}/{{ part_model_name }}/{{ mafatih_part.id }}/change/?_to_field=id&_popup=1&mafatih={{ object_id }}';
                src += '&child=' + {% if child %}({{ request.GET.child }} +1){% else %}'1'{% endif %};

                $("#change_id_{{ part_model_name }}_{{ mafatih_part.id }}").click(function () {
                    $('#iframe_change_id_{{ part_model_name }}_{{ mafatih_part.id }}').attr('src', src);
                    setTimeout(function () {
                        $(".iframe-loading").hide();
                    }, 2000)
                });

            });

        </script>
    {% endfor %}


{% endblock %}

{% block scripts %}
    {{ block.super }}
    <script src="{% static 'admin/panel/global_assets/js/plugins/ui/dragula.min.js' %}"></script>
    <script id="django-admin-form-add-constants"
            src="{% static 'admin/js/change_form.js' %}"
            {% if adminform and add %}
            data-model-name="{{ opts.model_name }}"
            {% endif %}
            async>
    </script>

    <script>
        $(document).ready(function () {
            let editor = document.getElementById('id_weekdays')
            $(editor).each((index, editor) => {
                let schema_str = JSON.parse(editor.value)
                $(editor).addClass("hidden")
                let json_viewer_div = $(`<div class="json-view-editor"></div>`)
                $(editor).parent().append(json_viewer_div)

                init_(editor, json_viewer_div, schema_str || [])

            })
        })


        function init_(editor, json_viewer_div, start_value = []) {
            let jsoneditor = new JSONEditor(
                json_viewer_div[0], {
                    theme: 'bootstrap4',
                    schema: {
                        type: "object",
                        format: "table",
                        title: " ",
                        required: ["weekdays",],
                        properties: {
                            weekdays: {
                                type: "array",
                                uniqueItems: true,
                                format: "checkbox",
                                items: {
                                    type: "string",
                                    enum: ["saturday", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday"]
                                }
                            },
                        }
                    },

                    disable_edit_json: true,
                    disable_properties: true,
                    disable_array_delete_all_rows: true,
                    disable_array_delete_last_row: true,
                    disable_array_reorder: true,
                    prompt_before_delete: false,
                    disable_collapse: true,
                    startval: start_value,
                    disable_array_add: true,
                    disable_array_delete: true,
                    grid_columns: 7,
                })
            jsoneditor.on('change', () => {
                $(editor).val(JSON.stringify(jsoneditor.getValue()))
            })
        }

    </script>


{% endblock %}


{% block djangoMedia %}

    {{ media }}

{% endblock djangoMedia %}