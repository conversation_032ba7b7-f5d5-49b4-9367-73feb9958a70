{% extends 'admin/change_form.html' %}
{% load static %}

{% block scripts %}
    {{ block.super }}

    <script src="{% static 'dic.js' %}"></script>
    <script src="{% static 'admin/panel/global_assets/js/plugins/ui/dragula.min.js' %}"></script>
    <script>
        function init_editor_json_editor(field_id, properties, default_value) {
            let editor = document.getElementById(field_id);
            let schema_str = JSON.parse(editor.value);
            $(editor).addClass("hidden");
            let json_viewer_div = $(`<div class="json-view-editor" id='${field_id}-view-editor'></div>`);
            $(editor).parent().append(json_viewer_div);
            $(editor).parents(".col-lg-10.pl-0").addClass("col-lg-12").removeClass("col-lg-10");

            function init(properties, start_value = []) {
                if (window[`jsoneditor_${field_id}`]) {
                    window[`jsoneditor_${field_id}`].destroy();
                }
                window[`jsoneditor_${field_id}`] = new JSONEditor(
                    json_viewer_div[0], {
                        theme: 'bootstrap4',
                        schema: {
                            type: "array",
                            format: 'table',
                            title: ' ',
                            items: {
                                type: 'object',
                                title: 'Translations',
                                properties: properties
                            },
                        },
                        disable_edit_json: true,
                        disable_properties: false,
                        disable_array_delete_all_rows: true,
                        disable_array_delete_last_row: true,
                        disable_array_reorder: true,
                        grid_columns: 1,
                        prompt_before_delete: false,
                        disable_collapse: true,
                        startval: start_value
                    }
                );
                window[`jsoneditor_${field_id}`].on('change', () => {
                    $(editor).val(JSON.stringify(window[`jsoneditor_${field_id}`].getValue()));
                });
            }

            init(properties, schema_str || default_value);
        }

        // Initialize JSON Editor for title_translations
        init_editor_json_editor('id_title_translations', {
            text: { type: 'string', format: 'textarea', title: "Title" },
            language_code: {
                type: "string",
                enum: JSON.parse("{{ languages | escapejs }}"),
                default: "en",
                title: "Language"
            },
            visibility: {
                type: "string",
                enum: ["visible", "invisible"],
                default: "visible",
                title: "Visibility"
            },
        }, []);

        // Initialize JSON Editor for slug
        init_editor_json_editor('id_slug', {
            text: { type: 'string', format: 'textarea', title: "Slug" },
            language_code: {
                type: "string",
                enum: JSON.parse("{{ languages | escapejs }}"),
                default: "en",
                title: "Language"
            },
        }, []);

        // Initialize JSON Editor for weekdays
        $(document).ready(function () {
            let editor = document.getElementById('id_weekdays');
            if (editor) {
                let schema_str = JSON.parse(editor.value || '{}');
                $(editor).addClass("hidden");
                let json_viewer_div = $(`<div class="json-view-editor" id='id_weekdays-view-editor'></div>`);
                $(editor).parent().append(json_viewer_div);
                $(editor).parents(".col-lg-10.pl-0").addClass("col-lg-12").removeClass("col-lg-10");

                init_weekdays_editor(editor, json_viewer_div, schema_str || {});
            }
        });

        function init_weekdays_editor(editor, json_viewer_div, start_value = {}) {
            window.jsoneditor_weekdays = new JSONEditor(
                json_viewer_div[0], {
                    theme: 'bootstrap4',
                    schema: {
                        type: "object",
                        format: "table",
                        title: " ",
                        required: ["weekdays"],
                        properties: {
                            weekdays: {
                                type: "array",
                                uniqueItems: true,
                                format: "checkbox",
                                items: {
                                    type: "string",
                                    enum: ["saturday", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday"]
                                }
                            },
                        }
                    },
                    disable_edit_json: true,
                    disable_properties: true,
                    disable_array_delete_all_rows: true,
                    disable_array_delete_last_row: true,
                    disable_array_reorder: true,
                    prompt_before_delete: false,
                    disable_collapse: true,
                    startval: start_value,
                    disable_array_add: true,
                    disable_array_delete: true,
                    grid_columns: 7,
                }
            );
            window.jsoneditor_weekdays.on('change', () => {
                $(editor).val(JSON.stringify(window.jsoneditor_weekdays.getValue()));
            });
        }
    </script>
{% endblock %}