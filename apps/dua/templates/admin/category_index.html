{% extends 'admin/change_form.html' %}
{% load i18n static admin_modify mptt_tags %}

{% block content %}
    <div class="p-3">
        <div class="row">
            <div class="col-md-7">
                <div class="card p-3">
                    <form {% if has_file_field %}enctype="multipart/form-data" {% endif %}{% if form_url %}action="{{ form_url }}" {% endif %}method="post"
                          id="{{ opts.model_name }}_form" novalidate>{% csrf_token %}
                        {% block form_top %}{% endblock %}
                        <div class="">
                            {% block field_sets %}
                                <div class="col-md-12 hidden">
                                    <h6>{% trans "Parent: " %} <span id="add-to-cat"></span></h6>
                                </div>
                                {% for fieldset in adminform %}
                                    {% include "admin/includes/fieldset.html" with fullwidth="" %}
                                {% endfor %}

                            {% endblock %}
                            <div class="card-body">
                                {% include "admin/submit_line.html" with show_save_and_continue=True show_delete_link=True %}
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-md-5">
                <!-- Tree with drag and drop support -->
                <div class="card">
                    <div class="card-header header-elements-inline">
                        <h6 class="card-title">{% trans "Category Tree Editor" %}</h6>
                    </div>
                    <div class="card-body" style="min-height: 400px">
                        <p class="mb-3">
                            {% trans "Make your category and sort it by drag and drop . and try to edit items by double click." %}
                        </p>
                        <div class="tree-drag card card-body border-left-info border-left-2 shadow-0 rounded-left-0"
                             style="overflow: hidden !important;">
                        </div>
                    </div>
                </div>
                <!-- /tree with drag and drop support -->

            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    {{ block.super }}
    <script src="{% static "/admin/panel/global_assets/js/plugins/extensions/jquery_ui/core.min.js" %}"></script>
    <script src="{% static "/admin/panel/global_assets/js/plugins/extensions/jquery_ui/effects.min.js" %}"></script>
    <script src="{% static "/admin/panel/global_assets/js/plugins/extensions/jquery_ui/interactions.min.js" %}"></script>
    <script src="{% static "/admin/panel/global_assets/js/plugins/trees/fancytree_all.min.js" %}"></script>
    <script src="{% static "/admin/panel/global_assets/js/plugins/trees/fancytree_childcounter.js" %}"></script>
    <style>
        .fancytree-node:hover .add-child {
            opacity: 1;
        }
    </style>
    <script>

        function add_child(elem) {
            $("input[name='parent']").val($(elem).data('key'))
            $("#id_language").parents('.form-group').hide()
            $("#add-to-cat").html($(elem).data('t'))
            $("#add-to-cat").parent().parent().removeClass('hidden')
        }


        // For Categories with long name add a class
        if (window.screen.availWidth < 400) {
            setTimeout(async () => {
                let titles = await document.getElementsByClassName('fansytree_title_value');
                titles.forEach(title => {
                    if (title.textContent.length > 25) {
                        title.classList.add('fancytree_title_class');
                    }
                });
            }, 4000);
        } else {
            setTimeout(async () => {
                let titles = await document.getElementsByClassName('fansytree_title_value');
                titles.forEach(title => {
                    if (title.textContent.length > 130) {
                        title.classList.add('fancytree_title_class');
                    }
                });
            }, 4000);
        }
        // When we change the size of window in browser
        $(window).resize(function () {
            if (window.screen.availWidth < 400) {
                setTimeout(async () => {
                    let titles = await document.getElementsByClassName('fansytree_title_value');
                    titles.forEach(title => {
                        if (title.textContent.length > 25) {
                            title.classList.add('fancytree_title_class');
                        } else {
                            title.classList.remove('fancytree_title_class');
                        }
                    });
                }, 4000);
            } else {
                setTimeout(async () => {
                    let titles = await document.getElementsByClassName('fansytree_title_value');
                    titles.forEach(title => {
                        if (title.textContent.length > 130) {
                            title.classList.add('fancytree_title_class');
                        } else {
                            title.classList.remove('fancytree_title_class');
                        }
                    });
                }, 4000);
            }
        });
        // End add class

        // Drag and drop support
        $('.tree-drag').fancytree({
            extensions: ['dnd', 'persist'],
            dnd: {
                autoExpandMS: 400,
                focusOnClick: false,
                preventRecursiveMoves: true, // Prevent dropping nodes on own descendants
                preventVoidMoves: true, // Prevent dropping nodes 'before self', etc.
                dragStart: function (node, data) {
                    return !node.key.includes('lang:');
                },
                dragEnter: function (node, data) {
                    // limit category nesting   level
                    return node.getLevel() < {{ limit_tree_level }};
                },
                dragDrop: async function (node, data) {
                    let res = false
                    if (node.key.includes(':')) {
                        console.log(data.otherNode.key, node.key.split(':')[1])
                        // user updating node to new language
                        res = await update_language(data.otherNode.key, node.key.split(':')[1]);

                    } else {
                        res = await update(data.hitMode, data.otherNode.key, node.key)
                    }
                    if (res) data.otherNode.moveTo(node, data.hitMode);

                }
            },
            persist: {
                // Available options with their default:
                cookieDelimiter: "~",    // character used to join key strings
                cookiePrefix: undefined, // 'fancytree-<treeId>-' by default
                cookie: { // settings passed to jquery.cookie plugin
                    raw: false,
                    expires: "",
                    path: "",
                    domain: "",
                    secure: false
                },
                expandLazy: false, // true: recursively expand and load lazy nodes
                expandOpts: undefined, // optional `opts` argument passed to setExpanded()
                overrideSource: true,  // true: cookie takes precedence over `source` data attributes.
                store: "local",     // 'cookie': use cookie, 'local': use localStore, 'session': use sessionStore
                types: "active expanded focus selected"  // which status types to store
            },

            init: function (event, data) {
                $('.has-tooltip .fancytree-title').tooltip();
            },
            source: {
                url: "{{ list_url }}",
                cache: false
            },
            dblclick: function (event, data) {
                let node_id = data.node.key
                if (!isNaN(node_id)) {
                    document.location.href = `{% url 'admin:index' %}{{ app_label }}/{{ opts.model_name }}/${node_id}/`
                }
            },

        });

        function update(hitMode, srcNode, otherNode) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: "{{ update_url }}",
                    type: 'POST',
                    data: {
                        hitMode: hitMode,
                        srcNode: srcNode,
                        otherNode: otherNode,
                    },
                    success: (resp) => {
                        resolve(resp)
                    },
                    error: (err) => {
                        alert(`error: ${err.responseJSON.data}`)
                        reject(err)
                    }
                })
            })
        }

        async function update_language(nodeID, langID) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: "{{ update_url }}change_lang/",
                    type: 'POST',
                    data: {
                        nodeID: nodeID,
                        langID: langID,
                    },
                    success: (resp) => {
                        resolve(resp)
                    },
                    error: (err) => {
                        alert(`error status ${err.status}`)
                        reject(err)
                    }
                })
            })
        }

        function edit(node_id) {
            $.ajax({
                url: '/admin/fancy/' + node_id + "/edit",
                type: 'get',
                success: (cat) => {
                    $("#modal_small input[name=title]").val(cat.title)
                    $("#modal_small input[name=category_id]").val(cat.id)
                    $("#modal_small").modal()
                },

            })
        }


        {% comment %}
            -------------------------------------------------
                reload tree data function | use for later if needed
            -------------------------------------------------
                function loadData(lang_id = null) {
                    $.ajax({
                        type: "GET",
                        data: {
                            lang_id: lang_id,
                        },
                        url: "{{ list_url }}",
                        success: function (result) {
                            if (!result.length) {
                                result = [{
                                    key: 0,
                                    title: 'No Item Found'
                                }]
                            }
                            let tree = $('.tree-drag').fancytree('option', 'source', result);
                        }
                    });
                }
        {% endcomment %}


    </script>

{% endblock %}
