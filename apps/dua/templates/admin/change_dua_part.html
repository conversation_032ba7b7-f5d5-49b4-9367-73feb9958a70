{% extends 'admin/change_form.html' %}
{% load static %}

{% block scripts %}
    {{ block.super }}

<style>
h3.card-title label {
    display: none;
}
</style>
    <script src="{% static 'dic.js' %}"></script>
<script>
function init_editor_json_editor(element_id) {
    let editor = document.getElementById(element_id);
    let schema_str = JSON.parse(editor.value || "[]"); // مقدار پیش‌فرض برای جلوگیری از خطا
    $(editor).addClass("hidden");

    let json_viewer_div = $(`<div class="json-view-editor" id="${element_id}-view-editor"></div>`);
    $(editor).parent().append(json_viewer_div);
    $(editor).parents(".col-lg-10.pl-0").addClass("col-lg-12").removeClass("col-lg-10");

    let properties = {
        text: {type: 'string', format: 'textarea', title: "Text"},
        language_code: {
            type: "string",
            enum: JSON.parse("{{ languages | escapejs }}"),
            default: "en",
            title: "Language"
        }
    };

    // ایجاد نمونه جدید برای این ادیتور
    let jsoneditor_instance = new JSONEditor(
        json_viewer_div[0],
        {
            theme: 'bootstrap4',
            schema: {
                type: "array",
                format: 'table',
                title: 'Details',
                items: {
                    type: 'object',
                    title: 'Translation',
                    properties: properties
                },
            },
            disable_edit_json: true,
            disable_properties: false,
            disable_array_delete_all_rows: true,
            disable_array_delete_last_row: true,
            disable_array_reorder: true,
            grid_columns: 1,
            prompt_before_delete: false,
            disable_collapse: true,
            startval: schema_str
        }
    );

    // مدیریت تغییرات و بروزرسانی مقدار ورودی
    jsoneditor_instance.on('change', () => {
        $(editor).val(JSON.stringify(jsoneditor_instance.getValue()));
    });

    // بازگرداندن نمونه برای ذخیره یا دسترسی در آینده
    return jsoneditor_instance;
}

// نمونه‌ها را در یک لیست ذخیره کنید
let editors = {
    'id_translation': init_editor_json_editor('id_translation'),
    'id_description': init_editor_json_editor('id_description'),
    'id_local_alpha': init_editor_json_editor('id_local_alpha')
};

    </script>

{% endblock %}