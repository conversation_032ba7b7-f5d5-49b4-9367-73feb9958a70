{% extends 'admin/base_site.html' %}
{% load i18n admin_urls static admin_modify %}
{% block styles %}
    <link href="{% static 'mafatih-style.css' %}" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noty/3.1.4/noty.min.css"/>
{% endblock %}
{% block content %}
    <div class="p-3">
        <form action="" method="post" id="{{ opts.model_name }}_form" novalidate>
            {% csrf_token %}

            <div class="hidden">
                {% for fieldset in adminform %}
                    {% include "admin/includes/fieldset.html" %}
                {% endfor %}
            </div>

            <div class="row">
                <div class="col-4">
                    <div class="card bg-dark">
                        <div class="card-header align-content-end">
                            <div class="col-12">
                                <p>جهت از بین نرفتن ویرایش شما هر 5 دقیقه ویرایش های شما ذخیره میشود</p>
                                <button class="form-control btn btn-success h3" type="submit">ذخیره صوت تقطیع شده
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-8">
                    <div class="card bg-dark">
                        <div class="card-header align-content-end">
                            <div class=" text-right rtl" style="line-height: 2.3em;">
                                <b>راهنما</b>
                                <p> انتقال به ستون تقطیع شده <span class="rounded-lg badge-dark px-3 py-1">ctrl</span>
                                </p>
                                <p> pause audio <span class="rounded-lg badge-dark px-3 py-1">0</span></p>
                                <p> play audio <span class="rounded-lg badge-dark px-3 py-1">1</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="audio">
                                <audio controls id="audio">
                                    <source src="{{ original.audio.url }}">
                                </audio>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-6 div-card-body-forms">
                    <div class="card clipped">
                        <div class="card-header bg-light h2 text-center text-grey-400">
                            تقطیع شده
                        </div>
                        <div class="card-body">
                            <ul class="dua-parts">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 div-card-body-forms">
                    <div class="card unclipped">
                        <div class="card-header bg-light h2 text-center text-grey-400">
                            تقطیع نشده
                        </div>
                        <div class="card-body">

                            <ul class="dua-parts">
                                {% for part in original.dua.Dua_part.all %}
                                    {% if part.text %}
                                        <li data-part-id="{{ part.id }}">
                                            <div class="dua-text">
                                                {{ part.text }}
                                            </div>
                                        <div class="text-muted  text-xsmall mt-1" style="opacity: 0.6;">
                                            {% if part.translation %}
                                                {% if part.translation|length > 0 and part.translation.0.language_code %}
                                                    {% for tr in part.translation %}
                                                        {% if tr.language_code == 'fa' %}
                                                            {{ tr.text }}
                                                        {% endif %}
                                                    {% endfor %}
                                                {% else %}
                                                    {{ part.translation }}
                                                {% endif %}
                                            {% endif %}
                                        </div>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>


                <div class="cut-notif hidden">
                    برش صوت فعال است
                </div>
            </div>

        </form>
    </div>




{% endblock %}

{% block scripts %}
    {{ block.super }}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/noty/3.1.4/noty.min.js"></script>
    <script src="https://rawgit.com/jeresig/jquery.hotkeys/master/jquery.hotkeys.js"></script>
    <script>

        function goToTime(e) {
            let audio = document.getElementById("audio");
            audio.currentTime = $(e).text();
        }

        // document.getElementById("unclipped-audio").defaultPlaybackRate = 3.0;
        function pushToUnClipped(e) {
            let part_id = $(e).parent().attr('data-part-id')
            let trackerInput = $('#id_audio_sync_data');
            let trackerInputArr = JSON.parse(trackerInput.val());


            if (trackerInputArr.length == 1) {
                trackerInput.val('[]');
            } else {
                for (i in trackerInputArr) {
                    console.log(i.id + 'ssssssssssssssssssssss')
                    if (trackerInputArr[i].id == part_id) {
                        console.log(trackerInputArr[i].id + 'ssssssssssssssssssssss')
                        console.log(trackerInputArr[i - 1] + 'trackerInputArr[i-1]')
                        console.log(trackerInputArr[i - 1] + 'trackerInputArr[i-1]')

                        lastTime = trackerInputArr[i - 1].duration[trackerInputArr[i - 1].duration.length - 1][1];
                        trackerInputArr.splice(i, 1)
                        console.log(lastTime)
                    }
                }
                trackerInput.val(JSON.stringify(trackerInputArr));
            }
            $(e).parent().removeClass('text-warning');
            $('.unclipped ul.dua-parts').prepend($(e).parent().clone())
            $(e).parent().remove()
            $('#pushToUnclipped').remove()
            $('.unclipped ul.dua-parts li:first-child .clipped-times ').remove()
            $('.clipped ul.dua-parts li:last-child').append(`
                <button type="button" id="pushToUnclipped" onclick="pushToUnClipped(this)" class="btn btn-danger rounded-pill"><i class="icon-arrow-right7 mr-2"></i></button>
                `)
        }

        let lastTime = 0;
        let cutEnable = false;
        $('.cut-notif').addClass('hidden')

        function clipAudio() {

            let unclipped = document.getElementById("audio");
            let cur_time = unclipped.currentTime;
            let cur_item = $('.unclipped ul.dua-parts li:first-child')
            $('.clipped ul.dua-parts').append(cur_item);
            let trackerInput = $('#id_audio_sync_data');
            if (cur_item.data('part-id') != undefined) {
                if (cutEnable == true) {
                    if (trackerInput.val() != '[]') {
                        let newTrack = [lastTime, cur_time]
                        let trackerInputArr = JSON.parse(trackerInput.val());
                        trackerInputArr[trackerInputArr.length - 1]["duration"].push(newTrack)
                        trackerInput.val(JSON.stringify(trackerInputArr));
                    }
                } else {


                    if (trackerInput.val() != '[]') {
                        let trackerInputArr = JSON.parse(trackerInput.val());
                        if (trackerInputArr[trackerInputArr.length - 1]["id"] == cur_item.data('part-id')) {
                            let newTrack = [lastTime, cur_time]
                            trackerInputArr[trackerInputArr.length - 1]["duration"].push(newTrack)
                            trackerInput.val(JSON.stringify(trackerInputArr));
                        } else {
                            console.log('asaaaaaa')
                            let trackerInputArr = JSON.parse(trackerInput.val());
                            let newTrack = [{
                                "id": cur_item.data('part-id'),
                                "duration": [[lastTime, cur_time]]
                            }]
                            trackerInputArr.push(newTrack[0])
                            trackerInput.val(JSON.stringify(trackerInputArr));

                        }

                    } else {
                        let newTrack = [{
                            "id": cur_item.data('part-id'),
                            "duration": [[0, cur_time]]
                        }]
                        trackerInput.val(JSON.stringify(newTrack));
                    }
                }

                $('#pushToUnclipped').remove();
                $('.clipped ul.dua-parts li:last-child').append(`
                <button type="button" id="pushToUnclipped" onclick="pushToUnClipped(this)" class="btn btn-danger rounded-pill"><i class="icon-arrow-right7 mr-2"></i></button>
                `)
                let trackerInputArr = JSON.parse(trackerInput.val());
                last_end_d = undefined;
                for (i of trackerInputArr) {
                    if (i.id == cur_item.data('part-id')) {
                        $('.clipped ul.dua-parts li:last-child').append(
                            `<div class="clipped-times"></div>`
                        )
                        for (d of i.duration) {
                            if (typeof last_end_d !== 'undefined') {
                                $('.clipped ul.dua-parts li:last-child .clipped-times').append(
                                    `<div class="clipped-time">
<span class="badge bg-danger" onclick="goToTime(this)" data-toggle="tooltip" title="به عنوان نامفید برش داده شده">` + last_end_d.toFixed(2) + `</span>
<span>to</span>
<span class="badge bg-danger" onclick="goToTime(this)" data-toggle="tooltip" title="به عنوان نامفید برش داده شده">` + d[0].toFixed(2) + `</span>
</div>`
                                )
                            }
                            $('.clipped ul.dua-parts li:last-child .clipped-times').append(
                                `<div class="clipped-time">
<span class="badge" onclick="goToTime(this)">` + d[0].toFixed(2) + `</span>
<span>to</span>
<span class="badge" onclick="goToTime(this)">` + d[1].toFixed(2) + `</span>
</div>`
                            )
                            last_end_d = d[1];
                        }
                    }
                }

                lastTime = cur_time;
                cutEnable = false;
                $('.cut-notif').addClass('hidden')
            }

        }

        function clipAudioWithoutDuaPart() {

            let unclipped = document.getElementById("audio");
            let cur_time = unclipped.currentTime;
            let cur_item = $('.unclipped ul.dua-parts li:first-child')
            let trackerInput = $('#id_audio_sync_data');

            if (cutEnable == true) {
                cutEnable = false;
                $('.cut-notif').addClass('hidden')
            } else {
                if (trackerInput.val() != '[]') {
                    let trackerInputArr = JSON.parse(trackerInput.val());
                    if (trackerInputArr[trackerInputArr.length - 1]["id"] != cur_item.data('part-id')) {
                        let newTrack = [{
                            "id": cur_item.data('part-id'),
                            "duration": [[lastTime, cur_time]]
                        }]
                        trackerInputArr.push(newTrack[0])
                    } else {
                        let newTrack = [lastTime, cur_time]
                        trackerInputArr[trackerInputArr.length - 1]["duration"].push(newTrack)
                    }
                    trackerInput.val(JSON.stringify(trackerInputArr));
                } else {
                    let newTrack = [{
                        "id": cur_item.data('part-id'),
                        "duration": [[0, cur_time]]
                    }]
                    trackerInput.val(JSON.stringify(newTrack));
                }
                cutEnable = true;
                $('.cut-notif').removeClass('hidden')
            }
            lastTime = cur_time;

        }

        function partHighlighter() {
            let trackerInput = $('#id_audio_sync_data');
            if (trackerInput.val() != '[]') {
                let trackerInputArr = JSON.parse(trackerInput.val());
                let clipped = document.getElementById("audio");
                let cur_time = clipped.currentTime;
                for (i of trackerInputArr) {
                    for (d of i['duration']) {
                        if (cur_time > d[0] && cur_time < d[1]) {
                            $('li[data-part-id]').each(function () {
                                $(this).removeClass('text-warning');
                            })
                            $('li[data-part-id=' + i.id + ']').addClass('text-warning')
                        } else {
                            $('li[data-part-id=' + i.id + ']').removeClass('text-warning');
                        }
                    }
                }
            }
        }

        const clippedAudio = document.getElementById("audio");
        clippedAudio.ontimeupdate = function () {

            partHighlighter()
        };
        var audio = $('#audio')[0];
        $(document).bind('keydown', 'ctrl', clipAudio);
        $(document).bind('keydown', '0', audio_pouse);
        $(document).bind('keydown', '1', audio_play);
        {#$(document).bind('keydown', 'c', clipAudioWithoutDuaPart);#}

        function audio_pouse() {
            audio.pause();
        }

        function audio_play() {
            audio.play();
        }


    </script>

    <script>
        setInterval(function () {
            $.ajax({
                url: $('form').attr('action'),
                type: 'post',
                data: $('form').serialize(),
                async: false,
                success: function (data) {
                    new Noty({
                        text: 'آخرین تغییرات شما ذخیره شد',
                        type: 'success'
                    }).show();
                },
                error: function () {
                    new Noty({
                        text: 'ذخیره سازی انجام نشد',
                        type: 'error'
                    }).show();
                }
            });
        }, 5 * 60 * 1000);

        if ($('#initial-id_audio_sync_data').val() != "[]") {
            let trackerInputArr = JSON.parse($('#initial-id_audio_sync_data').val());

            for (i of trackerInputArr) {
                last_end_d = undefined;
                let cur_item = $('.unclipped ul.dua-parts li:first-child')
                $('#pushToUnclipped').remove();
                $('.clipped ul.dua-parts').append(cur_item);
                $('.clipped ul.dua-parts li:last-child').append(`
                <button type="button" id="pushToUnclipped" onclick="pushToUnClipped(this)" class="btn btn-danger rounded-pill"><i class="icon-arrow-right7 mr-2"></i></button>
                `)
                if (i.id == cur_item.data('part-id')) {
                    console.log('kkkk')
                    $('.clipped ul.dua-parts li:last-child').append(
                        `<div class="clipped-times"></div>`
                    )
                    for (d of i.duration) {
                        if (typeof last_end_d !== 'undefined') {
                            $('.clipped ul.dua-parts li:last-child .clipped-times').append(
                                `<div class="clipped-time">
<span class="badge bg-danger" onclick="goToTime(this)" data-toggle="tooltip" title="به عنوان نامفید برش داده شده">` + last_end_d.toFixed(2) + `</span>
<span>to</span>
<span class="badge bg-danger" onclick="goToTime(this)" data-toggle="tooltip" title="به عنوان نامفید برش داده شده">` + d[0].toFixed(2) + `</span>
</div>`)
                        }
                        $('.clipped ul.dua-parts li:last-child .clipped-times').append(
                            `<div class="clipped-time">
<span class="badge" onclick="goToTime(this)">` + d[0].toFixed(2) + `</span>
<span>to</span>
<span class="badge" onclick="goToTime(this)">` + d[1].toFixed(2) + `</span>
</div>`
                        )
                        last_end_d = d[1];
                    }
                }
                lastTime = i.duration[i.duration.length - 1][1];

            }
        }


    </script>
{% endblock %}
