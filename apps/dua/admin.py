import json
import re

from ajaxdatatable.admin import AjaxDatatable
from ajaxdatatable.import_mixin import ImportMixin
from dj_category.admin import BaseCategoryAdmin
from dj_category.models import BaseCategoryAbstract
from dj_language.models import Language
from django import forms
from django.contrib import admin, messages
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import Count, Q
from django.http.response import JsonResponse
from django.shortcuts import redirect
from django.urls import path
from django.utils.safestring import mark_safe
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from djongo import models
from import_export import resources
from import_export.forms import ConfirmImportForm
from nwh_seo.admin import SeoAdminInline
from utils.json_editor_field import JsonEditorWidget
from utils import get_translation_schema
from mptt.forms import TreeNodeMultipleChoiceField

from .models import (
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>a<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>aR<PERSON>iter,
    DuaSeo
)

class DuaSeoInline(admin.StackedInline):
    model = DuaSeo
    extra = 0
    max_num = 0
    fieldsets = (
        ('', {
            # 'classes': ('collapse',),
            'fields': ('title', 'description', 'language')
        }),
    )
    
class DuaForm(forms.ModelForm):
    category = TreeNodeMultipleChoiceField(
        queryset=DuaCategory.objects.all(),
        required=False,
        widget=admin.widgets.FilteredSelectMultiple('categories', False)
    )

    class Meta:
        model = Dua
        fields = '__all__'
        
@admin.register(Dua)
class DuaAdmin(AjaxDatatable):
    form = DuaForm
    change_form_template = 'admin/change_dua_.html'
    list_display = ('title', '_categories', '_language', '_parts_count', '_has_audio', '_change_audio', 'updated_at', '_visibility')
    search_fields = ('title_translations', 'id')
    # list_filter = ()
    inlines = [DuaSeoInline]
    exclude = ('audio_sync_data', 'audio')
    ordering = ('-id',)
    autocomplete_fields = ('category',)
    readonly_fields = ('created_at', 'updated_at')  
    
    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):
        languages = list(Language.objects.filter(status=True).values_list('code', flat=True))
        extra_context = {
            "languages": json.dumps(languages)
        }

        return super().changeform_view(request, object_id, form_url, extra_context)

    @admin.display(description=_("Audio"), ordering='audio_count')
    def _change_audio(self, obj: Dua):
        # info = self.model._meta.app_label, self.model._meta.model_name
        return mark_safe(
            f'<a href="/admin/dua/duaaudio/?dua__id__exact={obj.id}">Manage Audios ({obj.audio_count})</a>'
        )

    @admin.display(description=_('Visibility'), ordering='status')
    def _visibility(self, obj):
        return mark_safe("<span class='badge badge-success'>True</span>" if obj.status else "<span class='badge badge-secondary'>False</span>")

    @admin.display(description=_('Categories'))
    def _categories(self, obj):
        return ", ".join([cat.get_translation('fa') for cat in obj.category.all()])

    @admin.display(description=_('Language'), ordering='category__language')
    def _language(self, obj):
        if isinstance(obj.title_translations, list):
            # استخراج کدهای زبان از هر آیتم در لیست
            langs = {tr.get('language_code', '') for tr in obj.title_translations if 'language_code' in tr}
        else:
            langs = []
        return ", ".join(sorted(langs))

    @admin.display(description=_('Parts Count'), ordering='parts_count')
    def _parts_count(self, obj):
        return mark_safe(f'<a href="/admin/dua/duapart/?dua__id__exact={obj.id}">{obj.Dua_part.count()}</a>')

    @admin.display(description=_('Has synced audio'), ordering="audio_count")
    def _has_audio(self, obj):
        if not obj.audios.exists():  # اگر فایل صوتی مرتبط وجود ندارد
            return mark_safe("<span>No</span>")
        return mark_safe("<span>Yes</span>")

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        # استفاده از prefetch_related برای بهینه‌سازی دسته‌بندی‌ها و annotate برای شمارش
        return queryset.prefetch_related('category').annotate(
            audio_count=Count("audios", distinct=True),
            parts_count=Count("Dua_part", distinct=True)
        )


# @admin.register(DuaCategory)
# class DuaCategoryAdmin(BaseCategoryAdmin):
#     list_display = ('get_translation', 'order')
#     search_fields = ('name',)
#
#     def get_translation(self, obj):
#         return obj.get_translation('fa') or '-'
#     get_translation.short_description = _('Name')
#


@admin.register(DuaCategory)
class DuaCategoryAdmin(BaseCategoryAdmin):
    change_form_template = 'admin/category_index.html'
    change_list_template = change_form_template
    fields = (
        'name', 'parent', 'is_active', 'order'
    )
    search_fields = ('name', 'is_active')
    list_filter = ('is_active',)
    
    def get_categories_groupby_language(self, request=None, selected_values=(), is_multiple=False):
        return super().get_categories(request, selected_values, is_multiple)
    
    def to_dict(self, c, selected_values=()):
        children = c.get_children()
        title = '<span class="fansytree_title_value">' + str(c) + '</span>'

        return {
            'selected': c.id in [int(i) for i in selected_values],
            'key': c.id,
            'title': title,
            'parent_id': c.parent.id if c.parent else None,
            'children': [] if not children else [self.to_dict(i, selected_values) for i in children]
        }

    def ajax_update(self, request):
        data = request.POST
        src_node = self.model.objects.get(pk=int(data['srcNode']))
        other_node = self.model.objects.get(pk=int(data['otherNode']))

        if src_node.slug in self.base_categories or other_node.slug in self.base_categories:
            return JsonResponse({'data': _('This item can not be modifed')}, status=401)

        mode = data['hitMode']
        if mode == 'over':
            src_node.move_to(other_node, 'first-child')
        elif mode == 'after':
            src_node.move_to(other_node, 'right')
        elif mode == 'before':
            src_node.move_to(other_node, 'left')

        return JsonResponse({'data': 'ok'}, safe=False)

    def save_model(self, request, obj, form, change):
        obj.content_type = self.get_for_model()
        obj.save()


@admin.register(DuaPart)
class DuaPartAdmin(AjaxDatatable):
    list_display = ('_short_text', 'dua', 'dua_part_index')
    change_form_template = 'admin/change_dua_part.html'

    search_fields = ('dua__title', 'text', 'description')
    list_filter = ('dua_part_index',)
    ordering = ('dua_part_index',)
    autocomplete_fields = ('dua',)
    formfield_overrides = {
        models.JSONField: {'widget': JsonEditorWidget(attrs={'rows': 10})},
    }

    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):
        languages = list(Language.objects.filter(status=True).values_list('code', flat=True))
        extra_context = {
            "languages": json.dumps(languages)
        }

        return super().changeform_view(request, object_id, form_url, extra_context)


    @admin.display(description=_('Short Text'))
    def _short_text(self, obj):
        return obj.short_text()

    def save_model(self, request, obj, form, change):
        if not change and obj.dua_part_index is None:
            obj.dua_part_index = DuaPart.objects.filter(dua=obj.dua).count() + 1
        super().save_model(request, obj, form, change)


@admin.register(DuaAudio)
class DuaAudioDuaAdmin(AjaxDatatable):
    list_display = ('dua', 'reciter_relation', 'created_at', 'audio', '_sync_audio')
    search_fields = ('dua__title_translations', 'reciter_relation__name')
    autocomplete_fields = ('dua', 'reciter_relation')
    fields = ('dua', 'reciter_relation', 'audio', 'audio_sync_data')

    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):
        if request.GET.get('sync_audio', False):
            self.change_form_template = 'admin/duas_audio_syncer.html'
        else:
            self.change_form_template = 'admin/change_form.html'

        return super().changeform_view(request, object_id, form_url, extra_context)

    def get_fields(self, request, obj=None):
        if request.GET.get('sync_audio', False):
            return 'reciter_relation', 'audio', 'audio_sync_data',

        return 'dua', 'reciter_relation', 'audio', 'audio_sync_data',

    def save_model(self, request, obj, form, change):
        try:
            if not obj.dua_id:
                dua_id = request.GET.get('dua')
                if dua_id:
                    obj.dua_id = int(dua_id)
                else:
                    # Extract from changelist filters if available
                    changelist_filters = request.GET.get('_changelist_filters', '')
                    if 'dua__id__exact=' in changelist_filters:
                        obj.dua_id = int(changelist_filters.replace('dua__id__exact=', ''))
        except Exception as e:
            pass

        return super().save_model(request, obj, form, change)

    @admin.display(description=_('Sync Audio'))
    def _sync_audio(self, obj):
        return mark_safe(
            f'<a href="/admin/dua/duaaudio/{obj.id}/change/?sync_audio=true&dua={obj.dua.id}" class="btn btn-primary btn-sm">تقطیع صوت</a>'
        )


class DuaReciterForm(forms.ModelForm):
    class Meta:
        model = DuaReciter
        exclude = ()
        widgets = {
            'translations': JsonEditorWidget(attrs={'schema': get_translation_schema(), 'rows': 5}),
        }

@admin.register(DuaReciter)
class DuaReciterAdmin(AjaxDatatable):
    form = DuaReciterForm
    list_display = ('name',)
    search_fields = ('name',)
    prepopulated_fields = {'slug': ('name',)}
