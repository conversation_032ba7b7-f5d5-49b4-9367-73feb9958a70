from rest_framework.views import APIView
from rest_framework.response import Response
from django.http import HttpResponse
from django.contrib.sitemaps import Sitemap
from django.utils.translation import override
from django.urls import reverse

from apps.dua.models import Dua
import config.settings.base as settings


class RobotsTxtView(APIView):
    def get(self, request, *args, **kwargs):
        # محتوای robots.txt
        content = """
        User-agent: *
        Allow: /
        Sitemap: https://duasapp.com/sitemap.xml
        """
        
        # حذف فاصله‌های اضافی و کاراکترهای جدید خط
        content = content.strip().replace("        ", "")
        
        # بازگرداندن محتوا به عنوان پاسخ
        return Response(content, content_type='text/plain')
    


class DuasSitemap(Sitemap):
    changefreq = "monthly"
    i18n = True
    priority = 1
    domain = "duasapp.com"
    # changefreq = "weekly"
    # priority = 0.5
    protocol = 'https'
    
    def __init__(self, language_code):
        self.language_code = language_code
        super().__init__()

    def items(self):
        # بازگرداندن تمام آیتم‌ها بدون محدودیت
        # return Dua.objects.filter(status=True).order_by('updated_at')
        # print(f'--item-> {self.language_code}')
        return Dua.objects.filter(status=True).order_by('updated_at').distinct()

    def lastmod(self, obj):
        return obj.updated_at
    
    def get_urls(self, **kwargs):
        # urls = super().get_urls(**kwargs)
        # افزودن تگهای hreflang برای تمام زبانها
        for url in urls:
            url['xhtml:link'] = []
            for lang_code, _ in settings.LANGUAGES:
                alternate_url = self._get_alternate_url(url['location'], lang_code)
                url['xhtml:link'].append({
                    "rel": "alternate",
                    "hreflang": lang_code,
                    "href": alternate_url
                })
        return urls
    
class DuasSitemapDetail(Sitemap):
    changefreq = "monthly"
    priority = 1
    protocol = 'https'
    domain = "duasapp.com"

    def __init__(self, language_code):
        self.language_code = language_code
        super().__init__()
    
    
    def items(self):
        return Dua.objects.filter(status=True).order_by('updated_at')

    def lastmod(self, obj):
        return obj.updated_at
    
    def location(self, obj):
        slug = obj.get_slug(self.language_code)
        
        return f'/{self.language_code}/duas/{slug}/'
    
    def get_urls(self, page=1, site=None, protocol=None):
        protocol = self.get_protocol(protocol)
        domain = "duasapp.com"
        return self._urls(page, protocol, domain)



sitemap_detail = {
    "en": DuasSitemapDetail("en"),
    "ar": DuasSitemapDetail("ar"),
    "az": DuasSitemapDetail("az"),
    "fr": DuasSitemapDetail("fr"),
    "in": DuasSitemapDetail("in"),
    "fa": DuasSitemapDetail("fa"),
    "ru": DuasSitemapDetail("ru"),
    "es": DuasSitemapDetail("es"),
    "sw": DuasSitemapDetail("sw"),
    "tr": DuasSitemapDetail("tr"),
    "de": DuasSitemapDetail("de"),
    "en": DuasSitemapDetail("en"),
    "ur": DuasSitemapDetail("ur"),
    "zh": DuasSitemapDetail("zh"),
    "he": DuasSitemapDetail("he"),
    "bn": DuasSitemapDetail("bn"),
}


sitemaps = {
    f'{lang_code}': DuasSitemap(lang_code)
    for lang_code, _ in settings.LANGUAGES
}

