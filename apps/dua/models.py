from dj_category.field import Category<PERSON><PERSON>l<PERSON>ield
from dj_category.models import BaseCategory, BaseCategoryAbstract
from dj_language.field import LanguageField
from django.db import models
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from nwh_seo.fields import SeoGenericRelation
from filer.fields.image import Filer<PERSON><PERSON><PERSON>ield
from filer.fields.file import Filer<PERSON>ile<PERSON>ield
from limitless_dashboard.fields.comma_sep import CommaSepModelField
from apps.najm_calendar.fields import JsonDateField
from django.utils.text import slugify



class Dua(models.Model):
    title = models.CharField(max_length=192)
    title_translations = models.JSONField(verbose_name=_('translation'), default=dict)
    slug = models.JSONField(verbose_name=_('slug'), default=dict)
    dates = JsonDateField(help_text=_('لیستی از تاریخ های قمری مربوط به قطعه'), null=True, blank=True)
    weekdays = models.JSONField(default=dict, blank=True, verbose_name=_('weekdays'))

    category = models.ManyToManyField(
        'DuaCategory', related_name='Dua_second', blank=True
    )
    audio = FilerFileField(
        verbose_name=_('audio file'), null=True, blank=True, help_text=_('nullable'), related_name='+',
        on_delete=models.SET_NULL,
    )
    audio_sync_data = models.JSONField(default=list, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    priority = models.FloatField(null=True, blank=True, help_text=_('اولویت نمایش بر اساس این فیلد است'))
    status = models.BooleanField(default=True, verbose_name=_('visibility'), )

    lat = models.FloatField(null=True, blank=True, verbose_name=_('Latitude'), help_text=_('Latitude of the location for the dua'))
    lon = models.FloatField(null=True, blank=True, verbose_name=_('Longitude'), help_text=_('Longitude of the location for the dua'))
    location_name = models.CharField(max_length=255, null=True, blank=True, verbose_name=_('Location Name'), help_text=_('Name of the shrine or pilgrimage site based on the coordinates'))

    similar_titles = models.JSONField(null=True, blank=True, verbose_name=_('Similar Titles'), default=dict)

    class Meta:
        verbose_name = _('Dua')
        verbose_name_plural = _('Duas')
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['title']),
        ]
    def __str__(self):
        return self.title

    # def save(self, *args, **kwargs):
        # if self.title_translations:
            # self.generate_slugs()
        # super().save(*args, **kwargs)  
        
    def generate_slugs(self):
        try:
            if isinstance(self.title_translations, list):  
                slugs = []
                for translation in self.title_translations:
                    if isinstance(translation, dict):  
                        language_code = translation.get('language_code')
                        text = translation.get('text')
                        if language_code and text:  
                            slug_text = slugify(text, allow_unicode=True)
                            slugs.append({
                                'language_code': language_code,
                                'text': slug_text
                            })
                self.slug = slugs
            else:
                self.slug = []  
        except Exception as e:
            print(f"Error generating slugs: {e}")
            self.slug = []  
        
    def get_seo_for_language(self, language_code):
        try:
            seo_field_object = self.dua_seos.filter(language__code=language_code).first()
            if seo_field_object:
                seo_field = {
                    "title": seo_field_object.title,
                    "description": seo_field_object.description,
                }
                return seo_field
            return None
        except Exception as exp:
            return None 

    def get_translation(self, lang):
        try:
            for tr in self.title_translations:
                if tr['language_code'] == lang:
                    visibility = tr.get('visibility')
                    if visibility and visibility == 'invisible':
                        continue
                    return tr['text']
            return str(self)
        except Exception as exp:
            return str(self)
        
    def get_slug(self, lang):
        try:
            for tr in self.slug:
                if tr['language_code'] == lang:
                    return tr['text']
                
            slug = next(slug['text'] for slug in self.slug if slug['language_code'] == "en")
            if not slug:
                slug = slugify(title, allow_unicode=True)                
            return slug
        except Exception as exp:
            print(f'--> {exp}')
            return str(self)
        



class DuaPart(models.Model):
    dua = models.ForeignKey(Dua, on_delete=models.CASCADE, related_name='Dua_part')
    text = models.TextField(null=True, blank=True)
    translation = models.JSONField(verbose_name=_('translations'), null=True, blank=True, default=dict)
    local_alpha = models.JSONField(verbose_name=_('Local alpha'), null=True, blank=True, default=dict)
    description = models.JSONField(verbose_name=_('descriptions'), null=True, blank=True, default=dict)
    dua_part_index = models.FloatField(help_text=_('شماره قطعه در دعای مورد نظر'), null=True, blank=True)

    def __str__(self):
        return str(self.dua_part_index)

    def short_text(self):
        try:
            if self.text:
                return self.text[:30]
            elif self.description:
                first_description = self.description[0] if isinstance(self.description, list) else {}
                return first_description.get('text', _('No description available'))[:30]
            return self.local_alpha[0]['text'][:30]
        except Exception as exp:
            return "-"


    def get_part(self, lang, dua_field):
        try:
            # بررسی اینکه dua_field یک لیست است و خالی نیست
            if isinstance(dua_field, list) and dua_field:
                for tr in dua_field:
                    # بررسی اینکه tr یک دیکشنری است و کلید language_code دارد
                    if isinstance(tr, dict) and tr.get('language_code') == lang:
                        return tr.get('text')
            return None
        except Exception as exp:
            print(f'---> Error in get_part: {exp}')
            return None        
        

    class Meta:
        verbose_name = _('Dua parts')
        verbose_name_plural = _('Dua parts')
        ordering = ('dua_part_index', 'id')
        indexes = [
            models.Index(fields=['dua_part_index']),
            models.Index(fields=['dua']),
        ]



models.PositiveSmallIntegerField(verbose_name=_('order'), null=True, blank=True).contribute_to_class(BaseCategoryAbstract,
                                                                                                     'order')
class DuaCategory(BaseCategoryAbstract):

    content_type = None
    language = None
    name = models.JSONField(default=dict, verbose_name=_('name'))
    slug = None

    class Meta:
        verbose_name_plural = _('Duas categories')
        verbose_name = _('category')
        ordering = ('order',)


    def __str__(self):
        for lang in ['fa', 'en', 'ar']:
            for tr in self.name:
                if tr['language_code'] == lang:
                    return tr['text']

        return self.name[0]['text']

    def get_translation(self, lang, fallback='en'):
        for tr in self.name:
            if tr['language_code'] == lang:
                return tr['text']

        if fallback:
            for tr in self.name:
                if tr['language_code'] == fallback:
                    return tr.get('text', '')   
        return str(self)


class DuaAudio(models.Model):
    dua = models.ForeignKey(Dua, on_delete=models.CASCADE, related_name='audios')
    reciter = models.CharField(_('reciter'), max_length=119, )
    reciter_relation = models.ForeignKey('DuaReciter', on_delete=models.SET_NULL, related_name='dua_audios', verbose_name=_('reciter relation'), null=True, blank=True)
    audio = FilerFileField(
        verbose_name=_('audio file'), related_name='+',
        on_delete=models.PROTECT,
    )
    audio_sync_data = models.JSONField(default=list, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))

    def __str__(self):
        return self.reciter

    class Meta:
        verbose_name = _('dua audio')
        verbose_name_plural = _('duas audios')
        ordering = ('-id',)
        indexes = [
            models.Index(fields=['dua']),
            models.Index(fields=['reciter']),
        ]

class DuaReciter(models.Model):
    name = models.CharField(_('reciter name'), max_length=120)
    slug = models.SlugField(_('slug'), max_length=120, unique=True, blank=True)
    translations = models.JSONField(verbose_name=_('translations'), default=dict)
    avatar = FilerImageField(related_name='+', on_delete=models.PROTECT, verbose_name=_('thumbnail'), null=True, blank=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def get_translation(self, lang):
        for tr in self.translations:
            if tr['language_code'] == lang:
                return tr['title']
        return None

    class Meta:
        verbose_name = _('Dua Reciter')
        verbose_name_plural = _('Dua Reciters')


class DuaSeo(models.Model):
    dua = models.ForeignKey(
        Dua, on_delete=models.CASCADE,
        verbose_name='dua',
        related_name="dua_seos"
    )
    title = models.CharField(
        _('seo title'), max_length=140, null=True, blank=True,
        help_text=_('maximum length of page title is 70 characters and minimum length is 30'),
    )
    keywords = CommaSepModelField(
        null=True, blank=True,
        help_text=_('keywords in the content that make it possible for people to find the site via search engines')
    )

    description = models.CharField(
        max_length=170, null=True, blank=True,
        help_text=_('describes and summarizes the contents of the page for the benefit of users and search engines'),
    )
    language = LanguageField()
    
    class Meta:
        verbose_name = 'dua seo fields'
        verbose_name_plural = 'dua seo fields'

