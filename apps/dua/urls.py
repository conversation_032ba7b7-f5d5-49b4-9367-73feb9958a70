from django.urls import path, include
from django.urls import re_path

from apps.dua.views.v1 import *
from apps.dua.views.v2 import MafatihDuaViewV2
from apps.dua.views.v3 import MafatihDuaViewV3
from apps.dua.views.web import *
from apps.dua.sitemap import sitemaps, sitemap_detail, RobotsTxtView
from django.contrib.sitemaps import views as sitemap_views




# cache_duration = 60 * 60
cache_duration = 60
SITEMAP_CACHE_PREFIX = "sitemap_cache"


web_urlpatterns = [
    path('mafatih-categories/', WebDuaCategoriesView.as_view()),
    path('mafatih-duas/', WebDuaListView.as_view()),    
    #get dua parts
    re_path(r'mafatih-duas/(?P<slug>[\w-]+)/parts/$', WebDuaPartListView.as_view()),
    path('mafatih-duas/<int:pk>/parts/', WebDuaPartListView.as_view()),    
    
    path('mafatih-duas/dhikrs/', MafatihDhikrView.as_view()),
    # get dua reciters
    path('mafatih/<int:pk>/reciters/', WebDuaReciterList.as_view()),
    path('mafatih/<slug:slug>/reciters/', WebDuaReciterList.as_view()),    
    re_path(r'mafatih/(?P<slug>[\w-]+)/reciters/$', WebDuaReciterList.as_view()),

    # get dua audios
    path('mafatih/<int:pk>/audios/', WebDuaAudioDetailList.as_view()),
    re_path(r'mafatih/(?P<slug>[\w-]+)/audios/$', WebDuaAudioDetailList.as_view()),
]


urlpatterns = [
    # path('mafatih-duas/', cache_page(cache_duration)(MafatihDuaView.as_view())),
    # path('mafatih-duas/v2/', cache_page(cache_duration)(MafatihDuaViewV2.as_view())),
    path('mafatih-duas/v3/', MafatihDuaViewV3.as_view()),  # Language-aware caching moved to view
    # path('mafatih-duas/<int:pk>/audios/', cache_page(cache_duration)(MafatihDuaAudioView.as_view())),
    # path('mafatih-duas/audios/<int:pk>/', cache_page(cache_duration)(MafatihDuaAudioDetailView.as_view())),
    path('mafatih-dua-parts/v2/', MafatihDuaPartsView.as_view()),
    path('mafatih-categories/v2/', DuaCategoriesView.as_view()),
    path('mafatih-duas/famous/', MafatihFamousView.as_view()),
    path('mafatih-duas/dhikrs/', MafatihDhikrView.as_view()),
    path('mafatih-duas/location/', MafatihLocationView.as_view()),
    # path('mafatih/<int:mafatih_dua_id>/reciters/', cache_page(cache_duration)(MafatihDuaRecitersView.as_view())),

    path('web/', include((web_urlpatterns, 'web'), namespace='web-duas')),
    path('web/duas/robots.txt', RobotsTxtView.as_view(), name='robots_txt'),
    path(
        "web/duas/sitemap.xml",
        cache_page(60 * 60 * 24, key_prefix=SITEMAP_CACHE_PREFIX)(sitemap_views.index),  
        {"sitemaps": sitemaps},
        name="django.contrib.sitemaps.views.sitemap",
    ),
    path(
        "web/duas/sitemap-<section>.xml",
        sitemap_views.sitemap,
        {"sitemaps": sitemap_detail},
        name="django.contrib.sitemaps.views.sitemap",
    ),
]
