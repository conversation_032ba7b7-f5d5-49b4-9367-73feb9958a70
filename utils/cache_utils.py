"""
Cache utilities for better performance
"""
import json
import gzip
import pickle
from django.core.cache import cache
from django.core.serializers.json import DjangoJSONEncoder


def compress_cache_data(data):
    """Compress data before caching"""
    try:
        # Convert to JSON string
        json_str = json.dumps(data, cls=DjangoJSONEncoder, ensure_ascii=False)
        # Compress with gzip
        compressed = gzip.compress(json_str.encode('utf-8'))
        return compressed
    except Exception:
        # Fallback to original data
        return data


def decompress_cache_data(compressed_data):
    """Decompress cached data"""
    try:
        if isinstance(compressed_data, bytes):
            # Try to decompress
            decompressed = gzip.decompress(compressed_data)
            json_str = decompressed.decode('utf-8')
            return json.loads(json_str)
        else:
            # Not compressed, return as is
            return compressed_data
    except Exception:
        # Fallback to original data
        return compressed_data


def set_compressed_cache(cache_key, data, timeout=3600):
    """Set cache with compression"""
    compressed_data = compress_cache_data(data)
    cache.set(cache_key, compressed_data, timeout)
    return len(str(data)), len(compressed_data) if isinstance(compressed_data, bytes) else len(str(compressed_data))


def get_compressed_cache(cache_key):
    """Get and decompress cached data"""
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        return decompress_cache_data(cached_data)
    return None


def set_chunked_cache(cache_key, data, chunk_size=1000, timeout=3600):
    """Cache large data in chunks"""
    if not isinstance(data, list):
        # For non-list data, use regular caching
        cache.set(cache_key, data, timeout)
        return
    
    # Split data into chunks
    chunks = [data[i:i + chunk_size] for i in range(0, len(data), chunk_size)]
    
    # Cache metadata
    metadata = {
        'total_items': len(data),
        'chunk_count': len(chunks),
        'chunk_size': chunk_size
    }
    cache.set(f"{cache_key}_meta", metadata, timeout)
    
    # Cache each chunk
    for i, chunk in enumerate(chunks):
        chunk_key = f"{cache_key}_chunk_{i}"
        cache.set(chunk_key, chunk, timeout)


def get_chunked_cache(cache_key):
    """Get chunked cached data"""
    # Get metadata
    metadata = cache.get(f"{cache_key}_meta")
    if not metadata:
        return None
    
    # Reconstruct data from chunks
    data = []
    for i in range(metadata['chunk_count']):
        chunk_key = f"{cache_key}_chunk_{i}"
        chunk = cache.get(chunk_key)
        if chunk is None:
            # If any chunk is missing, return None
            return None
        data.extend(chunk)
    
    return data


def smart_cache_set(cache_key, data, timeout=3600, compression_threshold=10000):
    """Smart caching with automatic compression for large data"""
    data_size = len(str(data))
    
    if data_size > compression_threshold:
        # Use compression for large data
        original_size, compressed_size = set_compressed_cache(cache_key, data, timeout)
        return {
            'method': 'compressed',
            'original_size': original_size,
            'compressed_size': compressed_size,
            'compression_ratio': f"{(1 - compressed_size/original_size)*100:.1f}%"
        }
    else:
        # Use regular caching for small data
        cache.set(cache_key, data, timeout)
        return {
            'method': 'regular',
            'size': data_size
        }


def smart_cache_get(cache_key):
    """Smart cache get that handles both compressed and regular data"""
    cached_data = cache.get(cache_key)
    if cached_data is not None:
        # Try to decompress (will return original if not compressed)
        return decompress_cache_data(cached_data)
    return None
